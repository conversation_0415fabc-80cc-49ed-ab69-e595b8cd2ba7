plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def signPropertiesFile = rootProject.file("sign/key.properties")
def signProperties = new Properties()
signProperties.load(new FileInputStream(signPropertiesFile))
def signStoreFile = file(signProperties['storeFile'])

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.daya.app"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.daya.app"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24 //flutter.minSdkVersion
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        manifestPlaceholders = [
                "VIVO_APPKEY" : "197ef3c99027b895b0a8968e7ab40992",
                "VIVO_APPID" : "105848601",
                "HONOR_APPID" : "104488028"
        ]
    }

    signingConfigs {
        release {
            //如果秘钥库文件和配置文件存在就签名，否则不签名
            if (signStoreFile.exists() && signPropertiesFile.exists()) {
                keyAlias signProperties['keyAlias']
                keyPassword signProperties['keyPassword']
                storeFile signStoreFile
                storePassword signProperties['storePassword']
            }
        }
    }

    buildTypes {
        debug {
            ndk {
                abiFilters 'arm64-v8a', 'x86_64'
            }
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false //删除无用资源
        }
        release {
            // Signing with the debug keys for now, so `flutter run --release` works.
            ndk {
                abiFilters 'arm64-v8a'
            }
            signingConfig signingConfigs.release
            minifyEnabled true //删除无用代码
            shrinkResources true //删除无用资源
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // Huawei
    implementation 'com.tencent.timpush:huawei:8.4.6667'
    // XiaoMi
    implementation 'com.tencent.timpush:xiaomi:8.4.6667'
    // OPPO
    implementation 'com.tencent.timpush:oppo:8.4.6667'
    // vivo
    implementation 'com.tencent.timpush:vivo:8.4.6667'
    // Honor
    implementation 'com.tencent.timpush:honor:8.4.6667'
    // Meizu
    implementation 'com.tencent.timpush:meizu:8.4.6667'
}

apply plugin: 'com.huawei.agconnect'
apply plugin: 'com.hihonor.mcs.asplugin'