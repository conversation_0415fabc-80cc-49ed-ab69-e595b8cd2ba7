import 'dart:io';

import 'package:dada/configs/app_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/router_report.dart';
import 'package:dada/routers/router.dart';
import 'common/values/themes.dart';
import 'generated/l10n.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => MyAppState();
}

class MyAppState extends State<MyApp> with WidgetsBindingObserver {
  //通过顶层视图的key，可以获取OverlayState对象
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    FlutterGromoreAds.showSplashAd(AppConfig.splashId, preload: false);
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      builder: (context, child) {
        return GetMaterialApp(
          title: "搭吖",
          //开启FPS监控
          showPerformanceOverlay: kDebugMode ? false : false,
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: kDebugMode ? true : false,
          initialBinding: InitBinding(),
          initialRoute: GetRouter.splash,
          getPages: GetRouter.getPages,
          navigatorObservers: [GetXRouterObserver()],
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            S.delegate,
          ],
          supportedLocales: S.delegate.supportedLocales,
          defaultTransition: Transition.native,
          theme: Themes.lightAppTheme,
          darkTheme: Themes.lightAppTheme,
          themeMode: ThemeMode.light,
          builder: EasyLoading.init(builder: (context, child) {
            return GestureDetector(
              onTap: () {
                FocusScopeNode currentFocus = FocusScope.of(context);
                if (!currentFocus.hasPrimaryFocus &&
                    currentFocus.focusedChild != null) {
                  FocusManager.instance.primaryFocus?.unfocus();
                }
              },
              child: child,
            );
          }),
        );
      },
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    switch (state) {
      case AppLifecycleState.paused:
        // if (Get.isRegistered<ChatRoomController>() ||
        //     Get.isRegistered<MatchTeamChatController>() ||
        //     GlobalFloatingManager().currentIsShowMiniWindow()) {
        //   await Permission.microphone.request();
        //   startForegroundService();
        // }
        break;
      case AppLifecycleState.inactive:
        // if (Get.isRegistered<ChatRoomController>() ||
        //     Get.isRegistered<MatchTeamChatController>() ||
        //     GlobalFloatingManager().currentIsShowMiniWindow()) {
        //   stopForegroundService();
        // }
        break;
      default:
        break;
    }
  }

  @override
  void dispose() {
    // FlutterForegroundTask.removeTaskDataCallback(onReceiveTaskData);
    super.dispose();
  }
}

class InitBinding extends Bindings {
  @override
  void dependencies() {}
}

class GetXRouterObserver extends RouteObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    RouterReportManager.reportCurrentRoute(route);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) async {
    RouterReportManager.reportRouteDispose(route);
  }
}
