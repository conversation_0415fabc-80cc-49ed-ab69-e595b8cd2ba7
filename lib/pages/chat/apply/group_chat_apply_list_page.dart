import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/model/group_chat_application_entity.dart';
import 'package:dada/pages/chat/apply/group_chat_apply_controller.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class GroupChatApplyListPage extends StatefulWidget {
  final String groupID;
  const GroupChatApplyListPage({super.key, required this.groupID});

  @override
  State<GroupChatApplyListPage> createState() => _GroupChatApplyListPageState();
}

class _GroupChatApplyListPageState extends State<GroupChatApplyListPage> {
  final GroupChatApplyController controller = GroupChatApplyController();

  @override
  void initState() {
    super.initState();

    controller.groupID = widget.groupID;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFF5F5F5,
      appBar: CustomAppBar(
        title: "进群申请",
        backgroundColor: AppColors.colorFFF5F5F5,
      ),
      body: GetBuilder(
        init: controller,
        global: false,
        id: controller.refreshId,
        builder: (controller) {
          return RefreshWidget.build(
            refreshController: controller.refreshController,
            onRefresh: () => controller.refreshData(),
            onLoadMore: () => controller.loadMoreData(),
            child: controller.data.isEmpty
                ? const ListPageEmptyWidget()
                : ListView.separated(
                    padding: EdgeInsets.zero,
                    itemBuilder: (context, index) {
                      GroupChatApplicationEntity entity =
                          controller.data[index];
                      return _buildListItem(entity);
                    },
                    separatorBuilder: (context, index) {
                      return Container(
                        color: AppColors.colorFFF5F5F5,
                        height: 10.h,
                      );
                    },
                    itemCount: controller.data.length,
                  ),
          );
        },
      ),
    );
  }

  Widget _buildListItem(GroupChatApplicationEntity application) {
    return Container(
      padding:
          EdgeInsets.only(left: 15.w, right: 15.w, top: 15.h, bottom: 10.h),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 2.5.h),
                child: GestureDetector(
                  onTap: () {},
                  child: ClipOval(
                    child: ImageUtils.getImage(
                      application.fromUserFaceUrl ?? "",
                      40.w,
                      40.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      application.fromUserNickName ?? "",
                      style: TextStyles.normal(16.sp),
                    ),
                    Container(
                      padding: EdgeInsets.only(top: 5.h),
                      constraints: BoxConstraints(
                        maxWidth: 186.w,
                      ),
                      child: Text(
                        "申请加入 【${application.groupName}】",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: TextStyles.common(14.sp, AppColors.colorFF999999,
                            h: 1.3),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 8.h),
                      child: Text(
                        application.requestMsg ?? "你好，我想加入群",
                        style:
                            TextStyles.common(14.sp, AppColors.colorFF999999),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          /// 0：未处理，1：被他人处理，2.自己处理
          application.handleStatus != 0
              ? Padding(
                  padding: EdgeInsets.only(top: 10.h),
                  child: Text(
                    application.handleResult == 0
                        ? "已拒绝"
                        : application.handleResult == 1
                            ? "已同意"
                            : "已过期",
                    style: TextStyles.common(14.sp, AppColors.colorFF666666),
                  ),
                )
              : Padding(
                  padding: EdgeInsets.only(top: 10.h),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          controller.acceptApplication(
                              application.groupID!,
                              application.fromUser!,
                              application.requestMsg,
                              application.addTime);
                        },
                        child: Container(
                          width: 50.w,
                          height: 30.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15.r),
                            color: AppColors.colorFF89E15C,
                          ),
                          child: Text(
                            "同意",
                            style: TextStyles.normal(14.sp),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 6.3.w,
                      ),
                      GestureDetector(
                        onTap: () {
                          controller.refuseApplication(
                              application.groupID!,
                              application.fromUser!,
                              application.addTime!,
                              application.requestMsg);
                        },
                        child: Container(
                          width: 50.w,
                          height: 30.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15.r),
                            border: Border.all(color: AppColors.colorFF999999),
                          ),
                          child: Text(
                            "拒绝",
                            style: TextStyles.normal(14.sp),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
        ],
      ),
    );
  }

  @override
  void dispose() {
    ChatIMManager.sharedInstance.clearGroupApplicationRead();
    super.dispose();
  }
}
