import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/group_chat_application_entity.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_application.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';

class GroupChatApplyController extends ListPageController<
    GroupChatApplicationEntity, GroupChatApplyController> {
  late String groupID;

  @override
  void onReady() {
    super.onReady();
  }

  @override
  Future<List<GroupChatApplicationEntity>?> loadData(int page) async {
    List<V2TimGroupApplication?>? timList =
        await ChatIMManager.sharedInstance.getGroupApplicationList();
    List<GroupChatApplicationEntity> list = [];
    if (timList?.isNotEmpty == true) {
      for (int i = 0; i < timList!.length; i++) {
        V2TimGroupApplication? application = timList[i];
        if (application != null && application.groupID == groupID) {
          V2TimGroupInfo? groupInfo = await ChatIMManager.sharedInstance
              .getGroupInfo(application.groupID);
          if (groupInfo != null) {
            Map<String, dynamic> jsonMap = application.toJson();
            GroupChatApplicationEntity entity =
                GroupChatApplicationEntity.fromJson(jsonMap);
            entity.groupName = groupInfo.groupName;
            list.add(entity);
          }
        }
      }
    }
    return list;
  }

  void acceptApplication(String groupID, String toUser, String? reason, int? addTime) async {
    ToastUtils.showLoading();
    bool success = await ChatIMManager.sharedInstance
        .acceptJoinGroupApplication(groupID, toUser, reason: reason, addTime: addTime);
    ToastUtils.hideLoading();
    if (success) {
      refreshData();
      EventBusEngine.fire(event: BusEvent.receiveGroupApplication);
      update();
    }
  }

  void refuseApplication(String groupID, String fromUser, int addTime, String? reason) async {
    ToastUtils.showLoading();
    bool success = await ChatIMManager.sharedInstance
        .refuseJoinGroupApplication(groupID, fromUser, addTime, reason);
    ToastUtils.hideLoading();
    if (success) {
      refreshData();
      EventBusEngine.fire(event: BusEvent.receiveGroupApplication);
    }
  }
}
