import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';

class JoinGroupChatApplySendPage extends StatefulWidget {
  final String groupID;

  const JoinGroupChatApplySendPage({super.key, required this.groupID});

  @override
  State<JoinGroupChatApplySendPage> createState() =>
      _JoinGroupChatApplySendPageState();
}

class _JoinGroupChatApplySendPageState
    extends State<JoinGroupChatApplySendPage> {
  final TextEditingController editingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "申请加群",
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w, top: 6.h),
            child: Text(
              "申请加群原因",
              style: TextStyles.common(14.sp, AppColors.colorFF666666),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.h, vertical: 10.h),
            child: Container(
              height: 100.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5.r),
                color: AppColors.colorFFF5F5F5,
              ),
              child: Stack(
                children: [
                  CustomTextField.build(
                    controller: editingController,
                    maxLength: 15,
                    showLeftLength: true,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: CommonGradientBtn(
              title: "发送申请",
              onTap: () {
                sendJoinGroupApplication();
              },
            ),
          ),
        ],
      ),
    );
  }

  void sendJoinGroupApplication() async {
    bool success = await ChatIMManager.sharedInstance.joinGroup(
        groupType: GroupType.Public,
        groupID: widget.groupID,
        message: editingController.text);
    if (success) {
      ToastUtils.showToast("申请已发送");
      Get.back();
    }
  }
}
