import 'package:dada/services/im/chat_im_manager.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';

class InviteJoinGroupMiddleController extends GetxController {
  RxBool hasJoined = false.obs;
  String? groupID;

  @override
  void onReady() {
    super.onReady();

    loadJoinedGroupStatus();
  }

  void loadJoinedGroupStatus() async {
    List<V2TimGroupInfo>? joinedGroupList =
        await ChatIMManager.sharedInstance.getJoinedGroupList();
    if (joinedGroupList?.isNotEmpty == true) {
      hasJoined.value =
          joinedGroupList!.where((e) => e.groupID == groupID).isNotEmpty;
    }
  }
}
