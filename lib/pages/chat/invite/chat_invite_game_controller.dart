import 'package:dada/model/invite_play_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

class ChatInviteGameController extends GetxController {
  List<InvitePlayEntity>? invitePlayList;
  bool loading = true;

  bool editMode = false;

  @override
  void onReady() {
    _initGames();
    super.onReady();
  }

  void _initGames() async {
    invitePlayList = await ApiService().queryInvitePlayList();
    loading = false;
    update();
  }

  void refreshGames() async {
    loading = true;
    update();
    invitePlayList = await ApiService().queryInvitePlayList();
    loading = false;
    update();
  }

  void addGame(String text) {
    ApiService().addInvitePlay(text).then((value) {
      if (value) {
        refreshGames();
      }
    });
  }

  void deleteGame(InvitePlayEntity entity) {
    ApiService().deleteInvitePlay(entity.id ?? "").then((value) {
      if (value) {
        refreshGames();
      }
    });
  }

  void updateGame(InvitePlayEntity entity, result) {
    ApiService().updateInvitePlay(entity.id ?? "", result).then((value) {
      if (value) {
        refreshGames();
      }
    });
  }
}
