import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat/apply/join_group_chat_apply_send_page.dart';
import 'package:dada/pages/chat/invite/invite_join_group_middle_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class InviteJoinGroupMiddlePage extends StatefulWidget {
  const InviteJoinGroupMiddlePage({super.key});

  @override
  State<InviteJoinGroupMiddlePage> createState() =>
      _InviteJoinGroupMiddlePageState();
}

class _InviteJoinGroupMiddlePageState extends State<InviteJoinGroupMiddlePage> {
  final InviteJoinGroupMiddleController controller =
      Get.put(InviteJoinGroupMiddleController());
  final String? groupID = Get.arguments["groupID"];
  final String? faceUrl = Get.arguments["faceUrl"];
  final String? groupName = Get.arguments["groupName"];
  final int? memberCount = Get.arguments["memberCount"];

  @override
  void initState() {
    super.initState();

    controller.groupID = groupID;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "",
      ),
      body: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              SizedBox(
                height: 60.h,
              ),
              Obx(
                () {
                  bool hasJoined = controller.hasJoined.value;
                  return Text(
                    hasJoined ? "你已接收该邀请" : "邀请你加入群聊",
                    style: TextStyles.normal(18.sp),
                  );
                },
              ),
              SizedBox(
                height: 30.h,
              ),
              ClipOval(
                child: ImageUtils.getImage(faceUrl ?? "", 50.w, 50.w,
                    showPlaceholder: true,
                    placeholder: Assets.imagesGroupChatDefaultAvatar),
              ),
              SizedBox(
                height: 15.h,
              ),
              Text(
                "${groupName ?? ""}(${(memberCount)})",
                style: TextStyles.common(16.sp, AppColors.colorFF666666),
              ),
              SizedBox(
                height: 15.h,
              ),
              Visibility(
                visible: controller.hasJoined.value == false,
                child: GestureDetector(
                  onTap: () {
                    if (groupID != null) {
                      Get.to(() => JoinGroupChatApplySendPage(groupID: groupID!));
                    }
                  },
                  child: Container(
                    width: 100.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.r),
                      color: AppColors.colorFF58C75D,
                    ),
                    child: Text(
                      "申请加入",
                      style: TextStyles.normal(14.sp),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
