import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/scale_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/invite_play_entity.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/pages/chat/invite/chat_invite_game_controller.dart';
import 'package:dada/pages/small_room/function_view/other/small_room_send_invite_msg_bottom_dialog.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

class ChatInviteGameSheet extends StatelessWidget {
  final String daUserId;

  ChatInviteGameSheet({super.key, required this.daUserId});

  final ChatInviteGameController controller =
      Get.put(ChatInviteGameController());

  static final List<String> assets = [
    Assets.imagesChatInviteBtnGreen,
    Assets.imagesChatInviteBtnPurple,
    Assets.imagesChatInviteBtnRed
  ];

  final List<String> bgs = List.generate(
      8, (index) => assets[Random().nextInt(assets.length)],
      growable: false);

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
      title: "邀请去玩",
      height: 270.w,
      titleLeftBtn: _buildLeftButton(context),
      child: _buildGridView(context),
    );
  }

  Widget _buildGridView(BuildContext context) {
    return GetBuilder(
        init: controller,
        builder: (controller) {
          if (controller.invitePlayList == null) {
            if (controller.loading) {
              return const SizedBox();
            } else {
              return Expanded(
                child: Center(
                  child: ScaleButton(
                      onTap: () {
                        controller.refreshGames();
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 10.w, horizontal: 20.w),
                        decoration: BoxDecoration(
                            color: AppColors.colorFF89E15C,
                            borderRadius: BorderRadius.circular(100)),
                        child: Text(
                          "加载失败请重试",
                          style: TextStyle(
                              color: AppColors.colorFF3D3D3D,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold),
                        ),
                      )),
                ),
              );
            }
          }
          return Expanded(
            child: GridView.extent(
              padding: EdgeInsets.symmetric(horizontal: 40.w),
              maxCrossAxisExtent: 130.w,
              mainAxisSpacing: 10.w,
              crossAxisSpacing: 20.w,
              childAspectRatio: 130.w / 38.w,
              children: _buildGridViewItem(context),
            ),
          );
        });
  }

  List<Widget> _buildGridViewItem(BuildContext context) {
    List<Widget> list = [];
    for (var i = 0; i < 8; i++) {
      if (i < controller.invitePlayList!.length) {
        list.add(_buildGameItem(context, controller.invitePlayList![i], i));
      }
    }
    if (list.length < 8) {
      list.add(_buildAddButton(context, 3));
    }
    return list;
  }

  Widget _buildGameItem(
      BuildContext context, InvitePlayEntity entity, int index) {
    return GestureDetector(
      onTap: () {
        _onItemTap(entity);
      },
      child: Stack(
        children: [
          ImageUtils.getAssetImage(bgs[index], width: 130.w, height: 38.w),
          Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 25.w),
              child: Text(
                entity.text ?? "",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: Colors.white, fontSize: 16.sp),
              ),
            ),
          ),
          //x号
          _buildDelete(context, entity)
        ],
      ),
    );
  }

  void _onItemTap(InvitePlayEntity entity) async {
    if (controller.editMode) {
      ToastUtils.showBottomDialog(
        SmallRoomSendInviteMsgBottomDialog(userId: daUserId),
      ).then((result) {
        if (result != null) {
          controller.updateGame(entity, result);
        }
      });
    } else {
      V2TimMessage? message = await ChatIMManager.sharedInstance
          .sendTextMessage(text: "想玩${entity.text}了，一起走起？~", toUserID: daUserId);
      if (message != null) {
        EventBusEngine.fire(event: BusEvent.refreshChatHistoryList);
        Get.back();
      }
    }
  }

  Widget _buildDelete(BuildContext context, InvitePlayEntity entity) {
    return GetBuilder(
      init: controller,
      builder: (controller) {
        return controller.editMode
            ? Positioned(
                top: 0,
                bottom: 0,
                right: 15.w,
                child: GestureDetector(
                    onTap: () {
                      controller.deleteGame(entity);
                    },
                    child: Padding(
                        padding: EdgeInsets.all(10.w),
                        child: ImageUtils.getAssetImage(
                          Assets.imagesChatInviteGameDelete,
                          width: 6.w,
                        ))))
            : const SizedBox();
      },
      filter: (controller) => controller.editMode,
    );
  }

  Widget _buildAddButton(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        ToastUtils.showBottomDialog(
          SmallRoomSendInviteMsgBottomDialog(userId: daUserId),
        ).then((result) {
          if (result != null) {
            controller.addGame(result);
          }
        });
      },
      child: Container(
        width: 130.w,
        height: 38.w,
        decoration: BoxDecoration(
            image: DecorationImage(
                image: AssetImage(assets[Random().nextInt(assets.length)]))),
        child: Center(
          child: Text(
            "+添加",
            style: TextStyle(color: Colors.white, fontSize: 16.sp),
          ),
        ),
      ),
    );
  }

  _buildLeftButton(BuildContext context) {
    return GetBuilder(
        init: controller,
        builder: (controller) {
          if (!(controller.invitePlayList?.isNotEmpty == true)) {
            return const SizedBox();
          }
          if (controller.editMode) {
            return ScaleButton(
              onTap: () {
                controller.editMode = false;
                controller.update();
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
                decoration: BoxDecoration(
                    color: AppColors.colorFF89E15C,
                    borderRadius: BorderRadius.circular(20.r)),
                child: Text("完成",
                    style: TextStyles.common(14.sp, AppColors.colorFF3D3D3D)),
              ),
            );
          } else {
            return GestureDetector(
              onTap: () {
                controller.editMode = true;
                controller.update();
              },
              child: Padding(
                padding: EdgeInsets.all(5.w),
                child: ImageUtils.getAssetImage(Assets.imagesMineInfoEdit,
                    width: 20.w, height: 20.w),
              ),
            );
          }
        });
  }
}
