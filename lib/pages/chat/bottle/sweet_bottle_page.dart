import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat/bottle/sweet_bottle_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SweetBottlePage extends StatefulWidget {
  const SweetBottlePage({super.key});

  @override
  State<SweetBottlePage> createState() => _SweetBottlePageState();
}

class _SweetBottlePageState extends State<SweetBottlePage> {
  final SweetBottleController controller = Get.put(SweetBottleController());

  @override
  void initState() {
    super.initState();
    if (Get.parameters["daUserId"] != null) {
      controller.daUserId = Get.parameters["daUserId"]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        //背景
        LayoutBuilder(builder: (context, constraint) {
          return ImageUtils.getAssetImage(Assets.imagesChatMemoBackground,
              width: constraint.maxWidth,
              height: constraint.maxHeight,
              fit: BoxFit.cover);
        }),
        Column(
          children: [
            CustomAppBar(backgroundColor: Colors.transparent, title: "拾忆罐"),
            SizedBox(height: 70.w),
            ImageUtils.getAssetImage(Assets.imagesChatMemoTitle,
                width: 166.w, fit: BoxFit.cover),
            SizedBox(height: 60.w),
            GetBuilder(
                init: controller,
                builder: (controller) {
                  return _buildBottle(controller.memoCount ?? 0);
                }),
            Expanded(child: Center(child: _buildButton()))
          ],
        )
      ],
    );
  }

  Widget _buildBottle(int count) {
    String asset;
    if (count <= 0) {
      asset = Assets.imagesChatMemoEmpty;
    } else if (count <= 15) {
      asset = Assets.imagesChatMemoLittle;
    } else if (count <= 60) {
      asset = Assets.imagesChatMemoSome;
    } else {
      asset = Assets.imagesChatMemoFull;
    }
    return GestureDetector(
        onTap: () {
          Get.toNamed(GetRouter.chatSweetList,
              parameters: {"daUserId": controller.daUserId})?.then((result) {
            if (result == true) {
              controller.loadMemoCount();
            }
          });
        },
        child:
            ImageUtils.getAssetImage(asset, width: 313.w, fit: BoxFit.cover));
  }

  Widget _buildButton() {
    return CommonGradientBtn(
      title: "存星星",
      width: 100.w,
      height: 40.h,
      normalImage: Assets.imagesCommonGradientBtnBg40h,
      onTap: () {
        Get.toNamed(GetRouter.chatSweetEdit, arguments: {
          "daUserId": controller.daUserId,
          "userId": UserService().user?.id
        });
      },
    );
  }
}
