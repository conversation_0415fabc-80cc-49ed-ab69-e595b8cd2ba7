import 'dart:io';

import 'package:dada/generated/l10n.dart';
import 'package:dada/model/sweet_bottle_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class SweetEditController extends GetxController {
  late String daUserId;
  SweetBottleEntity? sweetBottleEntity;

  String? editImage;
  String? editContent;

  bool editMode = false;

  TextSelection? _textSelection;

  void deleteImage() {
    editImage = null;
    update();
  }

  void setEdit() {
    editMode = true;
    update();
  }

  TextSelection textSelection() {
    _textSelection ??= TextSelection(
        baseOffset: editContent?.length ?? 0,
        extentOffset: editContent?.length ?? 0);
    return _textSelection!;
  }

  void updateSelection(TextSelection selection) {
    _textSelection = selection;
  }

  void selectImage() async {
    ToastUtils.showBottomSheet(
      [S.current.album, S.current.camera],
      onTap: (index) async {
        if (index == 0) {
          bool res = await ImagePickerUtil.checkPermission(
              1, "相册权限说明", "获取图片/视频用于上传图片/视频");
          if (!res) return;
          List<AssetEntity>? assets = await ImagePickerUtil.selectAsset();
          if (assets?.isNotEmpty == true) {
            AssetEntity? asset = assets!.first;
            File? file = await asset.originFile;
            editImage = file?.path;
          }
        } else {
          bool res = await ImagePickerUtil.checkPermission(
              1, "相机权限说明", "拍摄图片用于上传图片, 拍摄后的图片将存放在系统照片中");
          if (!res) return;
          AssetEntity? asset =
              await ImagePickerUtil.takeAsset(enableRecording: false);
          if (asset != null) {
            File? file = await asset.originFile;
            editImage = file?.path;
          }
        }
        update();
      },
    );
  }

  Future<bool> save() async {
    if (!(editContent?.isNotEmpty == true) &&
        !(editImage?.isNotEmpty == true)) {
      ToastUtils.showToast("请输入内容");
      return false;
    }
    if (editImage == sweetBottleEntity?.url &&
        editContent == sweetBottleEntity?.content) {
      editMode = false;
      update();
      return false;
    }
    if (editImage != null && !(editImage!.startsWith("http"))) {
      String? uploadedUrl = await ApiService().uploadFile(editImage!);
      if (uploadedUrl != null) {
        editImage = uploadedUrl;
      } else {
        ToastUtils.showToast("图片上传失败，请重新尝试！");
        return false;
      }
    }
    bool success = await ApiService().addSweetBottle(
        daUserId, editImage, editContent, sweetBottleEntity?.sweetBottleId);
    if (success) {
      ToastUtils.showToast("添加成功");
    }
    return success;
  }

  Future<bool> deleteSweetBottle() {
    return ApiService().deleteSweetBottle(sweetBottleEntity!.sweetBottleId!);
  }
}
