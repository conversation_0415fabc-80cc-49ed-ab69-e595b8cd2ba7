import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/mixins/state_lifecycle_mixin.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/components/widgets/scale_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat/bottle/sweet_edit_controller.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/extensions/border_extension.dart';
import 'package:dada/utils/extensions/lifecycle_extension.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/log.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/optimize_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';

class SweetEditPage extends StatefulWidget {
  const SweetEditPage({super.key});

  @override
  State<SweetEditPage> createState() => _SweetEditPageState();
}

class _SweetEditPageState extends State<SweetEditPage>
    with StateLifecycleMixin {
  final SweetEditController controller = Get.put(SweetEditController());

  late ScrollController scrollController = ScrollController();

  FocusNode focusNode = FocusNode();

  double keyBoardHeight = 0;
  double lastKeyBoardHeight = 0;

  late Function debounce = OptimizeUtils.debounce((height) {
    Log.d("debounce bottom = $height");
    setState(() {
      keyBoardHeight = height;
    });
  }, const Duration(milliseconds: 150));

  late TextEditingController _textEditController;

  @override
  void initState() {
    super.initState();
    if (Get.arguments["daUserId"] != null) {
      controller.daUserId = Get.arguments["daUserId"]!;
    }
    if (Get.arguments["sweetBottleEntity"] != null) {
      controller.sweetBottleEntity = Get.arguments["sweetBottleEntity"]!;
      controller.editImage = controller.sweetBottleEntity?.url;
      controller.editContent = controller.sweetBottleEntity?.content;
    }
    _textEditController = TextEditingController.fromValue(TextEditingValue(
        text: controller.editContent ?? "",
        selection: controller.textSelection()));
    _textEditController.addListenerOn(this, () {
      Log.i("_textEditController ${_textEditController.selection}");
      controller.updateSelection(_textEditController.selection);
    });
  }

  @override
  Widget build(BuildContext context) {
    var bottom = MediaQuery.of(context).viewInsets.bottom;
    if (bottom != keyBoardHeight) {
      debounce(bottom);
    }

    double space = MediaQuery.of(context).size.height - 167.w - keyBoardHeight;
    if (lastKeyBoardHeight != keyBoardHeight) {
      _handleContentScroll(space);
    }

    return Stack(children: [
      //背景
      ImageUtils.getAssetImage(Assets.imagesChatSweetEditBg,
          width: 375.w, height: 1000.w, fit: BoxFit.fill),
      CustomAppBar(
        backgroundColor: Colors.transparent,
        title: "拾忆罐",
        rightWidgets: [
          if (controller.sweetBottleEntity?.sweetBottleId != null)
            ScaleButton(
              onTap: () {
                ToastUtils.showDialog(
                  content: "确定要删除记录吗？",
                  onConfirm: () {
                    controller.deleteSweetBottle().then((success) {
                      if (success) {
                        Get.back(result: true);
                      }
                    });
                  },
                );
              },
              child: (Get.arguments["userId"] != UserService().user?.id)
                  ? Container()
                  : Padding(
                      padding: EdgeInsets.all(15.w),
                      child: ImageUtils.getAssetImage(
                          Assets.imagesChatSweetEditDelete,
                          width: 20.w,
                          height: 20.w,
                          fit: BoxFit.contain),
                    ),
            ),
        ],
      ),
      //纸片1
      Positioned(
          left: 17.w,
          top: 104.w,
          right: 4.w,
          child: ImageUtils.getAssetImage(Assets.imagesChatSweetEditPaper1,
              fit: BoxFit.fitWidth)),
      //纸片2
      Positioned(
          left: 10.w,
          top: 85.w,
          right: 0.8.w,
          child: ImageUtils.getAssetImage(Assets.imagesChatSweetEditPaper2,
              fit: BoxFit.fitWidth)),
      //记录美好
      Align(
          alignment: Alignment.topCenter,
          child: Padding(
            padding: EdgeInsets.only(top: 127.w),
            child: ImageUtils.getAssetImage(Assets.imagesChatSweetEditTitle,
                width: 111.w, fit: BoxFit.fitWidth),
          )),
      //染色
      Positioned(
          left: 30.w,
          top: 140.w,
          right: 10.w,
          child: ImageUtils.getAssetImage(Assets.imagesChatSweetEditInk,
              width: 334.w, height: 598.w, fit: BoxFit.cover)),
      //编辑/保存
      Positioned(top: 118.w, right: 42.w, child: _buildEdit()),
      //内容卡
      Positioned(
        left: 40.w,
        top: 167.w,
        right: 28.w,
        child: Container(
            decoration: BoxDecoration(
                color: AppColors.colorFFFAFAFA,
                borderRadius: BorderRadius.all(Radius.circular(5.w)),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.colorFFADB6B3,
                    offset: Offset(0, 2.h),
                    blurRadius: 6.w,
                  ),
                ]),
            height: min(526.w, space),
            child: SingleChildScrollView(
                controller: scrollController, child: _buildContentCard())),
      ),
      Positioned(
          left: 0,
          top: 167.w + 21.w,
          child: ImageUtils.getAssetImage(Assets.imagesChatSweetEditNail,
              width: 106.w))
    ]);
  }

  void _handleContentScroll(double space) {
    Future.delayed(Duration(
            milliseconds: keyBoardHeight != 0
                ? 0
                : (PlatformUtils().isAndroid)
                    ? 200
                    : 340))
        .then((value) {
      scrollController.animateTo(max(0, 526.w - space),
          duration:
              Duration(milliseconds: (PlatformUtils().isAndroid) ? 200 : 340),
          curve: Curves.fastOutSlowIn);
      lastKeyBoardHeight = keyBoardHeight;
    });
  }

  /// 内容部分
  Widget _buildContentCard() {
    return SizedBox(
      height: 526.w,
      child: Column(
        children: [
          SizedBox(height: 21.w),
          Container(
            width: 213.w,
            height: 284.w,
            alignment: Alignment.topCenter,
            padding: EdgeInsets.only(
                left: 14.w, right: 14.w, top: 15.w, bottom: 22.w),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(5.w)),
                boxShadow: [
                  BoxShadow(color: AppColors.colorFFBEA8A4, blurRadius: 5.w)
                ]),
            // 封面编辑/展示
            child: _buildCover(),
          ),
          SizedBox(height: 15.w),
          // 分割线
          Center(
            child: ImageUtils.getImage(
                Assets.imagesCrystalBallLifeRecordDetailSeparator, 281.w, 16.w),
          ),
          Expanded(child: _buildTextContent())
        ],
      ),
    );
  }

  /// 编辑保存
  Widget _buildEdit() {
    return GetBuilder<SweetEditController>(
      builder: (controller) {
        String? userId;
        if (Get.arguments != null && Get.arguments.containsKey("userId")) {
          userId = Get.arguments["userId"];
        }
        if (userId != null && userId != UserService().user?.id) {
          return Container();
        }
        if (controller.editMode) {
          return ScaleButton(
            onTap: () {
              controller.save().then((success) {
                if (success) {
                  Get.back(result: success);
                }
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
              decoration: BoxDecoration(
                  color: AppColors.colorFF89E15C,
                  borderRadius: BorderRadius.circular(20.r)),
              child: Text("完成",
                  style: TextStyles.common(14.sp, AppColors.colorFF3D3D3D)),
            ),
          );
        } else {
          return GestureDetector(
            onTap: () => controller.setEdit(),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: ImageUtils.getAssetImage(Assets.imagesMineInfoEdit,
                  width: 20.w, height: 20.w),
            ),
          );
        }
      },
      filter: (controller) => controller.editMode,
    );
  }

  //图片
  Widget _buildCover() {
    return LayoutBuilder(builder: (context, constraint) {
      return GetBuilder<SweetEditController>(
          init: controller,
          builder: (controller) {
            bool hasImage = controller.editImage?.isNotEmpty == true;
            return Stack(children: [
              GestureDetector(
                onTap: () {
                  if (controller.editMode) {
                    controller.selectImage();
                  } else if (hasImage) {
                    ImageUtils.showImageBrowser(
                      ImageBrowserArgs(
                        [HeroTagName.sweetBottleCover.name],
                        [controller.editImage!],
                      ),
                    );
                  } else if (hasImage == false && controller.editMode == false) {
                    setState(() {
                      controller.editMode = true;
                    });
                  }
                },
                child: ImageUtils.getImage(
                  hasImage
                      ? controller.editImage!
                      : controller.editMode == true
                          ? Assets.imagesCrystalBallLifeRecordDetailItemEmptyBg
                          : Assets.imagesSweetDetailImageEmpty,
                  constraint.maxWidth,
                  constraint.maxHeight,
                  fit: BoxFit.cover,
                ),
              ),
              IgnorePointer(
                child: Visibility(
                  visible: !hasImage && controller.editMode,
                  child: Align(
                      alignment: Alignment.center,
                      child: ImageUtils.getImage(
                          Assets.imagesCommonAddPlus, 40.w, 40.w)),
                ),
              ),
              Visibility(
                  visible: hasImage && controller.editMode,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () => controller.deleteImage(),
                        child: ImageUtils.getImage(
                            Assets.imagesUserLabelImgDelete, 20.w, 18.h)),
                  ))
            ]);
          });
    });
  }

  //文字
  Widget _buildTextContent() {
    return GetBuilder<SweetEditController>(
        init: controller,
        builder: (controller) {
          if (controller.editMode) {
            return Container(
                padding: EdgeInsets.all(6.w),
                margin: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                    border: Border.all(color: AppColors.colorFFD0C1BE),
                    borderRadius: 5.w.borderRadius),
                child: TextField(
                    focusNode: focusNode,
                    minLines: null,
                    maxLines: null,
                    expands: true,
                    decoration: null,
                    maxLength: 300,
                    style: TextStyle(
                        fontSize: 16.sp, color: AppColors.colorFF9F9694),
                    selectionControls: MaterialTextSelectionControls(),
                    onChanged: (text) => controller.editContent = text,
                    controller: _textEditController));
          } else {
            return GestureDetector(
              onTap: () {
                setState(() {
                  controller.editMode = true;
                });              },
              child: Container(
                  margin: EdgeInsets.all(4.w),
                  padding: EdgeInsets.all(10.w),
                  child: Builder(builder: (context) {
                    if (controller.editContent?.isNotEmpty != true) {
                      return Center(
                          child: Padding(
                        padding: const EdgeInsets.only(bottom: 50.0),
                        child: ImageUtils.getAssetImage(
                            Assets.imagesChatSweetEditWriteHint,
                            width: 106.w),
                      ));
                    }
                    return SingleChildScrollView(
                        child: Text(
                      controller.editContent ?? "",
                      style: TextStyle(
                          fontSize: 16.sp, color: AppColors.colorFF9F9694),
                    ));
                  })),
            );
          }
        });
  }
}
