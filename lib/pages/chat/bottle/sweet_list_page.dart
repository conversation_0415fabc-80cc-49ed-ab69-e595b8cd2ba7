import 'package:dada/common/values/colors.dart';
import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/sweet_bottle_entity.dart';
import 'package:dada/pages/chat/bottle/sweet_list_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/extensions/border_extension.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SweetListPage extends StatefulWidget {
  const SweetListPage({super.key});

  @override
  State<SweetListPage> createState() => _SweetListPageState();
}

class _SweetListPageState extends State<SweetListPage> {
  final SweetListController controller = Get.put(SweetListController());

  @override
  void initState() {
    super.initState();
    if (Get.parameters["daUserId"] != null) {
      controller.daUserId = Get.parameters["daUserId"]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.colorFFF5F5F5,
        appBar: CustomAppBar(title: "列表"),
        body: RefreshWidget.build(
            refreshController: controller.refreshController,
            onRefresh: controller.refreshData,
            onLoadMore: controller.loadMoreData,
            child: GetBuilder(
              init: controller,
              id: controller.refreshId,
              builder: (controller) => _buildList(controller),
            )));
  }

  Widget _buildList(SweetListController controller) {
    if (controller.data.isEmpty) {
      return const ListPageEmptyWidget();
    }
    return ListView.separated(
        padding: EdgeInsets.only(left: 15.w, top: 10.w, right: 15.w),
        itemBuilder: (context, index) {
          return _buildItem(controller.data[index]);
        },
        separatorBuilder: (context, index) {
          return SizedBox(height: 10.w);
        },
        itemCount: controller.data.length);
  }

  Widget _buildItem(SweetBottleEntity entity) {
    return Material(
      type: MaterialType.transparency,
      child: Ink(
        decoration:
            BoxDecoration(color: Colors.white, borderRadius: 10.w.borderRadius),
        child: InkWell(
          borderRadius: 10.w.borderRadius,
          onTap: () {
            Get.toNamed(GetRouter.chatSweetEdit, arguments: {
              "daUserId": controller.daUserId,
              "userId": entity.userId,
              "sweetBottleEntity": entity,
            })?.then((result) {
              if (result) {
                controller.refreshData();
              }
            });
          },
          child: Container(
            padding: EdgeInsets.all(10.w),
            child: Row(
              children: [
                ImageUtils.getImage(
                    entity.url ?? Assets.imagesSweetListImageEmpty,
                    60.w,
                    60.w,
                    radius: 8.w),
                SizedBox(width: 10.w),
                Expanded(
                    child: Text(
                  entity.content ?? "",
                  style: TextStyle(fontSize: 16.sp),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ))
              ],
            ),
          ),
        ),
      ),
    );
  }
}
