import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/sweet_bottle_entity.dart';
import 'package:dada/services/network/api_service.dart';

class SweetListController
    extends ListPageController<SweetBottleEntity, SweetListController> {
  late String daUserId;

  @override
  Future<List<SweetBottleEntity>?> loadData(int page) {
    return ApiService().querySweetBottleList(daUserId, page);
  }
}
