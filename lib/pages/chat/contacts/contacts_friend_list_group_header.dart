import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ContactsFriendListGroupHeader extends StatefulWidget {
  final String title;
  final bool unfold;
  final String groupKey;
  final Widget? rightWidget;
  final Function()? onTap;

  const ContactsFriendListGroupHeader(
      {super.key,
      required this.title,
      required this.unfold,
      required this.groupKey,
      this.rightWidget,
      this.onTap});

  @override
  State<ContactsFriendListGroupHeader> createState() =>
      _ContactsFriendListGroupHeaderState();
}

class _ContactsFriendListGroupHeaderState
    extends State<ContactsFriendListGroupHeader> {
  final ContactsController controller = Get.find<ContactsController>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        controller.updateGroupUnfoldState(widget.groupKey);
        widget.onTap?.call();
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 10.h),
        height: 40.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesChatContactsFriendGroupHeaderBg),
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 14.w),
                  child: ImageUtils.getImage(
                      widget.unfold
                          ? Assets.imagesArrowSharpDownGreen
                          : Assets.imagesArrowSharpRightGreen,
                      9.5.w,
                      9.5.w,
                      fit: BoxFit.cover),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 9.w),
                  child: Text(
                    widget.title,
                    style: TextStyles.normal(16.sp),
                  ),
                ),
              ],
            ),
            const Spacer(),
            widget.rightWidget ?? Container(),
          ],
        ),
      ),
    );
  }
}
