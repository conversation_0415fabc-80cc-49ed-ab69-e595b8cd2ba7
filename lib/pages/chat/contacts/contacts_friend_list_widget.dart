import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';

class ContactsFriendListWidget extends StatefulWidget {
  final List<FriendUserInfoEntity>? list;
  final int? groupType;
  final bool? canSelected;
  final List<V2TimGroupMemberFullInfo?>? disableSelectedList;

  const ContactsFriendListWidget(
      {super.key,
      this.list,
      this.groupType,
      this.canSelected,
      this.disableSelectedList});

  @override
  State<ContactsFriendListWidget> createState() =>
      _ContactsFriendListWidgetState();
}

class _ContactsFriendListWidgetState extends State<ContactsFriendListWidget> {
  final ContactsController controller = Get.find<ContactsController>();

  @override
  Widget build(BuildContext context) {
    if (!(widget.list?.isNotEmpty == true)) {
      return Container();
    }
    return ListView.separated(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.list!.length,
      itemBuilder: (context, index) {
        FriendUserInfoEntity friendInfo = widget.list![index];
        return _buildFriendListItemWidget(friendInfo,
            groupType: widget.groupType);
      },
      separatorBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 15.w),
          height: 1,
          color: AppColors.colorFFE5E5E5,
        );
      },
    );
  }

  Widget _buildFriendListItemWidget(FriendUserInfoEntity friendInfo,
      {int? groupType}) {
    return Obx(() {
      bool disabledSelect = false;
      bool selected = controller.checkSelectedFriendItem(friendInfo);
      double selectedIconWidth = 0.w;
      if (widget.canSelected == true) {
        disabledSelect = controller.checkDisableSelectFriendItem(
            friendInfo, widget.disableSelectedList);
        selectedIconWidth = 20.w;
      }
      String showName;
      if (friendInfo.friendRemark?.isNotEmpty == true) {
        showName = friendInfo.friendRemark!;
      } else {
        showName = friendInfo.nickname ?? "";
      }
      return InkWell(
        onTap: () {
          if (widget.canSelected == true) {
            if (disabledSelect == true) {
              return;
            }
            selected = !selected;
            controller.selectedFriendItem(friendInfo, selected);
            return;
          }
          String? userId = friendInfo.userFriendId ?? friendInfo.userBlackId;
          if (userId == UserService().user?.id) {
            userId = friendInfo.userId!;
          }
          if (userId != null) {
            Get.toNamed(
              GetRouter.userProfile,
              parameters: {
                "userId": userId,
              },
            );
          }
        },
        child: SizedBox(
          height: 75.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Visibility(
                    visible: widget.canSelected == true,
                    child: Padding(
                      padding: EdgeInsets.only(left: 10.w),
                      child: ImageUtils.getImage(
                        disabledSelect
                            ? Assets.imagesGroupChatInviteMemberDisabledSelected
                            : selected
                                ? Assets.imagesContactsListFriendItemSelected
                                : Assets.imagesContactsListFriendItemUnselected,
                        20.w,
                        20.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                        left: widget.canSelected == true ? 5.w : 15.w),
                    child: ClipOval(
                      child: ImageUtils.getImage(
                          friendInfo.avatar ?? "", 45.w, 45.w,
                          fit: BoxFit.cover),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 5.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          children: [
                            Container(
                              constraints: BoxConstraints(
                                maxWidth: groupType ==
                                        ContactsGroupType.dazi.index
                                    ? (230.w - selectedIconWidth)
                                    : groupType ==
                                            ContactsGroupType.newFriend.index
                                        ? (220.w - selectedIconWidth)
                                        : (295.w - selectedIconWidth),
                              ),
                              child: Text(
                                showName,
                                style: TextStyles.normal(16.sp),
                              ),
                            ),
                            _buildDaZiTagWidget(friendInfo, groupType),
                          ],
                        ),
                        groupType != ContactsGroupType.blacklist.index
                            ? groupType == ContactsGroupType.newFriend.index
                                ? _buildNewFriendApplyTypeWidget(friendInfo)
                                : _buildUserSignatureWidget(friendInfo)
                            : Container(),
                      ],
                    ),
                  ),
                ],
              ),

              ///黑名单取消拉黑或新好友申请右侧按钮
              groupType == ContactsGroupType.blacklist.index
                  ? _buildRemoveFromBlackListBtn(friendInfo)
                  : groupType == ContactsGroupType.newFriend.index
                      ? Row(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(right: 13.w),
                              child: _buildNewFriendApplyBtnWidget(friendInfo),
                            ),
                          ],
                        )
                      : Container(),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildDaZiTagWidget(FriendUserInfoEntity friendInfo, int? groupType) {
    if (groupType == ContactsGroupType.dazi.index &&
        friendInfo.daName?.isNotEmpty == true) {
      return Container(
        height: 20.h,
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 8.w),
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(color: AppColors.colorFFE1E1E1),
        ),
        child: Text(
          friendInfo.daName ?? "",
          style: TextStyles.common(12.sp, AppColors.colorFF999999),
        ),
      );
    }
    return Container();
  }

  Widget _buildUserSignatureWidget(FriendUserInfoEntity friendInfo) {
    return friendInfo.describesFlag == 1 //文字签名
        ? Container(
            padding: EdgeInsets.only(top: 5.h),
            constraints: BoxConstraints(
              maxWidth: widget.canSelected == true ? 280.w : 300.w,
            ),
            child: Text(
              friendInfo.userDescribes!,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.common(14.sp, AppColors.colorFF666666),
            ),
          )
        : (friendInfo.describesFlag == 0 &&
                friendInfo.voiceLength != null &&
                friendInfo.userDescribes != null && friendInfo.voiceLength != 0) //语音签名
            ? Container(
                margin: EdgeInsets.only(top: 5.h),
                width: 76.w,
                height: 20.h,
                decoration: BoxDecoration(
                  color: AppColors.colorFF4DC151,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 3.h),
                      child: ImageUtils.getImage(
                          Assets.imagesMatchDadaListAudioPlay, 15.w, 15.w),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 6.w),
                      child: ImageUtils.getImage(
                          Assets.imagesMatchDadaListAudioVolume, 13.w, 10.w),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 7.w),
                      child: Text(
                        "${friendInfo.voiceLength ?? 0}s",
                        style: TextStyles.common(14.sp, Colors.white),
                      ),
                    ),
                  ],
                ),
              )
            : Container();
  }

  Widget _buildNewFriendApplyTypeWidget(FriendUserInfoEntity userInfo) {
    return Padding(
      padding: EdgeInsets.only(top: 5.h),
      child: Text(
        _applyFriendTypeStr(
            userInfo.userId, userInfo.applyMsg, userInfo.friendType),
        style: TextStyles.normal(14.sp, c: AppColors.colorFF666666),
      ),
    );
  }

  Widget _buildNewFriendApplyBtnWidget(FriendUserInfoEntity userInfo) {
    return userInfo.friendType == 1 && userInfo.userId != UserService().user?.id
        ? GestureDetector(
            onTap: () {
              controller.agreeAddFriendRequest(userInfo);
            },
            child: Container(
              width: 56.w,
              height: 30.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15.r),
                color: AppColors.colorFF89E15C,
              ),
              child: Text(
                "同意",
                style: TextStyles.normal(14.sp),
              ),
            ),
          )
        : Text(
            _applyStateStr(userInfo.friendType),
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          );
  }

  Widget _buildRemoveFromBlackListBtn(FriendUserInfoEntity userInfo) {
    return Padding(
      padding: EdgeInsets.only(right: 15.w),
      child: Row(
        children: [
          Container(
            width: 1.w,
            height: 15.h,
            color: AppColors.colorFFE5E5E5,
          ),
          SizedBox(
            width: 10.w,
          ),
          GestureDetector(
            onTap: () {
              ToastUtils.showDialog(
                  content: "确定要将 ${userInfo.nickname} 移除黑名单吗？",
                  onConfirm: () {
                    ApiService().deleteFromBlackList(userInfo.userId!);
                  });
            },
            child: Text(
              "移除黑名单",
              style: TextStyles.common(12.sp, AppColors.colorFF23AF28),
            ),
          ),
        ],
      ),
    );
  }

  String _applyFriendTypeStr(
      String? senderId, String? applyMsg, int? applyState) {
    if (senderId == null) {
      return "";
    }
    String applyStr = "";
    if (senderId == UserService().user?.id) {
      if (applyState == 1 || applyState == 2) {
        applyStr = "申请添加对方为好友";
      } else if (applyState == 0) {
        applyStr = "已添加对方为好友";
      }
    } else {
      applyStr = applyMsg ?? "申请添加你为好友";
    }
    return applyStr;
  }

  String _applyStateStr(int? applyState) {
    if (applyState == null) {
      return "";
    }
    String applyStr = "";
    switch (applyState) {
      case 0:
        applyStr = "已同意";
        break;
      case 1:
        applyStr = "等待验证";
        break;
      case 2:
        applyStr = "已过期";
        break;
    }
    return applyStr;
  }
}
