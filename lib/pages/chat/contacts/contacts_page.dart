import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_group_info_entity.dart';
import 'package:dada/model/contact_group_list_item_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/pages/chat/contacts/contacts_friend_custom_group_widget.dart';
import 'package:dada/pages/chat/contacts/contacts_friend_list_group_header.dart';
import 'package:dada/pages/chat/contacts/contacts_friend_list_widget.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

class ContactsPage extends StatefulWidget {
  const ContactsPage({super.key});

  @override
  State<ContactsPage> createState() => _ContactsPageState();
}

class _ContactsPageState extends State<ContactsPage> {
  final ContactsController controller = Get.find<ContactsController>();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      controller.checkShouldReload();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: controller,
      builder: (controller) {
        if (!(controller.contactsGroupList?.isNotEmpty == true)) {
          return EmptyWidget();
        }
        return SingleChildScrollView(
          padding: EdgeInsets.only(bottom: 20.h),
          scrollDirection: Axis.vertical,
          child: Column(
            children: [
              ///好友分组
              _buildFriendGroupWidget(),

              ///搭子分组
              _buildDaZiGroupWidget(),

              ///群聊分组
              _buildGroupChatsGroupWidget(),

              ///黑名单分组
              _buildBlackListGroupWidget(),

              ///新好友请求分组
              _buildNewFriendsGroupWidget(),
            ],
          ),
        );
      },
    );
  }

  /*
  * 分组视图
  * */

  ///好友分组
  Widget _buildFriendGroupWidget() {
    ContactGroupListItemEntity friendGroupEntity = controller.contactsGroupList!
        .where((value) => value.groupType == ContactsGroupType.friend.index)
        .toList()
        .first;
    String groupName = friendGroupEntity.groupName ?? "好友";
    return Obx(
      () {
        String groupKey = "group_$groupName";
        bool? unfold = controller.groupUnfoldMap[groupKey];
        return Column(
          children: [
            ///好友组标题头部
            ContactsFriendListGroupHeader(
              groupKey: groupKey,
              title: groupName,
              unfold: unfold ?? false,
              rightWidget: Padding(
                padding: EdgeInsets.only(right: 10.w),
                child: ImageUtils.getImage(
                  Assets.imagesChatContactsFriendGroupHeaderFriendIcon,
                  17.w,
                  17.w,
                ),
              ),
            ),

            ///好友Group中 未分组的好友
            unfold == true
                ? ContactsFriendListWidget(
                    list:
                        friendGroupEntity.friendGroupInfo?.unGroupedFriendList,
                    groupType: friendGroupEntity.groupType,
                  )
                : Container(),

            ///好友自定义子分组
            unfold == true
                ? ContactsFriendCustomGroupWidget(
                    groupList: friendGroupEntity.friendGroupInfo?.subGroupList)
                : Container(),
          ],
        );
      },
    );
  }

  ///搭子分组
  Widget _buildDaZiGroupWidget() {
    ContactGroupListItemEntity daZiGroupEntity = controller.contactsGroupList!
        .where((value) => value.groupType == ContactsGroupType.dazi.index)
        .toList()
        .first;
    String groupName = daZiGroupEntity.groupName ?? "搭子";
    return Obx(
      () {
        String groupKey = "group_$groupName";
        bool? unfold = controller.groupUnfoldMap[groupKey];
        return Column(
          children: [
            ContactsFriendListGroupHeader(
              title: groupName,
              unfold: unfold ?? false,
              groupKey: groupKey,
            ),
            unfold == true
                ? ContactsFriendListWidget(
                    list: daZiGroupEntity.daziList,
                    groupType: ContactsGroupType.dazi.index)
                : Container(),
          ],
        );
      },
    );
  }

  ///群聊分组
  Widget _buildGroupChatsGroupWidget() {
    ContactGroupListItemEntity groupChatsGroupEntity = controller
        .contactsGroupList!
        .where((value) => value.groupType == ContactsGroupType.chatGroup.index)
        .toList()
        .first;
    String groupName = groupChatsGroupEntity.groupName ?? "群";
    return Obx(
      () {
        String groupKey = "group_$groupName";
        bool? unfold = controller.groupUnfoldMap[groupKey];
        List<ChatGroupInfoEntity>? groupsList =
            groupChatsGroupEntity.groupsList;
        return Column(
          children: [
            ContactsFriendListGroupHeader(
              title: groupName,
              unfold: unfold ?? false,
              groupKey: groupKey,
            ),
            unfold == true && groupsList?.isNotEmpty == true
                ? ListView.separated(
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: groupsList!.length,
                    itemBuilder: (context, index) {
                      ChatGroupInfoEntity groupInfo = groupsList[index];
                      return _buildGroupListItemWidget(groupInfo);
                    },
                    separatorBuilder: (context, index) {
                      return Container(
                        margin: EdgeInsets.symmetric(horizontal: 15.w),
                        height: 1,
                        color: AppColors.colorFFE5E5E5,
                      );
                    },
                  )
                : Container(),
          ],
        );
      },
    );
  }

  ///黑名单分组
  Widget _buildBlackListGroupWidget() {
    ContactGroupListItemEntity blackListGroupEntity = controller
        .contactsGroupList!
        .where((value) => value.groupType == ContactsGroupType.blacklist.index)
        .toList()
        .first;
    String groupName = blackListGroupEntity.groupName ?? "黑名单";
    return Obx(
      () {
        String groupKey = "group_$groupName";
        bool? unfold = controller.groupUnfoldMap[groupKey];
        return Column(
          children: [
            ContactsFriendListGroupHeader(
              title: groupName,
              unfold: unfold ?? false,
              groupKey: groupKey,
            ),
            unfold == true
                ? ContactsFriendListWidget(
                    list: blackListGroupEntity.blacklist,
                    groupType: ContactsGroupType.blacklist.index)
                : Container(),
          ],
        );
      },
    );
  }

  ///新好友分组
  Widget _buildNewFriendsGroupWidget() {
    ContactGroupListItemEntity newFriendsGroupEntity = controller
        .contactsGroupList!
        .where((value) => value.groupType == ContactsGroupType.newFriend.index)
        .toList()
        .first;
    String groupName = newFriendsGroupEntity.groupName ?? "新的朋友";
    return Obx(
      () {
        String groupKey = "group_$groupName";
        bool? unfold = controller.groupUnfoldMap[groupKey];
        int unreadCount =
            Get.find<MainController>().newFriendApplyUnreadCount.value;
        if (unreadCount != 0 && unfold == true) {
          ChatIMManager.sharedInstance.clearFriendApplicationUnreadCount();
        }
        return Column(
          children: [
            ContactsFriendListGroupHeader(
              title: groupName,
              unfold: unfold ?? false,
              groupKey: groupKey,
              rightWidget: unreadCount == 0
                  ? Container()
                  : Padding(
                      padding: EdgeInsets.only(right: 10.w),
                      child: BadgeWidget(
                        text: "$unreadCount",
                      ),
                    ),
              onTap: () {
                if (unreadCount != 0 && unfold == true) {
                  ChatIMManager.sharedInstance
                      .clearFriendApplicationUnreadCount();
                }
              },
            ),
            unfold == true
                ? ContactsFriendListWidget(
                    list: newFriendsGroupEntity.newFriendsList,
                    groupType: ContactsGroupType.newFriend.index)
                : Container(),
          ],
        );
      },
    );
  }

  ///群聊列表项
  Widget _buildGroupListItemWidget(ChatGroupInfoEntity groupInfo) {
    return InkWell(
      onTap: () async {
        if (!UserService().checkIsMonthCardUser()) {
          ToastUtils.showBottomDialog(const MonthCardExpiredLimitChatDialog());
        } else {
          V2TimConversation conversation = await ChatIMManager.sharedInstance
              .getConversation(
              type: 2,
              groupID: groupInfo.groupId,
              groupName: groupInfo.groupName,
              groupType: GroupType.Public);
          Get.toNamed(GetRouter.chatDetail, arguments: conversation);
        }
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 15.h, left: 23.w, bottom: 10.h),
            child: GestureDetector(
              onTap: () {},
              child: ClipOval(
                child: ImageUtils.getImage(
                    groupInfo.faceUrl?.isNotEmpty == true ? groupInfo.faceUrl! : Assets.imagesGroupChatDefaultAvatar2,
                    45.w,
                    45.w,
                    fit: BoxFit.cover,
                    showPlaceholder: true),
              ),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.only(
                    top: groupInfo.labels?.isNotEmpty == true ? 17.h : 27.h,
                    left: 5.w),
                constraints: BoxConstraints(
                  maxWidth: 300.w,
                ),
                child: Text(
                  controller.groupNameWithMemberList(groupInfo),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyles.normal(16.sp),
                ),
              ),
              _buildGroupLabelsWidget(groupInfo),
            ],
          )
        ],
      ),
    );
  }

  ///群聊标签
  Widget _buildGroupLabelsWidget(ChatGroupInfoEntity groupInfo) {
    return groupInfo.labels?.isNotEmpty == true
        ? Container(
            width: 280.w,
            padding: EdgeInsets.only(top: 5.h, bottom: 15.h),
            child: Wrap(
              spacing: 5.w,
              runSpacing: 5.h,
              children: groupInfo.labels!
                  .map(
                    (value) => LabelItemWidget(
                      text: value.tagName ?? "",
                      editable: false,
                      height: 20.h,
                      minWidth: 35.w,
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      bgColor: AppColors.colorFFEAECF1,
                      borderRadius: 10.r,
                      borderColor: Colors.transparent,
                      fontSize: 12.sp,
                    ),
                  )
                  .toList(),
            ),
          )
        : Container();
  }
}
