import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/pages/chat/contacts/contacts_friend_list_widget.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';

class ContactsFriendCustomGroupWidget extends StatefulWidget {
  final List<FriendSubGroupEntity>? groupList;
  final bool? isCreate;
  final List<V2TimGroupMemberFullInfo?>? disableSelectedList;

  const ContactsFriendCustomGroupWidget(
      {super.key, this.groupList, this.isCreate, this.disableSelectedList});

  @override
  State<ContactsFriendCustomGroupWidget> createState() =>
      _ContactsFriendCustomGroupWidgetState();
}

class _ContactsFriendCustomGroupWidgetState
    extends State<ContactsFriendCustomGroupWidget> {
  final ContactsController controller = Get.find<ContactsController>();

  @override
  Widget build(BuildContext context) {
    if (!(widget.groupList?.isNotEmpty == true)) {
      return Container();
    }
    List<Widget> list = [];
    for (int i = 0; i < widget.groupList!.length; i++) {
      FriendSubGroupEntity groupEntity = widget.groupList![i];
      Widget groupItemWidget =
          _buildFriendSubGroupListItemWidget(groupEntity, i);
      if (i != 0) {
        list.add(SizedBox(
          height: 10.h,
        ));
      }
      list.add(groupItemWidget);
    }
    return Column(
      children: list,
    );
  }

  ///好友自定义子分组
  Widget _buildFriendSubGroupListItemWidget(
      FriendSubGroupEntity? subGroupEntity, int index) {
    return Obx(
      () {
        String subGroupKey =
            "${widget.isCreate == true ? "create_" : ""}sub_group_$index";
        bool? unfold = controller.groupUnfoldMap[subGroupKey];
        if (subGroupEntity?.groupName == null) {
          return Container();
        }
        return Padding(
          padding: EdgeInsets.only(top: 0.h),
          child: Column(
            children: [
              _buildSubGroupHeaderWidget(
                  subGroupKey: subGroupKey,
                  groupName: subGroupEntity?.groupName,
                  friendCount: subGroupEntity?.friendList?.length,
                  onlineCount: subGroupEntity?.onlineCount,
                  unfold: unfold),
              unfold == true
                  ? ContactsFriendListWidget(
                      list: subGroupEntity?.friendList,
                      groupType: ContactsGroupType.friend.index,
                      canSelected: widget.isCreate,
                      disableSelectedList: widget.disableSelectedList,
                    )
                  : Container(),
            ],
          ),
        );
      },
    );
  }

  ///好友分组自定义子分组标题
  Widget _buildSubGroupHeaderWidget(
      {String? groupName,
      int? friendCount,
      int? onlineCount,
      bool? unfold,
      required String subGroupKey}) {
    if (groupName == null) {
      return Container();
    }
    return GestureDetector(
      onTap: () {
        controller.updateGroupUnfoldState(subGroupKey);
      },
      onLongPress: () {
        ToastUtils.showBottomSheet(["管理分组"], onTap: (index) {
          if (index == 0) {
            Get.toNamed(GetRouter.friendCustomGroupManager,
                parameters: {"isGroupManager": "1"});
          }
        });
      },
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w),
            child: ImageUtils.getImage(
                unfold == true
                    ? Assets.imagesChatContactSubGroupHeadIconUnfold
                    : Assets.imagesChatContactSubGroupHeadIcon,
                16.w,
                16.w),
          ),
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: 265.w,
              ),
              child: Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  groupName,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyles.normal(16.sp),
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 5.w),
            child: Text(
              "（${onlineCount ?? 0}/${friendCount ?? 0}）",
              style: TextStyles.normal(16.sp),
            ),
          ),
        ],
      ),
    );
  }
}
