import 'package:dada/model/chat_group_info_entity.dart';
import 'package:dada/model/contact_group_list_item_entity.dart';
import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/add/friend_apply_page.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';

class ContactsController extends GetxController {
  List<ContactGroupListItemEntity>? contactsGroupList;

  RxMap<String, bool> groupUnfoldMap = <String, bool>{}.obs;

  RxList<FriendUserInfoEntity> selectedFriendItems =
      <FriendUserInfoEntity>[].obs;

  RxString inputSearchText = "".obs;
  RxList<FriendUserInfoEntity> searchFriendResultList =
      <FriendUserInfoEntity>[].obs;

  RxBool isReceiveStrangerMsg = true.obs;

  @override
  void onReady() async {
    super.onReady();

    await loadData();
  }

  Future<List<ContactGroupListItemEntity>?> loadData(
      {bool? showLoading}) async {
    contactsGroupList =
        await ApiService().loadContactList(showLoading: showLoading);
    bool status =
        await ApiService().getStrangerMsgState(userId: UserService().user!.id!);
    isReceiveStrangerMsg.value = status;
    update();
    return contactsGroupList;
  }

  void checkShouldReload() {
    if (!(contactsGroupList?.isNotEmpty == true)) {
      loadData();
    }
  }

  void updateGroupUnfoldState(String group) {
    bool? unfold = groupUnfoldMap[group];
    if (unfold != true) {
      groupUnfoldMap[group] = true;
    } else {
      groupUnfoldMap[group] = false;
    }
  }

  void agreeAddFriendRequest(FriendUserInfoEntity userInfo) async {
    Get.to(() => FriendApplyPage(friendUserInfo: userInfo));
  }

  void selectedFriendItem(FriendUserInfoEntity friendItem, bool selected) {
    if (selected) {
      if (!selectedFriendItems.contains(friendItem)) {
        selectedFriendItems.add(friendItem);
      }
    } else {
      selectedFriendItems.removeWhere(
          (value) => value.userFriendId == friendItem.userFriendId);
    }
  }

  bool checkSelectedFriendItem(FriendUserInfoEntity friendItem) {
    return selectedFriendItems
        .where((value) => value.userFriendId == friendItem.userFriendId)
        .toList()
        .isNotEmpty;
  }

  bool checkDisableSelectFriendItem(FriendUserInfoEntity friendItem,
      List<V2TimGroupMemberFullInfo?>? disableList) {
    if (disableList == null || disableList.isEmpty == true) {
      return false;
    }
    return disableList
        .where((e) => e?.userID == friendItem.userFriendId)
        .toList()
        .isNotEmpty;
  }

  void searchResult() {
    if (inputSearchText.isEmpty) {
      searchFriendResultList.clear();
      return;
    }
    List<FriendUserInfoEntity> searchResult = [];
    List<FriendUserInfoEntity> allFriendList = getAllFriendList();

    for (int i = 0; i < allFriendList.length; i++) {
      FriendUserInfoEntity userInfoEntity = allFriendList[i];
      if (userInfoEntity.nickname!.contains(inputSearchText.value)) {
        if (searchResult
            .where((value) => value.userFriendId == userInfoEntity.userFriendId)
            .toList()
            .isEmpty) {
          searchResult.add(userInfoEntity);
        }
      }
    }
    searchFriendResultList.value = searchResult;
    update();
  }

  void clearCreateGroupState() {
    groupUnfoldMap.removeWhere((key, value) => key.contains("create_"));
    selectedFriendItems.clear();
    searchFriendResultList.clear();
  }

  String groupNameWithMemberList(ChatGroupInfoEntity groupInfo) {
    if (groupInfo.groupName?.isNotEmpty == true) {
      return groupInfo.groupName!;
    }
    String groupName = "";
    if (groupInfo.memberList?.isNotEmpty == true) {
      for (int i = 0; i < groupInfo.memberList!.length; i++) {
        FriendUserInfoEntity member = groupInfo.memberList![i];
        if (member.nickname?.isNotEmpty == true) {
          groupName += "、${member.nickname}";
        }
      }
    }
    if (groupName.isEmpty) {
      groupName = "群聊";
    }
    return groupName;
  }

  List<FriendUserInfoEntity> getAllFriendList() {
    List<FriendUserInfoEntity> allFriendList = [];
    ContactGroupListItemEntity friendGroupEntity = contactsGroupList!
        .where((value) => value.groupType == 1)
        .toList()
        .first;
    if (friendGroupEntity.friendGroupInfo?.unGroupedFriendList?.isNotEmpty ==
        true) {
      allFriendList
          .addAll(friendGroupEntity.friendGroupInfo!.unGroupedFriendList!);
    }

    if (friendGroupEntity.friendGroupInfo?.subGroupList?.isNotEmpty == true) {
      for (int i = 0;
          i < friendGroupEntity.friendGroupInfo!.subGroupList!.length;
          i++) {
        FriendSubGroupEntity subGroupEntity =
            friendGroupEntity.friendGroupInfo!.subGroupList![i];
        if (subGroupEntity.friendList?.isNotEmpty == true) {
          allFriendList.addAll(subGroupEntity.friendList!);
        }
      }
    }

    ContactGroupListItemEntity daziGroupEntity = contactsGroupList!
        .where((value) => value.groupType == 2)
        .toList()
        .first;
    if (daziGroupEntity.daziList?.isNotEmpty == true) {
      allFriendList.addAll(daziGroupEntity.daziList!);
    }
    return allFriendList;
  }

  Future<List<UserInfoEntity>> getFriendsAndDaziList() async {
    contactsGroupList ??= await loadData();
    if (!(contactsGroupList?.isNotEmpty == true)) {
      return [];
    }
    List<UserInfoEntity> result = [];
    List<FriendUserInfoEntity> allFriendList = getAllFriendList();
    for (int i = 0; i < allFriendList.length; i++) {
      FriendUserInfoEntity entity = allFriendList[i];
      UserInfoEntity userInfo = UserInfoEntity();
      userInfo.id = entity.userFriendId ?? entity.daUserId;
      userInfo.nickname = entity.nickname;
      userInfo.avatar = entity.avatar;
      userInfo.sex = entity.sex;
      result.add(userInfo);
    }
    return result;
  }
}
