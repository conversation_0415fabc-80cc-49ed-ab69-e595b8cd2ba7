import 'package:dada/common/values/constants.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/components/widgets/custom_popup_menu_item.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/popup_menu_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/pages/chat/contacts/contacts_page.dart';
import 'package:dada/pages/chat/conversation/chat_conversation_list_page.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with TickerProviderStateMixin {
  late TabController tabController;
  late PageController pageController;
  late ContactsController contactsController = ContactsController();

  late List tabItemTitles;

  Rx<UserInfoEntity?> user = Rx(UserService().user);

  @override
  void initState() {
    super.initState();

    tabItemTitles = [
      S.current.recently,
      S.current.contacts,
    ];

    tabController = TabController(
        initialIndex: 0, length: tabItemTitles.length, vsync: this);
    pageController = PageController(initialPage: 0);

    ///预先拉取通讯录数据
    Get.put(contactsController);

    UserService().updateStream.listen((event) {
      user.value = event;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GradientWidget(
      width: ScreenUtil().screenWidth,
      height: ScreenUtil().screenHeight -
          ScreenUtil().bottomBarHeight -
          Constants.kBottomBarHeight,
      stops: const [0, 0.4],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTopUserInfoWidget(),
          _buildTabBar(),
          _buildPageView(),
        ],
      ),
    );
  }

  ///顶部用户信息栏、搜索按钮、加好友按钮
  Widget _buildTopUserInfoWidget() {
    return Container(
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      height: 44.h,
      child: Row(
        children: [
          Row(
            children: [
              ///头像
              Padding(
                padding: EdgeInsets.only(left: 15.w),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(GetRouter.userProfile);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15.r),
                      border: Border.all(color: Colors.white),
                    ),
                    child: ClipOval(
                      child: Obx(
                        () => ImageUtils.getImage(
                          user.value?.avatar ?? "",
                          30.w,
                          30.w,
                          showPlaceholder: true,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              ///昵称
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Obx(
                  () => Text(
                    user.value?.nickname ?? "",
                    style: TextStyles.normal(16.sp),
                  ),
                ),
              ),
            ],
          ),
          const Spacer(),
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(GetRouter.chatSearch,
                        parameters: {"searchType": "1"});
                  },
                  child: ImageUtils.getImage(
                      Assets.imagesChatSearchIcon, 20.w, 20.w),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: PopupMenuWidget(
                  offset: Offset(20.w, 30.h),
                  items: _buildChatPopupMenuItems(),
                  onSelected: (value) async {},
                  child:
                      ImageUtils.getImage(Assets.imagesChatAddIcon, 20.w, 20.w),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 44.h,
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          TabBar(
            padding: EdgeInsets.only(left: 15.w, top: 0.h),
            tabAlignment: TabAlignment.start,
            controller: tabController,
            tabs: tabItemTitles.map((e) {
              if (e == S.current.contacts) {
                return Obx(() {
                  int count = Get.find<MainController>()
                      .newFriendApplyUnreadCount
                      .value;
                  return BadgeWidget(
                    offset: Offset(12.w, 0.h),
                    text: count == 0 ? null : "$count",
                    child: Tab(
                      text: e,
                    ),
                  );
                });
              }
              return Obx(() {
                int count = Get.find<MainController>().unreadMessageCount.value;
                return BadgeWidget(
                  offset: Offset(12.w, 0.h),
                  text: count == 0 ? null : "$count",
                  child: Tab(
                    text: e,
                  ),
                );
              });
            }).toList(),
            // tabs: tabItemTitles.map((e) => Tab(text: e)).toList(),
            dividerColor: Colors.transparent,
            isScrollable: true,
            labelPadding: EdgeInsets.only(right: 15.w),
            labelStyle: TextStyles.medium(18.sp),
            labelColor: AppTheme.themeData.textTheme.headlineLarge?.color,
            unselectedLabelColor:
                AppTheme.themeData.textTheme.bodyMedium?.color,
            unselectedLabelStyle: TextStyle(fontSize: 16.sp),
            indicatorWeight: 6.h,
            indicatorPadding:
                EdgeInsets.only(bottom: 14.h, top: 24.h, left: 5.w, right: 5.w),
            indicator: BoxDecoration(
              color: Theme.of(context)
                  .bottomNavigationBarTheme
                  .selectedLabelStyle
                  ?.color,
              borderRadius: BorderRadius.zero,
            ),
            onTap: (index) {
              pageController.animateToPage(index,
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInCubic);
            },
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(right: 10.w),
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 5.w),
                  child: Text(
                    "陌生人消息",
                    style: TextStyles.normal(15.sp),
                  ),
                ),
                Obx(
                  () => GestureDetector(
                    onTap: () async {
                      bool success = await ApiService().updateStrangerMsgState(
                          state:
                              contactsController.isReceiveStrangerMsg.value ==
                                      true
                                  ? false
                                  : true);
                      if (success) {
                        contactsController.isReceiveStrangerMsg.value =
                            !(contactsController.isReceiveStrangerMsg.value);
                      }
                    },
                    child: ImageUtils.getImage(
                        contactsController.isReceiveStrangerMsg.value
                            ? Assets.imagesSmallRoomSwitchBtnOpen
                            : Assets.imagesSmallRoomSwitchBtnClose,
                        40.w,
                        20.h),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageView() {
    return Expanded(
      child: PageView(
        controller: pageController,
        onPageChanged: (index) {
          tabController.animateTo(index);
        },
        children: const [
          ChatConversationListPage(),
          ContactsPage(),
        ],
      ),
    );
  }

  List<PopupMenuItem<int>> _buildChatPopupMenuItems() {
    List<PopupMenuItem<int>> list = [];
    list.add(
      CustomPopupMenuItem.create(
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 5.w),
              child:
                  ImageUtils.getImage(Assets.imagesChatCreateGroup, 18.w, 15.h),
            ),
            SizedBox(
              width: 10.w,
            ),
            Text(
              "创建群",
              style: TextStyles.normal(16.sp),
            ),
          ],
        ),
        value: 0,
        onTap: () {
          Get.toNamed(GetRouter.createGroupChat);
        },
      ),
    );
    list.add(
      CustomPopupMenuItem.create(
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 5.w),
              child:
                  ImageUtils.getImage(Assets.imagesChatAddFriend, 18.w, 15.h),
            ),
            SizedBox(
              width: 10.w,
            ),
            Text(
              "添加好友/群",
              style: TextStyles.normal(16.sp),
            ),
          ],
        ),
        value: 0,
        onTap: () {
          Get.toNamed(GetRouter.chatSearch, parameters: {"searchType": "0"});
        },
      ),
    );
    return list;
  }
}
