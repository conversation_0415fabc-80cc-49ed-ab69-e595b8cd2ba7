import 'package:dada/model/chat_searched_result_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class ChatSearchController extends GetxController {
  ChatSearchedResultEntity? searchedResult;
  RxBool showSearchHistoryList = true.obs;
  Rx<List<String>?> searchHistoryList = Rx(null);

  int searchType = 0;

  @override
  void onReady() {
    super.onReady();

    loadSearchHistoryList();
  }

  void searchText(String searchText) async {
    searchedResult =
        await ApiService().searchUsersAndGroupList(searchText, searchType);
    update();
  }

  void loadSearchHistoryList() async {
    searchHistoryList.value = await ApiService().getSearchHistoryList();
    showSearchHistoryList.value = searchHistoryList.value?.isNotEmpty == true;
  }

  void clearHistoryList() async {
    bool success = await ApiService().deleteSearchHistoryList(searchHistoryList.value!);
    if (success) {
      searchHistoryList.value = null;
      showSearchHistoryList.value = false;
    }
  }

  void deleteHistoryItem(String keyword) async {
    bool success = await ApiService().deleteSearchHistoryList([keyword]);
    if (success) {
      searchHistoryList.value?.remove(keyword);
    }
  }
}
