import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/constants.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_search_bar.dart';
import 'package:dada/components/widgets/highlight_text_label.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_group_info_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/pages/chat/search/chat_search_controller.dart';
import 'package:dada/pages/chat/apply/join_group_chat_apply_send_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatSearchPage extends StatefulWidget {
  const ChatSearchPage({super.key});

  @override
  State<ChatSearchPage> createState() => _ChatSearchPageState();
}

class _ChatSearchPageState extends State<ChatSearchPage> {
  final ChatSearchController controller = Get.put(ChatSearchController());
  final TextEditingController editingController = TextEditingController();
  final FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    if (Get.parameters["searchType"] != null) {
      controller.searchType = int.parse(Get.parameters["searchType"]!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      extendBodyBehindAppBar: true,
      body: Column(
        children: [
          _buildSearchBar(),
          Stack(
            children: [
              _buildSearchHistoryListWidget(),
              _buildSearchedResultListWidget(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Get.back(),
            icon: Container(
              width: 28.w,
              height: 28.w,
              alignment: Alignment.center,
              child: ImageUtils.getImage(Assets.imagesNaviBack, 8.w, 14.w),
            ),
          ),
          Expanded(
            child: CustomSearchBar(
              focusNode: focusNode,
              controller: editingController,
              bgColor: AppColors.colorFFF5F5F5,
              margin: EdgeInsets.only(
                  left: 0.w, top: 5.h, bottom: 5.h, right: 15.w),
              onChanged: (value) {
                if (value.isEmpty) {
                  controller.searchedResult = null;
                  controller.update();
                  controller.showSearchHistoryList.value =
                      controller.searchedResult == null;
                }
              },
              onSubmit: (value) {
                controller.showSearchHistoryList.value = false;
                controller.searchText(value);
              },
            ),
          ),
          ListenableBuilder(
            listenable: focusNode,
            builder: (context, child) {
              return Visibility(
                visible: focusNode.hasFocus,
                child: Padding(
                  padding: EdgeInsets.only(right: 15.w),
                  child: GestureDetector(
                    onTap: () {
                      focusNode.unfocus();
                      editingController.clear();
                      controller.searchedResult = null;
                      controller.update();
                      controller.showSearchHistoryList.value =
                          controller.searchedResult == null;
                    },
                    child: Text(
                      "取消",
                      style: TextStyles.common(16.sp, AppColors.colorFF23AF28),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchedResultListWidget() {
    return GetBuilder(
      init: controller,
      builder: (controller) {
        if (controller.searchedResult == null ||
            (controller.searchedResult?.searchUsers?.isEmpty == true &&
                controller.searchedResult?.searchCrowdChats?.isEmpty == true)) {
          return Container();
        }
        return SizedBox(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight -
              ScreenUtil().statusBarHeight -
              45.h -
              Constants.kBottomBarHeight,
          child: SingleChildScrollView(
            child: Column(
              children: [
                _buildSearchedUserList(controller.searchedResult?.searchUsers),
                _buildMiddleSeparator(),
                _buildSearchedGroupList(
                    controller.searchedResult?.searchCrowdChats),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchedUserList(List<FriendUserInfoEntity>? userList) {
    if (userList == null || userList.isEmpty == true) {
      return Container();
    }
    List<FriendUserInfoEntity> list = [];
    if (userList.length > 5) {
      list = userList.sublist(0, 5);
    } else {
      list = userList;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        controller.searchType == 0
            ? _buildListViewHeader("用户", () {})
            : Padding(
                padding: EdgeInsets.only(left: 15.w, top: 15.h),
                child: Text(
                  "联系人",
                  style: TextStyles.common(14.sp, AppColors.colorFF666666),
                ),
              ),
        ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            FriendUserInfoEntity user = list[index];
            return _buildSearchedUserListItem(user);
          },
          separatorBuilder: (context, index) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              height: 1,
              color: AppColors.colorFFE5E5E5,
            );
          },
          itemCount: list.length,
        ),
      ],
    );
  }

  Widget _buildSearchedUserListItem(FriendUserInfoEntity userInfo) {
    return InkWell(
      onTap: () {
        String? userId = userInfo.userId;
        if (userId != null) {
          Get.toNamed(
            GetRouter.userProfile,
            parameters: {
              "userId": userId,
            },
          );
        }
      },
      child: SizedBox(
        height: 75.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 15.w),
                  child: ClipOval(
                    child: ImageUtils.getImage(
                        userInfo.avatar ?? "", 45.w, 45.w,
                        fit: BoxFit.cover),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 5.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 230.w,
                        child: Row(
                          children: [
                            Flexible(
                              child: HighlightTextLabel(
                                content: userInfo.nickname ?? "",
                                targetText: editingController.text,
                              ),
                            ),
                            Text(
                              "（${userInfo.dadaNo ?? ""}）",
                              style: TextStyles.normal(16.sp),
                            ),
                          ],
                        ),
                      ),
                      _buildUserSignatureWidget(userInfo),
                    ],
                  ),
                ),
              ],
            ),
            _buildAddFriendBtn(userInfo),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchedGroupList(List<ChatGroupInfoEntity>? groupList) {
    if (groupList == null || groupList.isEmpty == true) {
      return Container();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        controller.searchType == 0
            ? _buildListViewHeader("群聊", () {})
            : Padding(
                padding: EdgeInsets.only(left: 15.w, top: 15.h),
                child: Text(
                  "群聊",
                  style: TextStyles.common(14.sp, AppColors.colorFF666666),
                ),
              ),
        ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            ChatGroupInfoEntity groupInfo = groupList[index];
            return _buildGroupListItem(groupInfo);
          },
          separatorBuilder: (context, index) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              height: 1,
              color: AppColors.colorFFE5E5E5,
            );
          },
          itemCount: groupList.length,
        ),
      ],
    );
  }

  Widget _buildGroupListItem(ChatGroupInfoEntity groupInfo) {
    return SizedBox(
      height: 75.h,
      child: Row(
        children: [
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(left: 15.w),
                child: ClipOval(
                  child: ImageUtils.getImage(
                    groupInfo.faceUrl ?? Assets.imagesGroupChatDefaultAvatar,
                    45.w,
                    45.w,
                    showPlaceholder: true,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        SizedBox(
                          width: 230.w,
                          child: Row(
                            children: [
                              Flexible(
                                child: HighlightTextLabel(
                                  content: groupInfo.groupName ?? "",
                                  targetText: editingController.text,
                                ),
                              ),
                              Text(
                                "（${groupInfo.memberCount ?? "0"}人）",
                                style: TextStyles.normal(16.sp),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    _buildGroupLabelWidget(groupInfo),
                  ],
                ),
              ),
            ],
          ),
          _buildJoinGroupBtn(groupInfo),
        ],
      ),
    );
  }

  Widget _buildGroupLabelWidget(ChatGroupInfoEntity groupInfo) {
    if (!(groupInfo.labels?.isNotEmpty == true)) {
      return Container();
    }
    return Padding(
      padding: EdgeInsets.only(top: 5.h),
      child: SizedBox(
        width: 220.w,
        height: 20.h,
        child: ListView.separated(
          padding: EdgeInsets.zero,
          scrollDirection: Axis.horizontal,
          itemCount: groupInfo.labels!.length,
          itemBuilder: (context, index) {
            String? label = groupInfo.tagStrList![index];
            return LabelItemWidget(
              text: label,
              editable: false,
              minWidth: 35.w,
              bgColor: AppColors.colorFFEAECF1,
              fontSize: 12.sp,
              borderRadius: 10.r,
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              borderColor: Colors.transparent,
            );
          },
          separatorBuilder: (context, index) {
            return SizedBox(width: 5.w);
          },
        ),
      ),
    );
  }

  Widget _buildUserSignatureWidget(FriendUserInfoEntity friendInfo) {
    return friendInfo.describesFlag == 1 //文字签名
        ? Container(
            padding: EdgeInsets.only(top: 5.h),
            constraints: BoxConstraints(
              maxWidth: controller.searchType == 0 ? 230.w : 310.w,
            ),
            child: Text(
              friendInfo.userDescribes!,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.common(14.sp, AppColors.colorFF666666),
            ),
          )
        : (friendInfo.describesFlag == 0 &&
                friendInfo.voiceLength != null) //语音签名
            ? Container(
                margin: EdgeInsets.only(top: 5.h),
                width: 76.w,
                height: 20.h,
                decoration: BoxDecoration(
                  color: AppColors.colorFF4DC151,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 3.h),
                      child: ImageUtils.getImage(
                          Assets.imagesMatchDadaListAudioPlay, 15.w, 15.w),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 6.w),
                      child: ImageUtils.getImage(
                          Assets.imagesMatchDadaListAudioVolume, 13.w, 10.w),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 7.w),
                      child: Text(
                        "${friendInfo.voiceLength ?? 0}s",
                        style: TextStyles.common(14.sp, Colors.white),
                      ),
                    ),
                  ],
                ),
              )
            : Container();
  }

  Widget _buildListViewHeader(String title, Function() onTapChanged) {
    return SizedBox(
      height: 41.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w),
            child: Text(
              title,
              style: TextStyles.common(16.sp, AppColors.colorFF666666),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: GestureDetector(
              onTap: () {
                controller.searchText(editingController.text);
                onTapChanged();
              },
              child: Row(
                children: [
                  ImageUtils.getImage(
                      Assets.imagesAddFriendExchange, 14.w, 14.w),
                  SizedBox(
                    width: 6.w,
                  ),
                  Text(
                    "换一批",
                    style: TextStyles.common(16.sp, AppColors.colorFF666666),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMiddleSeparator() {
    if (controller.searchedResult?.searchUsers?.isNotEmpty == true &&
        controller.searchedResult?.searchCrowdChats?.isNotEmpty == true) {
      return Container(
        height: 10.h,
        color: AppColors.colorFFE5E5E5,
      );
    }
    return Container();
  }

  Widget _buildAddFriendBtn(FriendUserInfoEntity userInfo) {
    if (controller.searchType == 1) {
      return Container();
    }
    return GestureDetector(
      onTap: () {
        if (userInfo.isFriend == 0) {
          Get.toNamed(GetRouter.addFriendSend, parameters: {
            "userId": userInfo.userId!,
            "avatar": userInfo.avatar!,
            "nickname": userInfo.nickname!,
          });
        }
      },
      child: Container(
        width: 60.w,
        height: 30.h,
        alignment: Alignment.center,
        margin: EdgeInsets.only(right: 15.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.r),
          color: userInfo.isFriend == 0 ? AppColors.colorFF89E15C : null,
          border: userInfo.isFriend == 1
              ? Border.all(color: AppColors.colorFFD4D4D4)
              : null,
        ),
        child: Text(
          userInfo.isFriend == 1 ? "已添加" : "加好友",
          style: TextStyles.common(
              14.sp,
              userInfo.isFriend == 1
                  ? AppColors.colorFF999999
                  : AppColors.colorFF3D3D3D),
        ),
      ),
    );
  }

  Widget _buildJoinGroupBtn(ChatGroupInfoEntity groupInfo) {
    if (controller.searchType == 1) {
      return Container();
    }
    return GestureDetector(
      onTap: () {
        if (groupInfo.isInGroup != 0 && groupInfo.chatsId != null) {
          Get.to(() => JoinGroupChatApplySendPage(groupID: groupInfo.chatsId!));
        }
      },
      child: Container(
        width: 60.w,
        height: 30.h,
        alignment: Alignment.center,
        margin: EdgeInsets.only(right: 15.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.r),
          color: groupInfo.isInGroup == 1 ? null : AppColors.colorFF89E15C,
          border: groupInfo.isInGroup == 1
              ? Border.all(color: AppColors.colorFFD4D4D4)
              : null,
        ),
        child: Text(
          groupInfo.isInGroup == 1 ? "已添加" : "+ 加入",
          style: TextStyles.common(
              14.sp,
              groupInfo.isInGroup == 1
                  ? AppColors.colorFF999999
                  : AppColors.colorFF3D3D3D),
        ),
      ),
    );
  }

  Widget _buildSearchHistoryListWidget() {
    return Obx(() {
      if (!(controller.searchHistoryList.value?.isNotEmpty == true)) {
        return Container();
      }
      return Visibility(
        visible: controller.showSearchHistoryList.value,
        child: SizedBox(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight -
              ScreenUtil().statusBarHeight -
              45.h -
              Constants.kBottomBarHeight,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 15.w, top: 16.h),
                    child: Text(
                      "历史记录",
                      style: TextStyles.common(14.sp, AppColors.colorFF666666),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      controller.clearHistoryList();
                    },
                    child: Padding(
                      padding: EdgeInsets.only(right: 30.w, top: 15.h),
                      child: ImageUtils.getImage(
                          Assets.imagesUserLabelDetailDelete, 16.w, 16.w),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(left: 14.w, right: 10.w, top: 10.h),
                child: Wrap(
                  spacing: 5.w,
                  runSpacing: 10.h,
                  children: controller.searchHistoryList.value!
                      .map(
                        (e) => LabelItemWidget(
                          text: e,
                          editable: true,
                          bgColor: AppColors.colorFFEAECF1,
                          borderRadius: 15.r,
                          borderColor: Colors.transparent,
                          deleteAction: () {
                            controller.deleteHistoryItem(e);
                          },
                          onTap: () {
                            editingController.text = e;
                            controller.showSearchHistoryList.value = false;
                            controller.searchText(e);
                          },
                        ),
                      )
                      .toList(),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
