import 'dart:async';
import 'dart:convert';

import 'package:dada/model/audio_box_list_item_entity.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/group_chat_application_entity.dart';
import 'package:dada/pages/chat/apply/group_chat_apply_controller.dart';
import 'package:dada/pages/chat/setting/group/group_chat_setting_controller.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';

class ChatDetailController extends GetxController {
  late V2TimConversation conversation;
  V2TimGroupInfo? groupInfo;
  FriendUserInfoEntity? friendUserInfo;
  TIMUIKitChatController timUIKitChatController = TIMUIKitChatController();
  RxBool hasNewApplication = false.obs;
  RxDouble bottomHeight = 0.0.obs;
  RxBool isOnline = false.obs;

  final AudioPlayerUtils audioPlayer = AudioPlayerUtils();
  final Map<String, SVGAAnimationController> svgaControllerMap = {};

  @override
  void onInit() {
    super.onInit();

    conversation = Get.arguments;

    audioPlayer.onInit();
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.complete) {
        stopOtherAnimation();
        update();
      }
    });

    timUIKitChatController.checkCanSendMsg =
        (int msgType, {String? text}) async {
      ///私聊做限制
      if (groupInfo == null && friendUserInfo != null) {
        if (friendUserInfo!.isFriend != 1) {
          bool openChat = await ApiService()
              .getStrangerMsgState(userId: conversation.userID!);
          if (openChat == false) {
            ToastUtils.showToast("发送失败，对方暂不接收陌生人消息");
          } else {
            if (msgType == MessageElemType.V2TIM_ELEM_TYPE_TEXT &&
                text != null &&
                text.length > 2000) {
              ToastUtils.showToast("发送失败，消息长度不能超过2000");
              return false;
            }
          }
          return openChat;
        }
      }
      return true;
    };

    EventBusEngine.eventBus.on().listen((event) {
      EventEntity entity = event;
      if (entity.event == BusEvent.refreshChatHistoryList) {
        refreshHistoryList();
      } else if (entity.event == BusEvent.reloadChatDetail) {
        loadData();
      } else if (entity.event == BusEvent.receiveGroupApplication) {
        checkHasNewGroupApplication();
      }
    });
  }

  @override
  void onReady() async {
    super.onReady();

    await loadData();

    await clearMessageUnReadCount();
  }

  Future<void> loadData() async {
    /// Conversation Type： 1.私聊；2.群聊
    if (conversation.type == 2 && conversation.groupID != null) {
      groupInfo = await ChatIMManager.sharedInstance
          .getGroupInfo(conversation.groupID!);
      if (groupInfo != null) {
        checkHasNewGroupApplication();
      }
    } else {
      friendUserInfo = await ApiService()
          .getFriendUserInfo(conversation.userID!, showLoading: false);
    }
    update();
  }

  Future<void> clearMessageUnReadCount() async {
    if (conversation.type == 2 && conversation.groupID != null) {
    } else {
      ChatIMManager.sharedInstance
          .clearC2CMessageUnReadCount(conversation.userID!);
    }
  }

  ///是否是群主
  Future<bool> checkIsGroupOwnerOrManager({String? userId}) async {
    String? targetUserId = userId ?? UserService().user?.id;
    bool isOwner = false;
    bool isManager = false;
    if (groupInfo != null && groupInfo!.owner != null) {
      isOwner = groupInfo!.owner == targetUserId;
    }
    if (groupInfo?.groupID != null) {
      GroupChatSettingController groupChatSettingController =
          GroupChatSettingController();
      groupChatSettingController.memberList = await groupChatSettingController
          .loadGroupMemberList(groupInfo!.groupID);
      await groupChatSettingController.getGroupManagerList();
      isManager = groupChatSettingController.checkIsGroupOwnerOrManager();
    }

    return isOwner || isManager;
  }

  void refreshHistoryList() {
    timUIKitChatController.refreshCurrentHistoryList();
  }

  void checkHasNewGroupApplication() async {
    if (groupInfo != null && groupInfo?.groupType != GroupType.Meeting) {
      Future.delayed(const Duration(seconds: 1), () async {
        GroupChatApplyController chatApplyController =
            GroupChatApplyController();
        chatApplyController.groupID = conversation.groupID!;
        List<GroupChatApplicationEntity>? list =
            await chatApplyController.loadData(1);
        List<GroupChatApplicationEntity>? list1 =
            list?.where((e) => e.handleStatus == 0).toList();
        hasNewApplication.value = list1?.isNotEmpty == true;
      });
    }
  }

  Future<bool> sendCustomMsg(dynamic data, String type,
      {String? userID, String? groupID}) async {
    bool success = await ChatIMManager.sharedInstance.sendCustomMessage(
        type: type,
        data: data,
        receiver: userID ??
            (friendUserInfo?.userFriendId != null
                ? friendUserInfo!.userFriendId!
                : ""),
        groupID:
            groupID ?? (groupInfo?.groupID != null ? groupInfo!.groupID : ""));
    if (success) {
      refreshHistoryList();
      return true;
    }
    return false;
  }

  ///AudioBox play control
  void stopCurrentAndPlayNewAudio({V2TimMessage? message}) {
    if (audioPlayer.state == AudioPlayerState.playing) {
      audioPlayer.stop();
    }

    if (message != null) {
      ///跟当前播放的是同一条语音
      SVGAAnimationController? currentSvgaController =
          svgaControllerMap[message.msgID];
      if (currentSvgaController?.isAnimating == true) {
        currentSvgaController?.stop();
        currentSvgaController?.reset();
        return;
      }
      String? jsonStr = message.customElem?.data;
      if (jsonStr != null) {
        ChatImCustomMsgEntity msgEntity =
            ChatImCustomMsgEntity.fromJson(jsonDecode(jsonStr));
        AudioBoxListItemEntity itemEntity =
            AudioBoxListItemEntity.fromJson(jsonDecode(msgEntity.data));
        audioPlayer.setUrl(itemEntity.url!);
        audioPlayer.play();
      }
    }

    playNewAndStopOtherAnimation(messageID: message?.msgID);
  }

  void playNewAndStopOtherAnimation({String? messageID}) {
    if (messageID != null) {
      SVGAAnimationController? currentSvgaController =
          svgaControllerMap[messageID];
      currentSvgaController?.repeat();
    }

    stopOtherAnimation(exclude: messageID);
  }

  void stopOtherAnimation({String? exclude}) {
    svgaControllerMap.forEach((key, svgaController) {
      if (svgaController.isAnimating &&
          ((key != exclude && exclude != null) || exclude == null)) {
        svgaController.stop();
        svgaController.reset();
      }
    });
    update();
  }

  void stopAudioPlayer() {
    if (audioPlayer.state == AudioPlayerState.playing) {
      audioPlayer.stop();
    }
  }

  void updateBottomHeight(double height) {
    if (bottomHeight.value != height) {
      bottomHeight.value = height;
    }
  }

  @override
  void onClose() {
    stopCurrentAndPlayNewAudio();
    super.onClose();
  }
}
