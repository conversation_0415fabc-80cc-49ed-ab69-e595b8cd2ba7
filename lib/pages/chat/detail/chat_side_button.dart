import 'package:dada/common/values/colors.dart';
import 'package:dada/components/widgets/scale_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat/invite/chat_invite_game_sheet.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/log.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///聊天侧边栏按钮
class ChatSideButton extends StatefulWidget {
  const ChatSideButton(
      {super.key, required this.daUserId, required this.friendLevel});

  final String daUserId;

  final String friendLevel;

  @override
  State<ChatSideButton> createState() => _ChatSideButtonState();
}

class _ChatSideButtonState extends State<ChatSideButton> {
  bool close = true;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      width: 55.w,
      height:
          close ? 60.w : (224.w + (widget.friendLevel.isNotEmpty ? 20.w : 0)),
      padding: EdgeInsets.fromLTRB(7.5.w, 10.w, 7.5.w, 10.w),
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(100)),
          color: Colors.black.withOpacity(0.12)),
      duration: const Duration(milliseconds: 300),
      curve: Curves.fastOutSlowIn,
      child: FittedBox(
        fit: BoxFit.fitWidth,
        clipBehavior: Clip.antiAlias,
        alignment: close ? Alignment.center : Alignment.topCenter,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (close)
              ScaleButton(
                  child: ImageUtils.getImage(
                      Assets.imagesChatSideMore, 40.w, 40.w,
                      fit: BoxFit.scaleDown),
                  onTap: () => setState(() => close = false))
            else
              ..._buildThreeButtons()
          ],
        ),
      ),
    );
  }

  List<Widget> _buildThreeButtons() {
    return [
      SizedBox(height: 6.5.w),
      //游戏
      ScaleButton(
          child: ImageUtils.getImage(Assets.imagesChatSidePlay, 40.w, 40.w,
              fit: BoxFit.scaleDown),
          onTap: () {
            ToastUtils.showBottomDialog(
              ChatInviteGameSheet(daUserId: widget.daUserId),
            );
          }),
      SizedBox(height: 10.w),
      //存甜罐
      ScaleButton(
          child: ImageUtils.getImage(Assets.imagesChatSideBottle, 40.w, 40.w,
              fit: BoxFit.scaleDown),
          onTap: () => Get.toNamed(GetRouter.chatBottle,
              parameters: {"daUserId": widget.daUserId})),
      SizedBox(height: 10.w),
      //亲密
      ImageUtils.getImage(Assets.imagesChatSideHeart, 40.w, 40.w,
          fit: BoxFit.scaleDown),
      if (widget.friendLevel.isNotEmpty)
        SizedBox(
          height: 20.w,
          child: Center(
              child: Text(
            widget.friendLevel,
            style: TextStyle(fontSize: 14.sp, color: AppColors.colorFF666666),
          )),
        ),
      SizedBox(height: 10.w),
      //亲密
      ScaleButton(
          child: ImageUtils.getImage(Assets.imagesChatSideLess, 40.w, 40.w,
              fit: BoxFit.scaleDown),
          onTap: () => setState(() => close = true)),
      SizedBox(height: 6.5.w),
    ];
  }
}
