import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CustomMsgInviteJoinChatRoom extends StatefulWidget {
  final String roomId;
  final String ownerAvatar;
  final String ownerNickname;

  const CustomMsgInviteJoinChatRoom(
      {super.key,
      required this.roomId,
      required this.ownerAvatar,
      required this.ownerNickname});

  @override
  State<CustomMsgInviteJoinChatRoom> createState() =>
      _CustomMsgInviteJoinChatRoomState();
}

class _CustomMsgInviteJoinChatRoomState
    extends State<CustomMsgInviteJoinChatRoom> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 204.w,
      height: 206.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: AppColors.colorFFACE49E,
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: Text(
              "邀请你来一起聊天",
              style: TextStyles.normal(16.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: ClipOval(
              child: ImageUtils.getImage(widget.ownerAvatar, 55.w, 55.w,
                  fit: BoxFit.cover),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 5.h),
            child: Text(
              "${widget.ownerNickname} 的房间",
              style: TextStyles.normal(12.sp),
            ),
          ),
          CommonGradientBtn(
            topMargin: 15.h,
            height: 40.h,
            width: 139.w,
            title: "立即加入",
            normalImage: Assets.imagesCommonGradientBtnBg40h,
            onTap: () async {
              if (GlobalFloatingManager().currentIsShowMiniWindow()) {
                return;
              }
              var result = await ApiService()
                  .getChatRoomInfo(roomId: widget.roomId, showLoading: true);
              if (result == null) {
                return;
              }
              Get.toNamed(GetRouter.chatRoomDetail,
                  parameters: {"roomId": widget.roomId});
            },
          ),
        ],
      ),
    );
  }
}
