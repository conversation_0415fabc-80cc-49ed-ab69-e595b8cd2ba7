import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

class CustomMsgDynamicPost extends StatefulWidget {
  final String postId;
  final String? text;
  final List<String>? imageUrls;
  final bool isSelf;
  final int postType;
  final String avatar;
  final String nickname;

  const CustomMsgDynamicPost({
    super.key,
    required this.avatar,
    required this.nickname,
    required this.postId,
    required this.text,
    required this.imageUrls,
    required this.isSelf,
    required this.postType,
  });

  @override
  State<CustomMsgDynamicPost> createState() => _CustomMsgDynamicPostState();
}

class _CustomMsgDynamicPostState extends State<CustomMsgDynamicPost> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 233.5.w,
      padding:
          EdgeInsets.only(left: 10.w, top: 15.h, right: 10.w, bottom: 15.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: widget.isSelf ? AppColors.colorFFACE49E : Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeaderContent(),
          _buildTextContent(),
          _buildImageContent(),
        ],
      ),
    );
  }

  Widget _buildHeaderContent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            ImageUtils.getImage(widget.avatar, 25.w, 25.w, radius: 25.w / 2),
            Padding(
              padding: EdgeInsets.only(
                left: 5.w,
              ),
              child: Text(
                widget.nickname,
                style: TextStyles.common(14.sp, AppColors.colorFF666666),
              ),
            ),
          ],
        ),
        GestureDetector(
          onTap: () {
            Get.toNamed(GetRouter.postDetail,
                parameters: {"postId": widget.postId});
          },
          child: Text(
            "查看详情",
            style: TextStyles.normal(14.sp),
          ),
        ),
      ],
    );
  }

  Widget _buildTextContent() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      child: Text(
        textAlign: TextAlign.start,
        widget.text?? "",
        maxLines: 4,
        overflow: TextOverflow.ellipsis,
        style: TextStyles.normal(16.sp),
      ),
    );
  }

  Widget _buildImageContent() {
    if (!(widget.imageUrls?.isNotEmpty == true)) {
      return Container();
    }
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      height: 60.w,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          String imageUrl = widget.imageUrls![index];
          if (widget.postType == PostType.video.index) {
            VideoPlayerController? videoPlayerController =
                VideoPlayerController.networkUrl(Uri.parse(imageUrl))
                  ..initialize();
            return ClipRRect(
              borderRadius: BorderRadius.circular(5.r),
              child: SizedBox(
                width: 60.w,
                height: 60.w,
                child: AspectRatio(
                  aspectRatio: videoPlayerController.value.aspectRatio,
                  child: VideoPlayer(videoPlayerController),
                ),
              ),
            );
          }
          return ImageUtils.getImage(imageUrl, 60.w, 60.w,
              radius: 5.r, fit: BoxFit.cover);
        },
        separatorBuilder: (context, index) {
          return Container(
            width: 10.w,
          );
        },
        itemCount: widget.imageUrls!.length,
      ),
    );
  }
}
