import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/profile/card/identity/user_identity_card_info_dialog.dart';
import 'package:dada/pages/profile/card/other/user_other_card_info_dialog.dart';
import 'package:dada/pages/profile/card/user_card_box_item_widget.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';

class CustomMsgUserCardBoxCard extends StatelessWidget {
  final UserIdCardEntity cardInfo;
  final String? userNickname;
  final String? userID;
  final String? userAvatar;

  const CustomMsgUserCardBoxCard(
      {super.key,
      required this.cardInfo,
      this.userNickname,
      this.userID,
      this.userAvatar});

  @override
  Widget build(BuildContext context) {
    return UserCardBoxItemWidget(
      cardInfo: cardInfo,
      onTap: () {
        if (cardInfo.cardType == 1) {
          _showIDCardInfoDialog(cardInfo);
        } else {
          _showOtherCardDialog(cardInfo);
        }
      },
    );
  }

  void _showIDCardInfoDialog(UserIdCardEntity cardInfo) {
    bool isMySelf = userID == UserService().user?.id;
    if (cardInfo.cardState == 1 || isMySelf) {
      ToastUtils.showDialog(
        dialog: UserIdCardInfoDialog(
          userInfo: UserInfoEntity()
            ..id = userID
            ..nickname = userNickname
            ..avatar = userAvatar,
          cardInfo: cardInfo,
        ),
      );
    } else {
      ToastUtils.showToast(S.current.cardInfoUnOpen);
    }
  }

  void _showOtherCardDialog(UserIdCardEntity cardInfo) {
    ToastUtils.showDialog(
      dialog: UserOtherCardInfoDialog(cardInfo: cardInfo),
    );
  }
}
