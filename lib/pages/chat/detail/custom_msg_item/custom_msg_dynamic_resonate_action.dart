import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

class CustomMsgDynamicResonateAction extends StatefulWidget {
  final String postId;
  final bool isScratch;
  final String text;
  final List<String> imageUrls;
  final bool isSelf;
  final int postType;

  const CustomMsgDynamicResonateAction({
    super.key,
    required this.postId,
    required this.isScratch,
    required this.text,
    required this.imageUrls,
    required this.isSelf,
    required this.postType,
  });

  @override
  State<CustomMsgDynamicResonateAction> createState() =>
      _CustomMsgDynamicResonateActionState();
}

class _CustomMsgDynamicResonateActionState
    extends State<CustomMsgDynamicResonateAction> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        jumpToPage();
      },
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 212.w,
                margin: EdgeInsets.only(bottom: widget.isScratch ? 55.h : 0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  color: widget.isSelf ? AppColors.colorFFACE49E : Colors.white,
                ),
                padding: EdgeInsets.only(
                    top: 15.h, left: 10.w, right: 10.w, bottom: 10.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildMsgTitle(),
                    _buildTextContent(),
                    _buildImageContent(),
                  ],
                ),
              ),
            ],
          ),
          _buildScratchTip(),
        ],
      ),
    );
  }

  Widget _buildMsgTitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 2.5.w),
      child: Text(
        "咱们是通过这条动态共鸣的哦",
        style: TextStyles.common(14.sp, AppColors.colorFF666666),
      ),
    );
  }

  Widget _buildTextContent() {
    return Visibility(
      visible: widget.text.isNotEmpty,
      child: Container(
        margin: EdgeInsets.only(top: 10.h),
        constraints: BoxConstraints(
          maxHeight: 75.h,
        ),
        child: Text(
          widget.text,
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
          style: TextStyles.normal(16.sp),
        ),
      ),
    );
  }

  Widget _buildImageContent() {
    List<String> imageUrls = [];
    if (widget.imageUrls.length > 3) {
      imageUrls = widget.imageUrls.sublist(0, 3);
    } else {
      imageUrls = widget.imageUrls;
    }
    VideoPlayerController? videoPlayerController;
    if (widget.postType == PostType.video.index) {
      videoPlayerController =
          VideoPlayerController.networkUrl(Uri.parse(widget.imageUrls.first))
            ..initialize();
    }

    return Visibility(
      visible: widget.imageUrls.isNotEmpty,
      child: Container(
        margin: EdgeInsets.only(top: 10.h),
        height: 60.h,
        alignment: Alignment.centerLeft,
        child: Wrap(
          children: imageUrls
              .map(
                (e) => ClipRRect(
                  borderRadius: BorderRadius.circular(5.r),
                  child: videoPlayerController != null
                      ? SizedBox(
                          width: 60.w,
                          height: 60.w,
                          child: AspectRatio(
                            aspectRatio:
                                videoPlayerController.value.aspectRatio,
                            child: VideoPlayer(videoPlayerController),
                          ),
                        )
                      : ImageUtils.getImage(e, 60.w, 60.w, fit: BoxFit.cover),
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  Widget _buildScratchTip() {
    return Visibility(
      visible: widget.isScratch == true && widget.isSelf == false,
      child: Positioned(
        left: -53.w,
        right: -35.w,
        bottom: 0.h,
        child: Text(
          "随心的言语碰撞共鸣，两地的人儿却羞于启口，有没有勇士来破一下冰呢？",
          maxLines: 2,
          style: TextStyles.common(14.sp, AppColors.colorFF666666, h: 1.3),
        ),
      ),
    );
  }

  void jumpToPage() {
    String postId = widget.postId;
    Get.toNamed(GetRouter.postDetail, parameters: {
      "postId": postId,
      "autoFocusNode": "0"
    });
  }
}
