import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomMsgTipsMsg extends StatelessWidget {
  final String text;

  const CustomMsgTipsMsg({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: ScreenUtil().screenWidth * 0.77,
      ),
      child: Text(
        text,
        style: TextStyles.common(12.sp, AppColors.colorFF999999, h: 1.3),
      ),
    );
  }
}
