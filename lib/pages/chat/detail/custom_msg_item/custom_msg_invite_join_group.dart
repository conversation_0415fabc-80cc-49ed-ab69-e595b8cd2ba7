import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/pages/chat/invite/invite_join_group_middle_page.dart';
import 'package:dada/pages/match/assemble/hall/join_team_input_pwd_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

class CustomMsgInviteJoinGroup extends StatelessWidget {
  final bool? isSelf;
  final String? type;
  final String title;
  final String text;
  final String? groupID;
  final String? teamID;
  final String invitorID;
  final String? faceUrl;
  final String? groupName;
  final int? lockState;
  final int? memberCount;
  final Function()? onTap;

  const CustomMsgInviteJoinGroup(
      {super.key,
      required this.type,
      required this.title,
      required this.text,
      required this.groupID,
      required this.teamID,
      required this.invitorID,
      required this.faceUrl,
      this.groupName,
      this.isSelf,
      this.lockState,
      this.memberCount,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        jumpToPage(type);
      },
      child: Container(
        width: 195.w,
        padding:
            EdgeInsets.only(left: 10.w, right: 10.w, top: 10.h, bottom: 10.h),
        // constraints: BoxConstraints(
        //   maxHeight: 87.h,
        // ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: isSelf == true
              ? AppColors.colorFFACE49E
              : const Color(0xFFEDEDED),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyles.normal(16.sp),
            ),
            Padding(
              padding: EdgeInsets.only(top: 3.h),
              child: Text(
                text,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style:
                    TextStyles.common(14.sp, AppColors.colorFF666666, h: 1.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void jumpToPage(String? type) async {
    if (invitorID == UserService().user?.id) {
      return;
    }
    if (type == ChatImCustomMsgType.InviteToJoinTeam) {
      MatchTeamResultEntity? teamEntity = await ApiService()
          .getAssembleTeamInfo(teamId: teamID!, showLoading: true);
      if (teamEntity?.teamLockState == 1) {
        ToastUtils.showDialog(
          dialog: JoinTeamInputPwdDialog(
            title: S.current.lockPwd,
            subTitle: S.current.setTeamPwd,
            callback: (pwd) {
              joinAssembleTeam(pwd: pwd.toString());
            },
          ),
        );
      } else {
        joinAssembleTeam();
      }
    } else if (type == ChatImCustomMsgType.InviteToJoinChatGroup) {
      Get.to(() => const InviteJoinGroupMiddlePage(), arguments: {
        "groupID": groupID,
        "faceUrl": faceUrl,
        "groupName": groupName,
        "memberCount": memberCount,
      });
    }
  }

  ///加入集结队
  void joinAssembleTeam({String? pwd}) async {
    if (GlobalFloatingManager().currentIsShowMiniWindow(
        teamEntity: MatchTeamResultEntity()..teamId = teamID)) {
      return;
    }
    MatchTeamResultEntity? entity = await ApiService().inviteToJoinTeam(
        teamId: teamID!, userId: UserService().user!.id!, pwd: pwd);
    if (entity != null) {
      bool joinSuccess = await ChatIMManager.sharedInstance.joinGroup(
          groupType: GroupType.Meeting, groupID: groupID!, message: '');
      if (joinSuccess) {
        V2TimConversation conversation = await ChatIMManager.sharedInstance
            .getConversation(groupID: groupID, type: 2);
        Get.toNamed(
          GetRouter.teamDetail,
          parameters: {"teamId": teamID!},
          arguments: {"conversation": conversation},
        );
      } else {
        ToastUtils.showToast("加入小队失败，请稍后再试！");
        await ApiService().exitTeam(teamId: groupID!);
      }
    }
  }
}
