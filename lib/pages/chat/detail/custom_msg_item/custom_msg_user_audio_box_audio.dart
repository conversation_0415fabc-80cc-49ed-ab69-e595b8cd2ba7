import 'dart:convert';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/audio_box_list_item_entity.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/pages/match/assemble/team/match_team_chat_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/svgaplayer_flutter.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

class CustomMsgUserAudioBoxAudio extends StatefulWidget {
  final V2TimMessage message;
  final ChatDetailController? chatDetailController;

  const CustomMsgUserAudioBoxAudio(
      {super.key, required this.message, this.chatDetailController});

  @override
  State<CustomMsgUserAudioBoxAudio> createState() =>
      _CustomMsgUserAudioBoxAudioState();
}

class _CustomMsgUserAudioBoxAudioState extends State<CustomMsgUserAudioBoxAudio>
    with TickerProviderStateMixin {
  late SVGAAnimationController svgaAnimationController;
  late AudioBoxListItemEntity audioItem;

  @override
  void initState() {
    super.initState();

    Map<String, SVGAAnimationController> svgaControllerMap = {};
    if (widget.chatDetailController != null) {
      svgaControllerMap = widget.chatDetailController!.svgaControllerMap;
    } else if (Get.isRegistered<MatchTeamChatController>()) {
      svgaControllerMap = Get.find<MatchTeamChatController>().svgaControllerMap;
    }

    SVGAAnimationController? animationController =
        svgaControllerMap[widget.message.msgID];
    if (animationController != null) {
      svgaAnimationController = animationController;
    } else {
      svgaAnimationController = SVGAAnimationController(vsync: this);
      svgaControllerMap[widget.message.msgID!] = svgaAnimationController;
    }

    setVideoItem(svgaAnimationController);

    String? jsonStr = widget.message.customElem?.data;
    if (jsonStr != null) {
      ChatImCustomMsgEntity msgEntity =
          ChatImCustomMsgEntity.fromJson(jsonDecode(jsonStr));
      AudioBoxListItemEntity audioEntity =
          AudioBoxListItemEntity.fromJson(jsonDecode(msgEntity.data));
      audioItem = audioEntity;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100.w,
      height: 43.h,
      alignment: Alignment.center,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.imagesCustomMsgAudioBoxBg),
        ),
      ),
      child: GestureDetector(
        onTap: () {
          if (widget.chatDetailController != null) {
            widget.chatDetailController!
                .stopCurrentAndPlayNewAudio(message: widget.message);
          } else if (Get.isRegistered<MatchTeamChatController>()) {
            Get.find<MatchTeamChatController>()
                .stopCurrentAndPlayNewAudio(message: widget.message);
          }
          setState(() {});
        },
        child: Container(
          width: 85.w,
          height: 27.h,
          decoration: BoxDecoration(
            color: AppColors.colorFFACE49E,
            borderRadius: BorderRadius.circular(13.5.r),
          ),
          child: Row(
            mainAxisAlignment: widget.message.isSelf == true
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
            children: [
              !(widget.message.isSelf == true)
                  ? Padding(
                      padding: EdgeInsets.only(left: 6.w),
                      child: SizedBox(
                        width: 12.w,
                        height: 16.h,
                        child: Stack(
                          children: [
                            Visibility(
                              visible: !svgaAnimationController.isAnimating,
                              child: ImageUtils.getImage(
                                  Assets.imagesAudioBoxVolume, 12.w, 16.h),
                            ),
                            Visibility(
                              visible: svgaAnimationController.isAnimating,
                              child: SVGAImage(svgaAnimationController),
                            ),
                          ],
                        ),
                      ),
                    )
                  : Container(),
              Padding(
                padding: EdgeInsets.only(left: 10.w, right: 5.w),
                child: Text(
                  "${audioItem.duration}\"",
                  style: TextStyles.normal(14.sp),
                ),
              ),
              widget.message.isSelf == true
                  ? Padding(
                      padding: EdgeInsets.only(right: 6.w),
                      child: SizedBox(
                        width: 12.w,
                        height: 16.h,
                        child: Stack(
                          children: [
                            Visibility(
                              visible: !svgaAnimationController.isAnimating,
                              child: ImageUtils.getImage(
                                  Assets.imagesAudioBoxVolume, 12.w, 16.h),
                            ),
                            Visibility(
                              visible: svgaAnimationController.isAnimating,
                              child: SVGAImage(svgaAnimationController),
                            ),
                          ],
                        ),
                      ),
                    )
                  : Container(),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> setVideoItem(SVGAAnimationController controller) async {
    final videoItem =
        await SVGAParser.shared.decodeFromAssets(Assets.svgaAudioPlaying);
    controller.videoItem = videoItem;
    controller.reset();
  }

  @override
  void dispose() {
    svgaAnimationController.stop();
    super.dispose();
  }
}
