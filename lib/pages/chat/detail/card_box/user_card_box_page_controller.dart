import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';

class UserCardBoxPageController
    extends ListPageController<UserIdCardEntity, UserCardBoxPageController> {
  late String cardType;

  @override
  Future<List<UserIdCardEntity>?> loadData(int page) async {
    List<UserIdCardEntity>? list = await ApiService().getUserCardList(
        cardType: cardType, pageIndex: page, userId: UserService().user!.id!);
    return list;
  }
}
