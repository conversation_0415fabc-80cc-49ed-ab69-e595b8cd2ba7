import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/pages/chat/detail/card_box/user_card_box_page_controller.dart';
import 'package:dada/pages/profile/card/user_card_box_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserCardBoxBottomSheet extends StatefulWidget {
  final Function(UserIdCardEntity cardInfo) onSelectedCard;

  const UserCardBoxBottomSheet({super.key, required this.onSelectedCard});

  @override
  State<UserCardBoxBottomSheet> createState() => _UserCardBoxBottomSheetState();
}

class _UserCardBoxBottomSheetState extends State<UserCardBoxBottomSheet>
    with TickerProviderStateMixin {
  late TabController tabController;
  late PageController pageController;
  final List<String> tabItemTitles = ["身份卡", "晒欧卡", "穿搭卡", "高光卡"];
  RxDouble bottomSheetHeight = (310.h).obs;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: 4, vsync: this);
    pageController = PageController();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      height: 344.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.r),
          topRight: Radius.circular(15.r),
        ),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.colorFFD2F6C0,
            Colors.white,
          ],
          stops: [0, 0.35],
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 15.w),
                child: Text(
                  "卡盒",
                  style: TextStyles.medium(16.sp),
                ),
              ),
              _buildTabBar(),
              _buildPageView(),
            ],
          ),
          _buildCloseBtn(),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 44.h,
      margin: EdgeInsets.only(top: 5.h),
      alignment: Alignment.centerLeft,
      child: TabBar(
        padding: EdgeInsets.only(left: 15.w, top: 0.h),
        tabAlignment: TabAlignment.start,
        controller: tabController,
        tabs: tabItemTitles.map((e) => Tab(text: e)).toList(),
        // tabs: tabItemTitles.map((e) => Tab(text: e)).toList(),
        dividerColor: Colors.transparent,
        isScrollable: true,
        labelPadding: EdgeInsets.only(right: 15.w),
        labelStyle: TextStyle(fontSize: 16.sp),
        labelColor: AppColors.colorFF3D3D3D,
        unselectedLabelColor: AppColors.colorFF666666,
        unselectedLabelStyle: TextStyle(fontSize: 16.sp),
        indicatorWeight: 4.h,
        indicatorPadding:
            EdgeInsets.only(bottom: 15.h, top: 24.h, left: 5.w, right: 5.w),
        indicator: BoxDecoration(
          color: Theme.of(context)
              .bottomNavigationBarTheme
              .selectedLabelStyle
              ?.color,
          borderRadius: BorderRadius.circular(2.r),
        ),
        onTap: (index) {
          pageController.animateToPage(index,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInCubic);
        },
      ),
    );
  }

  Widget _buildPageView() {
    return Expanded(
      child: PageView(
        controller: pageController,
        onPageChanged: (index) {
          tabController.animateTo(index);
        },
        children: tabItemTitles.map(
          (e) {
            int tabIndex = tabItemTitles.indexOf(e);
            String cardType = "${tabIndex + 1}";
            return GetBuilder(
              init: UserCardBoxPageController()..cardType = cardType,
              global: false,
              id: UserCardBoxPageController().refreshId,
              builder: (controller) {
                if (controller.data.isEmpty) {
                  return EmptyWidget();
                }
                return RefreshWidget.build(
                  enablePullDown: false,
                  triggerAxis: Axis.horizontal,
                  refreshController: controller.refreshController,
                  child: ListView.separated(
                    padding: EdgeInsets.only(
                        left: 10.w, bottom: tabIndex == 0 ? 72.h : 0),
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      UserIdCardEntity entity = controller.data[index];
                      return UserCardBoxItemWidget(
                        cardInfo: entity,
                        onTap: () {
                          widget.onSelectedCard.call(entity);
                          Get.back();
                        },
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Container(
                        width: tabIndex == 0 ? 5.w : 8.w,
                      );
                    },
                    itemCount: controller.data.length,
                  ),
                );
              },
            );
          },
        ).toList(),
      ),
    );
  }

  Widget _buildCloseBtn() {
    return Positioned(
      right: 15.w,
      top: 15.w,
      child: GestureDetector(
        onTap: () => Get.back(),
        child: Icon(
          Icons.close,
          color: AppColors.colorFF666666,
          size: 20.w,
        ),
      ),
    );
  }
}
