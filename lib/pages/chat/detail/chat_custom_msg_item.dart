import 'dart:convert';

import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/pages/chat/detail/custom_msg_item/custom_msg_dynamic_post.dart';
import 'package:dada/pages/chat/detail/custom_msg_item/custom_msg_dynamic_resonate_action.dart';
import 'package:dada/pages/chat/detail/custom_msg_item/custom_msg_invite_join_chat_room.dart';
import 'package:dada/pages/chat/detail/custom_msg_item/custom_msg_invite_join_group.dart';
import 'package:dada/pages/chat/detail/custom_msg_item/custom_msg_tips_msg.dart';
import 'package:dada/pages/chat/detail/custom_msg_item/custom_msg_user_audio_box_audio.dart';
import 'package:dada/pages/chat/detail/custom_msg_item/custom_msg_user_card_box_card.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/utils/string_util.dart';
import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

class ChatCustomMsgItem extends StatelessWidget {
  final V2TimMessage message;
  final ChatDetailController? chatDetailController;

  const ChatCustomMsgItem({super.key, required this.message, this.chatDetailController});

  @override
  Widget build(BuildContext context) {
    String? jsonStr = message.customElem?.data;
    if (jsonStr != null) {
      if (StringUtils.isJsonString(jsonStr)) {
        ChatImCustomMsgEntity customMsgEntity =
            ChatImCustomMsgEntity.fromJson(jsonDecode(jsonStr));
        String? type = customMsgEntity.type;
        switch (type) {
          case ChatImCustomMsgType.InviteToJoinTeam:
          case ChatImCustomMsgType.InviteToJoinChatGroup:
            {
              Map<String, dynamic> data = customMsgEntity.data;
              String title = data["title"];
              String text = data["text"];
              String groupID = data["groupID"];
              String? teamID = data["teamID"];
              String invitorID = data["invitorID"];
              String? faceUrl = data["faceUrl"];
              String? groupName = data["groupName"];
              int? memberCount = data["memberCount"];
              int? lockState = data["lockState"];

              return CustomMsgInviteJoinGroup(
                isSelf: message.isSelf,
                type: type,
                title: title,
                text: text,
                groupID: groupID,
                teamID: teamID,
                invitorID: invitorID,
                faceUrl: faceUrl,
                groupName: groupName,
                lockState: lockState,
                memberCount: memberCount,
              );
            }
          case ChatImCustomMsgType.UserCardBoxCardMsg:
            {
              Map<String, dynamic> json = jsonDecode(customMsgEntity.data);
              UserIdCardEntity cardInfo = UserIdCardEntity.fromJson(json);
              return CustomMsgUserCardBoxCard(
                cardInfo: cardInfo,
                userID: message.sender,
                userNickname: message.nickName,
                userAvatar: message.faceUrl,
              );
            }
          case ChatImCustomMsgType.UserAudioBoxAudioMsg:
            {
              return CustomMsgUserAudioBoxAudio(
                message: message,
                chatDetailController: chatDetailController,
              );
            }
          case ChatImCustomMsgType.ShareRoomToChatMsg:
            {
              Map<String, dynamic> json = customMsgEntity.data;
              String roomId = json["roomId"];
              String ownerAvatar = json["roomOwnerAvatar"];
              String ownerNickname = json["roomOwner"];
              return CustomMsgInviteJoinChatRoom(
                  roomId: roomId,
                  ownerAvatar: ownerAvatar,
                  ownerNickname: ownerNickname);
            }
          case ChatImCustomMsgType.DynamicResonateLightMsg:
          case ChatImCustomMsgType.DynamicResonateScratchMsg:
            {
              Map<String, dynamic> json = customMsgEntity.data;
              Map<String, dynamic> postEntityJson =
                  jsonDecode(json["postEntity"]);
              PostEntity postEntity = PostEntity.fromJson(postEntityJson);
              bool isScratch = json["isScratch"];
              return CustomMsgDynamicResonateAction(
                  postId: postEntity.postId ?? "",
                  isScratch: isScratch,
                  text: postEntity.content ?? "",
                  imageUrls: postEntity.imgUrls ?? [],
                  isSelf: message.isSelf == true,
                  postType: postEntity.postType ?? 1);
            }
          case ChatImCustomMsgType.ShareDynamicPostMsg:
            {
              Map<String, dynamic> json = jsonDecode(customMsgEntity.data);
              PostEntity postEntity = PostEntity.fromJson(json);
              return CustomMsgDynamicPost(
                  avatar: postEntity.avatar!,
                  nickname: postEntity.nickname!,
                  postId: postEntity.postId!,
                  text: postEntity.content,
                  imageUrls: postEntity.imgUrls,
                  isSelf: message.isSelf == true,
                  postType: postEntity.postType!);
            }
          case ChatImCustomMsgType.ChatGreetTipsMsg:
            {
              Map<String, dynamic> json = jsonDecode(customMsgEntity.data);
              String text = json["text"];
              return CustomMsgTipsMsg(text: text);
            }
        }
      }
    }
    return Container();
  }
}
