import 'package:cached_network_image/cached_network_image.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_audio_msg_tooltip_item.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/dress_utils.dart';
import 'package:dada/utils/emoji_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKItMessageList/tim_uikit_chat_history_message_list_config.dart';

class WorldChatPage extends StatefulWidget {
  const WorldChatPage({super.key});

  @override
  State<WorldChatPage> createState() => _WorldChatPageState();
}

class _WorldChatPageState extends State<WorldChatPage> {
  late V2TimConversation conversation;
  TIMUIKitChatController timUIKitChatController = TIMUIKitChatController();
  String? groupNotice;
  DateTime? lastSendMsgTime;

  @override
  void initState() {
    super.initState();

    conversation = Get.arguments;
    groupNotice = Get.parameters['groupNotice'];

    EmojiUtils.init();

    String? limit = UserService().systemConfig?.worldChatLimit;
    int chatLimit = int.parse(limit ?? "5");
    timUIKitChatController.checkCanSendMsg = (msgType, {text}) async {
      if (lastSendMsgTime != null &&
          DateTime.now().difference(lastSendMsgTime!).inSeconds < chatLimit) {
        ToastUtils.showToast("发言间隔${chatLimit}s哦~");
        return false;
      }
      lastSendMsgTime = DateTime.now();
      return true;
    };
  }

  @override
  Widget build(BuildContext context) {
    return TIMUIKitChat(
      conversation: conversation,
      controller: timUIKitChatController,
      extraTipsActionItemBuilder: (message, closeTip, [key, context]) {
        return Container();
      },
      mainHistoryListConfig: TIMUIKitHistoryMessageListConfig(
        padding: EdgeInsets.only(bottom: 12.h),
      ),
      customAppBar: Column(
        children: [
          CustomAppBar(
            title: conversation.showName,
            backAction: () {
              quitGroup();
              Get.back();
            },
          ),
          _buildGroupNoticeWidget(),
        ],
      ),
      userAvatarBuilder: (BuildContext context, V2TimMessage message) {
        String? avatarFrameUrl =
            DressUtils().getMessageUserDressUrl(message, DressUpType.avatar);
        return AvatarWidget(
          size: 50,
          url: message.faceUrl ?? "",
          dressUp: avatarFrameUrl,
          showPlaceholder: true,
          showDressUp: true,
        );
      },
      config: TIMUIKitChatConfig(
        isShowGroupReadingStatus: false,
        isUseMessageReaction: false,
        isShowReadingStatus: false,
        // isAllowEmojiPanel: false,
        isAllowSoundMessage: true,
        stickerPanelConfig: StickerPanelConfig(
          useQQStickerPackage: false,
          useTencentCloudChatStickerPackage: false,
          customStickerPackages:
              EmojiUtils().customEmojiStickerPackageList ?? [],
        ),
      ),
      customEmojiStickerList: EmojiUtils().customEmojiFaceDataList ?? [],
      morePanelConfig: MorePanelConfig(showFilePickAction: false),
      onTapAvatar: (userId, tapDetails) {
        Get.toNamed(GetRouter.userProfile, parameters: {"userId": userId});
      },
      textFieldHintText: "请输入文字",
      toolTipsConfig: ToolTipsConfig(
        showForwardMessage: false,
        showMultipleChoiceMessage: false,
        showRecallMessage: false,
        showTranslation: false,
      ),
      messageItemBuilder: MessageItemBuilder(groupTipsMessageItemBuilder:
          (V2TimMessage message, bool isShowJump, VoidCallback clearJump) {
        return Container();
      }),
      messageItemThemeData: MessageThemeData(messageTextColor: (message) {
        String? colorStr = DressUtils().getMessageUserDressUrl(
            message, DressUpType.chatBox,
            propertyType: 1);
        if (colorStr?.isNotEmpty == true) {
          colorStr = "FF$colorStr";
          Color color = Color(int.parse(colorStr, radix: 16));
          return color;
        }
        return Colors.black;
      }, messageBubbleBuilder: (message, child) {
        ///获取气泡框Url
        String? chatBubbleDressUrl =
            DressUtils().getMessageUserDressUrl(message, DressUpType.chatBox);
        if (!(chatBubbleDressUrl?.isNotEmpty == true)) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            constraints: const BoxConstraints(
              minWidth: 40,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: child,
          );
        }

        ///获取气泡框可拉伸区域
        String? chatBubbleSliceStr = DressUtils().getMessageUserDressUrl(
            message, DressUpType.chatBox,
            propertyType: 2);
        Rect? centerSlice = StringUtils.stringToRect(chatBubbleSliceStr ?? "");
        if (centerSlice != null) {
          centerSlice = Rect.fromLTRB(centerSlice.left / 3, centerSlice.top / 3,
              centerSlice.right / 3, centerSlice.bottom / 3);
        }
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          constraints: const BoxConstraints(
            minWidth: 40,
          ),
          decoration: BoxDecoration(
            image: DecorationImage(
              // centerSlice:
              //     centerSlice ?? const Rect.fromLTRB(18.5, 10, 20, 15),
              centerSlice: const Rect.fromLTRB(18.5, 10, 20, 15),
              image: CachedNetworkImageProvider(chatBubbleDressUrl!),
              scale: 3,
              fit: BoxFit.fill,
            ),
          ),
          child: child,
        );
      }),
    );
  }

  Widget _buildGroupNoticeWidget() {
    return Container(
      height: 85.h,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.colorFFE0F9D3, AppColors.colorFFF7FDF3],
          stops: [0, 0.2],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 10.w, top: 5.h),
            child: Row(
              children: [
                ImageUtils.getImage(
                  Assets.imagesWorldChatGroupNoticeIcon,
                  23.w,
                  25.h,
                ),
                Padding(
                  padding: EdgeInsets.only(left: 3.w),
                  child: Text(
                    "聊天室公告",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.colorFF23AF28,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 13.w, top: 5.h, right: 10.w),
            child: Text(
              groupNotice ?? "",
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.normal(14.sp),
            ),
          ),
        ],
      ),
    );
  }

  Future<bool> sendCustomMeg(dynamic data, String type,
      {String? userID, String? groupID}) async {
    bool success = await ChatIMManager.sharedInstance.sendCustomMessage(
        type: type, data: data, receiver: "", groupID: conversation.groupID!);
    if (success) {
      refreshHistoryList();
      return true;
    }
    return false;
  }

  void refreshHistoryList() {
    timUIKitChatController.refreshCurrentHistoryList();
  }

  void quitGroup() {
    ChatIMManager.sharedInstance.quitGroup(groupID: conversation.groupID!);
  }
}
