import 'package:dada/configs/app_config.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';

class ChatConversationListController extends GetxController {
  List<V2TimConversation?>? conversationList;
  int nextSeq = 0;

  @override
  void onReady() async {
    super.onReady();

    ToastUtils.showLoading();
    await refreshData();
    ToastUtils.hideLoading();
  }

  Future<void> refreshData() async {
    conversationList = await loadData(nexSeq: 0);
    update();
  }

  Future<List<V2TimConversation?>?> loadData({required int nexSeq}) async {
    List<V2TimConversation?>? list;
    V2TimValueCallback<V2TimConversationResult> result = await ChatIMManager
        .sharedInstance
        .getConversationList(nextSeq: "$nextSeq", count: 100);
    if (result.code == 0) {
      if (result.data != null) {
        if (!result.data!.isFinished!) {
          list = <V2TimConversation?>[];
          List<V2TimConversation?>? subList = await loadData(nexSeq: nexSeq++);
          if (subList != null) {
            list.addAll(subList);
          }
        } else {
          list = result.data!.conversationList
              ?.where((value) =>
                  value?.groupType != GroupType.Meeting &&
                  value?.userID != AppConfig.officialUser1)
              .toList();
        }
      }
    }
    return list;
  }

  void addNewConversation(V2TimConversation conversation) {
    if (conversationList?.isNotEmpty == true) {
      conversationList!.insert(0, conversation);
      update();
    }
  }

  void deleteConversation(String conversationID) async {
    bool success =
        await ChatIMManager.sharedInstance.deleteConversation(conversationID);
    if (success) {
      refreshData();
    }
  }
}
