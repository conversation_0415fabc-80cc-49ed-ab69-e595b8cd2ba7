import 'dart:convert';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/pages/chat/conversation/chat_conversation_list_controller.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_custom_elem.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

import 'package:dada/generated/assets.dart';

class ConversationListItemWidget extends StatefulWidget {
  final V2TimConversation conversation;

  const ConversationListItemWidget({super.key, required this.conversation});

  @override
  State<ConversationListItemWidget> createState() =>
      _ConversationListItemWidgetState();
}

class _ConversationListItemWidgetState
    extends State<ConversationListItemWidget> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // if (!UserService().checkIsMonthCardUser()) {
        //   ToastUtils.showBottomDialog(const MonthCardExpiredLimitChatDialog());
        // } else {
        //   Get.toNamed(GetRouter.chatDetail, arguments: widget.conversation);
        // }
        Get.toNamed(GetRouter.chatDetail, arguments: widget.conversation);
      },
      onLongPress: () {
        ToastUtils.showBottomSheet(
          ["删除会话"],
          onTap: (index) async {
            Get.find<ChatConversationListController>()
                .deleteConversation(widget.conversation.conversationID);
          },
        );
      },
      child: SizedBox(
        height: 75.h,
        child: Stack(
          alignment: Alignment.centerLeft,
          children: [
            Row(
              children: [
                ///头像
                GestureDetector(
                  onTap: () {
                    if (widget.conversation.userID != null) {
                      Get.toNamed(GetRouter.userProfile,
                          parameters: {"userId": widget.conversation.userID!});
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.only(left: 15.w),
                    child: ClipOval(
                      child: ImageUtils.getImage(
                        widget.conversation.faceUrl?.isNotEmpty == true
                            ? widget.conversation.faceUrl!
                            : Assets.imagesGroupChatDefaultAvatar2,
                        45.w,
                        45.w,
                        fit: BoxFit.cover,
                        showPlaceholder: true,
                      ),
                    ),
                  ),
                ),

                SizedBox(
                  width: 5.w,
                ),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ///昵称
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: 275.w,
                      ),
                      child: Text(
                        conversationShowName(),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyles.normal(16.sp),
                      ),
                    ),

                    ///最后一条消息
                    Visibility(
                      visible: showLastMsgStr().isNotEmpty,
                      child: Padding(
                        padding: EdgeInsets.only(top: 5.h),
                        child: Container(
                          constraints: BoxConstraints(
                              maxWidth: widget.conversation.unreadCount != null
                                  ? 265.w
                                  : 250.w),
                          child: Text(
                            showLastMsgStr(),
                            overflow: TextOverflow.ellipsis,
                            style: TextStyles.common(
                                14.sp, AppColors.colorFF666666),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Positioned(
              right: 15.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    TimeUtils.formatConversationDate(
                        widget.conversation.lastMessage?.timestamp ?? 0),
                    style: TextStyles.common(
                      12.sp,
                      AppColors.colorFF666666,
                    ),
                  ),
                  Visibility(
                    visible: widget.conversation.unreadCount != null &&
                        widget.conversation.unreadCount! > 0,
                    child: Padding(
                      padding: EdgeInsets.only(top: 6.h),
                      child: BadgeWidget(
                        visible: widget.conversation.unreadCount != null &&
                            widget.conversation.unreadCount! > 0,
                        text: widget.conversation.recvOpt != 0
                            ? null
                            : "${widget.conversation.unreadCount}",
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String showLastMsgStr() {
    String content = "";
    V2TimMessage? lastMsg = widget.conversation.lastMessage;
    if (lastMsg != null) {
      int msgType = lastMsg.elemType;
      switch (msgType) {
        case MessageElemType.V2TIM_ELEM_TYPE_TEXT:
          content = lastMsg.textElem?.text ?? "";
          break;
        case MessageElemType.V2TIM_ELEM_TYPE_IMAGE:
          content = "[图片]";
          break;
        case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
          content = "[语音]";
          break;
        case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
          content = "[视频]";
          break;
        case MessageElemType.V2TIM_ELEM_TYPE_FACE:
          content = "[表情]";
          break;
        case MessageElemType.V2TIM_ELEM_TYPE_CUSTOM:
          V2TimCustomElem? customElem = lastMsg.customElem;
          if (customElem != null && customElem.data != null) {
            bool isJson = StringUtils.isJsonString(customElem.data!);
            if (isJson) {
              dynamic json = jsonDecode(customElem.data!);
              ChatImCustomMsgEntity? customElemData;
              if (json is Map<String, dynamic>) {
                customElemData = ChatImCustomMsgEntity.fromJson(json);
              }
              if (customElemData != null) {
                switch (customElemData.type) {
                  case ChatImCustomMsgType.InviteToJoinTeam:
                    content = customElemData.data["title"] ?? "";
                    break;
                  default:
                    content = "[自定义消息]";
                    break;
                }
              }
            }
          }
          break;
      }
    }
    return content;
  }

  String conversationShowName() {
    String showName = "";
    if (widget.conversation.type == 2) {
      showName = widget.conversation.showName?.isNotEmpty == true
          ? "【群聊】${widget.conversation.showName}"
          : "";
    } else {
      showName = widget.conversation.showName ?? "";
    }
    return showName;
  }
}
