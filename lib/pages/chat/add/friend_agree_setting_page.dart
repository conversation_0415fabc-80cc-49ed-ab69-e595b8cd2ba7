import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/receive_message_opt_enum.dart';

class FriendAgreeSettingPage extends StatefulWidget {
  final FriendUserInfoEntity friendUserInfo;

  const FriendAgreeSettingPage({super.key, required this.friendUserInfo});

  @override
  State<FriendAgreeSettingPage> createState() => _FriendAgreeSettingPageState();
}

class _FriendAgreeSettingPageState extends State<FriendAgreeSettingPage> {
  TextEditingController editingController = TextEditingController();
  RxBool lookMeState = true.obs;
  RxBool lookFriendState = true.obs;
  RxBool messageDisturb = false.obs;
  RxString selectedGroupID = "".obs;
  RxString selectedGroupName = "".obs;

  @override
  void initState() {
    super.initState();

    editingController.text = "${widget.friendUserInfo.nickname}";

    selectedGroupID.value = "0";
    selectedGroupName.value = "我的好友";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "好友设置",
      ),
      body: Column(
        children: [
          _buildUserInfoWidget(),
          _buildRemarkWidget(),
          _buildFriendGroupWidget(),
          _buildLimitWidget(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildUserInfoWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 10.h),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w),
            child: ClipOval(
              child: ImageUtils.getImage(
                widget.friendUserInfo.avatar ?? "",
                40.w,
                40.w,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Container(
            constraints: BoxConstraints(
              maxWidth: 305.w,
            ),
            margin: EdgeInsets.only(left: 5.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.friendUserInfo.nickname ?? "",
                  overflow: TextOverflow.ellipsis,
                  style: TextStyles.normal(16.sp),
                ),
                Visibility(
                  visible: widget.friendUserInfo.userDescribes != null,
                  child: Padding(
                    padding: EdgeInsets.only(top: 5.h),
                    child: Text(
                      widget.friendUserInfo.userDescribes ?? "",
                      style: TextStyles.common(14.sp, AppColors.colorFF666666),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemarkWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 34.h, left: 15.w, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "备注",
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: Container(
              height: 40.h,
              decoration: BoxDecoration(
                color: AppColors.colorFFF5F5F5,
                borderRadius: BorderRadius.circular(5.r),
              ),
              child: CustomTextField.build(
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 10.w,
                ),
                showSuffixIcon: true,
                controller: editingController,
                style: TextStyles.common(16.sp, AppColors.colorFF666666),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFriendGroupWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 25.h, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "分组设置",
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFF5F5F5,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: InkWell(
              onTap: () {
                Get.toNamed(GetRouter.friendCustomGroupManager, parameters: {
                  "friendUserId": widget.friendUserInfo.userId!,
                  "friendGroupId": selectedGroupID.value,
                  "isSelectGroup": "1",
                })?.then((result) {
                  if (result != null) {
                    selectedGroupID.value = result["groupID"];
                    selectedGroupName.value = result["groupName"];
                  }
                });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10.w),
                    child: Obx(
                      () => Text(
                        selectedGroupName.value,
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF666666),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 10.w),
                    child: ImageUtils.getImage(
                        Assets.imagesCommonListMore, 8.w, 14.h),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLimitWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 20.h, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "权限设置",
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: AppColors.colorFFF5F5F5,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: 0.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 10.w),
                        child: Text(
                          "不让他看我的搭圈",
                          style:
                              TextStyles.common(16.sp, AppColors.colorFF666666),
                        ),
                      ),
                      Obx(
                        () => _buildSwitch(
                          open: !lookMeState.value,
                          onChanged: (value) {
                            lookMeState.value = !lookMeState.value;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 0.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 10.w),
                        child: Text(
                          "不看他",
                          style:
                              TextStyles.common(16.sp, AppColors.colorFF666666),
                        ),
                      ),
                      Obx(
                        () => _buildSwitch(
                          open: !lookFriendState.value,
                          onChanged: (value) {
                            lookFriendState.value = !lookFriendState.value;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 0.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 10.w),
                        child: Text(
                          "好友消息免打扰",
                          style:
                              TextStyles.common(16.sp, AppColors.colorFF666666),
                        ),
                      ),
                      Obx(
                        () => _buildSwitch(
                          open: messageDisturb.value,
                          onChanged: (value) {
                            messageDisturb.value = !messageDisturb.value;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitch({required bool open, required Function(bool) onChanged}) {
    return Switch(
      value: open,
      onChanged: onChanged,
      inactiveThumbColor: Colors.white,
      inactiveTrackColor: AppColors.colorFFD3D3D3,
      activeColor: Colors.white,
      activeTrackColor: AppColors.colorFF65D06A,
      trackOutlineColor: WidgetStateProperty.all<Color>(Colors.transparent),
      trackOutlineWidth: WidgetStateProperty.all<double>(0),
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      topMargin: 30.h,
      title: "完成",
      onTap: () {
        updateUserInfo(
          remark: editingController.text.isEmpty == true
              ? null
              : editingController.text,
          groupId: selectedGroupID.value,
          lookMeState: lookMeState.value == true ? 1 : 0,
          lookFriendState: lookFriendState.value == true ? 1 : 0,
          disturb: messageDisturb.value == true ? 1 : 0,
        );
      },
    );
  }

  Future<void> updateUserInfo(
      {String? remark,
      String? groupId,
      int? lookMeState,
      int? lookFriendState,
      int? disturb}) async {
    bool success = await ApiService().updateFriendUserInfo(
      widget.friendUserInfo.userId!,
      friendRemark: remark,
      friendGroupId: groupId,
      lookMeState: lookMeState,
      lookFriendState: lookFriendState,
      disturbed: disturb,
    );
    if (messageDisturb.value == true && success) {
      ChatIMManager.sharedInstance.setC2CReceiveMessageOpt(
          userId: widget.friendUserInfo.userId!,
          opt: ReceiveMsgOptEnum.V2TIM_RECEIVE_NOT_NOTIFY_MESSAGE);
    }
    Get.back();
  }
}
