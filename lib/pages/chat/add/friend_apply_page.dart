import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/pages/chat/add/friend_agree_setting_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class FriendApplyPage extends StatelessWidget {
  final FriendUserInfoEntity friendUserInfo;

  const FriendApplyPage({super.key, required this.friendUserInfo});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "好友申请",
      ),
      body: Column(
        children: [
          ///头像、名称、签名
          _buildUserInfoWidget(),
          _buildUserApplyMsgWidget(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildUserInfoWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 10.h),
      child: InkWell(
        onTap: () {
          Get.toNamed(GetRouter.userProfile,
              parameters: {"userId": friendUserInfo.userId!});
        },
        child: Row(
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 15.w),
                  child: ClipOval(
                    child: ImageUtils.getImage(
                      friendUserInfo.avatar ?? "",
                      40.w,
                      40.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Container(
                  constraints: BoxConstraints(
                    maxWidth: 290.w,
                  ),
                  margin: EdgeInsets.only(left: 5.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        friendUserInfo.nickname ?? "",
                        overflow: TextOverflow.ellipsis,
                        style: TextStyles.normal(16.sp),
                      ),
                      Visibility(
                        visible: friendUserInfo.userDescribes != null,
                        child: Padding(
                          padding: EdgeInsets.only(top: 5.h),
                          child: Text(
                            friendUserInfo.userDescribes ?? "",
                            style: TextStyles.common(
                                14.sp, AppColors.colorFF666666),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Spacer(),
            Padding(
              padding: EdgeInsets.only(right: 15.w),
              child:
                  ImageUtils.getImage(Assets.imagesCommonListMore, 8.w, 14.h),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserApplyMsgWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 30.h, left: 15.w, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "对方留言",
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          ),
          Container(
            margin: EdgeInsets.only(top: 10.h),
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            width: 345.w,
            height: 80.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFF5F5F5,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: Text(
              friendUserInfo.applyMsg ?? "",
              style: TextStyles.common(16.sp, AppColors.colorFF666666),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      topMargin: 25.h,
      title: "同意",
      onTap: () async {
        bool success = await ApiService().agreeFriendApply(
          userFriendId: friendUserInfo.userId!,
          friendRemark: friendUserInfo.nickname!,
          friendGroupId: "0",
          friendGroupName: "我的好友",
          lookMeState: 1,
          lookFriendState: 1,
          disturbed: 0,
        );
        if (success) {
          ChatIMManager.sharedInstance.sendTextMessage(
              text: "我通过了你的朋友验证请求，现在我们可以开始聊天了",
              toUserID: friendUserInfo.userId!);
          Get.off(
            () => FriendAgreeSettingPage(friendUserInfo: friendUserInfo),
          );
        }
      },
    );
  }
}
