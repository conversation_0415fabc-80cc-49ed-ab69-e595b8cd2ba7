import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class AddFriendSendPage extends StatefulWidget {
  const AddFriendSendPage({super.key});

  @override
  State<AddFriendSendPage> createState() => _AddFriendSendPageState();
}

class _AddFriendSendPageState extends State<AddFriendSendPage> {
  TextEditingController editingController1 = TextEditingController();
  TextEditingController editingController2 = TextEditingController();

  RxString selectedGroupName = "".obs;
  RxString selectedGroupID = "".obs;
  RxBool lookMeDaquan = true.obs;

  String? _userFriendId;
  String? _userAvatar;
  String? _userNickname;

  @override
  void initState() {
    super.initState();

    _userFriendId = Get.parameters["userId"];
    _userNickname = Get.parameters["nickname"];
    _userAvatar = Get.parameters["avatar"];

    editingController1.text = "我是${UserService().user?.nickname ?? ""}";

    selectedGroupID.value = "0";

    selectedGroupName.value = "我的好友";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "添加好友",
      ),
      body: Column(
        children: [
          _buildAvatarWidget(),
          _buildInputApplyDescWidget(),
          _buildInputRemarkWidget(),
          _buildFriendGroupWidget(),
          _buildLookMeDaquanLimitWidget(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildAvatarWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 10.h),
      child: Row(
        children: [
          ClipOval(
            child: ImageUtils.getImage(_userAvatar ?? "", 40.w, 40.w,
                fit: BoxFit.cover),
          ),
          Padding(
            padding: EdgeInsets.only(left: 5.w),
            child: Text(
              _userNickname ?? "",
              style: TextStyles.normal(16.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputApplyDescWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 34.h, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "填写验证信息",
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFF5F5F5,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: CustomTextField.build(
              style: TextStyles.common(16.sp, AppColors.colorFF666666),
              controller: editingController1,
              hintText: "请输入验证信息",
              contentPadding: EdgeInsets.only(left: 10.w),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputRemarkWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 20.h, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "设置对方备注",
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFF5F5F5,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: CustomTextField.build(
              style: TextStyles.common(16.sp, AppColors.colorFF666666),
              controller: editingController2,
              hintText: "输入备注",
              contentPadding: EdgeInsets.only(left: 10.w),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFriendGroupWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 25.h, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "分组设置",
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFF5F5F5,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: InkWell(
              onTap: () {
                Get.toNamed(GetRouter.friendCustomGroupManager, parameters: {
                  "friendUserId": _userFriendId!,
                  "friendGroupId": selectedGroupID.value,
                  "isSelectGroup": "1",
                })?.then((result) {
                  if (result != null) {
                    selectedGroupID.value = result["groupID"];
                    selectedGroupName.value = result["groupName"];
                  }
                });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10.w),
                    child: Obx(
                      () => Text(
                        selectedGroupName.value,
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF666666),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 10.w),
                    child: ImageUtils.getImage(
                        Assets.imagesCommonListMore, 8.w, 14.h),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLookMeDaquanLimitWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 20.h, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "权限设置",
            style: TextStyles.common(14.sp, AppColors.colorFF666666),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFF5F5F5,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 10.w),
                  child: Text(
                    "不让他看我的搭圈",
                    style: TextStyles.common(16.sp, AppColors.colorFF666666),
                  ),
                ),
                Obx(
                    () => Padding(
                      padding: EdgeInsets.only(right: 10.w),
                      child: Switch(
                        value: !lookMeDaquan.value,
                        onChanged: (value) {
                          lookMeDaquan.value = !lookMeDaquan.value;
                        },
                        inactiveThumbColor: Colors.white,
                        inactiveTrackColor: AppColors.colorFFD3D3D3,
                        activeColor: Colors.white,
                        activeTrackColor: AppColors.colorFF65D06A,
                        trackOutlineColor:
                        WidgetStateProperty.all<Color>(Colors.transparent),
                        trackOutlineWidth: WidgetStateProperty.all<double>(0),
                      ),
                    ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      topMargin: 30.h,
      title: "发送",
      onTap: () {
        sendAddFriendRequest();
      },
    );
  }

  void sendAddFriendRequest() async {
    if (_userFriendId != null) {
      bool success = await ApiService().sendAddFriendRequest(
        userFriendId: _userFriendId!,
        applyMsg: editingController1.text,
        friendType: 0,
        friendRemark: editingController2.text,
        friendGroupId: selectedGroupID.value,
        friendGroupName: selectedGroupName.value,
        lookMeState: lookMeDaquan.value == true ? 1 : 0,
      );
      if (success) {
        Get.back(result: 1);
      }
    }
  }
}
