import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/common_setting_list_item_widget.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/setting/group/group_chat_setting_controller.dart';
import 'package:dada/pages/chat/setting/manager/group_manager_list_page.dart';
import 'package:dada/pages/match/assemble/team/invite/invite_selected_user_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class GroupManageSettingPage extends StatelessWidget {
  const GroupManageSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "群管理",
      ),
      body: Column(
        children: [
          CommonSettingListItemWidget(
            title: "群主管理权转让",
            height: 60.h,
            subTitle: "",
            onTap: () {
              List<UserInfoEntity> userList =
                  Get.find<GroupChatSettingController>()
                      .transferMemberListToFriendList();
              if (userList.isNotEmpty) {
                Get.to(
                  () => InviteSelectedUserPage(
                    userList: userList,
                    groupID: Get.find<GroupChatSettingController>().groupID,
                    isSingleSelected: true,
                    isDeleteMember: false,
                    isTransferGroupOwner: true,
                    callback: (List<String> userIds) {},
                  ),
                )?.then((value) {
                  if (value != null) {
                    Get.find<GroupChatSettingController>().loadData();
                    Get.back();
                  }
                });
              }
            },
          ),
          CommonSettingListItemWidget(
            title: "群管理员",
            height: 60.h,
            subTitle: "",
            onTap: () {
              Get.to(() => const GroupManagerListPage());
            },
          ),
        ],
      ),
    );
  }
}
