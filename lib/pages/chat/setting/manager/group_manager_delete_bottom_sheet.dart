import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class GroupManagerDeleteBottomSheet extends StatelessWidget {
  final String title;
  final List<String> itemTitles;
  final Function(int) onSelectedIndex;
  final int? destructiveIndex;

  const GroupManagerDeleteBottomSheet(
      {super.key,
      required this.title,
      required this.itemTitles,
      required this.onSelectedIndex,
      this.destructiveIndex});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      constraints: BoxConstraints(
        minHeight: 157.h + ScreenUtil().bottomBarHeight,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.r),
          topRight: Radius.circular(15.r),
        ),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.colorFFD2F6C0,
            Colors.white,
          ],
          stops: [0, 0.25],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 15.h, bottom: 12.h),
            child: Text(
              title,
              style: TextStyles.common(14.sp, AppColors.colorFF666666),
            ),
          ),
          Container(
            height: 0.5,
            color: AppColors.colorFFE5E5E5,
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: itemTitles.map((e) {
              int index = itemTitles.indexOf(e);
              return InkWell(
                onTap: () {
                  Get.back();
                  onSelectedIndex(index);
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 50.h,
                  decoration: BoxDecoration(
                    border: index == itemTitles.length - 1
                        ? null
                        : const Border(
                            bottom: BorderSide(
                              color: AppColors.colorFFE5E5E5,
                              width: 1,
                            ),
                          ),
                  ),
                  child: Text(
                    e,
                    style: TextStyles.common(
                        16.sp,
                        destructiveIndex == index
                            ? AppColors.colorFFF41D1D
                            : AppColors.colorFF3D3D3D),
                  ),
                ),
              );
            }).toList(),
          ),
          Container(
            height: 10.h,
            color: AppColors.colorFFE5E5E5,
          ),
          Container(
            height: 50.h + ScreenUtil().bottomBarHeight,
            padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
            child: InkWell(
              onTap: () => Get.back(),
              child: Container(
                alignment: Alignment.center,
                height: 50.h,
                child: Text(
                  "取消",
                  style: TextStyles.common(16.sp, AppColors.colorFF999999),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
