import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/setting/group/group_chat_setting_controller.dart';
import 'package:dada/pages/chat/setting/manager/group_manager_delete_bottom_sheet.dart';
import 'package:dada/pages/match/assemble/team/invite/invite_selected_user_page.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_role_enum.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';

class GroupManagerListPage extends StatefulWidget {
  const GroupManagerListPage({super.key});

  @override
  State<GroupManagerListPage> createState() => _GroupManagerListPageState();
}

class _GroupManagerListPageState extends State<GroupManagerListPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "群管理",
      ),
      body: Column(
        children: [
          _buildManagerDescriptionWidget(),
          _buildManagerListWidget(),
        ],
      ),
    );
  }

  Widget _buildManagerDescriptionWidget() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 21.h),
              child: Text(
                "群管理员",
                style: TextStyles.normal(18.sp),
              ),
            ),
          ],
        ),
        _buildDescriptionLine("管理员可协助群主管理群聊，拥有发布群公告、移除群成员等能力。"),
        _buildDescriptionLine("只有群主具备设置管理员、解散群聊的能力。"),
        _buildDescriptionLine("最多可设置3个管理员。"),
      ],
    );
  }

  Widget _buildDescriptionLine(String text) {
    return Padding(
      padding: EdgeInsets.only(top: 10.h, left: 41.w, right: 41.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 5.w,
            height: 5.w,
            margin: EdgeInsets.only(top: 10.h, right: 8.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2.5.r),
              color: AppColors.colorFF3D3D3D,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyles.normal(16.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagerListWidget() {
    List<V2TimGroupMemberFullInfo?>? managerList =
        Get.find<GroupChatSettingController>().managerList;
    List<V2TimGroupMemberFullInfo?>? list = [];
    if (managerList != null) {
      list.addAll(managerList);
    }
    if (list.length < 3) {
      list.add(V2TimGroupMemberFullInfo(userID: "+"));
    }

    return Expanded(
      child: ListView.separated(
        shrinkWrap: false,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 37.5.w, vertical: 60.h),
        itemBuilder: (context, index) {
          V2TimGroupMemberFullInfo? managerInfo = list[index];
          return _buildManagerListItem(managerInfo, index);
        },
        separatorBuilder: (context, index) {
          return Container(
            height: 1.h,
            color: AppColors.colorFFE5E5E5,
          );
        },
        itemCount: list.length,
      ),
    );
  }

  Widget _buildManagerListItem(
      V2TimGroupMemberFullInfo? managerInfo, int index) {
    if (managerInfo == null) {
      return Container();
    }
    if (managerInfo.userID == "+") {
      return GestureDetector(
        onTap: () {
          List<UserInfoEntity> memberList =
              Get.find<GroupChatSettingController>()
                  .transferMemberListToFriendList();
          memberList = memberListFilterManager(memberList);
          Get.to(
            () => InviteSelectedUserPage(
              userList: memberList,
              isSingleSelected: false,
              callback: (userIds) {
                setGroupMemberRole(
                  userIDs: userIds,
                  role: GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_ROLE_ADMIN,
                );
              },
            ),
          );
        },
        child: Container(
          margin: EdgeInsets.only(top: 30.h),
          height: 30.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_circle_outline,
                size: 20.w,
                color: AppColors.colorFF3D3D3D,
              ),
              SizedBox(
                width: 8.w,
              ),
              Text(
                "添加成员",
                style: TextStyles.normal(16.sp),
              ),
            ],
          ),
        ),
      );
    }
    return SizedBox(
      height: 60.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              ClipOval(
                child: ImageUtils.getImage(
                    managerInfo.faceUrl ?? "", 40.w, 40.w,
                    fit: BoxFit.cover),
              ),
              SizedBox(
                width: 5.w,
              ),
              Text(
                managerInfo.nickName ?? "",
                style: TextStyles.normal(16.sp),
              ),
            ],
          ),
          GestureDetector(
            onTap: () async {
              Get.bottomSheet(
                GroupManagerDeleteBottomSheet(
                  title: "移除后，${managerInfo.nickName!}将无法管理群聊",
                  itemTitles: const ["移除管理权限"],
                  destructiveIndex: 0,
                  onSelectedIndex: (index) {
                    if (index == 0) {
                      setGroupMemberRole(
                        userIDs: [managerInfo.userID],
                        isDelete: true,
                        role: GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_ROLE_MEMBER,
                        index: index,
                      );
                    }
                  },
                ),
              );
            },
            child: Text(
              "移除",
              style: TextStyles.common(16.sp, AppColors.colorFFF41D1D),
            ),
          ),
        ],
      ),
    );
  }

  List<UserInfoEntity> memberListFilterManager(List<UserInfoEntity> userList) {
    List<V2TimGroupMemberFullInfo?>? managerList =
        Get.find<GroupChatSettingController>().managerList;
    List<UserInfoEntity> list = [];
    list.addAll(userList);
    if (managerList?.isNotEmpty == true) {
      for (int i = 0; i < managerList!.length; i++) {
        V2TimGroupMemberFullInfo? memberFullInfo = managerList[i];
        list.removeWhere((e) => e.id == memberFullInfo?.userID);
      }
    }
    return list;
  }

  void setGroupMemberRole(
      {required List<String> userIDs,
      required GroupMemberRoleTypeEnum role,
      bool? isDelete,
      int? index}) async {
    String groupID = Get.find<GroupChatSettingController>().groupID;
    int sendTimes = 0;
    for (int i = 0; i < userIDs.length; i++) {
      String userID = userIDs[i];
      bool success = await ChatIMManager.sharedInstance
          .setGroupMemberRole(groupID: groupID, userID: userID, role: role);
      if (success) {
        sendTimes++;
      }
    }
    if (sendTimes == userIDs.length) {
      if (isDelete == true && index != null) {
        Get.find<GroupChatSettingController>().managerList?.removeAt(index);
      }
      await Get.find<GroupChatSettingController>().loadData();
      setState(() {});
    }
  }
}
