import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class CustomFriendGroupManagerController extends GetxController {
  List<FriendSubGroupEntity>? customGroupList;
  RxString currentSelectedGroupId = "".obs;

  @override
  void onReady() {
    super.onReady();

    loadData();
  }

  Future<List<FriendSubGroupEntity>?> loadData() async {
    customGroupList = await ApiService().getFriendCustomGroupList();
    update();
    return customGroupList;
  }

  void updateSubGroupInfo(String groupId, String groupName) async {
    bool success =
        await ApiService().updateFriendCustomGroupInfo(groupId, groupName);
    if (success) {
      loadData();
      Get.find<ContactsController>().loadData();
    }
  }

  void addSubGroup(String groupName) async {
    int sort = customGroupList == null ? 0 : customGroupList!.length;
    bool success = await ApiService().addFriendCustomGroup(groupName, sort);
    if (success) {
      loadData();
      Get.find<ContactsController>().loadData();
    }
  }

  void deleteSubGroup(String groupId) async {
    bool success = await ApiService().deleteFriendCustomGroup(groupId);
    if (success) {
      loadData();
      Get.find<ContactsController>().loadData();
    }
  }

  Future<bool> updateSubGroupListSort() async {
    if (!(customGroupList?.isNotEmpty == true)) {
      return false;
    }
    List<Map<String, dynamic>> sortList = [];
    for (int i = 0; i < customGroupList!.length; i++) {
      FriendSubGroupEntity groupEntity = customGroupList![i];
      if (groupEntity.id != null) {
        sortList.add({
          "id": groupEntity.id,
          "sort": i,
        });
      }
    }
    if (sortList.isNotEmpty) {
      bool success = await ApiService().sortFriendCustomGroupList(sortList);
      if (success) {
        Get.find<ContactsController>().loadData();
      }
      return success;
    }
    return false;
  }

  Future<bool> changeUserToGroup(
      String userID, String groupID, String groupName) async {
    bool success = await ApiService().updateFriendUserInfo(userID,
        friendGroupId: groupID, friendGroupName: groupName);
    if (success) {
      Get.find<ContactsController>().loadData();
    }
    update();
    return success;
  }
}
