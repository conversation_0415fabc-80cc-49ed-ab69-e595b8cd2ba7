import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/pages/chat/conversation/chat_conversation_list_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class PrivateChatSettingController extends GetxController {
  FriendUserInfoEntity? friendUserInfo;

  void loadUserInfo({bool? showLoading}) async {
    if (friendUserInfo?.userFriendId != null) {
      friendUserInfo = await ApiService().getFriendUserInfo(
          friendUserInfo!.userFriendId!,
          showLoading: showLoading);
      update();
    }
  }

  ///更新用户信息
  Future<bool> updateUserInfo(
      {String? remark,
      String? groupId,
      int? lookMeState,
      int? lookFriendState,
      int? disturb,
      int? blacked}) async {
    bool success = await ApiService().updateFriendUserInfo(
      friendUserInfo!.userFriendId!,
      friendRemark: remark,
      friendGroupId: groupId,
      lookMeState: lookMeState,
      lookFriendState: lookFriendState,
      disturbed: disturb,
      blacked: blacked,
    );
    if (success) {
      if (remark != null) {
        friendUserInfo?.friendRemark = remark;
        Get.find<ChatConversationListController>().loadData(nexSeq: 0);
        Get.find<ContactsController>().loadData(showLoading: false);
      }
      if (groupId != null) {
        friendUserInfo?.friendGroupId = groupId;
        Get.find<ContactsController>().loadData(showLoading: false);
      }
      if (lookMeState != null) {
        friendUserInfo?.lookMeState = lookMeState;
      }
      if (lookFriendState != null) {
        friendUserInfo?.lookFriendState = lookFriendState;
      }
      if (disturb != null) {
        friendUserInfo?.isDisturb = disturb;
      }
      if (blacked != null) {
        friendUserInfo?.isBlack = blacked;
      }
      update();
      return true;
    }
    return false;
  }

  Future<void> deleteFriend() async {
    bool success =
        await ApiService().deleteFriendUser(friendUserInfo!.userFriendId!);
    if (success) {
      Get.find<ContactsController>().loadData(showLoading: false);
      Get.back(result: 1);
    }
  }

  Future<bool> blackFriendUser(bool blacked) async {
    bool success = false;
    if (blacked) {
      success =
          await ApiService().addToBlackList(friendUserInfo!.userFriendId!);
    } else {
      success =
          await ApiService().deleteFromBlackList(friendUserInfo!.userFriendId!);
    }
    return success;
  }
}
