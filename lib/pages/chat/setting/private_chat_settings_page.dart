import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/constants.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/input_text_page.dart';
import 'package:dada/components/widgets/common_setting_list_item_widget.dart';
import 'package:dada/components/widgets/switch_button.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/pages/chat/setting/private_chat_setting_controller.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/receive_message_opt_enum.dart';

class PrivateChatSettingsPage extends StatefulWidget {
  final FriendUserInfoEntity friendUserInfo;

  const PrivateChatSettingsPage({super.key, required this.friendUserInfo});

  @override
  State<PrivateChatSettingsPage> createState() =>
      _PrivateChatSettingsPageState();
}

class _PrivateChatSettingsPageState extends State<PrivateChatSettingsPage> {
  final PrivateChatSettingController controller =
      Get.put(PrivateChatSettingController());
  RxBool lookMeDaquanOpen = false.obs;
  RxBool lookHerDaquanOpen = false.obs;
  RxBool messageDisturb = false.obs;
  RxBool blacked = false.obs;

  @override
  void initState() {
    super.initState();

    controller.friendUserInfo = widget.friendUserInfo;
    lookMeDaquanOpen.value = widget.friendUserInfo.lookMeState == 1;
    lookHerDaquanOpen.value = widget.friendUserInfo.lookFriendState == 1;
    messageDisturb.value = widget.friendUserInfo.isDisturb == 1;
    blacked.value = widget.friendUserInfo.isBlack == 1;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFF5F5F5,
      appBar: CustomAppBar(
        title: "设置",
      ),
      body: Padding(
        padding: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
        child: GetBuilder(
          init: controller,
          builder: (controller) {
            return Column(
              children: [
                _buildFirstGroupWidget(),
                _buildSecondGroupWidget(),
                _buildThirdGroupWidget(),
                _buildBottomDeleteBtn(),
                const Spacer(),
                _buildBottomReportText(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildFirstGroupWidget() {
    bool isDaZi = Constants.daziRelationTypes
        .contains(controller.friendUserInfo?.friendType);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
      ),
      child: Column(
        children: [
          ///备注
          CommonSettingListItemWidget(
            title: "备注",
            subTitle: controller.friendUserInfo?.friendRemark,
            onTap: () {
              Get.to(
                () => InputTextPage(
                  title: "修改备注",
                  text: controller.friendUserInfo?.friendRemark,
                  placeholder: null,
                  callback: (value) {
                    controller.updateUserInfo(remark: value);
                  },
                  isMultiLine: false,
                  maxLength: 10,
                ),
              );
            },
          ),

          ///分组
          CommonSettingListItemWidget(
            title: isDaZi ? "搭子名称" : "分组",
            subTitle: controller.friendUserInfo?.daName ??
                controller.friendUserInfo?.friendGroupName,
            onTap: () {
              if (isDaZi) {
                return;
              }
              Get.toNamed(GetRouter.friendCustomGroupManager, parameters: {
                "friendUserId": controller.friendUserInfo!.userFriendId!,
                "friendGroupId": controller.friendUserInfo!.friendGroupId!,
              })?.then((value) {
                if (value != null) {
                  controller.loadUserInfo();
                }
              });
            },
            rightWidget: controller.friendUserInfo?.daName != null
                ? Text(
                    controller.friendUserInfo?.daName ?? "",
                    style: TextStyles.common(16.sp, AppColors.colorFF666666),
                  )
                : null,
          ),
        ],
      ),
    );
  }

  Widget _buildSecondGroupWidget() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
      ),
      child: Column(
        children: [
          ///不让他看我的搭圈
          Obx(
            () => CommonSettingListItemWidget(
              title: "不让他看我的搭圈",
              rightWidget: SwitchButton(
                value: !lookMeDaquanOpen.value,
                onChanged: (value) async {
                  bool success = await controller.updateUserInfo(
                      lookMeState: lookMeDaquanOpen.value == true ? 0 : 1);
                  if (success) {
                    lookMeDaquanOpen.value = !lookMeDaquanOpen.value;
                  }
                },
              ),
            ),
          ),

          ///不看他的搭圈
          Obx(
            () => CommonSettingListItemWidget(
              title: "不看他的搭圈",
              subTitle: controller.friendUserInfo?.friendGroupName,
              rightWidget: SwitchButton(
                value: !lookHerDaquanOpen.value,
                onChanged: (value) async {
                  bool success = await controller.updateUserInfo(
                      lookFriendState: lookHerDaquanOpen.value == true ? 0 : 1);
                  if (success) {
                    lookHerDaquanOpen.value = !lookHerDaquanOpen.value;
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThirdGroupWidget() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
      ),
      child: Column(
        children: [
          ///好友消息免打扰
          Obx(
            () => CommonSettingListItemWidget(
              title: "好友消息免打扰",
              rightWidget: SwitchButton(
                value: messageDisturb.value,
                onChanged: (value) async {
                  bool success = await controller.updateUserInfo(
                      blacked: messageDisturb.value == true ? 0 : 1);
                  if (success) {
                    messageDisturb.value = !messageDisturb.value;
                    if (messageDisturb.value == true) {
                      ChatIMManager.sharedInstance.setC2CReceiveMessageOpt(
                          userId: controller.friendUserInfo!.userFriendId!,
                          opt: ReceiveMsgOptEnum
                              .V2TIM_RECEIVE_NOT_NOTIFY_MESSAGE);
                    } else {
                      ChatIMManager.sharedInstance.setC2CReceiveMessageOpt(
                          userId: controller.friendUserInfo!.userFriendId!,
                          opt: ReceiveMsgOptEnum.V2TIM_RECEIVE_MESSAGE);
                    }
                  }
                },
              ),
            ),
          ),

          ///加入黑名单
          Obx(
            () => Visibility(
              visible: !_friendIsDaZi(),
              child: CommonSettingListItemWidget(
                title: "加入黑名单",
                subTitle: controller.friendUserInfo?.friendGroupName,
                rightWidget: SwitchButton(
                  value: blacked.value,
                  onChanged: (value) async {
                    // if (_friendIsDaZi()) {
                    //   ///拉入小黑屋
                    //   ChatIMManager.sharedInstance.setC2CReceiveMessageOpt(
                    //       userId: controller.friendUserInfo!.userFriendId!,
                    //       opt: ReceiveMsgOptEnum.V2TIM_NOT_RECEIVE_MESSAGE);
                    //   blacked.value = !blacked.value;
                    //   return;
                    // }

                    ///加入黑名单
                    bool success =
                        await controller.blackFriendUser(!blacked.value);
                    if (success) {
                      controller.loadUserInfo();
                      blacked.value = !blacked.value;
                    }
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomDeleteBtn() {
    bool isFriend = controller.friendUserInfo?.isFriend == 1;
    bool isDaZi = _friendIsDaZi();

    return GestureDetector(
      onTap: () {
        if (!isFriend) {
          Get.offAndToNamed(GetRouter.addFriendSend, parameters: {
            "userId": controller.friendUserInfo!.userId!,
            "avatar": controller.friendUserInfo!.avatar!,
            "nickname": controller.friendUserInfo!.nickname!,
          });
          return;
        }
        String? title = isDaZi ? "确定解除搭子关系？" : null;
        String? content = isDaZi ? "解除搭子关系，将降级为好友." : "确定删除好友？";
        ToastUtils.showDialog(
          title: title,
          content: content,
          hideTopImg: isDaZi,
          hideCloseBtn: true,
          onConfirm: () async {
            if (isDaZi) {
              bool result = await ApiService().relieveDaziRelation(
                  controller.friendUserInfo!.userFriendId!);
              if (result) {
                controller.loadUserInfo(showLoading: false);
                EventBusEngine.fire(event: BusEvent.reloadChatDetail);
              }
            } else {
              controller.deleteFriend();
            }
          },
        );
      },
      child: Container(
        height: 56.h,
        alignment: Alignment.center,
        margin: EdgeInsets.only(top: 20.h),
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: Colors.white,
        ),
        child: Text(
          !isFriend
              ? "添加好友"
              : isDaZi
                  ? "解除搭子关系"
                  : "删除好友",
          style: TextStyles.common(16.sp,
              !isFriend ? AppColors.colorFF23AF28 : AppColors.colorFFEA4A4A),
        ),
      ),
    );
  }

  Widget _buildBottomReportText() {
    return GestureDetector(
      onTap: () async {
        Get.to(() => ReportPage(
            reportType: ReportType.user,
            userId: controller.friendUserInfo?.userFriendId!));
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
        child: Text(
          "被骚扰了？举报改用户",
          style: TextStyles.common(14.sp, AppColors.colorFF1F9C23),
        ),
      ),
    );
  }

  bool _friendIsDaZi() {
    bool? isDaZi = controller.friendUserInfo?.isDaziRelation();
    return isDaZi == true;
  }
}
