import 'package:dada/model/chat_group_info_entity.dart';
import 'package:dada/model/chat_group_label_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/receive_message_opt_enum.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';

class GroupChatSettingController extends GetxController {
  late String groupID;
  late V2TimGroupInfo v2timGroupInfo;

  ChatGroupInfoEntity? groupInfo;
  String? inGroupShowName;
  List<V2TimGroupMemberFullInfo?>? memberList;
  List<V2TimGroupMemberFullInfo?>? managerList;

  @override
  void onReady() async {
    super.onReady();

    ToastUtils.showLoading();
    await loadData();
    ToastUtils.hideLoading();
  }

  Future<void> loadData() async {
    groupInfo = await loadGroupInfo(groupID);
    memberList = await loadGroupMemberList(groupID);
    update();
    await getInGroupShowName();
    await getGroupManagerList();
  }

  Future<ChatGroupInfoEntity?> loadGroupInfo(String groupID) async {
    return ApiService().getGroupInfo(groupID);
  }

  Future<List<V2TimGroupMemberFullInfo?>?> loadGroupMemberList(
      String groupID) async {
    return await ChatIMManager.sharedInstance
        .getAllGroupMemberList(groupID, "0");
  }

  Future<List<V2TimGroupMemberFullInfo?>?> getGroupManagerList() async {
    managerList = memberList?.where((e) => e?.role == 300).toList();
    return managerList;
  }

  bool checkIsGroupOwner() {
    if (memberList?.isNotEmpty == true) {
      bool isOwner = memberList!
          .where((e) => e?.userID == UserService().user?.id && e?.role == 400)
          .isNotEmpty;
      return isOwner;
    }
    return false;
  }

  bool checkIsGroupOwnerOrManager() {
    if (memberList?.isNotEmpty == true) {
      bool isOwnerOrManager = memberList!
          .where((e) =>
              e?.userID == UserService().user?.id &&
              (e?.role == 400 || e?.role == 300))
          .isNotEmpty;
      return isOwnerOrManager;
    }
    return false;
  }

  Future<String?> getInGroupShowName() async {
    String? showName;
    if (memberList?.isNotEmpty == true) {
      V2TimGroupMemberFullInfo? memberFullInfo = memberList
          ?.where((member) => member?.userID == UserService().user?.id)
          .toList()
          .first;
      if (memberFullInfo != null) {
        ///群成员自定义字段最多支持设置5个，且创建后不能删除，暂时用nameCard来存用户在群昵称
        showName = memberFullInfo.nameCard;
      }
    }
    inGroupShowName = showName;
    update();
    return showName;
  }

  Future<bool> setInGroupShowName(String showName) async {
    bool success = await ChatIMManager.sharedInstance.setGroupMemberInfo(
        groupID: groupID, userID: UserService().user!.id!, nameCard: showName);
    if (success) {
      inGroupShowName = showName;
      memberList = await loadGroupMemberList(groupID);
      update();
    }
    return success;
  }

  void uploadImageAndUpdateGroupFaceUrl(String filePath) async {
    String? uploadedUrl = await ApiService().uploadFile(filePath);
    if (uploadedUrl != null) {
      updateGroupInfo(groupFaceUrl: uploadedUrl);
    } else {
      ToastUtils.showToast("图片上传失败，请重新尝试！");
    }
  }

  Future<bool> updateGroupInfo(
      {String? groupName,
      String? groupNickname,
      String? groupFaceUrl,
      String? notice,
      int? messageDisturb,
      String? creatorId,
      List<Map<String, String>>? tagList}) async {
    bool success = await ApiService().updateGroupInfo(
        groupID: groupID,
        groupName: groupName,
        faceUrl: groupFaceUrl,
        notice: notice,
        creatorId: creatorId,
        isDisturb: messageDisturb,
        tagList: tagList);
    if (tagList == null || messageDisturb == null) {
      success = await ChatIMManager.sharedInstance.setGroupInfo(groupID,
          groupName: groupName, faceUrl: groupFaceUrl, notice: notice);
    }
    if (success) {
      groupInfo = await loadGroupInfo(groupID);
      Get.find<ContactsController>().loadData(showLoading: false);
      EventBusEngine.fire(event: BusEvent.reloadChatDetail);
      update();
    }
    return success;
  }

  List<UserInfoEntity> transferMemberListToFriendList() {
    List<UserInfoEntity> list = [];
    if (memberList?.isNotEmpty == true) {
      for (int i = 0; i < memberList!.length; i++) {
        V2TimGroupMemberFullInfo? memberFullInfo = memberList![i];
        if (memberFullInfo?.userID == UserService().user?.id) {
          continue;
        }
        if (memberFullInfo != null) {
          UserInfoEntity userInfo = UserInfoEntity();
          userInfo.id = memberFullInfo.userID;
          userInfo.nickname = memberFullInfo.nickName;
          userInfo.avatar = memberFullInfo.faceUrl;
          list.add(userInfo);
        }
      }
    }
    return list;
  }

  Future<bool> kickOutOfMembers(List<String> memberIds) async {
    bool success = await ChatIMManager.sharedInstance
        .kickGroupMember(groupID: groupID, memberList: memberIds);
    if (success) {
      memberList = await loadGroupMemberList(groupID);
      update();
      return true;
    }
    return false;
  }

  Future<bool> inviteMemberList(List<String> invitedMemberList) async {
    Map<String, dynamic> customMsgData = {
      "title": "邀请你加入群聊",
      "groupID": groupID,
      "ownerID": groupInfo!.creatorId!,
      "invitorID": UserService().user!.id!,
      "groupName": groupInfo!.groupName,
      "faceUrl": groupInfo!.faceUrl,
      "memberCount": invitedMemberList.length,
      "text": "${UserService().user!.nickname}邀请你加入【${groupInfo!.groupName}】",
    };
    int sendTimes = 0;
    for (int i = 0; i < invitedMemberList.length; i++) {
      String userID = invitedMemberList[i];
      bool success = await ChatIMManager.sharedInstance.sendCustomMessage(
          type: ChatImCustomMsgType.InviteToJoinChatGroup,
          data: customMsgData,
          receiver: userID,
          groupID: "");
      if (success) {
        sendTimes++;
      }
    }
    if (sendTimes == invitedMemberList.length) {
      ToastUtils.showToast("邀请已发送！");
      return true;
    }
    return false;
  }

  Future<void> updateGroupMessageReceiveLimit(bool limit) async {
    ReceiveMsgOptEnum optEnum = ReceiveMsgOptEnum.V2TIM_RECEIVE_MESSAGE;
    if (limit == true) {
      optEnum = ReceiveMsgOptEnum.V2TIM_RECEIVE_NOT_NOTIFY_MESSAGE;
    }
    bool success = await ChatIMManager.sharedInstance
        .setGroupReceiveMessageOpt(groupID, optEnum);
    if (success) {
      if (limit == true) {
        v2timGroupInfo.recvOpt = 2;
      } else {
        v2timGroupInfo.recvOpt = 0;
      }
    }
    update();
  }

  void clearGroupHistoryList() async {
    bool success = await ChatIMManager.sharedInstance
        .clearGroupHistoryMessageList(groupID);
    if (success) {
      ToastUtils.showToast("聊天记录已清空");
      EventBusEngine.fire(event: BusEvent.refreshChatHistoryList);
    }
  }

  void addGroupLabel(String labelName) async {
    List<Map<String, String>>? labelNames =
        getCurrentGroupLabelNamesMapList(groupInfo?.labels);
    if (labelName.isNotEmpty == true) {
      labelNames!.add({"tagName": labelName});
    }
    updateGroupInfo(tagList: labelNames);
  }

  void deleteGroupLabel(String labelID) async {
    groupInfo?.labels?.removeWhere((e) => e.id == labelID);
    List<Map<String, String>>? labelNames =
        getCurrentGroupLabelNamesMapList(groupInfo?.labels);
    updateGroupInfo(tagList: labelNames);
  }

  List<Map<String, String>>? getCurrentGroupLabelNamesMapList(
      List<ChatGroupLabelEntity>? labels) {
    List<Map<String, String>> labelNames = [];
    if (labels?.isNotEmpty == true) {
      for (int i = 0; i < labels!.length; i++) {
        ChatGroupLabelEntity labelEntity = labels[i];
        if (labelEntity.tagName != null) {
          labelNames.add({"tagName": labelEntity.tagName!});
        }
      }
    }
    return labelNames;
  }

  void disbandGroup() async {
    bool success = await ApiService().disbandGroup(groupID: groupID);
    if (success) {
      success =
          await ChatIMManager.sharedInstance.dismissGroup(groupID: groupID);
      String conversationID = "group_$groupID";
      success =
          await ChatIMManager.sharedInstance.deleteConversation(conversationID);
      Get.offNamedUntil(GetRouter.main, ModalRoute.withName('/main'));
    }
  }

  void exitGroup() async {
    if (checkIsGroupOwner()) {
      if (memberList?.isNotEmpty == true) {
        V2TimGroupMemberFullInfo? transferGroupOwner = memberList!
            .where(
                (e) => e?.userID != UserService().user?.id! && e?.role != 400)
            .first;
        if (transferGroupOwner != null) {
          bool success =
              await updateGroupInfo(creatorId: transferGroupOwner.userID);
          if (success) {
            await ChatIMManager.sharedInstance.transferGroupOwner(
                groupID: groupID, userID: transferGroupOwner.userID);
          }
        }
      }
    }

    bool success =
        await ChatIMManager.sharedInstance.quitGroup(groupID: groupID);
    if (success) {
      String conversationID = "group_$groupID";
      success =
          await ChatIMManager.sharedInstance.deleteConversation(conversationID);
      Get.find<ContactsController>().loadData(showLoading: false);
      Get.offNamedUntil(GetRouter.main, ModalRoute.withName('/main'));
    }
  }
}
