import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_search_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat/setting/group/group_chat_setting_controller.dart';
import 'package:dada/pages/match/assemble/team/invite/invite_selected_user_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';

class GroupAllMemberListPage extends StatefulWidget {
  const GroupAllMemberListPage({super.key});

  @override
  State<GroupAllMemberListPage> createState() => _GroupAllMemberListPageState();
}

class _GroupAllMemberListPageState extends State<GroupAllMemberListPage> {
  GroupChatSettingController controller =
      Get.find<GroupChatSettingController>();
  TextEditingController editingController = TextEditingController();
  Rx<List<V2TimGroupMemberFullInfo?>?> searchedList =
      Rx<List<V2TimGroupMemberFullInfo?>?>(null);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "聊天成员（${controller.memberList?.length ?? 0}）",
        backgroundColor: AppColors.colorFFF5F5F5,
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildMemberListWidget(),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      color: AppColors.colorFFF5F5F5,
      child: CustomSearchBar(
        bgColor: Colors.white,
        controller: editingController,
        onChanged: (value) {
          searchMembersWithKeyword(value);
        },
      ),
    );
  }

  Widget _buildMemberListWidget() {
    return Expanded(
      child: Obx(() {
        List<V2TimGroupMemberFullInfo?> list = [];
        if (searchedList.value != null) {
          list.addAll(searchedList.value!);
        } else {
          list.addAll(controller.memberList!);
          list.add(V2TimGroupMemberFullInfo(userID: "+"));
          if (controller.checkIsGroupOwnerOrManager()) {
            list.add(V2TimGroupMemberFullInfo(userID: "-"));
          }
        }
        return GridView.count(
          padding: EdgeInsets.only(top: 15.h, left: 15.w, bottom: 60.h, right: 15.w),
          crossAxisCount: 5,
          mainAxisSpacing: 20.h,
          crossAxisSpacing: 15.w,
          childAspectRatio: 55.w / 81.h,
          children: list.map((e) {
            if (e == null) {
              return Container();
            }
            return _buildGroupMemberListItem(e);
          }).toList(),
        );
      }),
    );
  }

  Widget _buildGroupMemberListItem(V2TimGroupMemberFullInfo memberInfo) {
    if (memberInfo.userID == "+") {
      return GestureDetector(
        onTap: () {
          Get.toNamed(GetRouter.createGroupChat,
                  parameters: {"isInviteMember": "1"},
                  arguments: controller.memberList)
              ?.then((value) async {
            if (value != null) {
              if (value is List<String>) {
                bool success = await controller.inviteMemberList(value);
                if (success) {
                  setState(() {});
                }
              }
            }
          });
        },
        child: SizedBox(
          height: 81.h,
          width: 55.w,
          child: Column(
            children: [
              ImageUtils.getImage(
                  Assets.imagesGroupChatMemberListAdd, 55.w, 55.w),
            ],
          ),
        ),
      );
    }
    if (memberInfo.userID == "-") {
      return GestureDetector(
        onTap: () {
          Get.to(
            () => InviteSelectedUserPage(
              userList: controller.transferMemberListToFriendList(),
              isSingleSelected: false,
              isDeleteMember: true,
              callback: (userIds) async {
                bool success = await controller.kickOutOfMembers(userIds);
                if (success) {
                  setState(() {});
                }
              },
            ),
          );
        },
        child: SizedBox(
          height: 81.h,
          width: 55.w,
          child: Column(
            children: [
              ImageUtils.getImage(
                  Assets.imagesGroupChatMemberListDelete, 55.w, 55.w),
            ],
          ),
        ),
      );
    }
    return GestureDetector(
      onTap: () {
        Get.toNamed(GetRouter.userProfile,
            parameters: {"userId": memberInfo.userID});
      },
      child: SizedBox(
        height: 81.h,
        width: 55.w,
        child: Column(
          children: [
            ClipOval(
              child: ImageUtils.getImage(memberInfo.faceUrl ?? "", 55.w, 55.w,
                  fit: BoxFit.cover),
            ),
            Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: Text(
                memberInfo.nickName ?? "",
                overflow: TextOverflow.ellipsis,
                style: TextStyles.normal(14.sp),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void searchMembersWithKeyword(String keyword) {
    if (keyword.isEmpty) {
      searchedList.value = null;
    } else {
      searchedList.value = controller.memberList
          ?.where((member) => member?.nickName?.contains(keyword) ?? false)
          .toList();
    }
  }
}
