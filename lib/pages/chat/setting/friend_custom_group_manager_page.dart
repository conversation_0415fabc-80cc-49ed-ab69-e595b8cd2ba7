import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/input_text_field_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/pages/chat/setting/friend_custom_group_manager_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class FriendChangeCustomGroup extends StatefulWidget {
  const FriendChangeCustomGroup({super.key});

  @override
  State<FriendChangeCustomGroup> createState() =>
      _FriendChangeCustomGroupState();
}

class _FriendChangeCustomGroupState extends State<FriendChangeCustomGroup> {
  bool _isGroupManager = false;
  String? _friendGroupId;
  String? _friendUserId;
  bool _isSelectGroup = false;
  CustomFriendGroupManagerController controller =
      CustomFriendGroupManagerController();

  @override
  void initState() {
    super.initState();

    _isGroupManager = Get.parameters["isGroupManager"] == "1";
    _friendGroupId = Get.parameters["friendGroupId"];
    _friendUserId = Get.parameters["friendUserId"];
    _isSelectGroup = Get.parameters["isSelectGroup"] == "1";
    if (_friendGroupId != null) {
      controller.currentSelectedGroupId.value = _friendGroupId!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
        appBar: CustomAppBar(
          title: _isGroupManager == true
              ? "子分组管理"
              : _isSelectGroup
                  ? "选择好友分组"
                  : "移至分组",
          rightWidgets: [_buildRightBarBtn()],
        ),
        body: Column(
          children: [
            _buildAddCustomGroupWidget(),
            _buildCustomGroupListWidget(),
          ],
        ));
  }

  Widget _buildAddCustomGroupWidget() {
    return InkWell(
      onTap: () {
        ToastUtils.showDialog(
          dialog: InputTextFieldDialog(
            title: "添加子分组",
            subTitle: "请输入新的分组名称",
            maxLength: 10,
            onSubmit: (value) {
              controller.addSubGroup(value);
            },
          ),
        );
      },
      child: Container(
        height: 55.h,
        margin: EdgeInsets.only(bottom: 7.5.h),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(
                top: 15.h,
                left: 14.5.w,
                bottom: 14.h,
              ),
              child: Row(
                children: [
                  ImageUtils.getImage(
                      Assets.imagesFriendCustomGroupAdd, 25.h, 25.h),
                  SizedBox(
                    width: 5.w,
                  ),
                  Text(
                    "添加子分组",
                    style: TextStyles.common(16.sp, AppColors.colorFF666666),
                  ),
                ],
              ),
            ),
            Container(
              height: 1,
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              color: AppColors.colorFFE5E5E5,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomGroupListWidget() {
    return GetBuilder(
      init: controller,
      global: false,
      builder: (controller) {
        if (_isGroupManager) {
          return _buildCustomGroupManagerList();
        }
        return _buildCustomGroupChangeList();
      },
    );
  }

  Widget _buildCustomGroupManagerList() {
    if (!(controller.customGroupList?.isNotEmpty == true)) {
      return Container();
    }
    return Expanded(
      child: ReorderableListView.builder(
        buildDefaultDragHandles: false,
        itemCount: controller.customGroupList!.length,
        itemBuilder: (context, index) {
          FriendSubGroupEntity entity = controller.customGroupList![index];
          return SizedBox(
            key: ObjectKey(entity),
            height: 40.h,
            child: InkWell(
              onTap: () {
                ToastUtils.showDialog(
                  dialog: InputTextFieldDialog(
                    title: "编辑分组",
                    subTitle: "请输入新的分组名称",
                    text: entity.groupName,
                    maxLength: 20,
                    isMultiLine: true,
                    onSubmit: (value) {
                      controller.updateSubGroupInfo(entity.id!, value);
                    },
                  ),
                );
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 14.5.w),
                        child: GestureDetector(
                          onTap: () {
                            ToastUtils.showBottomSheet(
                              ["删除分组"],
                              destructiveIndex: 0,
                              title: "删除该分组后，组内联系人将移至默认分组",
                              titleColor: AppColors.colorFF999999,
                              titleFontSize: 14.sp,
                              onTap: (index) {
                                controller.deleteSubGroup(entity.id!);
                              },
                            );
                          },
                          child: ImageUtils.getImage(
                              Assets.imagesFriendCustomGroupDelete, 20.w, 20.w),
                        ),
                      ),
                      Container(
                        constraints: BoxConstraints(
                          maxWidth: 300.w,
                        ),
                        child: Padding(
                          padding: EdgeInsets.only(left: 5.w),
                          child: Text(
                            "${entity.groupName}",
                            overflow: TextOverflow.ellipsis,
                            style:
                                TextStyles.common(16.sp, AppColors.colorFF666666),
                          ),
                        ),
                      ),
                    ],
                  ),
                  ReorderableDragStartListener(
                    index: index,
                    child: Padding(
                      padding: EdgeInsets.only(right: 15.w),
                      child: ImageUtils.getImage(
                          Assets.imagesChatDetailRightBarBtnGroupSetting,
                          12.w,
                          12.5.h),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        onReorder: _handleReorder,
      ),
    );
  }

  void _handleReorder(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final element = controller.customGroupList!.removeAt(oldIndex);
    controller.customGroupList!.insert(newIndex, element);
    controller.update();

    bool success = await controller.updateSubGroupListSort();
    if (!success) {
      final element = controller.customGroupList!.removeAt(newIndex);
      controller.customGroupList!.insert(oldIndex, element);
      controller.update();
    }
  }

  Widget _buildCustomGroupChangeList() {
    if (!(controller.customGroupList?.isNotEmpty == true)) {
      return Container();
    }
    List<FriendSubGroupEntity> list = [];
    list.addAll(controller.customGroupList!);
    if (!(list.where((element) => element.id == "0").toList().isNotEmpty ==
        true)) {
      FriendSubGroupEntity normalGroupEntity = FriendSubGroupEntity();
      normalGroupEntity.id = "0";
      normalGroupEntity.groupName = "我的好友";
      list.insert(0, normalGroupEntity);
    }
    return Expanded(
      child: ListView.separated(
        itemBuilder: (context, index) {
          FriendSubGroupEntity groupInfoEntity = list[index];
          return Obx(() {
            bool selected =
                controller.currentSelectedGroupId.value == groupInfoEntity.id;
            return SizedBox(
              height: 50.h,
              child: InkWell(
                onTap: () async {
                  if (controller.currentSelectedGroupId.value ==
                      groupInfoEntity.id) {
                    return;
                  }
                  if (_isSelectGroup == true) {
                    controller.currentSelectedGroupId.value =
                        groupInfoEntity.id!;
                    Get.back(result: {
                      "groupID": controller.currentSelectedGroupId.value,
                      "groupName": groupInfoEntity.groupName
                    });
                    return;
                  }
                  if (_friendUserId != null) {
                    bool success = await controller.changeUserToGroup(
                        _friendUserId!,
                        groupInfoEntity.id!,
                        groupInfoEntity.groupName!);
                    if (success) {
                      controller.currentSelectedGroupId.value =
                          groupInfoEntity.id!;
                      Get.back(result: controller.currentSelectedGroupId.value);
                    }
                  }
                },
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        left: 10.w,
                      ),
                      child: ImageUtils.getImage(
                        selected
                            ? Assets.imagesContactsListFriendItemSelected
                            : Assets.imagesContactsListFriendItemUnselected,
                        20.w,
                        20.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: 300.w,
                      ),
                      child: Padding(
                        padding: EdgeInsets.only(left: 10.w),
                        child: Text(
                          groupInfoEntity.groupName ?? "",
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          });
        },
        separatorBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 15.w),
            height: 1,
            color: AppColors.colorFFE5E5E5,
          );
        },
        itemCount: list.length,
      ),
    );
  }

  Widget _buildRightBarBtn() {
    return GestureDetector(
      onTap: () {
        Get.back();
      },
      child: Container(
        margin: EdgeInsets.only(right: 15.w),
        width: 56.w,
        height: 30.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.r),
          color: AppColors.colorFF89E15C,
        ),
        child: Text(
          "完成",
          style: TextStyles.common(14.sp, AppColors.colorFF3D3D3D),
        ),
      ),
    );
  }
}
