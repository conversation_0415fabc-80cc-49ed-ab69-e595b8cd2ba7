import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_search_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_group_info_entity.dart';
import 'package:dada/model/contact_group_list_item_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/pages/chat/contacts/contacts_friend_custom_group_widget.dart';
import 'package:dada/pages/chat/contacts/contacts_friend_list_group_header.dart';
import 'package:dada/pages/chat/contacts/contacts_friend_list_widget.dart';
import 'package:dada/pages/chat/conversation/chat_conversation_list_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_role_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';

class GroupChatCreatePage extends StatefulWidget {
  const GroupChatCreatePage({super.key});

  @override
  State<GroupChatCreatePage> createState() => _GroupChatCreatePageState();
}

class _GroupChatCreatePageState extends State<GroupChatCreatePage> {
  late ContactsController controller;
  final TextEditingController _textEditingController = TextEditingController();

  bool isInviteMember = false;
  List<V2TimGroupMemberFullInfo?>? alreadyInGroupMemberList;

  @override
  void initState() {
    super.initState();

    isInviteMember = Get.parameters["isInviteMember"] == "1";
    alreadyInGroupMemberList = Get.arguments;

    if (Get.isRegistered<ContactsController>()) {
      controller = Get.find<ContactsController>();
    } else {
      controller = Get.put(ContactsController());
    }

    WidgetsBinding.instance.addPostFrameCallback((timestamp) async {
      if (mounted) {
        if (!(controller.contactsGroupList?.isNotEmpty == true)) {
          await controller.loadData();
          setState(() {});
        }
        controller.clearCreateGroupState();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: isInviteMember == true ? "邀请好友" : "创建群聊",
        rightWidgets: [_buildRightBarButton()],
        backAction: () {
          controller.clearCreateGroupState();
          Get.back();
        },
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: _buildContactsList(),
          ),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return CustomSearchBar(
      height: 35.h,
      controller: _textEditingController,
      onChanged: (value) {
        controller.inputSearchText.value = _textEditingController.text;
        controller.searchResult();
      },
    );
  }

  Widget _buildContactsList() {
    return Obx(() {
      if (controller.searchFriendResultList.isNotEmpty) {
        return ContactsFriendListWidget(
          list: controller.searchFriendResultList,
          groupType: ContactsGroupType.dazi.index,
          canSelected: true,
          disableSelectedList: alreadyInGroupMemberList,
        );
      }

      return SingleChildScrollView(
        padding: EdgeInsets.only(bottom: 20.h, top: 10.h),
        scrollDirection: Axis.vertical,
        child: Column(
          children: [
            ///好友分组
            _buildFriendGroupWidget(),

            ///搭子分组
            _buildDaZiGroupWidget(),
          ],
        ),
      );
    });
  }

  ///好友分组
  Widget _buildFriendGroupWidget() {
    if (!(controller.contactsGroupList?.isNotEmpty == true)) {
      return Container();
    }
    List<ContactGroupListItemEntity>? result = controller.contactsGroupList!
        .where((value) => value.groupType == ContactsGroupType.friend.index)
        .toList();
    ContactGroupListItemEntity? friendGroupEntity;
    if (result.isNotEmpty == true) {
      friendGroupEntity = result.first;
    }
    String groupName = friendGroupEntity?.groupName ?? "好友";
    return Obx(
      () {
        String groupKey = "create_group_$groupName";
        bool? unfold = controller.groupUnfoldMap[groupKey];
        return Column(
          children: [
            ///好友组标题头部
            ContactsFriendListGroupHeader(
              groupKey: groupKey,
              title: groupName,
              unfold: unfold ?? false,
              rightWidget: Padding(
                padding: EdgeInsets.only(right: 10.w),
                child: ImageUtils.getImage(
                  Assets.imagesChatContactsFriendGroupHeaderFriendIcon,
                  17.w,
                  17.w,
                ),
              ),
            ),

            ///好友Group中 未分组的好友
            unfold == true
                ? ContactsFriendListWidget(
                    list:
                        friendGroupEntity?.friendGroupInfo?.unGroupedFriendList,
                    groupType: friendGroupEntity?.groupType,
                    canSelected: true,
                    disableSelectedList: alreadyInGroupMemberList,
                  )
                : Container(),

            ///好友自定义子分组
            unfold == true
                ? ContactsFriendCustomGroupWidget(
                    groupList: friendGroupEntity?.friendGroupInfo?.subGroupList,
                    isCreate: true,
                    disableSelectedList: alreadyInGroupMemberList,
                  )
                : Container(),
          ],
        );
      },
    );
  }

  ///搭子分组
  Widget _buildDaZiGroupWidget() {
    if (!(controller.contactsGroupList?.isNotEmpty == true)) {
      return Container();
    }
    List<ContactGroupListItemEntity>? result = controller.contactsGroupList!
        .where((value) => value.groupType == ContactsGroupType.dazi.index)
        .toList();
    ContactGroupListItemEntity? daZiGroupEntity;
    if (result.isNotEmpty == true) {
      daZiGroupEntity = result.first;
    }
    String groupName = daZiGroupEntity?.groupName ?? "搭子";
    return Obx(
      () {
        String groupKey = "create_group_$groupName";
        bool? unfold = controller.groupUnfoldMap[groupKey];
        return Column(
          children: [
            ContactsFriendListGroupHeader(
              title: groupName,
              unfold: unfold ?? false,
              groupKey: groupKey,
            ),
            unfold == true
                ? ContactsFriendListWidget(
                    list: daZiGroupEntity?.daziList,
                    groupType: ContactsGroupType.dazi.index,
                    canSelected: true,
                  )
                : Container(),
          ],
        );
      },
    );
  }

  Widget _buildBottomBtn() {
    if (isInviteMember == true) {
      return Container();
    }
    return Obx(
      () {
        bool enabled = controller.selectedFriendItems.isNotEmpty;
        return CommonGradientBtn(
          bottomMargin: ScreenUtil().bottomBarHeight + 10.h,
          title: "创建群聊",
          onTap: () {
            if (enabled) {
              createGroup();
            }
          },
        );
      },
    );
  }

  Widget _buildRightBarButton() {
    if (!isInviteMember) {
      return Container();
    }
    return Obx(() {
      bool enabled = controller.selectedFriendItems.isNotEmpty;
      return GestureDetector(
        onTap: () {
          if (enabled) {
            List<String> userIDs = controller.selectedFriendItems
                .map((e) => e.userFriendId!)
                .toList();
            Get.back(result: userIDs);
          }
        },
        child: Container(
          width: 56.w,
          height: 30.h,
          margin: EdgeInsets.only(right: 15.w),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.r),
            color: enabled ? AppColors.colorFF89E15C : AppColors.colorFFE1E1E1,
          ),
          child: Text(
            "完成",
            style: TextStyles.common(14.sp,
                enabled ? AppColors.colorFF3D3D3D : AppColors.colorFF999999),
          ),
        ),
      );
    });
  }

  void createGroup() async {
    String groupName = getNormalGroupName();
    ChatGroupInfoEntity? groupInfoEntity =
        await ApiService().createGroup(groupName: groupName);
    if (groupInfoEntity?.groupId != null) {
      String? groupID = await ChatIMManager.sharedInstance.createGroup(
        groupID: groupInfoEntity!.groupId!,
        groupType: GroupType.Public,
        groupName: groupName,
        faceUrl: "",
        memberList: transferGroupMemberList(),
      );
      if (groupID != null) {
        V2TimConversation conversation = await ChatIMManager.sharedInstance
            .getConversation(
                type: 2,
                groupID: groupID,
                groupName: groupName,
                groupType: GroupType.Public);
        Get.find<ChatConversationListController>()
            .addNewConversation(conversation);
        Get.offAndToNamed(GetRouter.chatDetail, arguments: conversation);
      }
    }
  }

  String getNormalGroupName() {
    String groupName = UserService().user?.nickname ?? "";
    for (int i = 0; i < controller.selectedFriendItems.length; i++) {
      FriendUserInfoEntity userInfoEntity = controller.selectedFriendItems[i];
      if (userInfoEntity.nickname != null) {
        if (groupName.isEmpty) {
          groupName = userInfoEntity.nickname!;
        } else {
          groupName = "$groupName、${userInfoEntity.nickname!}";
        }
      }
      if (i >= 4) {
        groupName = "群聊【$groupName】";
        return groupName;
      }
    }
    groupName = "群聊【$groupName】";
    return groupName;
  }

  List<V2TimGroupMember> transferGroupMemberList() {
    List<V2TimGroupMember> list = [];
    for (int i = 0; i < controller.selectedFriendItems.length; i++) {
      FriendUserInfoEntity userInfoEntity = controller.selectedFriendItems[i];
      GroupMemberRoleTypeEnum roleTypeEnum =
          GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_ROLE_MEMBER;
      V2TimGroupMember groupMember = V2TimGroupMember(
          userID: userInfoEntity.userFriendId!, role: roleTypeEnum);
      list.add(groupMember);
    }
    return list;
  }

  List<String> getCreateGroupOriginalMemberList() {
    List<String> list = [];
    for (int i = 0; i < controller.selectedFriendItems.length; i++) {
      FriendUserInfoEntity userInfoEntity = controller.selectedFriendItems[i];
      list.add(userInfoEntity.userFriendId!);
    }
    return list;
  }

  @override
  void dispose() {
    super.dispose();
  }
}
