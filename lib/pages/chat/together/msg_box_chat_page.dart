import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_audio_msg_tooltip_item.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/pages/chat/detail/card_box/user_card_box_bottom_sheet.dart';
import 'package:dada/pages/chat/detail/chat_custom_msg_item.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/pages/chat/setting/private_chat_settings_page.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/dress_utils.dart';
import 'package:dada/utils/emoji_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKItMessageList/tim_uikit_chat_history_message_list_config.dart';

class MsgBoxChatPage extends StatefulWidget {
  const MsgBoxChatPage({super.key});

  @override
  State<MsgBoxChatPage> createState() => _MsgBoxChatPageState();
}

class _MsgBoxChatPageState extends State<MsgBoxChatPage> {
  late ChatDetailController controller;
  V2TimConversation? conversation;

  @override
  void initState() {
    super.initState();
    conversation = Get.arguments;
    controller = ChatDetailController();
  }

  @override
  Widget build(BuildContext context) {
    if (conversation == null) {
      return const LoadingWidget();
    }
    return TIMUIKitChat(
      isMonthCardUser: UserService().checkIsMonthCardUser(),
      onMonthCardFailed: () {
        Get.dialog(TodoTogetherDialog(
          content: '此功能需要月卡权益，您的支持我们将全用来将产品做的更好哦~\n（在小屋-任务中，也可以免费获得月卡权益~)',
          leftBtnTitle: "取消",
          rightBtnTitle: "开通月卡",
          onLeftBtnTap: () {
            Get.back();
          },
          onRightBtnTap: () {
            Get.back();
            Get.toNamed(GetRouter.rechargeMonthCard);
          },
        ));
      },
      conversation: conversation!,
      controller: controller.timUIKitChatController,
      extraTipsActionItemBuilder: (message, closeTip, [key, context]) {
        return Container();
      },
      mainHistoryListConfig: TIMUIKitHistoryMessageListConfig(
        padding: EdgeInsets.only(bottom: 12.h),
      ),
      customAppBar: CustomAppBar(
        leftWidget: IconButton(
          onPressed: () => Get.back(),
          icon: Container(
            width: 28.w,
            height: 28.w,
            alignment: Alignment.center,
            child: ImageUtils.getImage(Assets.imagesNaviBack, 8.w, 14.w,
                color: Colors.white),
          ),
        ),
        centerWidget: _buildCenterWidget(),
        backgroundColor: Colors.transparent,
      ),
      bacImg: ImageUtils.getImage(Assets.imagesTodoTogetherDetailTimeBac,
          ScreenUtil().screenWidth, ScreenUtil().screenHeight,
          fit: BoxFit.cover),
      userAvatarBuilder: (BuildContext context, V2TimMessage message) {
        if (message.customElem != null &&
            message.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM) {
          String? jsonStr = message.customElem?.data;
          if (jsonStr != null) {
            if (StringUtils.isJsonString(jsonStr)) {
              ChatImCustomMsgEntity customMsgEntity =
                  ChatImCustomMsgEntity.fromJson(jsonDecode(jsonStr));
              String? type = customMsgEntity.type;
              if (type == ChatImCustomMsgType.ChatGreetTipsMsg) {
                return Container();
              }
            }
          }
        }
        String? avatarFrameUrl =
            DressUtils().getMessageUserDressUrl(message, DressUpType.avatar);
        TodoTogetherDetailController c =
            Get.find<TodoTogetherDetailController>();
        if (message.isSelf == true) {
          return AvatarWidget(
            size: 50,
            url: c.isLook
                ? (UserService().user?.sex == 0
                    ? Assets.imagesTodoTogetherDetailStartMale
                    : Assets.imagesTodoTogetherDetailStartFemale)
                : (c.detail?.stage1?.mySex == 0
                    ? Assets.imagesTodoTogetherDetailStartMale
                    : Assets.imagesTodoTogetherDetailStartFemale),
            dressUp: avatarFrameUrl,
            showPlaceholder: true,
            showDressUp: true,
          );
        } else {
          return AvatarWidget(
            size: 50,
            url: c.isLook
                ? (c.model?.taskSex == 0
                    ? Assets.imagesTodoTogetherDetailStartMale
                    : Assets.imagesTodoTogetherDetailStartFemale)
                : (c.detail?.stage1?.otherSex == 0
                    ? Assets.imagesTodoTogetherDetailStartMale
                    : Assets.imagesTodoTogetherDetailStartFemale),
            dressUp: avatarFrameUrl,
            showPlaceholder: true,
            showDressUp: true,
          );
        }
      },
      config: TIMUIKitChatConfig(
        isAllowLongPressAvatarToAt: conversation?.type != 1,
        isShowGroupReadingStatus: false,
        isUseMessageReaction: false,
        isShowReadingStatus: false,
        // isAllowEmojiPanel: false,
        isAllowClickAvatar: false,
        stickerPanelConfig: StickerPanelConfig(
          useQQStickerPackage: false,
          useTencentCloudChatStickerPackage: false,
          customStickerPackages:
              EmojiUtils().customEmojiStickerPackageList ?? [],
        ),
      ),
      onTapAvatar: (userId, tapDetails) {
        // Get.toNamed(GetRouter.userProfile, parameters: {"userId": userId});
      },
      morePanelConfig: MorePanelConfig(
        showCameraAction: false,
        showGalleryPickAction: false,
        showFilePickAction: false,
        extraAction: _buildExtraMorePanelActions(),
      ),
      textFieldHintText: "请输入文字",
      toolTipsConfig: ToolTipsConfig(
        showForwardMessage: false,
        showMultipleChoiceMessage: false,
        showTranslation: false,
        additionalMessageToolTips: (message, closeToolTip) {
          if (message.elemType == MessageElemType.V2TIM_ELEM_TYPE_SOUND) {
            return [
              CustomAudioMsgTooltipItem.build(
                  message: message, closeTooltip: closeToolTip),
            ];
          }
          return [];
        },
      ),
      customEmojiStickerList: EmojiUtils().customEmojiFaceDataList ?? [],
      messageItemBuilder: MessageItemBuilder(
        customMessageItemBuilder:
            (V2TimMessage message, isShowJump, clearJump) {
          if (message.customElem != null &&
              message.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM) {
            return ChatCustomMsgItem(
              message: message,
              chatDetailController: controller,
            );
          }
          return Container();
        },
      ),
      onBottomChange: (height) {
        WidgetsBinding.instance.addPostFrameCallback((time) {
          controller.updateBottomHeight(height);
        });
      },
      messageItemThemeData: MessageThemeData(
        messageTextColor: (message) {
          String? colorStr = DressUtils().getMessageUserDressUrl(
              message, DressUpType.chatBox,
              propertyType: 1);
          if (colorStr?.isNotEmpty == true) {
            colorStr = "FF$colorStr";
            Color color = Color(int.parse(colorStr, radix: 16));
            return color;
          }
          return Colors.black;
        },
        messageBubbleBuilder: (message, child) {
          ///获取气泡框Url
          String? chatBubbleDressUrl =
              DressUtils().getMessageUserDressUrl(message, DressUpType.chatBox);
          if (!(chatBubbleDressUrl?.isNotEmpty == true)) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              constraints: const BoxConstraints(
                minWidth: 40,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: child,
            );
          }

          ///获取气泡框可拉伸区域
          String? chatBubbleSliceStr = DressUtils().getMessageUserDressUrl(
              message, DressUpType.chatBox,
              propertyType: 2);
          Rect? centerSlice =
              StringUtils.stringToRect(chatBubbleSliceStr ?? "");
          if (centerSlice != null) {
            centerSlice = Rect.fromLTRB(
                centerSlice.left / 3,
                centerSlice.top / 3,
                centerSlice.right / 3,
                centerSlice.bottom / 3);
          }
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            constraints: const BoxConstraints(
              minWidth: 40,
            ),
            decoration: BoxDecoration(
              image: DecorationImage(
                centerSlice:
                    centerSlice ?? const Rect.fromLTRB(18.5, 10, 20, 15),
                image: CachedNetworkImageProvider(chatBubbleDressUrl!),
                scale: 3,
                fit: BoxFit.fill,
              ),
            ),
            child: child,
          );
        },
      ),
      textFieldBuilder: Get.find<TodoTogetherDetailController>().fromType ==
              TodoTogetherListType.history
          ? (context) {
              return Container();
            }
          : null,
    );
  }

  Widget _buildCenterWidget() {
    return GetBuilder(
        init: controller,
        global: false,
        builder: (c) {
          return Text(
            _getAppBarTitle(),
            style: TextStyles.common(16.sp, Colors.white),
          );
        });
    /* return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _getAppBarTitle(),
              style: TextStyles.common(16.sp, Colors.black),
            ),
            GetBuilder(
              init: controller,
              global: false,
              filter: (controller) => controller.friendUserInfo?.isOnline == 1,
              builder: (controller) {
                if (controller.friendUserInfo == null) {
                  return Container();
                }
                return Visibility(
                  visible: controller.friendUserInfo?.isOnline == 1,
                  child: Padding(
                    padding: EdgeInsets.only(left: 5.w),
                    child: Container(
                      width: 6.w,
                      height: 6.w,
                      decoration: BoxDecoration(
                        color: AppColors.colorFF23AF28,
                        borderRadius: BorderRadius.circular(3.w),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        GetBuilder(
          init: controller,
          global: false,
          builder: (controller) {
            if (controller.friendUserInfo == null) {
              return Container();
            }
            UserInfoEntity userInfo =
                UserInfoEntity.fromJson(controller.friendUserInfo!.toJson());
            return UserMedalTagWidget(
              userInfo: userInfo,
              placement: Placement.bottom,
            );
          },
        ),
      ],
    ); */
  }

  String _getAppBarTitle() {
    return "信息盒";
    /* String title = "";
    if (conversation?.type != null) {
      if (conversation!.type == 1) {
        title = conversation!.showName ?? "";
      } else {
        String? groupName = conversation!.showName;
        if (groupName?.isNotEmpty == true) {
          title = "${conversation!.showName}";
          if (controller.groupInfo != null) {
            title =
                "${getGroupChatTitle(conversation!.showName!)}（${controller.groupInfo!.memberCount ?? 0}）";
          }
        }
      }
    }
    return title; */
  }

  Widget _buildRightBarBtnWidget() {
    return GetBuilder(
      init: controller,
      global: false,
      builder: (controller) {
        return _buildPrivateMoreBtn();
      },
    );
  }

  Widget _buildPrivateMoreBtn() {
    if (controller.friendUserInfo?.isFriend == 1) {
      return Padding(
        padding: EdgeInsets.only(right: 15.w),
        child: GestureDetector(
          onTap: () {
            if (controller.friendUserInfo != null) {
              Get.to(
                () => PrivateChatSettingsPage(
                    friendUserInfo: controller.friendUserInfo!),
              )?.then((result) {
                if (result == 1) {
                  controller.loadData();
                }
              });
            }
          },
          child: Icon(
            Icons.more_horiz,
            size: 20.w,
            color: AppColors.colorFF999999,
          ),
        ),
      );
    }
    return Container(
      width: 35.w,
    );
  }

  String getGroupChatTitle(String groupName) {
    if (groupName.length > 8) {
      return "${groupName.substring(0, 8)}...";
    }
    return groupName;
  }

  List<MorePanelItem> _buildExtraMorePanelActions() {
    List<MorePanelItem> list = [];
    list.add(
      MorePanelItem(
        id: "card_box",
        title: "卡盒",
        onTap: (c) {
          Get.bottomSheet(
            UserCardBoxBottomSheet(
              onSelectedCard: (cardInfo) {
                controller.sendCustomMsg(jsonEncode(cardInfo),
                    ChatImCustomMsgType.UserCardBoxCardMsg);
              },
            ),
          );
        },
        icon: Container(
          height: 50,
          width: 50,
          margin: const EdgeInsets.only(bottom: 4),
          alignment: Alignment.center,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(5))),
          child: ImageUtils.getImage(
              Assets.imagesChatDetailMorePanelCardBox, 20.w, 18.h),
        ),
      ),
    );
    return list;
  }

  @override
  void dispose() {
    controller.stopAudioPlayer();
    super.dispose();
  }
}
