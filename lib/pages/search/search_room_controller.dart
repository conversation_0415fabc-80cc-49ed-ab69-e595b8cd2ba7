import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/chat_room_list_result_entity.dart';
import 'package:dada/model/search_room_history_result_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';

class SearchRoomController extends GetxController {
  List<ChatRoomInfoEntity> searchResult = [];
  String? searchResultRecommend;

  SearchRoomHistoryResultEntity? historyResultEntity;

  @override
  void onReady() {
    super.onReady();

    loadData();
  }

  void loadData({bool? showLoading}) async {
    SearchRoomHistoryResultEntity? resultEntity =
        await ApiService().getSearchRoomHistory(showLoading: showLoading);
    historyResultEntity = resultEntity;
    update();
  }

  ///搜索房间
  void searchRoomWithKeyword(String keyword) async {
    if (!(keyword.isNotEmpty == true)) {
      return;
    }
    ChatRoomListResultEntity? resultEntity =
        await ApiService().searchRoomWithKeyword(keyword);
    if (resultEntity != null) {
      searchResultRecommend =
          resultEntity.isSearchResult == 0 ? "没有搜到完全一致，但找到了类似的~" : null;
      searchResult = resultEntity.roomVoLists ?? [];
      historyResultEntity?.searchHistoryList ??= [];
      historyResultEntity?.searchHistoryList?.add(keyword);
      update();
    }
  }

  void clearSearchHistoryList() async {
    if (historyResultEntity?.searchHistoryList != null) {
      bool success = await ApiService()
          .deleteHomeSearchHistoryList(historyResultEntity!.searchHistoryList!);
      if (success) {
        historyResultEntity!.searchHistoryList = [];
        update();
      }
    }
  }

  void deleteSearchHistoryKeyword(String keyword) async {
    if (historyResultEntity?.searchHistoryList != null) {
      bool success = await ApiService().deleteHomeSearchHistoryList([keyword]);
      if (success) {
        historyResultEntity!.searchHistoryList!.remove(keyword);
        update();
      }
    }
  }

  void topicPower(String topic, int count, {bool? shouldReload}) async {
    if (topic.isNotEmpty) {
      bool success = await ApiService().topicNomination(keyword: topic);
      if (success) {
        ToastUtils.showToast("助力成功");
        if (shouldReload == true) {
          loadData();
        } else {
          count++;
          historyResultEntity?.topicBoosterList?.map((e) {
            if (e.topic == topic) {
              e.topicCount = count;
            }
          });
          update();
        }
      }
    }
  }
}
