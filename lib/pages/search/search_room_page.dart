import 'dart:ui' as ui show PlaceholderAlignment;
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/search_room_history_result_entity.dart';
import 'package:dada/pages/search/search_room_controller.dart';
import 'package:dada/pages/search/topic_nomination_bottom_sheet.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SearchRoomPage extends StatefulWidget {
  const SearchRoomPage({super.key});

  @override
  State<SearchRoomPage> createState() => _SearchRoomPageState();
}

class _SearchRoomPageState extends State<SearchRoomPage> {
  TextEditingController textEditingController = TextEditingController();
  SearchRoomController controller = SearchRoomController();
  FocusNode focusNode = FocusNode();
  RxBool isFocusNode = false.obs;

  @override
  void initState() {
    super.initState();

    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        isFocusNode.value = true;
      } else {
        isFocusNode.value = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView(
        padding: EdgeInsets.zero,
        children: [
          _buildAppBar(),
          _buildBody(),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight, left: 0),
      height: 44.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 0.w),
            child: IconButton(
              onPressed: () => Get.back(),
              icon: Container(
                width: 28.w,
                height: 28.w,
                alignment: Alignment.center,
                child: ImageUtils.getImage(Assets.imagesNaviBack, 8.w, 14.w),
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 35.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(35.h / 2),
                color: AppColors.colorFFF5F5F5,
              ),
              child: Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 9.w),
                    child: ImageUtils.getImage(
                        Assets.imagesHomeSearchIcon, 15.w, 14.h),
                  ),
                  SizedBox(
                    width: 250.w,
                    height: 35.h,
                    child: CustomTextField.build(
                      contentPadding: EdgeInsets.only(left: 7.w),
                      controller: textEditingController,
                      hintText: "请输入房间号或话题关键字",
                      showSuffixIcon: true,
                      focusNode: focusNode,
                      textInputAction: TextInputAction.search,
                      suffixIconOnTap: () {
                        controller.update();
                      },
                      onChanged: (value) {},
                      onSubmitted: (value) {
                        controller.searchRoomWithKeyword(value);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 9.w, right: 12.w),
            child: GestureDetector(
              onTap: () {
                if (isFocusNode.value == true) {
                  focusNode.unfocus();
                  textEditingController.clear();
                  controller.searchResult.clear();
                  controller.update();
                } else {
                  controller.searchRoomWithKeyword(textEditingController.text);
                }
              },
              child: Obx(
                () => Text(
                  isFocusNode.value == true ? "取消" : "搜索",
                  style: TextStyles.common(16.sp, AppColors.colorFF23AF28),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return GetBuilder<SearchRoomController>(
      init: controller,
      builder: (controller) {
        if (textEditingController.text.isNotEmpty) {
          return _searchResultListWidget();
        }
        return _searchHistoryWidget();
      },
    );
  }

  Widget _searchResultListWidget() {
    if (controller.searchResultRecommend == null &&
        controller.searchResult.isEmpty) {
      return EmptyWidget(
        padding: EdgeInsets.only(top: ScreenUtil().screenHeight / 4),
      );
    }
    bool showTopTip = controller.searchResultRecommend != null &&
        controller.searchResult.isNotEmpty;
    return Column(
      children: [
        Visibility(
          visible: showTopTip,
          child: Padding(
            padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
            child: Text(
              controller.searchResultRecommend ?? "",
              style: TextStyles.normal(14.sp),
            ),
          ),
        ),
        SizedBox(
          height: ScreenUtil().screenHeight -
              ScreenUtil().statusBarHeight -
              ScreenUtil().bottomBarHeight -
              (showTopTip ? 44.h : 0),
          child: controller.searchResult.isNotEmpty
              ? GridView.extent(
                  padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 10.h),
                  maxCrossAxisExtent: 165.w,
                  childAspectRatio: 165.w / 150.h,
                  crossAxisSpacing: 15.w,
                  mainAxisSpacing: 15.w,
                  children: controller.searchResult
                      .map((e) => _buildListItem(e))
                      .toList(),
                )
              : const ListPageEmptyWidget(),
        ),
      ],
    );
  }

  Widget _buildListItem(ChatRoomInfoEntity itemEntity) {
    String bgImage = Assets.imagesHomeListBg1;
    String roomTypeStr = "去市集";
    String catImageAssetName = Assets.imagesChatRoomListCat1;
    double catImageWidth = 51.w;
    double catImageHeight = 32.h;
    if (itemEntity.roomType == ChatRoomType.fair.index) {
      bgImage = Assets.imagesHomeListBg1;
      roomTypeStr = "去市集";
    } else if (itemEntity.roomType == ChatRoomType.teapot.index) {
      bgImage = Assets.imagesHomeListBg2;
      roomTypeStr = "去茶壶";
    } else if (itemEntity.roomType == 3) {
      bgImage = Assets.imagesHomeListBg3;
      roomTypeStr = "去睡前卧谈会";
    }

    if (itemEntity.onlineNumber != null && itemEntity.onlineNumber! > 2) {
      catImageAssetName = Assets.imagesChatRoomListCat2;
      catImageWidth = 76.w;
      catImageHeight = 27.h;
    }

    return GestureDetector(
      onTap: () {
        if (!GlobalFloatingManager()
            .currentIsShowMiniWindow(roomInfo: itemEntity)) {
          Get.toNamed(GetRouter.chatRoomDetail,
              parameters: {"roomId": itemEntity.roomNo!});
        }
      },
      child: Container(
        width: 165.w,
        height: 150.h,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(bgImage),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 13.h),
              child: Row(
                children: [
                  SizedBox(width: 16.w),
                  ImageUtils.getImage(Assets.imagesHomeListItemIcon, 8.w, 12.h),
                  SizedBox(
                    width: 5.w,
                  ),
                  Text(
                    roomTypeStr,
                    style: TextStyles.normal(12.sp,
                        c: AppTheme.themeData.textTheme.headlineMedium?.color),
                  ),
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.only(right: 6.w),
                    child: Text(
                      "ID：${itemEntity.roomNo}",
                      style: TextStyles.common(12.sp, AppColors.colorFF666666),
                    ),
                  ),
                ],
              ),
            ),

            ///房间名称
            Visibility(
              visible: itemEntity.roomName?.isNotEmpty == true,
              child: Padding(
                padding: EdgeInsets.only(left: 14.w, top: 5.h),
                child: Text(
                  "名称：${itemEntity.roomName}",
                  style: TextStyles.common(12.sp, AppColors.colorFF666666),
                ),
              ),
            ),

            ///话题
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 15.w, right: 10.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      itemEntity.roomTopicList?.first.topicText ??
                          "没有固定话题，\n随便聊吧",
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyles.normal(16.sp),
                    ),
                  ],
                ),
              ),
            ),

            ///底部在线
            Padding(
              padding: EdgeInsets.only(left: 10.w, bottom: 15.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ImageUtils.getImage(
                      catImageAssetName, catImageWidth, catImageHeight),
                  Padding(
                    padding: EdgeInsets.only(right: 10.w),
                    child: Text(
                      "${itemEntity.onlineNumber ?? 0}${S.current.people}在线",
                      style: TextStyles.normal(14.sp,
                          c: AppTheme
                              .themeData.textTheme.headlineMedium?.color),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _searchHistoryWidget() {
    return Column(
      children: [
        _buildTopicNominationWidget(),
        Container(
          height: 10.h,
          color: AppColors.colorFFE5E5E5,
        ),
        _buildBottomContentWidget(),
      ],
    );
  }

  Widget _buildTopicNominationWidget() {
    return SizedBox(
      height: 60.h,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 25.w),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "（没有找到你要的话题？试试这个",
                    style: TextStyles.common(12.sp, AppColors.colorFF999999),
                  ),
                  WidgetSpan(
                    alignment: ui.PlaceholderAlignment.middle,
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 5.h),
                      child: ImageUtils.getImage(
                          Assets.imagesSearchRoomRightHand, 40.w, 40.w),
                    ),
                  ),
                  TextSpan(
                    text: " ）",
                    style: TextStyles.common(12.sp, AppColors.colorFF999999),
                  ),
                ],
              ),
            ),
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(right: 13.w),
            child: CommonGradientBtn(
              title: "话题提名",
              width: 92.w,
              height: 40.h,
              normalImage: Assets.imagesCommonGradientBtnBgShort,
              onTap: () {
                ToastUtils.showBottomDialog(
                  TopicNominationBottomSheet(
                    callback: (value) {
                      controller.topicPower(value, 0, shouldReload: true);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomContentWidget() {
    return SizedBox(
      height: ScreenUtil().screenHeight -
          10.h -
          60.h -
          ScreenUtil().statusBarHeight -
          44.h,
      child: ListView(
        shrinkWrap: true,
        padding: EdgeInsets.only(
            bottom: ScreenUtil().bottomBarHeight > 0
                ? (10.h + ScreenUtil().bottomBarHeight)
                : 20.h),
        children: [
          _buildSearchHistoryList(),
          _buildHotTopicListWidget(),
          _buildTopicNominationListWidget(),
        ],
      ),
    );
  }

  Widget _buildSearchHistoryList() {
    if (!(controller.historyResultEntity?.searchHistoryList?.isNotEmpty ==
        true)) {
      return Container();
    }
    List<String> searchList = [];
    if (controller.historyResultEntity!.searchHistoryList!.length > 10) {
      searchList = [
        ...controller.historyResultEntity!.searchHistoryList!.sublist(0, 10)
      ];
    } else {
      searchList = [...controller.historyResultEntity!.searchHistoryList!];
    }
    return Visibility(
      visible:
          controller.historyResultEntity?.searchHistoryList?.isNotEmpty == true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 19.w, top: 15.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "历史记录",
                  style: TextStyles.common(14.sp, AppColors.colorFF666666),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 33.w),
                  child: GestureDetector(
                    onTap: () {
                      controller.clearSearchHistoryList();
                    },
                    child: ImageUtils.getImage(
                        Assets.imagesSmallRoomBubbleWordItemDelete, 16.w, 16.w),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h, left: 19.w, right: 10.w),
            child: Wrap(
              runSpacing: 10.h,
              spacing: 5.w,
              children: searchList.map(
                (e) {
                  return _buildSeatHistoryListItemWidget(e);
                },
              ).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeatHistoryListItemWidget(String text) {
    return LabelItemWidget(
      text: text,
      height: 30.h,
      minWidth: 75.w,
      bgColor: AppColors.colorFFEAECF1,
      borderRadius: 15.r,
      textMaxLength: 20,
      borderColor: Colors.transparent,
      closeBtnColor: AppColors.colorFF999999,
      onTap: () {
        textEditingController.text = text;
        controller.searchRoomWithKeyword(text);
      },
      deleteAction: () {
        controller.deleteSearchHistoryKeyword(text);
      },
    );
  }

  Widget _buildHotTopicListWidget() {
    if (!(controller.historyResultEntity?.hotTopicList?.isNotEmpty == true)) {
      return Container();
    }
    List<String> hotList = [];
    if (controller.historyResultEntity!.hotTopicList!.length > 10) {
      hotList = [
        ...controller.historyResultEntity!.hotTopicList!.sublist(0, 10)
      ];
    } else {
      hotList = [...controller.historyResultEntity!.hotTopicList!];
    }
    return Padding(
      padding: EdgeInsets.only(top: 15.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 19.w),
            child: Text(
              "#热门话题",
              style: TextStyles.normal(16.sp),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(left: 16.w, top: 10.h),
            itemBuilder: (context, index) {
              String text = hotList[index];
              return GestureDetector(
                onTap: () {
                  textEditingController.text = text;
                  controller.searchRoomWithKeyword(text);
                },
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: ScreenUtil().screenWidth - 16.w * 2,
                  ),
                  child: Text(
                    text,
                    style: TextStyles.common(16.sp, AppColors.colorFF666666),
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 10.h,
              );
            },
            itemCount: hotList.length,
          )
        ],
      ),
    );
  }

  Widget _buildTopicNominationListWidget() {
    if (!(controller.historyResultEntity?.topicBoosterList?.isNotEmpty ==
        true)) {
      return Container();
    }
    List<SearchRoomHistoryResultTopicBoosterList> topicBoosterList = [];
    if (controller.historyResultEntity!.topicBoosterList!.length > 10) {
      topicBoosterList = [
        ...controller.historyResultEntity!.topicBoosterList!.sublist(0, 10)
      ];
    } else {
      topicBoosterList = [...controller.historyResultEntity!.topicBoosterList!];
    }
    return Padding(
      padding: EdgeInsets.only(top: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 19.w),
            child: Text(
              "#提名话题",
              style: TextStyles.normal(16.sp),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(left: 16.w, top: 10.h),
            itemBuilder: (context, index) {
              SearchRoomHistoryResultTopicBoosterList topic =
                  topicBoosterList[index];
              return GestureDetector(
                onTap: () {
                  textEditingController.text = topic.topic!;
                  controller.searchRoomWithKeyword(topic.topic!);
                },
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: ScreenUtil().screenWidth - 16.w - 13.w,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        constraints: BoxConstraints(
                          maxWidth: ScreenUtil().screenWidth -
                              16.w -
                              13.w -
                              50.w -
                              10.w,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${topic.topic}",
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyles.common(
                                  16.sp, AppColors.colorFF666666),
                            ),
                            Padding(
                              padding: EdgeInsets.only(top: 2.h),
                              child: Text(
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                "(${topic.topicCount}人提案，目前${topic.roomCount}房间，快来参与吧！快来参与吧！快来参与吧！）",
                                style: TextStyles.common(
                                    12.sp, AppColors.colorFF999999,
                                    h: 1.3),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 10.w, right: 13.w),
                        child: GestureDetector(
                          onTap: () {
                            if (topic.topic != null) {
                              controller.topicPower(
                                  topic.topic!, topic.topicCount ?? 0,
                                  shouldReload: true);
                            }
                          },
                          child: Container(
                            width: 50.w,
                            height: 25.h,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: AppColors.colorFF89E15C,
                              borderRadius: BorderRadius.circular(25.h / 2),
                            ),
                            child: Text(
                              "助力",
                              style: TextStyles.normal(14.sp),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 10.h,
              );
            },
            itemCount: topicBoosterList.length,
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // 移除监听器
    focusNode.dispose();
    super.dispose();
  }
}
