import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TopicNominationBottomSheet extends StatefulWidget {
  final Function(String value) callback;
  const TopicNominationBottomSheet({super.key, required this.callback});

  @override
  State<TopicNominationBottomSheet> createState() =>
      _TopicNominationBottomSheetState();
}

class _TopicNominationBottomSheetState
    extends State<TopicNominationBottomSheet> {
  TextEditingController textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GradientWidget(
      height: 258.h,
      width: ScreenUtil().screenWidth,
      cornerRadius: 20.r,
      stops: const [0, 0.3],
      colors: const [
        AppColors.colorFFD2F6C0,
        Colors.white,
      ],
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildContainer(),
          _buildCloseBtn(),
        ],
      ),
    );
  }

  Widget _buildCloseBtn() {
    return Positioned(
      right: 15.w,
      top: 15.h,
      child: GestureDetector(
        onTap: () {
          Get.back();
        },
        child: Icon(
          Icons.close,
          size: 20.w,
          color: AppColors.colorFF666666,
        ),
      ),
    );
  }

  Widget _buildContainer() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 21.h),
          child: Text(
            "话题提名",
            style: TextStyles.bold(16.sp, w: FontWeight.w500),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 26.h),
          child: Text(
            "请输入要提名的话题",
            style: TextStyles.common(16.sp, AppColors.colorFF666666),
          ),
        ),
        _buildInputTextField(),
        _buildBottomBtnWidget(),
      ],
    );
  }

  Widget _buildInputTextField() {
    return Container(
      margin: EdgeInsets.only(top: 25.h, left: 15.w, right: 15.w),
      height: 49.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49.h / 2),
        color: AppColors.colorFFF5F6F7,
      ),
      child: CustomTextField.build(
        contentPadding: EdgeInsets.symmetric(horizontal: 15.w),
        controller: textEditingController,
        maxLength: 20,
      ),
    );
  }

  Widget _buildBottomBtnWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 27.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Container(
              width: 130.w,
              height: 40.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(color: AppColors.colorFF999999),
              ),
              child: Text(
                "取消",
                style: TextStyles.common(16.sp, AppColors.colorFF666666),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 8.w),
            child: GestureDetector(
              onTap: () {
                widget.callback(textEditingController.text);
                Get.back();
              },
              child: Container(
                width: 143.w,
                height: 44.h,
                alignment: Alignment.center,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Assets.imagesCommonGradientBtnBg),
                  ),
                ),
                child: Text(
                  "确认",
                  style: TextStyles.common(16.sp, AppColors.colorFF666666),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
