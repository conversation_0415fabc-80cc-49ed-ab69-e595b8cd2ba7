import 'dart:math';
import 'dart:ui';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/profile/card/identity/user_identity_card_edit_page.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserIdCardInfoDialog extends StatelessWidget {
  final UserInfoEntity userInfo;
  final UserIdCardEntity cardInfo;
  final Function(int index)? callback; //0: 删除, 1:编辑
  final bool? canEditable; //可编辑

  const UserIdCardInfoDialog(
      {super.key,
      required this.userInfo,
      required this.cardInfo,
      this.callback,
      this.canEditable});

  @override
  Widget build(BuildContext context) {
    final Rx<bool?> liked = cardInfo.liked.obs;
    final Rx<int?> likedNo = cardInfo.likeNo.obs;
    bool isMySelf = userInfo.id == UserService().user?.id;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 320.w,
          child: Stack(
            children: [
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20.r),
                  child: ImageUtils.getImage(
                    Assets.imagesUserIdCardDetailDialogBg,
                    320.w,
                    320.w,
                    fit: BoxFit.fill,
                  ),
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  isMySelf && canEditable == true
                      ? Padding(
                          padding: EdgeInsets.only(top: 14.h, right: 10.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  Get.back();
                                  callback?.call(1);
                                },
                                child: ImageUtils.getImage(
                                    Assets.imagesUserIdCardDialogEdit,
                                    30.w,
                                    30.w),
                              ),
                              SizedBox(
                                width: 5.w,
                              ),
                              GestureDetector(
                                onTap: () {
                                  Get.back();
                                  callback?.call(0);
                                },
                                child: ImageUtils.getImage(
                                    Assets.imagesUserIdCardDialogDelete,
                                    30.w,
                                    30.w),
                              ),
                            ],
                          ),
                        )
                      : SizedBox(
                          height: 30.h,
                        ),
                  Container(
                    margin: EdgeInsets.only(
                        top: 30.h, left: 10.w, right: 10.w, bottom: 15.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.white,
                    ),
                    child: Container(
                      padding: EdgeInsets.all(10.w),
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(
                            Assets.imagesUserIdCardDialogContentBg,
                          ),
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                cardInfo.cardName ?? "",
                                style: TextStyles.medium(18.sp),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 25.h,
                          ),

                          ///头像、昵称、ID、区服
                          _buildIdCardUserInfoWidget(),

                          ///标签
                          _buildIdCardLabelsWidget(),

                          ///点赞、@用户名
                          Padding(
                            padding: EdgeInsets.only(top: 20.h),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Obx(
                                  () => Row(
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          liked.value = !(liked.value ?? false);
                                          if (liked.value == true) {
                                            likedNo.value =
                                                (likedNo.value ?? 0) + 1;
                                          } else {
                                            likedNo.value =
                                                (likedNo.value ?? 0) - 1;
                                          }
                                          operationCard(cardInfo.cardId!,
                                              liked.value ?? false);
                                        },
                                        child: ImageUtils.getImage(
                                          liked.value == true
                                              ? Assets
                                                  .imagesUserIdCardDialogLiked
                                              : Assets
                                                  .imagesUserIdCardDialogUnlike,
                                          16.w,
                                          16.w,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 3.w,
                                      ),
                                      Text(
                                        "${likedNo.value ?? ""}",
                                        style: TextStyles.common(
                                            14.sp,
                                            liked.value == true
                                                ? AppColors.colorFFF79F51
                                                : AppColors.colorFF999999),
                                      ),
                                    ],
                                  ),
                                ),
                                Text(
                                  "@${userInfo.nickname}",
                                  style: TextStyles.common(
                                      14.sp, AppColors.colorFF999999),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
              Visibility(
                visible: isMySelf,
                child: Positioned(
                  top: 12.5.h,
                  right: 12.5.w,
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          Get.back();
                          Get.to(
                            () => UserIdCardEditPage(cardInfo: cardInfo),
                            arguments: {"title": S.current.editIDCard},
                          )?.then((value) {
                            callback?.call(value);
                          });
                        },
                        child: ImageUtils.getImage(
                            Assets.imagesUserLabelDetailDialogEdit, 30.w, 30.w),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        GestureDetector(
          onTap: () {
            Get.back();
          },
          child: Padding(
            padding: EdgeInsets.only(top: 20.h),
            child:
                ImageUtils.getImage(Assets.imagesDialogBottomClose, 40.w, 40.w),
          ),
        ),
      ],
    );
  }

  Widget _buildIdCardUserInfoWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        ClipOval(
          child: ImageUtils.getImage(
              cardInfo.cardUrl ?? userInfo.avatar ?? "", 70.w, 70.w,
              fit: BoxFit.cover),
        ),
        SizedBox(
          width: 5.w,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///昵称
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  alignment: Alignment.centerRight,
                  child: Text(
                    "${S.current.nickname}：",
                    style: TextStyles.normal(16.sp),
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(top: 1.h),
                  constraints: BoxConstraints(
                    maxWidth: 150.w,
                  ),
                  child: Text.rich(
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    TextSpan(children: [
                      TextSpan(
                        text: cardInfo.cardNickname ?? "",
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF666666),
                      ),
                      TextSpan(
                        text: cardInfo.cardNickname?.isNotEmpty == true
                            ? " 复制"
                            : "",
                        style:
                            TextStyles.common(12.sp, AppColors.colorFF23AF28),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Clipboard.setData(ClipboardData(
                                text: cardInfo.cardNickname ?? ""));
                            ToastUtils.showToast("已复制到剪切板");
                          },
                      ),
                    ]),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 6.h,
            ),

            ///ID
            Row(
              children: [
                Container(
                  width: 49.w,
                  alignment: Alignment.centerRight,
                  child: Text(
                    "ID：",
                    style: TextStyles.normal(16.sp),
                  ),
                ),
                Container(
                  constraints: BoxConstraints(
                    maxWidth: 130.w,
                  ),
                  child: Text.rich(
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    TextSpan(children: [
                      TextSpan(
                        text: cardInfo.gameId ?? "",
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF666666),
                      ),
                      TextSpan(
                        text: cardInfo.gameId?.isNotEmpty == true ? " 复制" : "",
                        style:
                            TextStyles.common(12.sp, AppColors.colorFF23AF28),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Clipboard.setData(ClipboardData(
                                text: cardInfo.gameId ?? ""));
                            ToastUtils.showToast("已复制到剪切板");
                          },
                      )
                    ]),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 6.h,
            ),

            ///区服
            Row(
              children: [
                Container(
                  width: 49.w,
                  alignment: Alignment.centerRight,
                  child: Text(
                    "${S.current.serverName}：",
                    style: TextStyles.normal(16.sp),
                  ),
                ),
                Container(
                  constraints: BoxConstraints(
                    maxWidth: 123.w,
                  ),
                  child: Text(
                    cardInfo.serverName ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyles.common(16.sp, AppColors.colorFF666666),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildIdCardLabelsWidget() {
    if (cardInfo.cardText == null || cardInfo.cardText?.isEmpty == true) {
      return Container(
        height: 100.h,
        margin: EdgeInsets.only(top: 15.h),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: AppColors.colorFFF8F8F8,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: ImageUtils.getImage(
            Assets.imagesUserLabelDetailDialogSublistEmpty, 43.w, 36.h),
      );
    }
    return Container(
      width: 320.w - 25.w * 2,
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.only(top: 16.h, left: 7.w, right: 7.w),
      constraints: BoxConstraints(
        minHeight: 100.h,
        maxHeight: 100.h,
      ),
      decoration: BoxDecoration(
        color: AppColors.colorFFF8F8F8,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.zero,
        child: Wrap(
          spacing: 5.w,
          runSpacing: 10.h,
          children: cardInfo.cardText!.map((e) {
            return Container(
              height: 30.h,
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              constraints: BoxConstraints(
                minWidth: 79.w,
              ),
              decoration: BoxDecoration(
                color: AppColors.colorFFEAECF1,
                borderRadius: BorderRadius.circular(15.r),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    e,
                    style:
                        TextStyles.common(14.sp, AppColors.colorFF666666),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Future<bool?> operationCard(String cardId, bool required) async {
    bool? success = await ApiService()
        .operationCard(cardId: cardId, operationType: "1", required: required);
    return success;
  }
}
