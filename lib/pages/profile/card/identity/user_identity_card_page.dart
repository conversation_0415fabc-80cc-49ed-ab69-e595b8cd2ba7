import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/profile/card/identity/user_identity_card_controller.dart';
import 'package:dada/pages/profile/card/identity/user_identity_card_edit_page.dart';
import 'package:dada/pages/profile/card/identity/user_identity_card_info_dialog.dart';
import 'package:dada/pages/profile/card/user_card_box_item_widget.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserIdentityCardPage extends StatefulWidget {
  const UserIdentityCardPage({super.key});

  @override
  State<UserIdentityCardPage> createState() => _UserIdentityCardPageState();
}

class _UserIdentityCardPageState extends State<UserIdentityCardPage> {
  final controller = Get.put(UserIdCardController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
        title: S.current.idCard,
      ),
      body: Stack(
        children: [
          _buildGradientBgWidget(),
          _buildCardList(),
          _buildAddCardBtn(),
        ],
      ),
    );
  }

  Widget _buildGradientBgWidget() {
    return GradientWidget(
      width: ScreenUtil().screenWidth,
      height: ScreenUtil().screenHeight,
      stops: const [0, 0.45],
    );
  }

  Widget _buildCardList() {
    return Container(
      padding: EdgeInsets.only(top: 56 + ScreenUtil().statusBarHeight),
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            child: Text(
              "忙碌之余，游戏放松一下可以让身心更健康哦~",
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.colorFF999999,
              ),
            ),
          ),
          Expanded(
            child: RefreshWidget.build(
              refreshController: controller.refreshController,
              onRefresh: () => controller.refreshData(),
              //onLoadMore: () => controller.loadMoreData(),
              child: GetBuilder<UserIdCardController>(
                init: controller,
                builder: (controller) {
                  if (controller.data.isEmpty) {
                    return ListPageEmptyWidget(
                      bottomBarHeight: 0,
                      child: Container(
                        margin: EdgeInsets.only(top: 9.h),
                        alignment: Alignment.center,
                        child: Text(
                          "忙碌之余，游戏放松一下可以让身心更健康哦~",
                          style: TextStyles.common(12.sp, AppColors.colorFF999999),
                        ),
                      ),
                    );
                  }
                  return GridView.builder(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
                    gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                      maxCrossAxisExtent: 110.w,
                      childAspectRatio: 110.w / 180.h,
                      crossAxisSpacing: 7.5.w,
                      mainAxisSpacing: 15.w,
                    ),
                    itemCount: controller.data.length,
                    itemBuilder: (ctx, index) {
                      UserIdCardEntity entity = controller.data[index];
                      return _buildCardItem(entity, index);
                    },
                  );
                },
                id: controller.refreshId,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardItem(UserIdCardEntity entity, int index) {
    bool isMySelf = controller.userInfo.id == UserService().user?.id;
    return UserCardBoxItemWidget(
      cardInfo: entity,
      onTap: () {
        if (entity.cardState == 1 || isMySelf) {
          ToastUtils.showDialog(
            dialog: UserIdCardInfoDialog(
              userInfo: controller.userInfo,
              cardInfo: entity,
              canEditable: false,
              callback: (value) {
                controller.refreshData();
              },
            ),
          );
        } else {
          ToastUtils.showToast(S.current.cardInfoUnOpen);
        }
        // if (isMySelf) {
        //   Get.to(
        //     () => UserIdCardEditPage(cardInfo: entity),
        //     arguments: {"title": S.current.editIDCard},
        //   )?.then((value) => controller.refreshData());
        // } else {
        //
        // }
      },
    );
  }

  Widget _buildAddCardBtn() {
    UserInfoEntity? userInfoEntity = Get.arguments;
    bool isMySelf = userInfoEntity?.id == UserService().user?.id;
    if (!isMySelf) {
      return Container();
    }
    return Positioned(
      bottom: 70.35,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Get.to(() => const UserIdCardEditPage(), arguments: {"title": null})
              ?.then((value) => controller.refreshData());
        },
        child: ImageUtils.getImage(Assets.imagesUserIdCardAdd, 120.w, 45.h),
      ),
    );
  }
}
