import 'dart:io';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class UserIdCardEditPage extends StatefulWidget {
  final UserIdCardEntity? cardInfo;

  const UserIdCardEditPage({super.key, this.cardInfo});

  @override
  State<UserIdCardEditPage> createState() => _UserIdCardEditPageState();
}

class _UserIdCardEditPageState extends State<UserIdCardEditPage> {
  TextEditingController nameEditingController = TextEditingController();
  TextEditingController gameNameEditingController = TextEditingController();
  TextEditingController gameIdEditingController = TextEditingController();
  TextEditingController serverNameEditingController = TextEditingController();

  String? title;

  RxList<String> subLabels = <String>[].obs;
  RxBool isOpen = true.obs;
  Rx<String?> selectedAsset = Rx<String?>(null);

  @override
  void initState() {
    super.initState();

    title = Get.arguments["title"];
    nameEditingController.text = widget.cardInfo?.cardName ?? "";
    gameNameEditingController.text = widget.cardInfo?.cardNickname ?? "";
    gameIdEditingController.text = widget.cardInfo?.gameId ?? "";
    serverNameEditingController.text = widget.cardInfo?.serverName ?? "";

    List<String> list = [];
    if (widget.cardInfo?.cardText != null) {
      list.addAll(widget.cardInfo!.cardText!);
    }
    subLabels.value = list;
    if (widget.cardInfo != null) {
      isOpen.value = widget.cardInfo?.cardState == 1 ? true : false;
    } else {
      isOpen.value = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: title ?? S.current.addIDCard,
        rightWidgets: widget.cardInfo != null
            ? [
                GestureDetector(
                  onTap: () {
                    if (widget.cardInfo != null) {
                      ToastUtils.showDialog(
                          hideTopImg: true,
                          content: "确定要删除身份卡吗？",
                          onConfirm: () {
                            deleteCard(widget.cardInfo!.cardId!);
                          });
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.only(left: 10.w, right: 10.w),
                    child: ImageUtils.getImage(
                        Assets.imagesSmallRoomMailMessageDelete, 16.w, 15.w),
                  ),
                ),
              ]
            : null,
      ),
      body: Container(
        color: AppTheme.themeData.colorScheme.surfaceTint,
        child: ListView(
          children: [
            Container(
              margin: EdgeInsets.only(top: 20.h, left: 15.w, right: 15.w),
              padding: EdgeInsets.only(
                  top: 25.h, left: 18.w, right: 18.w, bottom: 24.h),
              decoration: BoxDecoration(
                color: AppTheme.themeData.colorScheme.primary,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTopWidget(),
                  _buildIdCardRoleInfoWidget(),
                  _buildSubLabelListWidget(),
                  _buildSwitchWidget(),
                  _buildUserName(),
                  _buildBottomBtn(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopWidget() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          "*",
                          style: TextStyles.common(
                            16.sp,
                            AppColors.colorFFE22D2D,
                          ),
                        ),
                        Text(
                          S.current.idCardField,
                          style: TextStyles.normal(16.sp),
                        ),
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 3.w, top: 3.h),
                      child: Text(
                        S.current.labelNameTip,
                        style:
                            TextStyles.common(14.sp, AppColors.colorFF999999),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            _buildCommonTextField(
              nameEditingController,
              width: 130.w,
              borderColor: AppColors.colorFF999999,
              maxLength: 9,
            ),
          ],
        ),
        SizedBox(
          height: 8.h,
        ),
        Container(
          margin: EdgeInsets.only(left: 3.w, right: 3.w),
          height: 1.h,
          color: AppColors.colorFFD8D8D8,
        ),
      ],
    );
  }

  Widget _buildIdCardRoleInfoWidget() {
    UserInfoEntity? userInfo = UserService().user;
    return Padding(
      padding: EdgeInsets.only(top: 23.h, left: 3.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              ToastUtils.showBottomSheet(
                [S.current.album, S.current.camera],
                onTap: (index) async {
                  bool res = await ImagePickerUtil.checkPermission(
                      index == 0 ? 1 : 2,
                      index == 0 ? "相册权限说明" : "相机权限说明",
                      index == 0
                          ? "获取图片用于更换卡片信息"
                          : "拍摄图片用于更换卡片信息, 拍摄后的图片将存放在系统照片中");

                  if (!res) return;

                  AssetEntity? assetEntity;

                  if (index == 0) {
                    List<AssetEntity>? assets =
                        await ImagePickerUtil.selectAsset();
                    if (assets != null) {
                      assetEntity = assets.first;
                      // selectedAsset.value = assets.first;
                    }
                  } else {
                    AssetEntity? asset =
                        await ImagePickerUtil.takeAsset(enableRecording: false);
                    if (asset != null) {
                      assetEntity = asset;
                      // selectedAsset.value = asset;
                    }
                  }
                  if (assetEntity != null) {
                    File? file = await assetEntity.loadFile(isOrigin: false);
                    String? croppedPath =
                        await ImageUtils.cropImage(file?.path ?? "");
                    selectedAsset.value = croppedPath;
                  }
                },
              );
            },
            child: ClipOval(
              child: Obx(
                () {
                  if (selectedAsset.value != null) {
                    return Container(
                      width: 70.w,
                      height: 70.w,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          fit: BoxFit.cover,
                          image: FileImage(File(selectedAsset.value ?? "")),
                        ),
                      ),
                      child: Container(
                        width: 70.w,
                        height: 25.h,
                        color: Colors.black.withOpacity(0.5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ImageUtils.getImage(
                                Assets.imagesCommonEditWhite, 12.w, 12.w),
                            Padding(
                              padding: EdgeInsets.only(left: 2.w),
                              child: Text(
                                "编辑",
                                style: TextStyles.common(12.sp, Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
                  return Stack(
                    children: [
                      ImageUtils.getImage(
                        widget.cardInfo?.cardUrl ?? userInfo?.avatar ?? "",
                        70.w,
                        70.w,
                        fit: BoxFit.cover,
                      ),
                      Positioned(
                        bottom: 0,
                        child: Container(
                          width: 70.w,
                          height: 25.h,
                          color: Colors.black.withOpacity(0.5),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ImageUtils.getImage(
                                  Assets.imagesCommonEditWhite, 12.w, 12.w),
                              Padding(
                                padding: EdgeInsets.only(left: 2.w),
                                child: Text(
                                  "编辑",
                                  style: TextStyles.common(12.sp, Colors.white),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
          SizedBox(
            width: 5.w,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///昵称
              Row(
                children: [
                  Container(
                    width: 49.w,
                    alignment: Alignment.centerRight,
                    child: Text(
                      "${S.current.nickname}：",
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                  _buildCommonTextField(gameNameEditingController,
                      maxLength: 9),
                ],
              ),
              SizedBox(
                height: 10.h,
              ),

              ///ID
              Row(
                children: [
                  Container(
                    width: 49.w,
                    alignment: Alignment.centerRight,
                    child: Text(
                      "ID：",
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                  _buildCommonTextField(
                    gameIdEditingController,
                    maxLength: 16,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    ],
                  ),
                ],
              ),
              SizedBox(
                height: 10.h,
              ),

              ///区服
              Row(
                children: [
                  Container(
                    width: 49.w,
                    alignment: Alignment.centerRight,
                    child: Text(
                      "${S.current.serverName}：",
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                  _buildCommonTextField(serverNameEditingController,
                      maxLength: 6),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCommonTextField(TextEditingController controller,
      {double? width,
      Color? borderColor,
      int? maxLength,
      TextInputType? keyboardType,
      List<TextInputFormatter>? inputFormatters}) {
    return Container(
      width: width ?? 150.w,
      height: 30.h,
      decoration: BoxDecoration(
        color: AppColors.colorFFF5F5F5,
        borderRadius: BorderRadius.circular(5.r),
        border: Border.all(
            color: borderColor ?? AppColors.colorFFD8D8D8, width: 1.h),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 3.h, bottom: 3.h),
        child: CustomTextField.build(
          contentPadding: EdgeInsets.only(left: 6.w, right: 6.w),
          controller: controller,
          maxLength: maxLength,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
        ),
      ),
    );
  }

  Widget _buildSubLabelListWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 15.h, left: 4.w, bottom: 10.h),
          child: Obx(() {
            List<String> list = subLabels;
            if (list.isEmpty) {
              if (!list.contains("")) {
                list.add("");
              }
            } else if (list.length < 8) {
              if (!list.contains("")) {
                list.add("");
              }
            } else if (list.length > 8) {
              list = list.sublist(0, 8);
            }
            return Container(
              constraints: BoxConstraints(
                minHeight: 110.w,
              ),
              child: Wrap(
                spacing: 10.w,
                runSpacing: 10.w,
                children: _buildSubLabelListChildren(list),
              ),
            );
          }),
        ),
      ],
    );
  }

  List<Widget> _buildSubLabelListChildren(List<String> data) {
    List<Widget> list = <Widget>[];
    for (int i = 0; i < data.length; i++) {
      String text = data[i];
      list.add(_buildSubLabelItem(text, i));
    }
    return list;
  }

  Widget _buildSubLabelItem(String text, int index) {
    if (text.isEmpty) {
      return LabelItemWidget(
        text: "添加子标签",
        height: 30.h,
        isAddItem: true,
        fontSize: 14.sp,
        textColor: AppColors.colorFF666666,
        borderRadius: 15.r,
        addAction: (text) {
          if (text.isNotEmpty) {
            subLabels.insert(subLabels.length - 1, text);
          }
        },
      );
    }

    return LabelItemWidget(
      text: text,
      textColor: AppColors.colorFF666666,
      padding: EdgeInsets.only(left: 8.w, right: 6.w),
      fontSize: 14.sp,
      borderRadius: 15.r,
      closeBtnColor: AppColors.colorFF999999,
      borderColor: Colors.transparent,
      bgColor: AppColors.colorFFEAECF1,
      deleteAction: () {
        subLabels.removeAt(index);
      },
    );
  }

  Widget _buildSwitchWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          S.current.open,
          style: TextStyles.normal(16.sp),
        ),
        SizedBox(
          width: 8.w,
        ),
        Obx(
          () => Switch(
            value: isOpen.value,
            onChanged: (value) {
              isOpen.value = !isOpen.value;
            },
            inactiveThumbColor: Colors.white,
            inactiveTrackColor: AppColors.colorFFD3D3D3,
            activeColor: Colors.white,
            activeTrackColor: AppColors.colorFF65D06A,
            trackOutlineColor:
                WidgetStateProperty.all<Color>(Colors.transparent),
            trackOutlineWidth: WidgetStateProperty.all<double>(0),
          ),
        ),
      ],
    );
  }

  Widget _buildUserName() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 9.h, right: 15.w),
          child: Text(
            "@${UserService().user?.nickname}",
            style: TextStyles.common(14.sp, AppColors.colorFF999999),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBtn() {
    return GestureDetector(
      onTap: () {
        postUpdateCardRequest();
      },
      child: Container(
        alignment: Alignment.center,
        height: 45.h,
        margin: EdgeInsets.only(left: 8.w, right: 8.w, top: 23.h, bottom: 20.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(45.h / 2),
          gradient: const LinearGradient(
            colors: [AppColors.colorFF96F09B, AppColors.colorFF61CD66],
          ),
        ),
        child: Text(
          S.current.ok,
          style: TextStyles.medium(18.sp, c: Colors.white),
        ),
      ),
    );
  }

  Future<void> postUpdateCardRequest() async {
    if (nameEditingController.text.isEmpty) {
      ToastUtils.showToast(S.current.addCardEmptyTip);
      return;
    }
    String? cardUrl;
    if (selectedAsset.value != null) {
      // File? imageFile = await selectedAsset.value?.loadFile(isOrigin: false);
      File imageFile = File(selectedAsset.value ?? "");
      cardUrl = await ApiService().uploadFile(imageFile.path);
    }
    bool success = await ApiService().addUserCard(
      cardType: "1",
      cardId: widget.cardInfo?.cardId,
      cardName: nameEditingController.text,
      cardNickname: gameNameEditingController.text,
      gameId: gameIdEditingController.text,
      serverName: serverNameEditingController.text,
      cardText: subLabels.isEmpty ? null : subLabels,
      cardState: isOpen.value == true ? "1" : "0",
      //switch控件圆点在左边是false状态，在右边才是开启状态。
      cardUrl: cardUrl,
    );
    if (success) {
      ApiService().doTask(taskId: "7");
      Get.back(result: 1);
    }
  }

  Future<bool?> deleteCard(String cardId) async {
    bool success = await ApiService().deleteUserCard(cardId: cardId);
    if (success) {
      Get.back(result: 1);
    }
    return true;
  }
}
