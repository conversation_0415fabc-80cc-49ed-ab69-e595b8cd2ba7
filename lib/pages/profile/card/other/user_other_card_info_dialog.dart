import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserOtherCardInfoDialog extends StatelessWidget {
  final UserIdCardEntity cardInfo;

  const UserOtherCardInfoDialog({super.key, required this.cardInfo});

  @override
  Widget build(BuildContext context) {
    final Rx<bool?> known = cardInfo.known.obs;
    final Rx<int?> knowNo = cardInfo.knowNo.obs;
    final Rx<bool?> read = cardInfo.read.obs;
    final Rx<int?> readNo = cardInfo.readNo.obs;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(20.r),
          child: Container(
            width: 320.w,
            height: 573.h,
            color: Colors.white,
            child: Column(
              children: [
                ImageUtils.getImage(cardInfo.cardUrl ?? "", 320.w, 523.h,
                    fit: BoxFit.cover, showPlaceholder: false),
                Padding(
                  padding: EdgeInsets.only(left: 16.w, top: 15.h),
                  child: Row(
                    children: [
                      Obx(
                        () => GestureDetector(
                          onTap: () {
                            known.value = !(known.value ?? false);
                            if (known.value == true) {
                              knowNo.value = (knowNo.value ?? 0) + 1;
                            } else {
                              knowNo.value = (knowNo.value ?? 0) - 1;
                              if (knowNo.value == 0) {
                                knowNo.value = null;
                              }
                            }
                            operationCard(cardInfo.cardId!, "3", known.value ?? false);
                          },
                          child: Row(
                            children: [
                              ImageUtils.getImage(
                                  known.value == true
                                      ? Assets.imagesUserOtherCardDialogKnow
                                      : Assets.imagesUserOtherCardDialogUnknown,
                                  16.w,
                                  16.w),
                              SizedBox(
                                width: 2.w,
                              ),
                              Text(
                                S.current.know,
                                style: TextStyles.common(
                                    14.sp,
                                    known.value == true
                                        ? AppColors.colorFFF79F51
                                        : AppColors.colorFF666666),
                              ),
                              SizedBox(
                                width: 6.w,
                              ),
                              Text(
                                "${knowNo.value ?? ""}",
                                style: TextStyles.common(
                                  14.sp,
                                  known.value == true
                                      ? AppColors.colorFFF79F51
                                      : AppColors.colorFF666666,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 30.w,
                      ),
                      Obx(
                        () => GestureDetector(
                          onTap: () {
                            read.value = !(read.value ?? false);
                            if (read.value == true) {
                              readNo.value = (readNo.value ?? 0) + 1;
                            } else {
                              readNo.value = (readNo.value ?? 0) - 1;
                              if (readNo.value == 0) {
                                readNo.value = null;
                              }
                            }
                            operationCard(cardInfo.cardId!, "2", read.value ?? false);
                          },
                          child: Row(
                            children: [
                              ImageUtils.getImage(
                                  read.value == true
                                      ? Assets.imagesUserOtherCardDialogRead
                                      : Assets.imagesUserOtherCardDialogUnread,
                                  16.w,
                                  16.w),
                              SizedBox(
                                width: 2.w,
                              ),
                              Text(
                                S.current.read,
                                style: TextStyles.common(
                                    14.sp,
                                    read.value == true
                                        ? AppColors.colorFFF79F51
                                        : AppColors.colorFF666666),
                              ),
                              SizedBox(
                                width: 6.w,
                              ),
                              Text(
                                "${readNo.value ?? ""}",
                                style: TextStyles.common(
                                  14.sp,
                                  read.value == true
                                      ? AppColors.colorFFF79F51
                                      : AppColors.colorFF666666,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            Get.back();
          },
          child: Padding(
            padding: EdgeInsets.only(top: 20.h),
            child:
            ImageUtils.getImage(Assets.imagesDialogBottomClose, 40.w, 40.w),
          ),
        ),
      ],
    );
  }

  Future<bool?> operationCard(
      String cardId, String operationType, bool required) async {
    bool? success = await ApiService().operationCard(
        cardId: cardId, operationType: operationType, required: required);
    return success;
  }
}
