import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/profile/card/other/user_other_card_controller.dart';
import 'package:dada/pages/profile/card/other/user_other_card_info_dialog.dart';
import 'package:dada/pages/profile/card/user_card_box_item_widget.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserOtherCardPage extends StatefulWidget {
  const UserOtherCardPage({super.key});

  @override
  State<UserOtherCardPage> createState() => _UserOtherCardPageState();
}

class _UserOtherCardPageState extends State<UserOtherCardPage> {
  final controller = UserOtherCardController();

  String? title;

  @override
  void initState() {
    super.initState();

    title = Get.parameters["title"];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
        title: title ?? S.current.goodCard,
      ),
      body: Stack(
        children: [
          _buildGradientBgWidget(),
          _buildCardList(),
          _buildAddCardBtn(),
        ],
      ),
    );
  }

  Widget _buildGradientBgWidget() {
    return Container(
      width: ScreenUtil().screenWidth,
      height: ScreenUtil().screenHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0, 0.45],
          colors: [
            AppColors.colorFFADF0B0,
            AppColors.color00E4FFBA,
          ],
        ),
      ),
    );
  }

  Widget _buildCardList() {
    return Container(
      padding: EdgeInsets.only(top: 56 + ScreenUtil().statusBarHeight),
      child: RefreshWidget.build(
        refreshController: controller.refreshController,
        onRefresh: () => controller.refreshData(),
        child: GetBuilder<UserOtherCardController>(
          init: controller,
          global: false,
          builder: (controller) {
            if (controller.data.isEmpty) {
              return EmptyWidget();
            }
            return GridView.builder(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
              gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                maxCrossAxisExtent: 110.w,
                childAspectRatio: 110.w / (180.h + 34.h),
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 20.w,
              ),
              itemCount: controller.data.length,
              itemBuilder: (ctx, index) {
                UserIdCardEntity entity = controller.data[index];
                return _buildCardItem(entity, index);
              },
            );
          },
          id: controller.refreshId,
        ),
      ),
    );
  }

  Widget _buildCardItem(UserIdCardEntity entity, int index) {
    return UserCardBoxItemWidget(
      cardInfo: entity,
      showDelete: controller.userInfo.id == UserService().user?.id,
      onTap: () {
        ToastUtils.showDialog(
          dialog: UserOtherCardInfoDialog(cardInfo: entity),
        );
      },
      onDelete: () {
        ToastUtils.showDialog(
          content: S.current.dialogCommonDeleteTitle,
          onConfirm: () {
            controller.deleteCardAtIndex(
                index, entity.cardId!);
          },
        );
      },
    );
  }

  Widget _buildAddCardBtn() {
    UserInfoEntity? userInfoEntity = Get.arguments;
    bool isMySelf = userInfoEntity?.id == UserService().user?.id;
    if (!isMySelf) {
      return Container();
    }
    return Positioned(
      bottom: 70.35,
      right: 0,
      child: GestureDetector(
        onTap: () {
          String title = "${S.current.add}${Get.parameters["title"]}";
          if (!Get.parameters["title"]!.contains(S.current.add)) {
            title = "${S.current.add}${Get.parameters["title"]}";
          } else {
            title = "${Get.parameters["title"]}";
          }
          String? cardType = Get.parameters["cardType"];
          Get.toNamed(GetRouter.addOtherCard,
                  parameters: {"title": title, "cardType": cardType ?? ""})
              ?.then((value) {
            if (value != null) {
              controller.refreshData();
            }
          });
        },
        child: ImageUtils.getImage(Assets.imagesUserIdCardAdd, 120.w, 45.h),
      ),
    );
  }
}
