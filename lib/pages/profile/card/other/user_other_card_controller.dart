import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:get/get.dart';

class UserOtherCardController
    extends ListPageController<UserIdCardEntity, UserOtherCardController> {
  late UserInfoEntity userInfo;

  @override
  void onInit() {
    super.onInit();

    userInfo = Get.arguments ?? UserService().user;

    if (userInfo.id != UserService().user?.id) {
      ApiService().doTask(taskId: "7");
    }
  }

  @override
  Future<List<UserIdCardEntity>?> loadData(int page) async {
    String? cardType = Get.parameters["cardType"];
    List<UserIdCardEntity>? list = await ApiService().getUserCardList(
        cardType: cardType ?? "2", pageIndex: page, userId: userInfo.id);
    return list;
  }

  Future<bool?> deleteCardAtIndex(int index, String cardId) async {
    bool success = await ApiService().deleteUserCard(cardId: cardId);
    if (success) {
      data.removeAt(index);
      update([refreshId]);
    }
    return true;
  }
}
