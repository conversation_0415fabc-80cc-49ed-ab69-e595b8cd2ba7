import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UserCardBoxItemWidget extends StatelessWidget {
  final UserIdCardEntity cardInfo;
  final Function() onTap;
  final bool? showDelete;
  final Function()? onDelete;

  const UserCardBoxItemWidget({
    super.key,
    required this.cardInfo,
    required this.onTap,
    this.showDelete,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    if (cardInfo.cardType == 1) {
      return _buildIDCardItem(cardInfo);
    }
    return _buildOtherCardItemWidget(cardInfo);
  }

  Widget _buildIDCardItem(UserIdCardEntity entity) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 110.w,
        padding: EdgeInsets.all(3.w),
        constraints: BoxConstraints(
          maxHeight: 180.w,
        ),
        decoration: const BoxDecoration(
          image: DecorationImage(
              image: AssetImage(Assets.imagesUserIdCardItemBg),
              fit: BoxFit.fill),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(
                top: 14.h,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6.r),
                child: ImageUtils.getImage(
                  entity.cardUrl ?? "",
                  60.w,
                  53.h,
                  fit: BoxFit.cover,
                  showPlaceholder: false,
                ),
              ),
            ),
            SizedBox(height: 50.h),
            Container(
              alignment: Alignment.center,
              constraints: BoxConstraints(
                maxWidth: 110.w - 16.w,
              ),
              child: Text(
                entity.cardName ?? "",
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: TextStyles.medium(16.sp),
              ),
            ),
            entity.cardText != null
                ? Padding(
                    padding: EdgeInsets.symmetric(horizontal: 11.w),
                    child: Row(
                      children: [
                        entity.cardText!.isNotEmpty
                            ? Container(
                                constraints: BoxConstraints(
                                  maxWidth: 50.w,
                                ),
                                child: Text(
                                  entity.cardText![0],
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyles.common(
                                    12.sp,
                                    const Color(0xFF666666),
                                  ),
                                ),
                              )
                            : Container(),
                        entity.cardText!.length > 1
                            ? Expanded(
                                child: Text(
                                  " | ${entity.cardText![1]}",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyles.common(
                                    12.sp,
                                    const Color(0xFF666666),
                                  ),
                                ),
                              )
                            : Container(),
                      ],
                    ),
                  )
                : Container(
                    height: 23.h,
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildOtherCardItemWidget(UserIdCardEntity entity) {
    String bgImage = "";
    if (entity.cardType == 2) {
      bgImage = Assets.imagesUserCardTypeShaiouBorder;
    } else if (entity.cardType == 3) {
      bgImage = Assets.imagesUserCardTypeDressBorder;
    } else if (entity.cardType == 4) {
      bgImage = Assets.imagesUserCardTypeStrengthBorder;
    }
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: 110.w,
        height: 180.h + 34.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(5.r),
                  child: Container(
                    width: 110.w,
                    height: 180.h,
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                    child: ImageUtils.getImage(entity.cardUrl ?? "", 110.w, 180.h,
                        fit: BoxFit.cover, showPlaceholder: false),
                  ),
                ),
                ImageUtils.getImage(bgImage, 110.w, 180.h, fit: BoxFit.fill),
                Visibility(
                  visible: showDelete == true,
                  child: Positioned(
                    right: 5.w,
                    top: 5.h,
                    child: GestureDetector(
                      onTap: onDelete,
                      child: ImageUtils.getImage(
                          Assets.imagesCloseRounded, 25.w, 25.w),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 5.h,
            ),
            Text(
              entity.cardName ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.normal(14.sp),
            ),
          ],
        ),
      ),
    );
  }
}
