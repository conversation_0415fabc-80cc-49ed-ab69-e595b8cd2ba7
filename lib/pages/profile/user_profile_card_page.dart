import 'dart:math' as math;
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_card_box_entity.dart';
import 'package:dada/model/user_label_entity.dart';
import 'package:dada/pages/match/dada/dialog/match_dada_list_label_detail_dialog.dart';
import 'package:dada/pages/profile/user_profile_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserProfileCardPage extends StatefulWidget {
  final UserProfileController ctrl;

  const UserProfileCardPage({super.key, required this.ctrl});

  @override
  State<UserProfileCardPage> createState() => _UserProfileCardPageState();
}

class _UserProfileCardPageState extends State<UserProfileCardPage> {
  late UserProfileController controller;

  @override
  void initState() {
    super.initState();

    controller = widget.ctrl;
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: const ClampingScrollPhysics(),
      slivers: [
        SliverToBoxAdapter(
          child: _buildUserLabelList(),
        ),
        SliverToBoxAdapter(
          child: _buildUserCardList(),
        ),
      ],
    );
  }

  Widget _buildUserLabelList() {
    return Obx(() {
      bool isMySelf =
          controller.userInfoEntity.value?.id == UserService().user?.id;
      return Container(
        width: ScreenUtil().screenWidth,
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        padding: EdgeInsets.symmetric(vertical: 10.h),
        decoration: BoxDecoration(
          color: AppTheme.themeData.colorScheme.primary,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Text(
                isMySelf
                    ? S.current.myLabels
                    : controller.userInfoEntity.value?.sex == 0
                        ? "${S.current.his}${S.current.label}"
                        : "${S.current.hers}${S.current.label}",
                style: TextStyles.normal(16.sp),
              ),
            ),
            Obx(() {
              if (controller.userInfoEntity.value?.labels == null ||
                  (controller.userInfoEntity.value?.labels != null &&
                      controller.userInfoEntity.value!.labels!.isEmpty)) {
                if (isMySelf) {
                  List<UserLabelEntity> labels = <UserLabelEntity>[];
                  labels.add(UserLabelEntity());
                  controller.userInfoEntity.value?.labels = labels;
                } else {
                  return EmptyWidget(imageWidth: 120.w, imageHeight: 70.h);
                }
              } else {
                if (isMySelf) {
                  UserLabelEntity firstEntity =
                      controller.userInfoEntity.value!.labels!.first;
                  if (firstEntity.labelId != null) {
                    controller.userInfoEntity.value!.labels!
                        .insert(0, UserLabelEntity());
                  }
                }
              }

              List<UserLabelEntity?> labels =
                  controller.userInfoEntity.value!.labels!;
              bool showMore = false;
              if (labels.length > 6 && controller.labelsUnfold.value == false) {
                labels = labels.sublist(0, 6);
                showMore = true;
              }

              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 15.w, top: 15.h),
                    child: Wrap(
                      spacing: 10.w,
                      runSpacing: 5.w,
                      children: labels
                          .map((e) => _buildLabelListItem(e!, isMySelf))
                          .toList(),
                    ),
                  ),
                  Visibility(
                    visible: showMore,
                    child: GestureDetector(
                      onTap: () {
                        controller.labelsUnfold.value = true;
                      },
                      child: Padding(
                        padding: EdgeInsets.only(top: 15.h),
                        child: ImageUtils.getImage(
                            Assets.imagesUserProfileLabelsMore, 20.w, 20.w),
                      ),
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      );
    });
  }

  Widget _buildLabelListItem(UserLabelEntity labelEntity, bool isMySelf) {
    if (isMySelf && labelEntity.labelId == null) {
      return Container(
        width: 100.w,
        height: 36.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesUserProfileLabelAdd),
            fit: BoxFit.fill,
          ),
        ),
        child: GestureDetector(
          onTap: () {
            Get.toNamed(GetRouter.userLabelAdd)?.then((value) {
              if (value != null) {
                controller.onLoad();
              }
            });
          },
        ),
      );
    }
    int randomInt = _getRandomIndex();
    String bgImage = _getRandomLabelBgImage(randomInt);
    Color textColor = _getRandomLabelTextColor(randomInt);
    Color textShadowColor = _getRandomLabelTextShadowColor(randomInt);
    return GestureDetector(
      onTap: () {
        ToastUtils.showDialog(
          dialog: MatchDadaListLabelDetailDialog(
            labelEntity: labelEntity,
            bgImageIndex: randomInt,
            isMySelf: isMySelf,
            callback: () {
              controller.onLoad();
            },
          ),
        );
      },
      child: Container(
        width: 100.w,
        height: 36.h,
        decoration: BoxDecoration(
          image: DecorationImage(image: AssetImage(bgImage), fit: BoxFit.fill),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 1.5.h),
                  constraints: BoxConstraints(maxWidth: 65.w),
                  child: Text(
                    labelEntity.labelName ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                      color: textColor,
                      shadows: [
                        Shadow(
                          color: textShadowColor,
                          offset: const Offset(0, 0.5),
                          blurRadius: 0.5,
                        ),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: labelEntity.labelImgs != null &&
                      labelEntity.labelImgs!.isNotEmpty,
                  child: Padding(
                    padding: EdgeInsets.only(left: 5.w, top: 2.h),
                    child: ImageUtils.getImage(
                        Assets.imagesUserProfileLabelImg, 13.w, 11.h),
                  ),
                ),
              ],
            ),
            Positioned(
              bottom: 1.h,
              child: ImageUtils.getImage(
                  Assets.imagesUserProfileLabelHasSubTag, 10.w, 13.h),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserCardList() {
    return Obx(() {
      bool isMySelf =
          controller.userInfoEntity.value?.id == UserService().user?.id;
      int idCardNum = 0;
      int shaiouCardNum = 0;
      int dressCardNum = 0;
      int strengthCardNum = 0;

      List<UserCardBoxEntity?>? idCardList =
          controller.cardBoxList.where((e) => e.cardType == "1").toList();
      List<UserCardBoxEntity?>? shaiouCardList =
          controller.cardBoxList.where((e) => e.cardType == "2").toList();
      List<UserCardBoxEntity?>? dressCardList =
          controller.cardBoxList.where((e) => e.cardType == "3").toList();
      List<UserCardBoxEntity?>? strengthCardList =
          controller.cardBoxList.where((e) => e.cardType == "4").toList();
      if (idCardList.isNotEmpty == true) {
        UserCardBoxEntity idCard = idCardList.first!;
        idCardNum = idCard.cardNum ?? 0;
      }
      if (shaiouCardList.isNotEmpty == true) {
        UserCardBoxEntity shaiouCard = shaiouCardList.first!;
        shaiouCardNum = shaiouCard.cardNum ?? 0;
      }
      if (dressCardList.isNotEmpty == true) {
        UserCardBoxEntity dressCard = dressCardList.first!;
        dressCardNum = dressCard.cardNum ?? 0;
      }
      if (strengthCardList.isNotEmpty == true) {
        UserCardBoxEntity strengthCard = strengthCardList.first!;
        strengthCardNum = strengthCard.cardNum ?? 0;
      }
      return Container(
        width: ScreenUtil().screenWidth,
        margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
        padding: EdgeInsets.only(left: 13.w, top: 8.h),
        height: 195.h,
        decoration: BoxDecoration(
          color: AppTheme.themeData.colorScheme.primary,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isMySelf
                  ? S.current.myCardBox
                  : controller.userInfoEntity.value?.sex == 0
                      ? "${S.current.his}${S.current.cardBox}"
                      : "${S.current.hers}${S.current.cardBox}",
              style: TextStyles.normal(16.sp),
            ),
            Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  ///身份卡
                  _cardItemWidget(
                    size: Size(75.w, 110.h),
                    margin: EdgeInsets.only(
                        left: 3.5.w, right: 1.5.w, top: 10.h, bottom: 8.h),
                    image: Assets.imagesUserProfileCardIdentity,
                    onTap: () {
                      Get.toNamed(GetRouter.userIdCard,
                          arguments: controller.userInfoEntity.value,
                          parameters: {"cardType": "1"})?.then((value) {
                        controller.loadUserCardBoxList();
                      });
                    },
                    cardName: "身份卡",
                    cardNum: idCardNum,
                  ),

                  ///晒欧卡
                  _cardItemWidget(
                    size: Size(88.w, 124.h),
                    margin: EdgeInsets.only(bottom: 4.h),
                    image: shaiouCardNum == 0
                        ? Assets.imagesUserProfileCardShaiouEmpty
                        : Assets.imagesUserProfileCardShaiou,
                    onTap: () {
                      Get.toNamed(
                        GetRouter.otherCard,
                        arguments: controller.userInfoEntity.value,
                        parameters: {
                          "title": S.current.goodCard,
                          "cardType": "2"
                        },
                      )?.then((value) {
                        controller.loadUserCardBoxList();
                      });
                      ;
                    },
                    cardName: S.current.goodCard,
                    cardNum: shaiouCardNum,
                  ),

                  ///穿搭卡
                  _cardItemWidget(
                    size: Size(82.w, 118.h),
                    margin: EdgeInsets.only(top: 6.h, bottom: 4.h),
                    image: dressCardNum == 0
                        ? Assets.imagesUserProfileCardDressEmpty
                        : Assets.imagesUserProfileCardDress,
                    onTap: () {
                      Get.toNamed(
                        GetRouter.otherCard,
                        arguments: controller.userInfoEntity.value,
                        parameters: {
                          "title": S.current.dressCard,
                          "cardType": "3"
                        },
                      )?.then((value) {
                        controller.loadUserCardBoxList();
                      });
                      ;
                    },
                    cardName: S.current.dressCard,
                    cardNum: dressCardNum,
                  ),

                  ///高光卡
                  _cardItemWidget(
                    size: Size(82.w, 124.h),
                    margin: EdgeInsets.only(top: 3.h, left: 2.w),
                    image: strengthCardNum == 0
                        ? Assets.imagesUserProfileCardStrengthEmpty
                        : Assets.imagesUserProfileCardStrength,
                    onTap: () {
                      Get.toNamed(
                        GetRouter.otherCard,
                        arguments: controller.userInfoEntity.value,
                        parameters: {
                          "title": S.current.strengthCard,
                          "cardType": "4"
                        },
                      )?.then((value) {
                        controller.loadUserCardBoxList();
                      });
                      ;
                    },
                    cardName: S.current.strengthCard,
                    cardNum: strengthCardNum,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _cardItemWidget(
      {required Size size,
      required EdgeInsets margin,
      required String image,
      required Function() onTap,
      required String cardName,
      required int cardNum}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            margin: margin,
            width: size.width,
            height: size.height,
            child: ImageUtils.getImage(image, size.width, size.height,
                fit: BoxFit.cover),
          ),
          Padding(
            padding: EdgeInsets.only(top: 2.h),
            child: Text(
              "$cardName($cardNum)",
              style: TextStyles.common(14.sp, AppColors.colorFF2D6D0B),
            ),
          ),
        ],
      ),
    );
  }

  int _getRandomIndex() {
    var random = math.Random();
    int randomInt = random.nextInt(3);
    return randomInt;
  }

  String _getRandomLabelBgImage(int index) {
    List<String> images = [
      Assets.imagesProfileTagLabelRandomBg1,
      Assets.imagesProfileTagLabelRandomBg2,
      Assets.imagesProfileTagLabelRandomBg3,
    ];
    return images[index];
  }

  Color _getRandomLabelTextColor(int index) {
    List<Color> bgColors = [
      const Color(0xFFFF17BA),
      const Color(0xFF23AF28),
      const Color(0xFFF9781D),
    ];
    return bgColors[index];
  }

  Color _getRandomLabelTextShadowColor(int index) {
    List<Color> list = [
      const Color(0xFFAF83A2),
      const Color(0xFF87B07D),
      const Color(0xFFB09A5B)
    ];
    return list[index];
  }
}
