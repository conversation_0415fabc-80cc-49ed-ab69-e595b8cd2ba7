import 'package:dada/model/user_label_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserLabelController extends GetxController {
  RxList<String> subLabelList = <String>[].obs;
  RxList<String> labelImgList = <String>[].obs;

  final textEditingController = TextEditingController();

  late Rx<UserLabelEntity> labelEntity = UserLabelEntity().obs;

  void updateLabelEntity(UserLabelEntity entity) {
    textEditingController.text = entity.labelName ?? "";
    subLabelList.value = entity.labelText ?? [];
    labelImgList.value = entity.labelImgs ?? [];
  }

  void addLabel() async {
    List<String> urls = [];
    if (labelImgList.isNotEmpty) {
      for (String filePath in labelImgList) {
        if (filePath.isNotEmpty &&
            (filePath.startsWith("file:") ||
                filePath.startsWith("/storage") ||
                filePath.startsWith("/"))) {
          String? imageUrl = await ApiService().uploadFile(filePath);
          if (imageUrl != null) {
            urls.add(imageUrl);
          }
        } else if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
          urls.add(filePath);
        }
      }
    }
    bool success = await ApiService().addUserLabel(
        labelId: labelEntity.value.labelId,
        labelName: textEditingController.text,
        labelText: subLabelList,
        labelImgs: urls);
    if (success) {
      Get.back(result: 1);
    }
  }

  void deleteLabel() async {
    if (labelEntity.value.labelId != null) {
      bool success = await ApiService()
          .deleteUserLabel(labelId: labelEntity.value.labelId!);
      if (success) {
        Get.back(result: 1);
      }
    }
  }
}
