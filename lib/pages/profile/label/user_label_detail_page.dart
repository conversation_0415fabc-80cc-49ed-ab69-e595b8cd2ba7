import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/profile/label/user_label_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserLabelDetailPage extends StatefulWidget {
  const UserLabelDetailPage({super.key});

  @override
  State<UserLabelDetailPage> createState() => _UserLabelDetailPageState();
}

class _UserLabelDetailPageState extends State<UserLabelDetailPage> {
  late UserLabelController controller;
  bool hasChanged = false;

  @override
  void initState() {
    super.initState();

    if (Get.isRegistered<UserLabelController>()) {
      controller = Get.find<UserLabelController>();
    } else {
      controller = Get.put(UserLabelController());
    }

    if (Get.arguments != null) {
      controller.labelEntity.value = Get.arguments;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: S.current.label,
        backAction: () {
          Get.back(result: hasChanged ? "1" : null);
        },
      ),
      body: Container(
        color: AppTheme.themeData.colorScheme.surfaceTint,
        child: ListView(
          children: [
            Container(
              margin: EdgeInsets.only(top: 20.h, left: 15.w, right: 15.w),
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
              decoration: BoxDecoration(
                color: AppTheme.themeData.colorScheme.primary,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(),
                      Row(
                        children: [
                          Obx(
                            () => Text(
                              controller.labelEntity.value.labelName ?? "",
                              style: TextStyles.normal(16.sp),
                            ),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Visibility(
                            visible: controller.labelEntity.value.userId ==
                                UserService().user?.id,
                            child: GestureDetector(
                              onTap: () {
                                Get.toNamed(GetRouter.userLabelAdd,
                                        parameters: {
                                          "title": S.current.editLabel
                                        },
                                        arguments: Get.arguments)
                                    ?.then((value) {
                                  if (value != null) {
                                    setState(() {});
                                    hasChanged = true;
                                  } else {
                                    hasChanged = false;
                                  }
                                });
                              },
                              child: ImageUtils.getImage(
                                  Assets.imagesMineInfoEdit, 13.w, 13.w),
                            ),
                          ),
                        ],
                      ),
                      Visibility(
                        visible: controller.labelEntity.value.userId ==
                            UserService().user?.id,
                        child: GestureDetector(
                          onTap: () {
                            controller.deleteLabel();
                          },
                          child: ImageUtils.getImage(
                              Assets.imagesUserLabelDetailDelete, 16.w, 16.w),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 8.h,
                  ),
                  _buildSubLabelListWidget(),
                  _buildLabelImageWidget(),
                  // Obx(
                  //   () => _buildSubLabelListWidget(),
                  // ),
                  // Obx(
                  //   () => _buildLabelImageWidget(),
                  // ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubLabelListWidget() {
    if (controller.labelEntity.value.labelText == null ||
        controller.labelEntity.value.labelText?.isEmpty == true) {
      return Container(
        constraints: BoxConstraints(minHeight: 110.h),
      );
    }
    return Padding(
      padding: EdgeInsets.only(top: 15.h, bottom: 20.h),
      child: Container(
        constraints: BoxConstraints(
          minHeight: 165.h,
        ),
        child: Wrap(
          spacing: 10.w,
          runSpacing: 10.w,
          children: controller.labelEntity.value.labelText!
              .map((e) => _buildSubLabelItem(e))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildSubLabelItem(String text) {
    if (text.isEmpty) {
      return Container();
    }
    return LabelItemWidget(
      text: text,
      fontSize: 14.sp,
      textColor: AppColors.colorFF666666,
      editable: false,
      padding: EdgeInsets.only(left: 14.w, right: 14.w),
      minWidth: 70.w,
      bgColor: AppColors.colorFFEAECF1,
      borderColor: Colors.transparent,
      alignment: MainAxisAlignment.center,
    );
  }

  Widget _buildLabelImageWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.current.gameScreenShot,
          style: TextStyles.normal(16.sp),
        ),
        SizedBox(
          height: 22.h,
        ),
        _buildLabelImageList(),
      ],
    );
  }

  Widget _buildLabelImageList() {
    if (controller.labelEntity.value.labelImgs?.isNotEmpty == true) {
      controller.labelEntity.value.labelImgs?.removeWhere((e) => e.isEmpty);
    }
    if (controller.labelEntity.value.labelImgs == null ||
        controller.labelEntity.value.labelImgs?.isEmpty == true) {
      return Container(
        constraints: BoxConstraints(minHeight: 60.h),
      );
    }
    return Container(
      constraints: BoxConstraints(minHeight: 60.h),
      child: Wrap(
        spacing: 10.w,
        runSpacing: 10.w,
        children: controller.labelEntity.value.labelImgs!
            .map((e) => _buildLabelImageItem(e))
            .toList(),
      ),
    );
  }

  Widget _buildLabelImageItem(String url) {
    return Container(
      width: 95.w,
      height: 56.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Stack(
        alignment: Alignment.topRight,
        children: [
          GestureDetector(
            onTap: () {
              ImageUtils.showImageBrowser(
                ImageBrowserArgs(
                  [HeroTagName.labelImg.of(url)],
                  [url],
                ),
              );
            },
            child: Hero(
              tag: HeroTagName.labelImg.of(url),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5.r),
                child: ImageUtils.getImage(url, 95.w, 56.h, fit: BoxFit.fitWidth),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
