import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/profile/label/user_label_controller.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class UserLabelAddPage extends StatefulWidget {
  const UserLabelAddPage({super.key});

  @override
  State<UserLabelAddPage> createState() => _UserLabelAddPageState();
}

class _UserLabelAddPageState extends State<UserLabelAddPage> {
  late UserLabelController controller;

  @override
  void initState() {
    super.initState();

    if (Get.isRegistered<UserLabelController>()) {
      controller = Get.find<UserLabelController>();
    } else {
      controller = Get.put(UserLabelController());
    }

    if (Get.arguments != null) {
      controller.labelEntity.value = Get.arguments;
      controller.updateLabelEntity(Get.arguments);
    }
  }

  @override
  Widget build(BuildContext context) {
    String? title = Get.parameters["title"];
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: title ?? S.current.addLabel,
        rightWidgets: controller.labelEntity.value.labelId != null
            ? [
                GestureDetector(
                  onTap: () {
                    ToastUtils.showDialog(
                      hideTopImg: true,
                      content: "确定要删除标签吗？",
                      onConfirm: () {
                        controller.deleteLabel();
                      },
                    );
                  },
                  child: Padding(
                    padding: EdgeInsets.only(left: 8.w, right: 10.w),
                    child: ImageUtils.getImage(
                        Assets.imagesSmallRoomMailMessageDelete, 16.w, 15.h),
                  ),
                ),
              ]
            : null,
      ),
      body: Container(
        color: AppTheme.themeData.colorScheme.surfaceTint,
        child: ListView(
          children: [
            Container(
              margin: EdgeInsets.only(top: 20.h, left: 15.w, right: 15.w),
              padding: EdgeInsets.only(
                  top: 25.h, left: 18.w, right: 18.w, bottom: 24.h),
              decoration: BoxDecoration(
                color: AppTheme.themeData.colorScheme.primary,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTopWidget(),
                  _buildSubLabelListWidget(),
                  _buildLabelImageList(),
                  _buildBottomBtn(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopWidget() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          "*",
                          style: TextStyles.common(
                            16.sp,
                            AppColors.colorFFE22D2D,
                          ),
                        ),
                        Text(
                          S.current.labelName,
                          style: TextStyles.normal(16.sp),
                        ),
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 3.w, top: 3.h),
                      child: Text(
                        S.current.labelNameTip,
                        style:
                            TextStyles.common(14.sp, AppColors.colorFF999999),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Container(
              width: 130.w,
              height: 30.h,
              decoration: BoxDecoration(
                color: AppColors.colorFFF5F5F5,
                borderRadius: BorderRadius.circular(5.r),
                border: Border.all(color: AppColors.colorFFE1E1E1, width: 1.h),
              ),
              child: Padding(
                padding: EdgeInsets.only(top: 3.h, bottom: 3.h),
                child: CustomTextField.build(
                  contentPadding: EdgeInsets.only(left: 8.w, right: 6.w),
                  controller: controller.textEditingController,
                  maxLength: 8,
                ),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 8.h,
        ),
        Container(
          margin: EdgeInsets.only(left: 3.w, right: 3.w),
          height: 1.h,
          color: AppColors.colorFFD8D8D8,
        ),
      ],
    );
  }

  Widget _buildSubLabelListWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 15.h, left: 4.w, bottom: 40.h),
          child: Obx(() {
            List<String> list = controller.subLabelList;
            if (list.isEmpty) {
              if (!list.contains("")) {
                list.add("");
              }
            } else if (list.length < 8) {
              if (!list.contains("")) {
                list.add("");
              }
            } else if (list.length > 8) {
              list = list.sublist(0, 8);
            }
            return Container(
              constraints: BoxConstraints(
                minHeight: 80.w,
              ),
              child: Wrap(
                spacing: 10.w,
                runSpacing: 10.w,
                children: _buildSubLabelListChildren(list),
              ),
            );
          }),
        ),
        Text(
          "子标签是对标签的补充描述哦~比如标签名称为旅游，子标签可以是爱做攻略、走哪算哪、喜欢自驾游、喜欢跟团游等。又比如标签名称为XX游戏，则子标签可以是XX区服、XX段位、游戏风格（如莽、谨慎、喜欢上单等）。",
          style: TextStyles.common(12.sp, AppColors.colorFF666666)
              .copyWith(height: 1.3),
        ),
        SizedBox(
          height: 6.h,
        ),
      ],
    );
  }

  List<Widget> _buildSubLabelListChildren(List<String> data) {
    List<Widget> list = <Widget>[];
    for (int i = 0; i < data.length; i++) {
      String text = data[i];
      list.add(_buildSubLabelItem(text, i));
    }
    return list;
  }

  Widget _buildSubLabelItem(String text, int index) {
    if (text.isEmpty) {
      return LabelItemWidget(
        text: "添加子标签",
        fontSize: 14.sp,
        height: 30.h,
        isAddItem: true,
        textColor: AppColors.colorFF666666,
        borderRadius: 15.r,
        addAction: (text) {
          if (text.isNotEmpty) {
            controller.subLabelList
                .insert(controller.subLabelList.length - 1, text);
          }
        },
      );
    }

    return LabelItemWidget(
      text: text,
      borderRadius: 15.r,
      textColor: AppColors.colorFF666666,
      bgColor: AppColors.colorFFEAECF1,
      padding: EdgeInsets.only(left: 8.w, right: 6.w),
      fontSize: 14.sp,
      borderColor: Colors.transparent,
      closeBtnColor: AppColors.colorFF999999,
      deleteAction: () {
        controller.subLabelList.removeAt(index);
      },
    );
  }

  Widget _buildLabelImageList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "相关截图",
          style: TextStyles.normal(16.sp),
        ),
        SizedBox(
          height: 22.h,
        ),
        Obx(() {
          List<String> list = controller.labelImgList;
          if (list.isEmpty) {
            if (!list.contains("")) {
              list.add("");
            }
          } else if (list.length < 5) {
            if (!list.contains("")) {
              list.add("");
            }
          } else if (list.length > 6) {
            list = list.sublist(0, 6);
          }
          return Padding(
            padding: EdgeInsets.only(bottom: 26.h),
            child: Wrap(
              spacing: 10.w,
              runSpacing: 10.w,
              children: _buildLabelImageListChildren(list),
            ),
          );
        }),
      ],
    );
  }

  List<Widget> _buildLabelImageListChildren(List<String> data) {
    List<Widget> list = <Widget>[];
    for (int i = 0; i < data.length; i++) {
      String text = data[i];
      list.add(_buildLabelImageItem(text, i));
    }
    return list;
  }

  Widget _buildLabelImageItem(String url, int index) {
    if (url.isEmpty) {
      return GestureDetector(
        onTap: () {
          ToastUtils.showBottomSheet(
            [S.current.album, S.current.camera],
            onTap: (index) async {
              bool res = await ImagePickerUtil.checkPermission(
                  index == 0 ? 1 : 2,
                  index == 0 ? "相册权限说明" : "相机权限说明",
                  index == 0
                      ? "获取图片用于补充标签信息"
                      : "拍摄图片用于补充标签信息, 拍摄后的图片将存放在系统照片中");

              if (!res) return;
              /* XFile? imageFile = await ImagePicker().pickImage(
                  source:
                      index == 0 ? ImageSource.gallery : ImageSource.camera); */
              AssetEntity? asset;
              if (index == 0) {
                List<AssetEntity>? assets = await ImagePickerUtil.selectAsset(
                    maxAssets: 1, isImage: true);
                if (assets != null) {
                  asset = assets.first;
                }
              } else {
                asset = await ImagePickerUtil.takeAsset();
              }
              String? imagePath = await ImagePickerUtil.getEntityPath(asset);
              if (imagePath != null && imagePath.isNotEmpty) {
                controller.labelImgList.insert(0, imagePath);
              }
            },
          );
        },
        child: ImageUtils.getImage(Assets.imagesUserLabelImgAdd, 96.w, 57.w,
            fit: BoxFit.fill),
      );
    }
    return Container(
      width: 96.w,
      height: 56.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Stack(
        alignment: Alignment.topRight,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(5.r),
            child: ImageUtils.getImage(url, 95.w, 56.h, fit: BoxFit.cover),
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: GestureDetector(
              onTap: () {
                controller.labelImgList.removeAt(index);
              },
              child: ImageUtils.getImage(
                  Assets.imagesUserLabelImgDelete, 20.w, 18.h),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      title: S.current.sure,
      onTap: () {
        controller.addLabel();
      },
    );
  }
}
