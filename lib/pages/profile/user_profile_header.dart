import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/components/widgets/input_text_page.dart';
import 'package:dada/components/widgets/sex_widget.dart';
import 'package:dada/components/widgets/user_medal_tag_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/mine/edit/audio_edit_dialog.dart';
import 'package:dada/pages/profile/user_profile_controller.dart';
import 'package:dada/pages/small_room/small_room_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';

class UserProfileHeader extends StatefulWidget {
  final Function(double) heightUpdate;
  final UserProfileController ctrl;

  const UserProfileHeader(
      {super.key, required this.heightUpdate, required this.ctrl});

  @override
  State<UserProfileHeader> createState() => _UserProfileHeaderState();
}

class _UserProfileHeaderState extends State<UserProfileHeader>
    with TickerProviderStateMixin {
  late UserProfileController controller;
  final RxBool signatureUnfold = false.obs;

  @override
  void initState() {
    super.initState();

    controller = widget.ctrl;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Obx(
          () => Container(
            margin: EdgeInsets.only(top: 119.h),
            height: controller.containerHeight.value,
            width: ScreenUtil().screenWidth,
            decoration: BoxDecoration(
              color: AppTheme.themeData.colorScheme.surfaceTint,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: _buildNameIDSignature(),
          ),
        ),
        _buildInfoContainer(),
      ],
    );
  }

  Widget _buildInfoContainer() {
    return Column(
      children: [
        ///语音
        _buildAudioPlayWidget(),

        ///追剧、头像
        _buildAvatarWidget(),

        ///等级
        _buildLevelWidget(),
      ],
    );
  }

  Widget _buildAudioPlayWidget() {
    return Obx(
      () {
        final svgaAnimationController = SVGAAnimationController(vsync: this);
        if (controller.userInfoEntity.value?.voiceSignature == null) {
          return Container(height: 69.h);
        }

        String audioBtnImg = controller.audioPlaying.value == true
            ? Assets.imagesUserProfileAudioPauseBtn
            : Assets.imagesUserProfileAudioPlayBtn;
        return Row(
          children: [
            const Spacer(),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                bool hasAudio =
                    controller.userInfoEntity.value?.voiceSignature != null &&
                        controller.userInfoEntity.value?.voiceSignature != "";
                if (hasAudio) {
                  if (!(controller.userId != null &&
                      controller.userId != UserService().user?.id)) {
                    ToastUtils.showDialog(
                      dialog: AudioEditDialog(
                        canTry: true,
                        onRepeat: () {
                          pushAudioRecordPage();
                        },
                        onDelete: () {
                          controller.deleteAudio();
                        },
                        onTry: () {
                          if (controller.audioPlaying.value == true) {
                            controller.audioPlayer.stop();
                          } else {
                            controller.audioPlayer.play();
                          }
                        },
                      ),
                    ).then((v) {
                      if (controller.audioPlaying.value == true) {
                        controller.audioPlayer.stop();
                      }
                    });
                  } else {
                    if (controller.audioPlaying.value == true) {
                      controller.audioPlayer.stop();
                    } else {
                      controller.audioPlayer.play();
                    }
                  }
                } else {
                  if (!(controller.userId != null &&
                      controller.userId != UserService().user?.id)) {
                    pushAudioRecordPage();
                  }
                }
              },
              child: Padding(
                padding: EdgeInsets.only(top: 39.h),
                child: Container(
                  width: 92.w,
                  height: 30.h,
                  decoration: BoxDecoration(
                    color: const Color(0x77000000),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(15.r),
                        bottomLeft: Radius.circular(15.r)),
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 5.w,
                      ),
                      ImageUtils.getImage(audioBtnImg, 18.w, 18.w),
                      SizedBox(
                        width: 5.w,
                      ),
                      SVGAImage(
                        svgaAnimationController,
                      ),
                      SizedBox(
                        width: 14.w,
                      ),
                      Text(
                        "${controller.userInfoEntity.value?.voiceLength ?? 0}\"",
                        style: TextStyles.common(
                            14.sp, AppTheme.themeData.primaryColor),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAvatarWidget() {
    return Stack(
      children: [
        Container(
          constraints: BoxConstraints(
            maxWidth: 130.w,
          ),
          padding: EdgeInsets.only(left: 13.w),
          child: Text(
            controller.userInfoEntity.value?.recentState ?? "",
            maxLines: 2,
            style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.themeData.colorScheme.primary,
                shadows: [
                  Shadow(
                    color: const Color(0x80000000),
                    offset: Offset(0, 1.h),
                    blurRadius: 1.r,
                  ),
                ]),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () {
                ImageUtils.showImageBrowser(ImageBrowserArgs(
                  [
                    HeroTagName.profileAvatar
                        .of(controller.userInfoEntity.value?.id ?? "0")
                  ],
                  [
                    controller.userInfoEntity.value?.avatar ??
                        Assets.imagesAvatarPlaceholder
                  ],
                ));
              },
              child: Container(
                width: 100.w,
                height: 100.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.w / 2),
                  border: Border.all(
                    color: AppTheme.themeData.colorScheme.surface,
                    width: 2.w,
                  ),
                ),
                child: Hero(
                  tag: HeroTagName.profileAvatar
                      .of(controller.userInfoEntity.value?.id ?? "0"),
                  child: ClipOval(
                    child: Obx(
                      () => ImageUtils.getImage(
                        controller.userInfoEntity.value?.avatar ?? "",
                        100.w,
                        100.w,
                        placeholder: Assets.imagesAvatarPlaceholder,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLevelWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 5.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if ((int.parse(
                  controller.userInfoEntity.value?.monthlyPassDay ?? "0")) >
              0)
            Container(
              // width: 56.w,
              height: 16.h,
              constraints: BoxConstraints(minWidth: 42.w, maxWidth: 105.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.h),
                gradient: const LinearGradient(colors: [
                  Color(0xFFFFF3D1),
                  Color(0xFFFFD18D),
                ]),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ImageUtils.getImage(Assets.imagesMineInfoLevel, 16.w, 16.w),
                  Padding(
                    padding: EdgeInsets.only(left: 3.w, right: 5.w),
                    child: Text(
                      "vip",
                      textAlign: TextAlign.center,
                      style: TextStyles.medium(
                        13.sp,
                        c: const Color(0xFF612103),
                        h: 1.25,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          if (controller.userInfoEntity.value?.socialState != null)
            SizedBox(width: 5.w),
          if (controller.userInfoEntity.value?.socialState != null)
            Container(
              height: 16.h,
              constraints: BoxConstraints(minWidth: 42.w, maxWidth: 105.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.h),
                gradient: const LinearGradient(colors: [
                  Color(0xFFFFC8BF),
                  Color(0xFFFA7761),
                ]),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // ImageUtils.getImage(Assets.imagesMineInfoDadaLevel, 16.w, 16.w),
                  Padding(
                    padding: EdgeInsets.only(left: 5.w, right: 5.w),
                    child: Obx(
                      () => Text(
                        _getUserRelationState(
                            controller.userInfoEntity.value?.socialState),
                        textAlign: TextAlign.center,
                        style: TextStyles.medium(
                          13.sp,
                          c: const Color(0xFF612103),
                          h: 1.25,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          SizedBox(width: 5.w),
          Obx(
            () => Visibility(
              visible:
                  controller.userInfoEntity.value?.constellation?.isNotEmpty ==
                      true,
              child: Container(
                height: 16.h,
                padding: EdgeInsets.only(left: 5.w, right: 5.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.h),
                  color: controller.userInfoEntity.value?.sex == 1
                      ? AppColors.colorFFFF97E0
                      : AppColors.colorFF6265FF,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      controller.userInfoEntity.value?.constellation ?? "",
                      style: TextStyles.common(12.sp, Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNameIDSignature() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 83.h, right: 15.w),
      child: Stack(
        children: [
          Column(
            children: [
              ///昵称、性别
              Row(
                children: [
                  Text(
                    controller.userInfoEntity.value?.nickname ?? "",
                    style: TextStyles.medium(20.sp),
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  SexAgeWidget(
                    sex: controller.userInfoEntity.value?.sex ?? 0,
                    age: controller.userInfoEntity.value?.age ?? 0,
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  UserMedalTagWidget(userInfo: controller.userInfoEntity.value),
                ],
              ),
              SizedBox(
                height: 5.h,
              ),

              ///ID、IP
              Row(
                children: [
                  Text(
                    "${S.current.dadaID}：${controller.userInfoEntity.value?.dadaNo ?? 0}",
                    style: TextStyles.normal(12.sp,
                        c: AppTheme.themeData.textTheme.bodyMedium?.color),
                  ),
                  SizedBox(
                    width: 15.w,
                  ),
                  Text(
                    controller.userInfoEntity.value?.hometown != null
                        ? "地区:${controller.userInfoEntity.value?.hometown ?? ""}"
                        : "地区:火星",
                    style: TextStyles.normal(12.sp,
                        c: AppTheme.themeData.textTheme.bodyMedium?.color),
                  ),
                ],
              ),
              SizedBox(height: 15.h),

              ///签名
              _buildTextSignature(),
            ],
          ),
          _buildSmallRoomEntry(),
        ],
      ),
    );
  }

  Widget _buildTextSignature() {
    double textHeight = 0;
    if (controller.userInfoEntity.value?.txtSignature != null &&
        controller.userInfoEntity.value!.txtSignature!.isNotEmpty) {
      textHeight = StringUtils.calculateTextHeight(
        controller.userInfoEntity.value?.txtSignature ?? "",
        maxWidth: ScreenUtil().screenWidth - 15.w * 2,
        textStyle: TextStyle(fontSize: 14.sp, height: 1.2),
      );
      textHeight = max(21.h, textHeight);
    }

    int maxLines = 1;
    if (textHeight > 21.h && signatureUnfold.value == true) {
      maxLines = 3;
      textHeight = min(63.h, textHeight);
    }
    bool showArrow =
        controller.userId != null && controller.userId != UserService().user?.id
            ? false
            : true;
    if (textHeight == 0 &&
        controller.userId != null &&
        controller.userId != UserService().user?.id) {
      return Container(
        height: 100.h,
        width: ScreenUtil().screenWidth - 15.w * 2,
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(10.r)),
        child: Text(
          "这个人竟然还没有介绍自己呢^_^！",
          maxLines: 20,
          overflow: TextOverflow.visible,
          style: TextStyles.common(
              14.sp, AppTheme.themeData.textTheme.bodyMedium?.color,
              h: 1.3),
        ),
      );
    }
    return Container(
      height: 100.h,
      width: ScreenUtil().screenWidth - 15.w * 2,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(10.r)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            height: 100.h,
            width: ScreenUtil().screenWidth - 15.w * 2 - 15.w - 6.w - 16.w,
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            child: SingleChildScrollView(
              child: Text(
                controller.userInfoEntity.value?.txtSignature ?? "",
                maxLines: 20,
                overflow: TextOverflow.visible,
                style: TextStyles.common(
                    14.sp, AppTheme.themeData.textTheme.bodyMedium?.color,
                    h: 1.3),
              ),
            ),
          ),
          Visibility(
            visible: showArrow,
            child: Padding(
              padding: EdgeInsets.only(left: 3.w, bottom: 10.h),
              child: GestureDetector(
                onTap: () {
                  /* signatureUnfold.value = true;
                controller.containerHeight.value = 149.h + textHeight;
                widget.heightUpdate.call(textHeight); */
                  List<String> placeholders = [
                    "偷偷告诉你可以这样介绍自己哦~\n\n06年大学生\n爱笑、爱瞎想\n容易感动容易满足\n认定的东西就会守候\n喜欢简简单单的关系\n\n我能成为你单身路上的一个不一样的点吗？",
                    "偷偷告诉你可以这样介绍自己哦~\n\n找队友一起开黑咯~\n我真是搞不懂你们\n那么喜欢玩游戏\n打个游戏还废寝忘食\n眼睛不疲劳吗\n都不知道注意身体吗\n\n当然\n如果你愿意带我玩\n这些话当我没说",
                    "偷偷告诉你可以这样介绍自己哦~\n\n这里是叶神~\n声音算是少年/少女音吧~\n平时喜欢暖酷风，\n不会粘着人，又希望别人粘着自己。\n喜欢羽毛球和看动漫~\n性别偏内向，遇到喜欢的人又不知道如何开口\n如果你看到这里对我感兴趣的话，\n可以来找我聊天哦。",
                  ];
                  String placeholder =
                      placeholders[Random().nextInt(placeholders.length)];
                  Get.to(
                    () => InputTextPage(
                      title: "介绍自己",
                      text: controller.userInfoEntity.value?.txtSignature ?? "",
                      placeholder: placeholder,
                      callback: (value) {
                        controller.updateUserInfo(text: value);
                      },
                      isMultiLine: true,
                      maxLength: 120,
                      height: 350.h,
                      maxLines: 20,
                    ),
                  );
                },
                child: /* ImageUtils.getImage(
                  Assets.imagesUserProfileSignatureArrow, 15.w, 10.h) */
                    Text(
                  '编辑',
                  style: TextStyles.common(14.sp, Color(0xFF2D6D0B)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmallRoomEntry() {
    return Positioned(
      top: 0.h,
      right: 5.w,
      child: Visibility(
        visible: controller.userInfoEntity.value?.id != UserService().user?.id,
        child: Row(
          children: [
            if (controller.userInfoEntity.value?.isFriend != 1)
              GestureDetector(
                onTap: () {
                  Get.toNamed(GetRouter.addFriendSend, parameters: {
                    "userId": controller.userInfoEntity.value!.id!,
                    "avatar": controller.userInfoEntity.value!.avatar!,
                    "nickname": controller.userInfoEntity.value!.nickname!,
                  });
                },
                child: Column(
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesProfileAddFriends, 36.w, 33.h),
                    Padding(
                      padding: EdgeInsets.only(
                        top: 1.h,
                      ),
                      child: Text(
                        "加好友",
                        style: TextStyles.normal(12.sp),
                      ),
                    ),
                  ],
                ),
              ),
            SizedBox(width: 3.w),
            GestureDetector(
              onTap: () {
                Get.to(() => SmallRoomPage(userId: controller.userId!));
              },
              child: Column(
                children: [
                  ImageUtils.getImage(Assets.imagesProfileKankanta, 36.w, 33.h),
                  Padding(
                    padding: EdgeInsets.only(
                      top: 1.h,
                    ),
                    child: Text(
                      "看看TA", //访问小屋
                      style: TextStyles.normal(12.sp),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  String _getUserRelationState(int? state) {
    switch (state) {
      case 1:
        return "冰冷";
      case 2:
        return "慢热";
      case 3:
        return "适中";
      case 4:
        return "热情";
      case 5:
        return "非常热情";
      default:
        return "";
    }
  }

  void pushAudioRecordPage() {
    Get.toNamed(GetRouter.audioRecord)?.then((value) {
      if (value != null) {
        int duration = value["duration"];
        String filepath = value["filePath"];
        controller.updateUserInfo(audioPath: filepath, audioLength: duration);
      }
    });
  }
}
