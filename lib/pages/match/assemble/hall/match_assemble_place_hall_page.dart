import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/pages/match/assemble/hall/match_assemble_place_hall_controller.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tolyui/tolyui.dart';

import 'join_team_input_pwd_dialog.dart';

class MatchAssemblePlaceHallPage extends StatefulWidget {
  const MatchAssemblePlaceHallPage({super.key});

  @override
  State<MatchAssemblePlaceHallPage> createState() =>
      _MatchAssemblePlaceHallPageState();
}

class _MatchAssemblePlaceHallPageState
    extends State<MatchAssemblePlaceHallPage> {
  final controller = Get.put(MatchAssemblePlaceHallController());
  TextEditingController editingController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          GradientWidget(
            width: ScreenUtil().screenWidth,
            height: ScreenUtil().screenHeight,
            stops: const [0, 0.45],
            colors: const [AppColors.colorFFADF0B0, AppColors.colorFFF5F5F5],
          ),
          _buildContentWidget(),
        ],
      ),
    );
  }

  Widget _buildContentWidget() {
    return Column(
      children: [
        _buildSearchBar(),
        _buildFilterBar(),
        _buildContentList(),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight, bottom: 10.h),
      child: Row(
        children: [
          IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Container(
              width: 28.w,
              height: 28.w,
              alignment: Alignment.center,
              child: ImageUtils.getImage(Assets.imagesNaviBack, 8.w, 14.w),
            ),
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(right: 15.w),
              height: 35.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(17.5.r),
              ),
              child: Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10.w),
                    child: ImageUtils.getImage(
                        Assets.imagesHomeSearchIcon, 13.33.w, 13.33.w),
                  ),
                  SizedBox(width: 7.w),
                  Expanded(
                    child: CustomTextField.build(
                        controller: editingController,
                        hintText: "请输入集结处标签、号码或名字",
                        showSuffixIcon: true,
                        onSubmitted: (value) {
                          controller.searchTeam(editingController.text);
                        },
                        onChanged: (value) {
                          if (value.isEmpty) {
                            controller.clearSearchResult();
                          }
                        }),
                  ),
                  Container(
                    width: 1.w,
                    color: AppColors.colorFF999999,
                    height: 20.h,
                  ),
                  TextButton(
                    onPressed: () {
                      controller.searchTeam(editingController.text);
                    },
                    child: Text(
                      S.current.search,
                      style: TextStyles.normal(14.sp),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      height: 30.h,
      alignment: Alignment.topCenter,
      child: Row(
        children: [
          Obx(
            () => Container(
              alignment: Alignment.centerRight,
              width: ScreenUtil().screenWidth / 2.0 - 1,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  controller.showFilledRoom.value =
                      controller.showFilledRoom.value == 1 ? 0 : 1;
                  if (editingController.text.isNotEmpty) {
                    controller.searchTeam(editingController.text);
                  } else {
                    controller.refreshData();
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ImageUtils.getImage(
                        controller.showFilledRoom.value == 1
                            ? Assets.imagesMatchTeamHallFilterSelected
                            : Assets.imagesMatchTeamHallFilterUnSelected,
                        20.w,
                        20.w),
                    Padding(
                      padding: EdgeInsets.only(left: 5.w),
                      child: Text(
                        "显示已满房间",
                        style: TextStyles.common(
                            16.sp,
                            controller.showFilledRoom.value == 1
                                ? AppColors.colorFF23AF28
                                : AppColors.colorFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            width: 1.w,
            height: 15.h,
            color: AppColors.colorFF9DB896,
          ),
          Obx(
            () => Container(
              width: ScreenUtil().screenWidth / 2.0 - 1,
              padding: EdgeInsets.only(left: 27.w),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  controller.showLockedRoom.value =
                      controller.showLockedRoom.value == 1 ? 0 : 1;
                  if (editingController.text.isNotEmpty) {
                    controller.searchTeam(editingController.text);
                  } else {
                    controller.refreshData();
                  }
                },
                child: Row(
                  children: [
                    ImageUtils.getImage(
                        controller.showLockedRoom.value == 1
                            ? Assets.imagesMatchTeamHallFilterSelected
                            : Assets.imagesMatchTeamHallFilterUnSelected,
                        20.w,
                        20.w),
                    Padding(
                      padding: EdgeInsets.only(left: 5.w),
                      child: Text(
                        "显示已上锁房间",
                        style: TextStyles.common(
                            16.sp,
                            controller.showLockedRoom.value == 1
                                ? AppColors.colorFF23AF28
                                : AppColors.colorFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentList() {
    return GetBuilder(
      init: controller,
      builder: (controller) {
        if (controller.searchResultList?.isNotEmpty == true) {
          return Expanded(
            child: _buildListView(controller.searchResultList!),
          );
        }
        return Expanded(
          child: RefreshWidget.build(
              refreshController: controller.refreshController,
              onRefresh: () => controller.refreshData(),
              onLoadMore: () => controller.loadMoreData(),
              child: controller.data.isEmpty
                  ? const ListPageEmptyWidget()
                  : _buildListView(controller.data)),
        );
      },
      id: MatchAssemblePlaceHallController().refreshId,
    );
  }

  Widget _buildListView(List<MatchTeamResultEntity> list) {
    return ListView.separated(
      padding: EdgeInsets.only(top: 10.h),
      itemBuilder: (context, index) {
        MatchTeamResultEntity entity = list[index];
        return _buildListItem(entity);
      },
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 10.h,
        );
      },
      itemCount: list.length,
    );
  }

  Widget _buildListItem(MatchTeamResultEntity entity) {
    return GestureDetector(
      onTap: () {
        if (controller.checkIsTeamOwner(entity)) {
          if (!GlobalFloatingManager()
              .currentIsShowMiniWindow(teamEntity: entity)) {
            controller.enterTeam(entity.teamId!, entity.teamNo!);
          }
        }
      },
      child: Container(
        margin: EdgeInsets.only(left: 10.w, right: 15.w),
        padding: EdgeInsets.only(top: 20.h, bottom: 10.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildListAvatar(entity),
            _buildListTitle(entity),
            const Spacer(),
            _buildRightBtn(entity),
          ],
        ),
      ),
    );
  }

  Widget _buildListAvatar(MatchTeamResultEntity entity) {
    return Padding(
      padding: EdgeInsets.only(
        left: 10.w,
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          ClipOval(
            child: ImageUtils.getImage(entity.teamImg ?? "", 60.w, 60.w,
                fit: BoxFit.cover),
          ),
          Positioned(
            bottom: 0,
            child: Visibility(
              visible: entity.teamNum == entity.teamTotalNum,
              child: Container(
                alignment: Alignment.center,
                width: 42.w,
                height: 20.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.r),
                  color: AppColors.colorFF23AF28,
                  border: Border.all(color: Colors.white),
                ),
                child: Text(
                  S.current.fulled,
                  style: TextStyles.common(12.sp, Colors.white),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0.h,
            child: Visibility(
              visible: entity.teamLockState == 1,
              child: Container(
                width: 42.w,
                height: 20.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.r),
                  color: AppColors.colorFF23AF28,
                  border: Border.all(color: Colors.white),
                ),
                child: Row(
                  children: [
                    SizedBox(width: 1.w),
                    Icon(
                      Icons.lock,
                      color: Colors.white,
                      size: 12.w,
                    ),
                    Text(
                      S.current.locked,
                      style: TextStyles.common(12.sp, Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListTitle(MatchTeamResultEntity entity) {
    List<String>? labels = [];
    if (entity.teamLabels != null && entity.teamLabels!.length > 3) {
      labels = entity.teamLabels!.sublist(0, 3);
    } else {
      labels = entity.teamLabels;
    }

    double textHeight = StringUtils.calculateTextHeight(
      entity.teamName ?? "",
      maxWidth: 114.w,
      textStyle: TextStyles.medium(16.sp),
    );

    bool showToolTip = false;
    double textMaxHeight = 50.h;
    if (textHeight > textMaxHeight) {
      showToolTip = true;
    }

    return Padding(
      padding: EdgeInsets.only(
        left: 10.w,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                constraints: BoxConstraints(
                  maxWidth: 114.w,
                  maxHeight: 42.h,
                ),
                child: showToolTip
                    ? TolyTooltip(
                        placement: Placement.bottom,
                        message: entity.teamName,
                        triggerMode: TooltipTriggerMode.tap,
                        maxWidth: 190.w,
                        gap: 5.h,
                        textStyle:
                            TextStyles.common(14.sp, Colors.white, h: 1.3),
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.w, vertical: 5.h),
                        child: Text(
                          entity.teamName ?? "",
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyles.medium(16.sp),
                        ),
                      )
                    : Text(
                        entity.teamName ?? "",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyles.medium(16.sp),
                      ),
              ),
              Text(
                "（${entity.teamNum ?? 0}/${entity.teamTotalNum}${S.current.people}）",
                style: TextStyles.medium(16.sp),
              ),
            ],
          ),
          labels?.isNotEmpty == true
              ? Container(
                  padding: EdgeInsets.only(top: 5.h),
                  constraints: BoxConstraints(
                    maxWidth: 200.w,
                    minWidth: 180.w,
                  ),
                  child: Wrap(
                    spacing: 5.w,
                    runSpacing: 5.h,
                    children: labels!
                        .map(
                          (e) => LabelItemWidget(
                            text: e,
                            editable: false,
                            minWidth: 50.w,
                            height: 20.h,
                            fontSize: 12.sp,
                            borderRadius: 10.r,
                            textMaxLength: 13,
                            bgColor: AppColors.colorFFEAECF1,
                            borderColor: Colors.transparent,
                          ),
                        )
                        .toList(),
                  ),
                )
              : Container(),
        ],
      ),
    );
  }

  Widget _buildRightBtn(MatchTeamResultEntity entity) {
    return GestureDetector(
      onTap: () {
        if (GlobalFloatingManager()
            .currentIsShowMiniWindow(teamEntity: entity)) {
          return;
        }
        if (entity.teamLockState == 1) {
          ToastUtils.showDialog(
            dialog: JoinTeamInputPwdDialog(
              title: S.current.lockPwd,
              subTitle: S.current.setTeamPwd,
              callback: (pwd) {
                controller.joinTeam(entity.teamId!, entity.teamNo!,
                    pwd: pwd.toString());
              },
            ),
          );
        } else {
          controller.joinTeam(entity.teamId!, entity.teamNo!);
        }
      },
      child: Container(
        width: 60.w,
        height: 30.h,
        alignment: Alignment.center,
        margin: EdgeInsets.only(right: 15.w, top: 12.5.h),
        decoration: BoxDecoration(
          color: AppColors.colorFF89E15C,
          borderRadius: BorderRadius.circular(15.r),
        ),
        child: Text(
          "+ ${S.current.join}",
          style: TextStyles.normal(14.sp),
        ),
      ),
    );
  }
}
