import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/verification_box/verification_box.dart';
import 'package:dada/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class JoinTeamInputPwdDialog extends StatelessWidget {
  final String title;
  final String subTitle;
  final Function(int) callback;

  const JoinTeamInputPwdDialog(
      {super.key,
      required this.title,
      required this.subTitle,
      required this.callback});

  @override
  Widget build(BuildContext context) {
    String inputNumber = "";
    return GradientWidget(
      cornerRadius: 20.r,
      width: ScreenUtil().screenWidth - 52.5.w * 2,
      height: 210.h,
      colors: const [AppColors.colorFFD2F6C0, Colors.white],
      stops: const [0, 0.45],
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 25.h),
            child: Text(
              title,
              style: TextStyles.medium(16.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: Text(
              subTitle,
              style: TextStyles.common(14.sp, AppColors.colorFF666666),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 15.h, left: 35.w, right: 35.w),
            height: 45.h,
            child: VerificationBox(
              count: 4,
              textStyle: TextStyles.normal(16.sp),
              focusBorderColor: AppColors.colorFF23AF28,
              onSubmitted: (value) {
                inputNumber = value;
              },
            ),
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(bottom: 28.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    width: 80.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(17.5.r),
                      border: Border.all(color: AppColors.colorFF999999),
                    ),
                    child: Text(
                      S.current.cancel,
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                ),
                SizedBox(width: 10.w,),
                GestureDetector(
                  onTap: () {
                    callback.call(int.parse(inputNumber));
                    Get.back();
                  },
                  child: Container(
                    width: 80.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [AppColors.colorFFA0F6A5, AppColors.colorFF58C75D],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(17.5.r),
                    ),
                    child: Text(
                      S.current.sure,
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
