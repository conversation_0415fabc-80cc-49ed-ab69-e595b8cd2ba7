import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/match_team_member_entity.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class MatchAssemblePlaceHallController extends ListPageController<
    MatchTeamResultEntity, MatchAssemblePlaceHallController> {
  List<MatchTeamResultEntity>? searchResultList = [];
  RxInt showFilledRoom = 1.obs;
  RxInt showLockedRoom = 1.obs;

  @override
  Future<List<MatchTeamResultEntity>?> loadData(int page) async {
    List<MatchTeamResultEntity>? list = await ApiService()
        .getAssemblePlaceHallList(
            pageIndex: page,
            showFilled: showFilledRoom.value,
            showLocked: showLockedRoom.value);
    return list;
  }

  ///SearchKey可以是teamNo or teamName
  Future<void> searchTeam(String searchKey) async {
    if (searchKey.trimRight().isNotEmpty) {
      bool isPureNum = StringUtils.isPureNumber(searchKey);
      searchResultList = await ApiService().searchTeam(
        teamNo: isPureNum ? searchKey : null,
        teamName: isPureNum ? null : searchKey,
        showFilled: showFilledRoom.value,
        showLocked: showLockedRoom.value,
      );
      if (searchResultList?.isNotEmpty == true) {
        update([refreshId]);
      } else {
        ToastUtils.showToast(S.current.searchTeamNoResultTip);
      }
      return;
    }
  }

  void clearSearchResult() {
    searchResultList?.clear();
    update([refreshId]);
  }

  void joinTeam(String teamId, String groupID, {String? pwd}) async {
    if (GlobalFloatingManager().teamChatController?.teamEntity.value?.teamId ==
        teamId) {
      V2TimConversation conversation = await ChatIMManager.sharedInstance
          .getConversation(groupID: groupID.toString(), type: 2);
      Get.toNamed(
        GetRouter.teamDetail,
        parameters: {"teamId": teamId},
        arguments: {"conversation": conversation},
      )?.then((value) {
        if (value != null) {
          refreshData();
        }
      });
      return;
    }

    MatchTeamResultEntity? entity =
        await ApiService().joinTeam(teamId: teamId, pwd: pwd);
    if (entity != null) {
      bool joinSuccess = await ChatIMManager.sharedInstance.joinGroup(
          groupType: GroupType.Meeting, groupID: groupID, message: '');
      if (joinSuccess) {
        V2TimConversation conversation = await ChatIMManager.sharedInstance
            .getConversation(groupID: groupID.toString(), type: 2);
        Get.toNamed(
          GetRouter.teamDetail,
          parameters: {"teamId": teamId},
          arguments: {"conversation": conversation},
        )?.then((value) {
          if (value != null) {
            refreshData();
          }
        });
      } else {
        ToastUtils.showToast("加入小队失败，请稍后再试！");
        await ApiService().exitTeam(teamId: teamId);
      }
    }
  }

  void enterTeam(String teamId, String groupID) async {
    V2TimConversation conversation = await ChatIMManager.sharedInstance
        .getConversation(groupID: groupID, type: 2);
    Get.toNamed(
      GetRouter.teamDetail,
      parameters: {"teamId": teamId.toString()},
      arguments: {"conversation": conversation},
    );
  }

  bool checkIsTeamOwner(MatchTeamResultEntity teamEntity) {
    ///是否是群主
    if (teamEntity.teamMembers == null ||
        teamEntity.teamMembers?.isEmpty == true) {
      return false;
    }
    bool roomOwner = false;
    for (int i = 0; i < teamEntity.teamMembers!.length; i++) {
      MatchTeamMemberEntity member = teamEntity.teamMembers![i];
      if (member.userId == UserService().user!.id && member.role == 2) {
        roomOwner = true;
        break;
      }
    }
    return roomOwner;
  }
}
