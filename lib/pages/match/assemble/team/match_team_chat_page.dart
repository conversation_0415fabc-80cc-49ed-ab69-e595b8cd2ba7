import 'dart:convert';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_audio_msg_tooltip_item.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/components/widgets/popup_menu_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/match_team_member_entity.dart';
import 'package:dada/pages/chat/detail/card_box/user_card_box_bottom_sheet.dart';
import 'package:dada/pages/chat/detail/chat_custom_msg_item.dart';
import 'package:dada/pages/match/assemble/hall/join_team_input_pwd_dialog.dart';
import 'package:dada/pages/match/assemble/team/invite/invite_selected_user_page.dart';
import 'package:dada/pages/match/assemble/team/match_team_chat_controller.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/utils/emoji_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tolyui/tolyui.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class MatchTeamChatPage extends StatefulWidget {
  const MatchTeamChatPage({super.key});

  @override
  State<MatchTeamChatPage> createState() => _MatchTeamChatPageState();
}

class _MatchTeamChatPageState extends State<MatchTeamChatPage> {
  MatchTeamChatController controller = Get.put(MatchTeamChatController());

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      WakelockPlus.enable();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (controller.conversation == null) {
      return const LoadingWidget();
    }

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        debugPrint("didPop: $didPop, result: $result");
        if (didPop && result == null) {
          GlobalFloatingManager().showMiniWindow(teamController: controller);
        }
        WakelockPlus.disable();
      },
      child: Stack(
        children: [
          TIMUIKitChat(
            conversation: controller.conversation!,
            controller: controller.timuiKitChatController,
            extraTipsActionItemBuilder: (message, closeTip, [key, context]) {
              return Container();
            },
            customAppBar: _buildTopWidget(),
            userAvatarBuilder: (BuildContext context, V2TimMessage message) {
              return ClipOval(
                child: ImageUtils.getImage(message.faceUrl ?? "", 50, 50,
                    fit: BoxFit.cover, showPlaceholder: true),
              );
            },
            config: TIMUIKitChatConfig(
              isShowGroupReadingStatus: false,
              isUseMessageReaction: false,
              isShowReadingStatus: false,
              // isAllowEmojiPanel: false,
              stickerPanelConfig: StickerPanelConfig(
                useQQStickerPackage: false,
                useTencentCloudChatStickerPackage: false,
                customStickerPackages:
                    EmojiUtils().customEmojiStickerPackageList ?? [],
              ),
            ),
            customEmojiStickerList: EmojiUtils().customEmojiFaceDataList ?? [],
            onTapAvatar: (userId, tapDetails) {
              Get.toNamed(GetRouter.userProfile,
                  parameters: {"userId": userId});
            },
            morePanelConfig: MorePanelConfig(
              showFilePickAction: false,
              extraAction: _buildExtraMorePanelActions(),
            ),
            toolTipsConfig: ToolTipsConfig(
              showForwardMessage: false,
              showMultipleChoiceMessage: false,
              showRecallMessage: false,
              showTranslation: false,
              additionalMessageToolTips: (message, closeToolTip) {
                if (message.elemType == MessageElemType.V2TIM_ELEM_TYPE_SOUND) {
                  return [
                    CustomAudioMsgTooltipItem.build(
                        message: message, closeTooltip: closeToolTip),
                  ];
                }
                return [];
              },
            ),
            messageItemBuilder: MessageItemBuilder(
              customMessageItemBuilder:
                  (V2TimMessage message, isShowJump, clearJump) {
                if (message.customElem != null &&
                    message.elemType ==
                        MessageElemType.V2TIM_ELEM_TYPE_CUSTOM) {
                  return ChatCustomMsgItem(message: message);
                }
                return Container();
              },
            ),
          ),
          _buildMicrophoneAndSpeakerBtn(),
          _buildTeamMatchStateBtn(),
        ],
      ),
    );
  }

  Widget _buildTopWidget() {
    return Column(
      children: [
        CustomAppBar(
          backAction: () {
            GlobalFloatingManager().showMiniWindow(teamController: controller);
            Get.back();
          },
          backgroundColor: AppColors.colorFFF5F5F5,
          centerWidget: _buildTitleWidget(),
          rightWidgets: [_buildAppBarRightBtn()],
        ),
        _buildTeamUserListWidget(),
        _buildLockStateAndLabelsWidget(),
      ],
    );
  }

  Widget _buildTitleWidget() {
    return Obx(
      () {
        bool showToolTip = false;
        double titleMaxWidth = 200.w;
        double textWidth = StringUtils.calculateTextWidth(
          controller.teamEntity.value?.teamName ?? "",
          textStyle: TextStyles.normal(16.sp),
        );
        if (textWidth > titleMaxWidth) {
          showToolTip = true;
        }
        return Container(
          padding: EdgeInsets.only(right: 15.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                constraints: BoxConstraints(
                  maxWidth: titleMaxWidth,
                ),
                child: showToolTip
                    ? TolyTooltip(
                        gap: 10.h,
                        triggerMode: TooltipTriggerMode.tap,
                        placement: Placement.bottom,
                        message: controller.teamEntity.value?.teamName ?? "",
                        child: Text(
                          controller.teamEntity.value?.teamName ?? "",
                          style: TextStyles.common(16.sp, Colors.black),
                        ),
                      )
                    : Text(
                        controller.teamEntity.value?.teamName ?? "",
                        overflow: TextOverflow.ellipsis,
                        style: TextStyles.common(16.sp, Colors.black),
                      ),
              ),
              Text(
                "(${controller.teamEntity.value?.teamNum ?? "0"}/${controller.teamEntity.value?.teamTotalNum ?? "0"})",
                style: TextStyles.common(16.sp, Colors.black),
              ),
              Obx(
                () => Visibility(
                  visible: controller.locked.value == true,
                  child: Padding(
                    padding: EdgeInsets.only(left: 5.w, top: 3.h),
                    child: ImageUtils.getImage(
                        Assets.imagesTeamChatTitleLock, 20.w, 20.w),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAppBarRightBtn() {
    return Padding(
      padding: EdgeInsets.only(right: 15.w),
      child: PopupMenuWidget(
        offset: Offset(20.w, 30.h),
        titles: [
          S.current.report,
          S.current.leave,
        ],
        onSelected: (value) async {
          if (value == 0) {
            //举报
            Get.to(() => ReportPage(
                reportType: ReportType.team, groupId: controller.teamId));
          } else if (value == 1) {
            //离开
            controller.exitRoom();
          }
        },
        child: Icon(
          Icons.more_horiz,
          size: 20.w,
          color: AppColors.colorFF999999,
        ),
      ),
    );
  }

  Widget _buildTeamUserListWidget() {
    return GetBuilder(
        init: controller,
        builder: (controller) {
          if (controller.teamEntity.value?.teamMembers == null ||
              controller.teamEntity.value?.teamMembers?.isEmpty == true) {
            return Container();
          }
          List<MatchTeamMemberEntity> list = [];
          list.addAll(controller.teamEntity.value!.teamMembers!);
          return Row(
            children: [
              Expanded(
                child: Container(
                  color: Colors.white,
                  height: 80.h,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    separatorBuilder: (context, index) {
                      return SizedBox(width: 10.w);
                    },
                    itemBuilder: (context, index) {
                      MatchTeamMemberEntity member = list[index];
                      return _buildTeamMemberListItem(member);
                    },
                    itemCount: list.length,
                  ),
                ),
              ),
              Visibility(
                visible: controller.checkIsRoomOwner(),
                child: Container(
                  width: 1.5.w,
                  height: 80.h,
                  color: AppColors.colorFFF5F5F5,
                ),
              ),
              Visibility(
                visible: controller.checkIsRoomOwner(),
                child: Container(
                  width: 80.w,
                  height: 80.h,
                  color: Colors.white,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          if (controller
                                  .teamEntity.value!.teamMembers!.length >=
                              controller.teamEntity.value!.teamTotalNum!) {
                            return;
                          }
                          Get.toNamed(GetRouter.createGroupChat,
                                  parameters: {"isInviteMember": "1"},
                                  arguments: controller
                                      .transferTeamMemberListToGroupMemberList())
                              ?.then((value) {
                            if (value != null) {
                              if (value is List<String>) {
                                controller.sendInviteMsgToUsers(value);
                              }
                            }
                          });
                        },
                        child: ImageUtils.getImage(
                            controller.teamEntity.value!.teamMembers!.length >=
                                    controller.teamEntity.value!.teamTotalNum!
                                ? Assets.imagesTeamChatInviteMemberDisable
                                : Assets.imagesTeamChatInviteMemberNormal,
                            52.w,
                            30.h),
                      ),
                      SizedBox(
                        height: 5.h,
                      ),
                      GestureDetector(
                        onTap: () {
                          if (controller
                                  .teamEntity.value!.teamMembers!.length <=
                              1) {
                            return;
                          }
                          Get.to(
                            () => InviteSelectedUserPage(
                              userList:
                                  controller.transferMemberListToFriendList(),
                              isSingleSelected: true,
                              isDeleteMember: true,
                              callback: (userIds) {
                                String userId = userIds.first;
                                controller.kickOutOfMember(userId);
                              },
                            ),
                          );
                        },
                        child: ImageUtils.getImage(
                            controller.teamEntity.value!.teamMembers!.length <=
                                    1
                                ? Assets.imagesTeamChatKickoutMemberDisable
                                : Assets.imagesTeamChatKickoutMemberNormal,
                            52.w,
                            30.h),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        });
  }

  Widget _buildTeamMemberListItem(MatchTeamMemberEntity member) {
    return Container(
      width: 56.w,
      height: 80.h,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.bottomCenter,
            children: [
              ClipOval(
                child: ImageUtils.getImage(member.avatar ?? "", 40.w, 40.w,
                    fit: BoxFit.cover),
              ),
              Visibility(
                visible: member.role == 2,
                child: Positioned(
                  bottom: -1,
                  child: ImageUtils.getImage(
                      Assets.imagesMatchTeamChatMemberOwner, 35.w, 21.h),
                ),
              ),
              Obx(
                () {
                  int? volume = controller.userVolume[member.userId];
                  return Visibility(
                    visible: volume != null && volume > 0,
                    child: Positioned(
                      top: -5.h,
                      right: -5.w,
                      child: ImageUtils.getImage(
                          Assets.imagesChatRoomSeatSpeakingStateIcon,
                          15.w,
                          15.w),
                    ),
                  );
                },
              ),
            ],
          ),
          Container(
            padding: EdgeInsets.only(top: 1.h),
            constraints: BoxConstraints(
              maxWidth: 56.w,
            ),
            child: Text(
              member.nickname ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.normal(14.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLockStateAndLabelsWidget() {
    return Container(
      height: 40.h,
      padding: EdgeInsets.only(top: 5.h, left: 15.w, bottom: 5.h),
      color: AppColors.colorFFF5F5F5,
      child: Row(
        children: [
          _buildLockStateWidget(),
          _buildTeamLabelsWidget(),
        ],
      ),
    );
  }

  Widget _buildLockStateWidget() {
    return Obx(() {
      bool locked = controller.locked.value;
      return Container(
        width: locked ? 100.w : 55.w,
        height: 25.h,
        decoration: BoxDecoration(
          color: AppColors.colorFFEAECF1,
          borderRadius: BorderRadius.circular(25.h / 2),
        ),
        child: GestureDetector(
          onTap: () {
            if (!controller.checkIsRoomOwner()) {
              ToastUtils.showToast("仅房主才能上锁/解锁");
              return;
            }
            if (!locked) {
              ToastUtils.showDialog(
                dialog: JoinTeamInputPwdDialog(
                  title: S.current.lockPwd,
                  subTitle: "请设置集结处密码",
                  callback: (pwd) {
                    controller.updateTeamLockState(password: pwd);
                  },
                ),
              );
            } else {
              controller.updateTeamLockState();
            }
          },
          child: locked
              ? Row(
                  children: [
                    Container(
                      width: 55.w,
                      height: 25.h,
                      margin: EdgeInsets.only(right: 6.w),
                      decoration: BoxDecoration(
                        color: AppColors.colorFF23AF28,
                        borderRadius: BorderRadius.circular(25.h / 2),
                      ),
                      child: Row(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.h),
                            child: Container(
                              width: 21.w,
                              height: 21.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(21.w / 2),
                              ),
                              child: Icon(
                                Icons.lock,
                                size: 12.w,
                                color: AppColors.colorFF23AF28,
                              ),
                            ),
                          ),
                          Text(
                            S.current.lock,
                            style: TextStyles.common(12.sp, Colors.white),
                          ),
                        ],
                      ),
                    ),
                    Visibility(
                      visible:
                          controller.teamEntity.value?.teamPassword != null &&
                              controller.checkIsRoomOwner(),
                      child: Text(
                        controller.teamEntity.value?.teamPassword ?? "",
                        style:
                            TextStyles.common(14.sp, AppColors.colorFF666666),
                      ),
                    ),
                  ],
                )
              : Container(
                  width: 55.w,
                  height: 25.h,
                  decoration: BoxDecoration(
                    color: AppColors.colorFFD4D4D4,
                    borderRadius: BorderRadius.circular(25.h / 2),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 3.w),
                        child: Text(
                          "公开",
                          style: TextStyles.common(12.sp, Colors.white),
                        ),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.only(right: 2.w, top: 2.h, bottom: 2.h),
                        child: Container(
                          width: 21.w,
                          height: 21.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(21.w / 2),
                          ),
                          child: Icon(
                            Icons.lock_open,
                            size: 12.w,
                            color: AppColors.colorFFD4D4D4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      );
    });
  }

  Widget _buildTeamLabelsWidget() {
    return Obx(() {
      List<String> labels = [];
      if (controller.teamEntity.value?.teamLabels != null) {
        labels.addAll(controller.teamEntity.value!.teamLabels!);
      }
      if (controller.labels.length < 3 && controller.checkIsRoomOwner()) {
        labels.add("");
      }

      return Expanded(
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.only(right: 10.w, left: 28.5.w),
          separatorBuilder: (context, index) {
            return SizedBox(width: 5.w);
          },
          itemBuilder: (context, index) {
            String label = labels[index];
            return _buildLabelItemWidget(label);
          },
          itemCount: labels.length,
        ),
      );
    });
  }

  Widget _buildLabelItemWidget(String label) {
    if (label.isEmpty && controller.checkIsRoomOwner()) {
      return LabelItemWidget(
        text: S.current.addLabel,
        borderColor: AppColors.colorFFE1E1E1,
        bgColor: Colors.white,
        borderRadius: 15.h,
        editable: false,
        isAddItem: true,
        addAction: (value) {
          controller.addLabel(value);
        },
      );
    }
    return LabelItemWidget(
      text: label,
      bgColor: AppColors.colorFFEAECF1,
      borderColor: Colors.transparent,
      textColor: AppColors.colorFF666666,
      textMaxLength: 3,
      borderRadius: 15.h,
      editable: controller.checkIsRoomOwner(),
      deleteAction: () {
        int index = controller.labels.indexOf(label);
        controller.deleteLabel(index);
      },
    );
  }

  Widget _buildMicrophoneAndSpeakerBtn() {
    return Positioned(
      left: 18.w,
      bottom: 50 + ScreenUtil().bottomBarHeight + 12.h,
      child: Column(
        children: [
          Obx(
            () => GestureDetector(
              onTap: () {
                controller.micOpen.value = !controller.micOpen.value;
                if (controller.micOpen.value == true) {
                  controller.startLocalAudio();
                } else {
                  controller.stopLocalAudio();
                }
              },
              child: ImageUtils.getImage(
                  controller.micOpen.value == true
                      ? Assets.imagesMatchTeamChatMicOpen
                      : Assets.imagesMatchTeamChatMicClose,
                  40.w,
                  40.w),
            ),
          ),
          SizedBox(
            height: 15.h,
          ),
          Obx(
            () => GestureDetector(
              onTap: () {
                controller.speakerOpen.value = !controller.speakerOpen.value;
                controller.muteAllRemoteAudio(!controller.speakerOpen.value);
              },
              child: ImageUtils.getImage(
                  controller.speakerOpen.value == true
                      ? Assets.imagesMatchTeamChatSpeakerOpen
                      : Assets.imagesMatchTeamChatSpeakerClose,
                  40.w,
                  40.w),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTeamMatchStateBtn() {
    return Obx(
      () => Visibility(
        visible: controller.checkIsRoomOwner(),
        child: Positioned(
          right: 10.w,
          top: ScreenUtil().statusBarHeight + 50 + 90.h + 80.h,
          child: Column(
            children: [
              GestureDetector(
                onTap: () {
                  controller.updateMatchState();
                },
                child: ImageUtils.getImage(
                    controller.matching.value == true
                        ? Assets.imagesMatchTeamChatStateMatching
                        : Assets.imagesMatchTeamChatStatePaused,
                    50.w,
                    44.w),
              ),
              SizedBox(
                height: 5.h,
              ),
              Text(
                controller.matching.value == true
                    ? S.current.matching
                    : S.current.matchPaused,
                style: TextStyles.common(
                  14.sp,
                  controller.matching.value == true
                      ? AppColors.colorFF23AF28
                      : AppColors.colorFF666666,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<MorePanelItem> _buildExtraMorePanelActions() {
    List<MorePanelItem> list = [];
    list.add(
      MorePanelItem(
        id: "audio_box",
        title: "语音盒子",
        onTap: (c) {
          Get.toNamed(GetRouter.audioBox, parameters: {
            "isSelect": "1"
          }, arguments: {
            "groupID": controller.teamEntity.value!.teamNo,
          });
        },
        icon: Container(
          height: 64,
          width: 64,
          margin: const EdgeInsets.only(bottom: 4),
          alignment: Alignment.center,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(5))),
          child: ImageUtils.getImage(
              Assets.imagesChatDetailMorePanelAudioBox, 22.w, 22.w,
              fit: BoxFit.scaleDown),
        ),
      ),
    );

    list.add(
      MorePanelItem(
        id: "card_box",
        title: "卡盒",
        onTap: (c) {
          Get.bottomSheet(
            UserCardBoxBottomSheet(
              onSelectedCard: (cardInfo) {
                controller.sendCustomMsg(jsonEncode(cardInfo),
                    ChatImCustomMsgType.UserCardBoxCardMsg);
              },
            ),
          );
        },
        icon: Container(
          height: 50,
          width: 50,
          margin: const EdgeInsets.only(bottom: 4),
          alignment: Alignment.center,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(5))),
          child: ImageUtils.getImage(
              Assets.imagesChatDetailMorePanelCardBox, 20.w, 18.h),
        ),
      ),
    );
    return list;
  }

  @override
  void dispose() {
    super.dispose();
  }
}
