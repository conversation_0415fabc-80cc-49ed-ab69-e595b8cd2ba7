import 'package:dada/model/user_info_entity.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:get/get.dart';
import 'package:pinyin/pinyin.dart';

class InviteSelectedUserController extends GetxController {
  Map<String, List<UserInfoEntity>> userGroupMap =
      <String, List<UserInfoEntity>>{};
  List<UserInfoEntity> searchedUserList = <UserInfoEntity>[];

  List<String> indexList = [];

  RxList<String> selectedUserIds = <String>[].obs;
  RxString selectedUserId = "".obs;

  RxList<UserInfoEntity> friendAllList = <UserInfoEntity>[].obs;

  bool isSingleSelected = false;

  void setupData(List<UserInfoEntity> friendList) {
    friendAllList.value = friendList;

    ///设置索引
    setIndexListData(friendList);

    update();
  }

  void setIndexListData(List<UserInfoEntity> friendList) {
    for (int i = 0; i < friendList.length; i++) {
      UserInfoEntity userInfo = friendList[i];
      String firstCharacter = userInfo.nickname!.substring(0, 1);
      if (RegExp(r"[a-zA-Z]").hasMatch(firstCharacter)) {
        ///名字第一个字是否是英文
        addToIndexList(firstCharacter);

        ///将该用户设置到对应索引的好友数组中
        setGroupList(firstCharacter.toUpperCase(), userInfo);
      } else if (RegExp(r"[\u4e00-\u9fa5]").hasMatch(firstCharacter)) {
        ///名字第一个字是否是汉字
        final pinyin = PinyinHelper.getShortPinyin(firstCharacter);
        addToIndexList(pinyin);
        setGroupList(pinyin.toUpperCase(), userInfo);
      } else {
        addToIndexList("#");
        setGroupList("#", userInfo);
      }
    }
    if (indexList.length > 1) {
      indexList = [...indexList]..sort((String a, String b) {
          if (a == "#" || b == "#") {
            return a == "#" ? 1 : -1;
          }
          return a.compareTo(b);
        });
    }
  }

  void setGroupList(String key, UserInfoEntity user) {
    List<UserInfoEntity>? list = userGroupMap[key];
    if (list?.isNotEmpty == true) {
      if (!list!.contains(user)) {
        list.add(user);
      }
    } else {
      list = <UserInfoEntity>[];
      list.add(user);
    }
    userGroupMap[key] = list;
  }

  void addToIndexList(String firstCharacter) {
    String indexStr = firstCharacter.toUpperCase();
    if (!indexList.contains(indexStr)) {
      indexList.add(indexStr);
    }
  }

  void searchUser(String value) {
    searchedUserList.clear();
    for (int i = 0; i < friendAllList.length; i++) {
      UserInfoEntity user = friendAllList[i];
      String nickname = user.nickname!;
      if (nickname.contains(value)) {
        searchedUserList.add(user);
      }
    }
    update();
  }

  void clearSearchResult() {
    searchedUserList.clear();
    update();
  }

  bool checkUserSelected(String userId) {
    bool selected = false;
    if (isSingleSelected) {
      selected = userId == selectedUserId.value;
    } else {
      for (int i = 0; i < selectedUserIds.length; i++) {
        String id = selectedUserIds[i];
        if (id == userId) {
          selected = true;
          break;
        }
      }
    }
    return selected;
  }

  void selectUser(String userId) {
    if (isSingleSelected) {
      selectedUserId.value = userId;
    } else {
      if (selectedUserIds.contains(userId)) {
        selectedUserIds.remove(userId);
      } else {
        selectedUserIds.add(userId);
      }
    }
  }

  void getOffsetYForIndex(String key) {}

  void addTempFriendList(List<UserInfoEntity> friendList) {
    int userId = 10000;
    int i = 0;

    UserInfoEntity entity1 = UserInfoEntity();
    entity1.nickname = "阿瑶";
    entity1.id = (userId + i++).toString();
    entity1.avatar = UserService().user?.avatar;
    friendList.add(entity1);

    UserInfoEntity entity2 = UserInfoEntity();
    entity2.nickname = "毕福剑";
    entity2.id = (userId + i++).toString();
    entity2.avatar = UserService().user?.avatar;
    friendList.add(entity2);

    UserInfoEntity entity3 = UserInfoEntity();
    entity3.nickname = "迟航";
    entity3.id = (userId + i++).toString();
    entity3.avatar = UserService().user?.avatar;
    friendList.add(entity3);

    UserInfoEntity entity4 = UserInfoEntity();
    entity4.nickname = "冯绍峰";
    entity4.id = (userId + i++).toString();
    entity4.avatar = UserService().user?.avatar;
    friendList.add(entity4);

    UserInfoEntity entity5 = UserInfoEntity();
    entity5.nickname = "龚俊";
    entity5.id = (userId + i++).toString();
    entity5.avatar = UserService().user?.avatar;
    friendList.add(entity5);

    UserInfoEntity entity6 = UserInfoEntity();
    entity6.nickname = "洪亮";
    entity6.id = (userId + i++).toString();
    entity6.avatar = UserService().user?.avatar;
    friendList.add(entity6);

    UserInfoEntity entity7 = UserInfoEntity();
    entity7.nickname = "弘基";
    entity7.id = (userId + i++).toString();
    entity7.avatar = UserService().user?.avatar;
    friendList.add(entity7);

    UserInfoEntity entity8 = UserInfoEntity();
    entity8.nickname = "静仪";
    entity8.id = (userId + i++).toString();
    entity8.avatar = UserService().user?.avatar;
    friendList.add(entity8);

    UserInfoEntity entity10 = UserInfoEntity();
    entity10.nickname = "龙皓晨";
    entity10.id = (userId + i++).toString();
    entity10.avatar = UserService().user?.avatar;
    friendList.add(entity10);

    UserInfoEntity entity11 = UserInfoEntity();
    entity11.nickname = "龙语音";
    entity11.id = (userId + i++).toString();
    entity11.avatar = UserService().user?.avatar;
    friendList.add(entity11);

    UserInfoEntity entity12 = UserInfoEntity();
    entity12.nickname = "冷峰";
    entity12.id = (userId + i++).toString();
    entity12.avatar = UserService().user?.avatar;
    friendList.add(entity12);

    UserInfoEntity entity13 = UserInfoEntity();
    entity13.nickname = "蒙恬";
    entity13.id = (userId + i++).toString();
    entity13.avatar = UserService().user?.avatar;
    friendList.add(entity13);

    UserInfoEntity entity14 = UserInfoEntity();
    entity14.nickname = "蒙毅";
    entity14.id = (userId + i++).toString();
    entity14.avatar = UserService().user?.avatar;
    friendList.add(entity14);

    UserInfoEntity entity15 = UserInfoEntity();
    entity15.nickname = "梦红尘";
    entity15.id = (userId + i++).toString();
    entity15.avatar = UserService().user?.avatar;
    friendList.add(entity15);

    UserInfoEntity entity16 = UserInfoEntity();
    entity16.nickname = "梦笑笑";
    entity16.id = (userId + i++).toString();
    entity16.avatar = UserService().user?.avatar;
    friendList.add(entity16);

    UserInfoEntity entity17 = UserInfoEntity();
    entity17.nickname = "宁荣荣";
    entity17.id = (userId + i++).toString();
    entity17.avatar = UserService().user?.avatar;
    friendList.add(entity17);

    UserInfoEntity entity18 = UserInfoEntity();
    entity18.nickname = "宁天辰";
    entity18.id = (userId + i++).toString();
    entity18.avatar = UserService().user?.avatar;
    friendList.add(entity18);

    UserInfoEntity entity19 = UserInfoEntity();
    entity19.nickname = "彭少雨";
    entity19.id = (userId + i++).toString();
    entity19.avatar = UserService().user?.avatar;
    friendList.add(entity19);

    UserInfoEntity entity20 = UserInfoEntity();
    entity20.nickname = "培雨辰";
    entity20.id = (userId + i++).toString();
    entity20.avatar = UserService().user?.avatar;
    friendList.add(entity20);

    UserInfoEntity entity21 = UserInfoEntity();
    entity21.nickname = "齐昊";
    entity21.id = (userId + i++).toString();
    entity21.avatar = UserService().user?.avatar;
    friendList.add(entity21);

    UserInfoEntity entity22 = UserInfoEntity();
    entity22.nickname = "邱天刃";
    entity22.id = (userId + i++).toString();
    entity22.avatar = UserService().user?.avatar;
    friendList.add(entity22);

    UserInfoEntity entity23 = UserInfoEntity();
    entity23.nickname = "屈婷";
    entity23.id = (userId + i++).toString();
    entity23.avatar = UserService().user?.avatar;
    friendList.add(entity23);

    UserInfoEntity entity24 = UserInfoEntity();
    entity24.nickname = "曲婉婷";
    entity24.id = (userId + i++).toString();
    entity24.avatar = UserService().user?.avatar;
    friendList.add(entity24);

    UserInfoEntity entity25 = UserInfoEntity();
    entity25.nickname = "宋彬";
    entity25.id = (userId + i++).toString();
    entity25.avatar = UserService().user?.avatar;
    friendList.add(entity25);

    UserInfoEntity entity26 = UserInfoEntity();
    entity26.nickname = "苏灵韵";
    entity26.id = (userId + i++).toString();
    entity26.avatar = UserService().user?.avatar;
    friendList.add(entity26);

    UserInfoEntity entity27 = UserInfoEntity();
    entity27.nickname = "滕化元";
    entity27.id = (userId + i++).toString();
    entity27.avatar = UserService().user?.avatar;
    friendList.add(entity27);

    UserInfoEntity entity28 = UserInfoEntity();
    entity28.nickname = "杨洋";
    entity28.id = (userId + i++).toString();
    entity28.avatar = UserService().user?.avatar;
    friendList.add(entity28);

    UserInfoEntity entity29 = UserInfoEntity();
    entity29.nickname = "章泽天";
    entity29.id = (userId + i++).toString();
    entity29.avatar = UserService().user?.avatar;
    friendList.add(entity29);

    UserInfoEntity entity30 = UserInfoEntity();
    entity30.nickname = "魏武";
    entity30.id = (userId + i++).toString();
    entity30.avatar = UserService().user?.avatar;
    friendList.add(entity30);

    UserInfoEntity entity9 = UserInfoEntity();
    entity9.nickname = "孔融";
    entity9.id = (userId + i++).toString();
    entity9.avatar = UserService().user?.avatar;
    friendList.add(entity9);
  }
}
