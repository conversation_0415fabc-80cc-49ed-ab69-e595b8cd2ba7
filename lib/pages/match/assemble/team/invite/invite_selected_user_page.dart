import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_search_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/setting/group/group_chat_setting_controller.dart';
import 'package:dada/pages/match/assemble/team/invite/contacts_index_widget.dart';
import 'package:dada/pages/match/assemble/team/invite/invite_selected_user_controller.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class InviteSelectedUserPage extends StatefulWidget {
  final List<UserInfoEntity> userList;
  final String? groupID;
  final bool isSingleSelected;
  final bool? isDeleteMember;
  final bool? isTransferGroupOwner;
  final Function(List<String> userIds)? callback;

  const InviteSelectedUserPage(
      {super.key,
      required this.userList,
      required this.isSingleSelected,
      required this.callback,
      this.groupID,
      this.isDeleteMember,
      this.isTransferGroupOwner});

  @override
  State<InviteSelectedUserPage> createState() => _InviteSelectedUserPageState();
}

class _InviteSelectedUserPageState extends State<InviteSelectedUserPage> {
  final controller = Get.put(InviteSelectedUserController());
  TextEditingController editingController = TextEditingController();
  ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    controller.setupData(widget.userList);
    controller.isSingleSelected = widget.isSingleSelected;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFF5F5F5,
      appBar: CustomAppBar(
        backgroundColor: AppColors.colorFFF5F5F5,
        title: widget.isTransferGroupOwner == true
            ? "选择新群主"
            : S.current.selectMember,
        rightWidgets: [_rightBarButton()],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildListWidget(),
        ],
      ),
    );
  }

  Widget _rightBarButton() {
    if (widget.isTransferGroupOwner == true) {
      return Container();
    }
    return Obx(
      () {
        bool enabled = widget.isSingleSelected
            ? controller.selectedUserId.isNotEmpty
            : controller.selectedUserIds.isNotEmpty;
        return GestureDetector(
          onTap: () {
            if (enabled) {
              if (widget.isDeleteMember == true) {
                ToastUtils.showDialog(
                    content: "确定删除",
                    topImg: null,
                    onConfirm: () {
                      if (widget.isSingleSelected) {
                        widget.callback
                            ?.call([controller.selectedUserId.value]);
                        Get.back();
                      } else {
                        widget.callback?.call(controller.selectedUserIds);
                        Get.back();
                      }
                    });
              } else {
                if (widget.isSingleSelected) {
                  widget.callback?.call([controller.selectedUserId.value]);
                  Get.back();
                } else {
                  widget.callback?.call(controller.selectedUserIds);
                  Get.back(result: controller.selectedUserIds);
                }
              }
            }
          },
          child: Container(
            width: 56.w,
            height: 30.h,
            margin: EdgeInsets.only(right: 15.w),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color:
                  enabled ? AppColors.colorFF89E15C : AppColors.colorFFE1E1E1,
              borderRadius: BorderRadius.circular(15.r),
            ),
            child: Text(
              S.current.finish,
              style: TextStyles.common(14.sp,
                  enabled ? AppColors.colorFF3D3D3D : AppColors.colorFF999999),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
        height: 50.h,
        color: AppColors.colorFFF5F5F5,
        margin:
            EdgeInsets.only(left: 15.w, right: 15.w, top: 5.h, bottom: 10.h),
        child: CustomSearchBar(
          controller: editingController,
          bgColor: Colors.white,
          onChanged: (value) {
            if (value.isEmpty) {
              controller.clearSearchResult();
            }
          },
          onSubmit: (value) {
            controller.searchUser(editingController.text);
          },
        ));
  }

  Widget _buildListWidget() {
    return Expanded(
      child: GetBuilder(
        init: controller,
        global: false,
        builder: (controller) {
          if (controller.searchedUserList.isEmpty &&
              controller.userGroupMap.isEmpty) {
            return EmptyWidget(
              backgroundColor: Colors.white,
            );
          }
          if (controller.searchedUserList.isNotEmpty) {
            return _searchResultList();
          }
          return _buildAllFriendList();
        },
      ),
    );
  }

  Widget _buildAllFriendList() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          width: ScreenUtil().screenWidth,
          color: Colors.white,
          child: ListView.builder(
            padding: EdgeInsets.only(bottom: 550.h),
            controller: scrollController,
            itemBuilder: (context, index) {
              String groupTitle = controller.indexList[index];
              List<UserInfoEntity>? users = controller.userGroupMap[groupTitle];
              if (users?.isNotEmpty == true) {
                return _buildUserGroupWidget(groupTitle, users!);
              }
              return Container();
            },
            itemCount: controller.indexList.length,
          ),
        ),
        ContactsIndexWidget(
          indexList: controller.indexList,
          currentSelectedIndexCallback: (index) {
            updateOffsetYToIndex(index);
          },
        ),
      ],
    );
  }

  Widget _buildUserGroupWidget(String groupTitle, List<UserInfoEntity> users) {
    List<Widget> children = <Widget>[];
    for (int i = 0; i < users.length; i++) {
      children.add(_buildUserListItem(users[i]));
    }
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 30.h,
            padding: EdgeInsets.only(left: 15.w, top: 10.h),
            child: Text(
              groupTitle,
              style: TextStyles.normal(16.sp),
            ),
          ),
          Column(
            children: children,
          ),
        ],
      ),
    );
  }

  Widget _searchResultList() {
    return ListView.builder(
      padding: EdgeInsets.only(top: 10.h),
      itemBuilder: (context, index) {
        UserInfoEntity entity = controller.searchedUserList[index];
        return _buildUserListItem(entity);
      },
      itemCount: controller.searchedUserList.length,
    );
  }

  Widget _buildUserListItem(UserInfoEntity user) {
    return Obx(() {
      bool selected = controller.checkUserSelected(user.id!);
      return InkWell(
        onTap: () {
          if (widget.isTransferGroupOwner == true && widget.groupID != null) {
            showTransferGroupOwnerDialog(
                widget.groupID!, user.id!, user.nickname!);
          } else {
            controller.selectUser(user.id!);
          }
        },
        child: Container(
          alignment: Alignment.center,
          height: 70.h,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Row(
                children: [
                  Visibility(
                    visible: !(widget.isTransferGroupOwner == true),
                    child: Padding(
                      padding: EdgeInsets.only(left: 15.w),
                      child: Icon(
                        selected
                            ? Icons.radio_button_checked
                            : Icons.radio_button_off,
                        size: 16.w,
                        color: selected
                            ? AppColors.colorFF23AF28
                            : AppColors.colorFF999999,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                        left: widget.isTransferGroupOwner == true ? 15.w : 5.w),
                    child: ClipOval(
                      child: ImageUtils.getImage(user.avatar ?? "", 40.w, 40.w,
                          fit: BoxFit.cover),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 5.w),
                    child: Text(
                      user.nickname ?? "",
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                ],
              ),
              Column(
                children: [
                  const Spacer(),
                  Container(
                    margin: EdgeInsets.only(left: 15.w, right: 15.w),
                    width: ScreenUtil().screenWidth - 15.w * 2,
                    height: 1,
                    color: AppColors.colorFFE5E5E5,
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  void updateOffsetYToIndex(String indexKey) {
    double offsetY = 0;
    int index = controller.indexList.indexOf(indexKey);
    for (int i = 0; i < index; i++) {
      String groupKey = controller.indexList[i];
      int groupUsersCount = controller.userGroupMap[groupKey]!.length;
      offsetY += (30.h + 70.h * groupUsersCount);
    }
    scrollController.animateTo(offsetY,
        duration: const Duration(milliseconds: 500), curve: Curves.linear);
  }

  void showTransferGroupOwnerDialog(
      String groupID, String toUserID, String toUserName) async {
    ToastUtils.showDialog(
      content: "确定选择 $toUserName 为新群主，你将自动放弃群主身份。",
      onConfirm: () async {
        bool success = await Get.find<GroupChatSettingController>()
            .updateGroupInfo(creatorId: toUserID);
        success = await ChatIMManager.sharedInstance
            .transferGroupOwner(groupID: groupID, userID: toUserID);
        if (success) {
          ToastUtils.showToast("群主权限转让成功！");
          Get.back(result: "1");
        }
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
