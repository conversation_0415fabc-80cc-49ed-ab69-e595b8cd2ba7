import 'dart:math';

import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ContactsIndexWidget extends StatefulWidget {
  final List<String> indexList;
  final Function(String index) currentSelectedIndexCallback;

  const ContactsIndexWidget(
      {super.key,
      required this.indexList,
      required this.currentSelectedIndexCallback});

  @override
  State<ContactsIndexWidget> createState() => _ContactsIndexWidgetState();
}

class _ContactsIndexWidgetState extends State<ContactsIndexWidget> {
  RxDouble indexY = 0.0.obs;
  RxBool showBubble = false.obs;
  RxString selectedIndex = "".obs;

  @override
  Widget build(BuildContext context) {
    double indexWidgetH = 20.h * 2 + widget.indexList.length * 20.h;
    return Positioned(
      right: 0,
      width: 120.w,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ///指示器
          Obx(
            () => Visibility(
              visible: showBubble.value,
              child: Container(
                margin: EdgeInsets.only(top: indexY.value, right: 5.w),
                child: Container(
                  width: 30.w,
                  height: 30.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    // image: DecorationImage(
                    //   image: AssetImage(Assets.imagesDialogBottomClose),
                    //   fit: BoxFit.cover,
                    // ),
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  child: Text(
                    selectedIndex.value,
                    style: TextStyles.common(14.sp, Colors.white),
                  ),
                ),
              ),
            ),
          ),

          GestureDetector(
            onVerticalDragDown: (DragDownDetails details) {
              showBubble.value = true;
              setCurrentIndex(details.localPosition.dy);
            },
            onVerticalDragUpdate: (DragUpdateDetails details) {
              if (details.localPosition.dy < 0) {
                return;
              } else if (details.localPosition.dy > indexWidgetH){
                return;
              }
              showBubble.value = true;
              setCurrentIndex(details.localPosition.dy);
            },
            onVerticalDragEnd: (DragEndDetails details) {
              showBubble.value = false;
            },
            onTapUp: (TapUpDetails details) {
              showBubble.value = false;
            },
            child: Container(
              padding: EdgeInsets.only(
                  right: 10.w, left: 10.w, top: 20.h, bottom: 20.h),
              child: Column(
                children: widget.indexList
                    .map(
                      (e) => SizedBox(
                        height: 20.h,
                        width: 15.w,
                        child: Text(
                          e,
                          style: TextStyles.normal(14.sp),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void setCurrentIndex(double offsetY) {
    int chu = ((offsetY - 20.h) / 20.h).toInt();
    int index = min(max(0, chu), widget.indexList.length - 1);
    indexY.value = max(offsetY - 16.h, 0);
    selectedIndex.value = widget.indexList[index];
    widget.currentSelectedIndexCallback.call(selectedIndex.value);
  }
}
