import 'dart:async';
import 'dart:convert';

import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/audio_box_list_item_entity.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/match_team_member_entity.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/trtc/chat_room_rtc_event.dart';
import 'package:dada/services/trtc/chat_room_rtc_manager.dart';
import 'package:dada/services/trtc/chat_room_service_subscribe_delegate.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';
import 'package:tencent_trtc_cloud/trtc_cloud_def.dart';

class MatchTeamChatController extends BasePageController {
  /// 服务器生成的 teamId, 同时用于创建 Meeting Group 的 GroupID.
  String? teamId;
  Rx<MatchTeamResultEntity?> teamEntity = Rx(null);

  /// 聊天会话（进房前创建群组聊天时生成新的，或根据GroupID获取）
  V2TimConversation? conversation;

  RxBool micOpen = true.obs;
  RxBool speakerOpen = true.obs;
  RxBool matching = true.obs;
  RxBool locked = false.obs;

  RxList<String> labels = <String>[].obs;

  /// 消息接收器、发送器等
  final TIMUIKitChatController timuiKitChatController =
      TIMUIKitChatController();

  ///TRTCManager
  TRTCManager trtcManager = TRTCManager.sharedInstance;

  bool isComeFromMini = Get.parameters["comeFromMini"] == "1";

  ///席位音量监听
  RxMap<String, int> userVolume = <String, int>{}.obs;

  ChatRoomRTCListener? _rtcListener;

  final AudioPlayerUtils audioPlayer = AudioPlayerUtils();
  final Map<String, SVGAAnimationController> svgaControllerMap = {};

  @override
  void onInit() async {
    super.onInit();

    ///teamId用来作为服务器接口主参数，teamNo用来作为TRTC SDK 群组ID
    teamId = Get.parameters["teamId"];
    conversation = Get.arguments["conversation"];

    audioPlayer.onInit();
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.complete) {
        stopOtherAnimation();
        update();
      }
    });
  }

  @override
  void onReady() async {
    super.onReady();
    if (conversation == null) {
      ToastUtils.showToast("进入小队失败");
      resetMicAndSpeakerState();
      Get.back(result: -1);
      return;
    }
    GlobalFloatingManager().hideMiniWindow();
    super.onReady();
    await loadData();
    await enterRoom();
    await addRTCService();
  }

  Future<void> enterRoom() async {
    if (teamEntity.value?.teamNo != null) {
      if (!isComeFromMini) {
        await trtcManager.enterRoom(
          roomId: int.parse(teamEntity.value!.teamNo!),
          userId: UserService().user!.id!,
          role: TRTCCloudDef.TRTCRoleAnchor,
          scene: TRTCCloudDef.TRTC_APP_SCENE_VOICE_CHATROOM,
        );
        await startLocalAudio();
        updateMicAndSpeakerState();
      } else {
        updateMicAndSpeakerState();
      }
    }
  }

  Future<void> startLocalAudio() async {
    trtcManager.localMicState = 1;
    await trtcManager.startLocalAudio();
  }

  Future<void> stopLocalAudio() async {
    trtcManager.localMicState = 0;
    await trtcManager.stopLocalAudio();
  }

  Future<void> muteAllRemoteAudio(bool mute) async {
    trtcManager.localSpeakerState = mute ? 0 : 1;
    await trtcManager.muteAllRemoteAudio(mute);
  }

  Future<void> updateMatchState() async {
    bool success = await ApiService().editTeamInfo(
        teamId: teamId ?? teamEntity.value!.teamId!,
        teamMatchState: matching.value == true ? 0 : 1);
    if (success) {
      matching.value = !matching.value;

      ///发送自定义消息通知远端，变更匹配按钮状态
      trtcManager.sendCustomCmdMsg(
          event: ChatRoomRtcEvent.RoomInfoUpdate,
          data: teamEntity.value!.teamNo!);
    }
  }

  Future<void> updateTeamLockState({int? password}) async {
    bool success = await ApiService().lockTeam(
        teamId: teamId ?? teamEntity.value!.teamId!,
        type: locked.value == true ? 0 : 1,
        pwd: password.toString());
    if (success) {
      locked.value = !locked.value;
      teamEntity.value?.teamPassword = password.toString();

      ///发送自定义消息通知远端，变更房间锁状态
      trtcManager.sendCustomCmdMsg(
          event: ChatRoomRtcEvent.RoomInfoUpdate,
          data: teamEntity.value!.teamNo!);
    }
  }

  Future<void> deleteLabel(int index) async {
    if (labels.length <= index) {
      return;
    }
    if (labels.length == 1) {
      ToastUtils.showToast("小队标签不能为空！");
      return;
    }
    final editedLabels = [];
    editedLabels.addAll(labels);
    editedLabels.removeAt(index);
    bool success = await ApiService()
        .editTeamInfo(teamId: teamId!, teamLabels: editedLabels);
    if (success) {
      labels.removeAt(index);
      update();

      ///发送自定义消息通知远端，变更房间锁状态
      trtcManager.sendCustomCmdMsg(
          event: ChatRoomRtcEvent.RoomInfoUpdate,
          data: teamEntity.value!.teamNo!);
    }
  }

  Future<void> addLabel(String label) async {
    final editedLabels = [];
    editedLabels.addAll(labels);
    editedLabels.add(label);
    bool success = await ApiService().editTeamInfo(
        teamId: teamId ?? teamEntity.value!.teamId!, teamLabels: editedLabels);
    if (success) {
      labels.add(label);
      update();

      ///发送自定义消息通知远端，变更房间锁状态
      trtcManager.sendCustomCmdMsg(
          event: ChatRoomRtcEvent.RoomInfoUpdate,
          data: teamEntity.value!.teamNo!);
    }
  }

  ///退出房间
  Future<void> exitRoom({bool? force}) async {
    await closeRoom();
    resetMicAndSpeakerState();
    if (force != true) {
      removeRTCListener();
      Get.back(result: -1);
    }
  }

  ///关闭房间
  Future<void> closeRoom() async {
    await stopLocalAudio();
    await trtcManager.exitRoom();

    if (teamId != null || teamEntity.value?.teamId != null) {
      ///如果是群主，就转让群主权限给下一个人
      bool isRoomOwner = checkIsRoomOwner();
      if (isRoomOwner) {
        await transferRoomOwner();
        // await dismissGroup();
      } else {
        await quitGroup();
      }
      await exitTeam();
    }
  }

  Future<void> exitTeam() async {
    if (teamId == null && teamEntity.value?.teamId == null) {
      return;
    }
    bool success = await ApiService()
        .exitTeam(teamId: teamId ?? teamEntity.value!.teamId!);
    if (!success) {
      debugPrint("退出小队失败");
    }
  }

  ///解散群组
  Future<void> dismissGroup() async {
    if (teamEntity.value?.teamMembers?.length == 1) {
      bool success = await ChatIMManager.sharedInstance
          .dismissGroup(groupID: teamEntity.value!.teamNo!);
      if (!success) {
        debugPrint("集结队群组解散失败！");
      }
    } else {
      await quitGroup();
    }
  }

  Future<void> quitGroup() async {
    if (teamMemberIsEmpty()) {
      return;
    }
    bool success = await ChatIMManager.sharedInstance
        .quitGroup(groupID: teamEntity.value!.teamNo!);
    if (!success) {
      debugPrint("退出集结队群组失败！");
    }
  }

  ///是否是群主
  bool checkIsRoomOwner({String? userId}) {
    if (teamMemberIsEmpty()) {
      return false;
    }
    bool roomOwner = false;
    String? targetUserId = userId ?? UserService().user!.id;
    for (int i = 0; i < teamEntity.value!.teamMembers!.length; i++) {
      MatchTeamMemberEntity member = teamEntity.value!.teamMembers![i];
      if (member.userId == targetUserId && member.role == 2) {
        roomOwner = true;
        break;
      }
    }
    return roomOwner;
  }

  ///转让群主
  Future<void> transferRoomOwner() async {
    if (teamMemberIsEmpty()) {
      return;
    }
    MatchTeamMemberEntity? nextOwner = findNextRoomOwner();
    if (nextOwner != null) {
      await ChatIMManager.sharedInstance.transferGroupOwner(
          groupID: teamEntity.value!.teamNo!, userID: nextOwner.userId!);
    }
  }

  ///要转让的下一个群主人选
  MatchTeamMemberEntity? findNextRoomOwner() {
    MatchTeamMemberEntity? roomOwner;
    for (int i = 0; i < teamEntity.value!.teamMembers!.length; i++) {
      MatchTeamMemberEntity member = teamEntity.value!.teamMembers![i];
      if (member.userId != UserService().user!.id) {
        roomOwner = member;
        return roomOwner;
      }
    }
    return roomOwner;
  }

  ///踢群成员
  void kickOutOfMember(String userId) async {
    bool success = await ChatIMManager.sharedInstance.kickGroupMember(
        groupID: teamEntity.value!.teamNo!, memberList: [userId]);
    success = await ApiService().kickOutOfTeam(
        teamId: teamId ?? teamEntity.value!.teamId!, userId: userId);
    if (success) {
      trtcManager.sendCustomCmdMsg(
          event: ChatRoomRtcEvent.KickOutOfRoom,
          data: {"roomId": teamEntity.value!.teamNo!, "userId": userId});
      loadData();
    }
  }

  ///邀请加入小队
  void sendInviteMsgToUsers(List<String> userIds) async {
    int sendTimes = 0;
    for (int i = 0; i < userIds.length; i++) {
      String userId = userIds[i];
      bool success = await ChatIMManager.sharedInstance.sendCustomMessage(
          type: ChatImCustomMsgType.InviteToJoinTeam,
          data: {
            "title": "邀请你加入集结队",
            "groupID": teamEntity.value!.teamNo!,
            "teamID": teamEntity.value!.teamId!,
            "ownerID": teamEntity.value!.teamMembers
                ?.where((e) => e.role == 2)
                .first
                .userId,
            "invitorID": UserService().user!.id!,
            "lockState": teamEntity.value!.teamLockState ?? 0,
            "faceUrl": teamEntity.value?.teamImg,
            "text":
                "${UserService().user!.nickname}邀请你加入【${teamEntity.value!.teamName}】",
          },
          receiver: userId,
          groupID: "");
      if (success) {
        sendTimes++;
      }
    }

    if (sendTimes == userIds.length) {
      ToastUtils.showToast(S.current.inviteAlreadySend);
    }
  }

  ///群成员是否为空
  bool teamMemberIsEmpty() {
    if (teamEntity.value == null ||
        teamEntity.value?.teamMembers == null ||
        teamEntity.value?.teamMembers?.isEmpty == true) {
      return true;
    }
    return false;
  }

  Future<void> loadData({bool? showLoading}) async {
    if (teamId?.isNotEmpty == true) {
      teamEntity.value = await ApiService()
          .getAssembleTeamInfo(teamId: teamId!, showLoading: showLoading);
      if (teamEntity.value == null && teamEntity.value?.teamNo == null) {
        stopLocalAudio();
        resetMicAndSpeakerState();
        Get.back(result: -1);
        return;
      }
      matching.value = teamEntity.value?.teamMatchState == 1;
      locked.value = teamEntity.value?.teamLockState == 1;
      labels.value = teamEntity.value?.teamLabels ?? <String>[];
      update();
    }
  }

  ///RTC Manager 响应回调
  Future<void> addRTCService() async {
    _rtcListener = ChatRoomRTCListener(
      updateRoomInfo: (roomId) async {
        if (teamEntity.value == null || teamEntity.value?.teamNo != roomId) {
          return;
        }
        if (teamEntity.value?.teamNo != null &&
            teamEntity.value?.teamNo == roomId &&
            !isClosed) {
          loadData(showLoading: false);
        }
      },
      onRemoteUserEnterRoom: (userId) async {
        if (teamEntity.value == null) {
          return;
        }
        if (teamEntity.value?.teamNo != null &&
            !isClosed &&
            userId != UserService().user?.id) {
          loadData(showLoading: false);
        }
      },
      onRemoteUserLeaveRoom: (userId) async {
        if (teamEntity.value == null) {
          return;
        }
        if (teamEntity.value?.teamNo != null &&
            !isClosed &&
            userId != UserService().user?.id) {
          await loadData(showLoading: false);
        }
      },
      kickOutRoom: (roomId, userId) async {
        if (roomId != teamEntity.value?.teamNo) {
          return;
        }
        if (teamId == null && teamEntity.value?.teamId == null && !isClosed) {
          return;
        }
        if (roomId == teamId || roomId == teamEntity.value?.teamNo) {
          if (userId == UserService().user?.id) {
            await stopLocalAudio();
            await trtcManager.exitRoom();
            ToastUtils.showToast("你已被踢出小队");
            resetMicAndSpeakerState();
            Get.back(result: -1);
          }
        }
      },
      onUserVoiceVolume: (userVolumeInfo) {
        userVolume.value = userVolumeInfo;
      },
    );
    trtcManager.addListener(_rtcListener!);
  }

  List<UserInfoEntity> transferMemberListToFriendList() {
    List<UserInfoEntity> list = [];
    for (int i = 0; i < teamEntity.value!.teamMembers!.length; i++) {
      MatchTeamMemberEntity member = teamEntity.value!.teamMembers![i];
      if (member.userId == UserService().user?.id) {
        continue;
      }
      UserInfoEntity userInfo = UserInfoEntity();
      userInfo.id = member.userId;
      userInfo.nickname = member.nickname;
      userInfo.avatar = member.avatar;
      list.add(userInfo);
    }
    return list;
  }

  List<V2TimGroupMemberFullInfo> transferTeamMemberListToGroupMemberList() {
    List<V2TimGroupMemberFullInfo> list = [];
    for (int i = 0; i < teamEntity.value!.teamMembers!.length; i++) {
      MatchTeamMemberEntity member = teamEntity.value!.teamMembers![i];
      if (member.userId == UserService().user?.id) {
        continue;
      }
      V2TimGroupMemberFullInfo memberFullInfo =
          V2TimGroupMemberFullInfo(userID: member.userId!);
      memberFullInfo.nickName = member.nickname;
      memberFullInfo.faceUrl = member.avatar;
      list.add(memberFullInfo);
    }
    return list;
  }

  ///更新Mic和Speaker状态
  void updateMicAndSpeakerState() {
    if (trtcManager.localMicState == 0) {
      micOpen.value = false;
    } else {
      micOpen.value = true;
    }
    if (trtcManager.localSpeakerState == 0) {
      speakerOpen.value = false;
    } else {
      speakerOpen.value = true;
    }
  }

  ///回复麦克风、扬声器状态
  void resetMicAndSpeakerState() {
    trtcManager.localMicState = 1;
    trtcManager.localSpeakerState = 1;
  }

  ///AudioBox play control
  void stopCurrentAndPlayNewAudio({V2TimMessage? message}) {
    if (audioPlayer.state == AudioPlayerState.playing) {
      audioPlayer.stop();
    }

    if (message != null) {
      ///跟当前播放的是同一条语音
      SVGAAnimationController? currentSvgaController =
          svgaControllerMap[message.msgID];
      if (currentSvgaController?.isAnimating == true) {
        currentSvgaController?.stop();
        currentSvgaController?.reset();
        return;
      }
      String? jsonStr = message.customElem?.data;
      if (jsonStr != null) {
        ChatImCustomMsgEntity msgEntity =
            ChatImCustomMsgEntity.fromJson(jsonDecode(jsonStr));
        AudioBoxListItemEntity itemEntity =
            AudioBoxListItemEntity.fromJson(jsonDecode(msgEntity.data));
        audioPlayer.setUrl(itemEntity.url!);
        audioPlayer.play();
      }
    }

    playNewAndStopOtherAnimation(messageID: message?.msgID);
  }

  void playNewAndStopOtherAnimation({String? messageID}) {
    if (messageID != null) {
      SVGAAnimationController? currentSvgaController =
          svgaControllerMap[messageID];
      currentSvgaController?.repeat();
    }

    stopOtherAnimation(exclude: messageID);
  }

  void stopOtherAnimation({String? exclude}) {
    svgaControllerMap.forEach((key, svgaController) {
      if (svgaController.isAnimating &&
          ((key != exclude && exclude != null) || exclude == null)) {
        svgaController.stop();
        svgaController.reset();
      }
    });
    update();
  }

  ///发送自定义消息
  Future<bool> sendCustomMsg(dynamic data, String type,
      {String? userID, String? groupID}) async {
    bool success = await ChatIMManager.sharedInstance.sendCustomMessage(
        type: type,
        data: data,
        receiver: "",
        groupID: groupID ?? teamEntity.value?.teamNo ?? "");
    if (success) {
      timuiKitChatController.refreshCurrentHistoryList();
      return true;
    }
    return false;
  }

  void removeRTCListener() {
    if (_rtcListener != null) {
      trtcManager.removeListener(_rtcListener!);
      _rtcListener = null;
    }
  }

  @override
  void onClose() {
    removeRTCListener();
    super.onClose();
  }
}
