import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/fonts_family.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/radio_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/match/assemble/match_assemble_place_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MatchAssemblePlacePage extends StatefulWidget {
  const MatchAssemblePlacePage({super.key});

  @override
  State<MatchAssemblePlacePage> createState() => _MatchAssemblePlacePageState();
}

class _MatchAssemblePlacePageState extends State<MatchAssemblePlacePage> {
  final controller = Get.put(MatchAssemblePlaceController());
  final TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    setNormalRoomName();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFFFFEEE,
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
      ),
      extendBodyBehindAppBar: true,
      body: ListView(
        padding: EdgeInsets.zero,
        children: [
          Stack(
            children: [
              ImageUtils.getImage(Assets.imagesDadaMatchTopBg, 375.w, 270.h,
                  fit: BoxFit.cover),
              _buildContentWidget(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTitleWidget() {
    return Padding(
      padding:
          EdgeInsets.only(top: 44.h + ScreenUtil().statusBarHeight, left: 35.w),
      child: ImageUtils.getImage(Assets.imagesMatchTopTitleImg, 195.w, 80.h),
    );
  }

  Widget _buildAssemblePlaceHallBtn() {
    return Padding(
      padding: EdgeInsets.only(top: 3.h, left: 26.w),
      child: GestureDetector(
        onTap: () {
          Get.toNamed(GetRouter.assembleHall);
        },
        child:
            ImageUtils.getImage(Assets.imagesMatchAssembleHallBtn, 165.w, 50.h),
      ),
    );
  }

  Widget _buildAssembleTipWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 18.h, left: 80.w),
      child: ImageUtils.getImage(
          Assets.imagesMatchAssembleTitleTipImg, 213.w, 97.h),
    );
  }

  Widget _buildContentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitleWidget(),
        _buildAssemblePlaceHallBtn(),
        _buildAssembleTipWidget(),
        _buildTeamContainer(),
        _buildBottomBtn(),
        _buildBottomTipLabel(),
      ],
    );
  }

  Widget _buildTeamContainer() {
    return Container(
      margin: EdgeInsets.only(top: 15.h, left: 15.w, right: 15.w),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15.r),
        border: Border.all(
          color: AppColors.colorFFABEEE3,
        ),
        color: Colors.white,
      ),
      child: Column(
        children: [
          _buildLabelsWidget(),
          _buildCreateSelectionWidget(),
        ],
      ),
    );
  }

  Widget _buildLabelsWidget() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        color: AppColors.colorFFEBFCE0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 15.h),
                child: Text(
                  "我要寻找队员/小队",
                  style:
                      TextStyles.normal(18.sp, f: FontsFamily.youSheBiaoTiHei),
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h, left: 12.5.w),
            child: Text(
              "寻找小队/队员，需要设置标签哦：",
              style: TextStyles.medium(16.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 3.h, left: 12.5.w, bottom: 12.h),
            child: Text(
              '(如：永劫无间、三排、双排，您添加的标签将显示在房间信息中，并作为匹配依据)',
              style: TextStyles.common(12.sp, AppColors.colorFF87958A),
            ),
          ),
          Obx(() {
            List<String> list = [];
            list.addAll(controller.subLabelList);
            if (list.isEmpty) {
              if (!list.contains("")) {
                list.add("");
              }
            } else if (list.length < 4) {
              if (!list.contains("")) {
                list.add("");
              }
            } else if (list.length > 4) {
              list = list.sublist(0, 4);
            }
            return Container(
              padding: EdgeInsets.only(bottom: 15.h, left: 12.5.w),
              constraints: BoxConstraints(
                minHeight: 35.w,
              ),
              child: Wrap(
                spacing: 10.w,
                runSpacing: 10.w,
                children: _buildSubLabelListChildren(list),
              ),
            );
          }),
        ],
      ),
    );
  }

  List<Widget> _buildSubLabelListChildren(List<String> data) {
    List<Widget> list = <Widget>[];
    for (int i = 0; i < data.length; i++) {
      String text = data[i];
      list.add(_buildSubLabelItem(text, i));
    }
    return list;
  }

  Widget _buildSubLabelItem(String text, int index) {
    if (text.isEmpty) {
      return LabelItemWidget(
        text: S.current.addLabel,
        fontSize: 14.sp,
        height: 30.h,
        isAddItem: true,
        textColor: AppColors.colorFF23AF28,
        bgColor: Colors.transparent,
        borderColor: AppColors.colorFF23AF28,
        recommendWhenAddAction: true,
        addAction: (text) {
          if (text.isNotEmpty) {
            if (controller.subLabelList.isEmpty) {
              controller.subLabelList.add(text);
            } else {
              controller.subLabelList
                  .insert(controller.subLabelList.length - 1, text);
            }
          }
        },
      );
    }

    return LabelItemWidget(
      text: text,
      textColor: AppColors.colorFF23AF28,
      padding: EdgeInsets.only(left: 8.w, right: 6.w),
      fontSize: 14.sp,
      textMaxLength: 4,
      closeBtnSize: 16.w,
      bgColor: AppColors.colorFF23AF28.withOpacity(0.2),
      borderColor: Colors.transparent,
      deleteAction: () {
        controller.subLabelList.removeAt(index);
      },
    );
  }

  Widget _buildCreateSelectionWidget() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 5.h),
      padding: EdgeInsets.only(left: 10.w, top: 15.h, bottom: 10.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        color: AppColors.colorFFEBFCE0,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Obx(
                () => RadioButton(
                  text: S.current.createTeam,
                  selected: controller.isCreate.value,
                  onTap: () {
                    controller.isCreate.value = true;
                  },
                ),
              ),
              SizedBox(
                width: 33.w,
              ),
              Obx(
                () => RadioButton(
                  text: S.current.joinTeam,
                  selected: !controller.isCreate.value,
                  onTap: () {
                    controller.isCreate.value = false;
                  },
                ),
              )
            ],
          ),
          _buildNumberSelectionWidget(),
          _buildInputTextFieldWidget(),
        ],
      ),
    );
  }

  Widget _buildNumberSelectionWidget() {
    return Obx(() {
      if (controller.isCreate.value == false) {
        return Container();
      }
      return Padding(
        padding: EdgeInsets.only(top: 15.h),
        child: Row(
          children: [
            Text(
              S.current.needPeopleNumber,
              style: TextStyles.common(16.sp, AppColors.colorFF666666),
            ),
            SizedBox(
              width: 10.w,
            ),
            Row(
              children: controller.peopleNumberList
                  .map((e) => _buildPeopleNumberItemWidget(e))
                  .toList(),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildPeopleNumberItemWidget(String title) {
    return Obx(() {
      if (controller.isCreate.value == false) {
        return Container();
      }
      int index = controller.peopleNumberList.indexOf(title);
      bool selected = controller.selectedNumberIndex.value == index;
      return GestureDetector(
        onTap: () {
          controller.selectedNumberIndex.value = index;
        },
        child: Container(
          width: 40.w,
          height: 25.h,
          margin: EdgeInsets.only(right: 3.w),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.5.r),
            border: Border.all(
              color:
                  selected ? AppColors.colorFF23AF28 : AppColors.colorFFBACEAE,
            ),
            color: selected ? AppColors.colorFFDDF8CF : Colors.transparent,
          ),
          child: Text(
            title,
            style: TextStyles.common(14.sp,
                selected ? AppColors.colorFF23AF28 : AppColors.colorFFBACEAE),
          ),
        ),
      );
    });
  }

  Widget _buildInputTextFieldWidget() {
    return Obx(() {
      if (controller.isCreate.value == false) {
        return Container();
      }
      return Padding(
        padding: EdgeInsets.only(top: 15.h, left: 0.w, right: 10.w),
        child: Container(
          height: 34.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(17.r),
            color: Colors.white,
            border: Border.all(color: AppColors.colorFFBACEAE),
          ),
          child: CustomTextField.build(
            controller: textEditingController,
            contentPadding: EdgeInsets.only(left: 16.w, right: 10.w),
            hintText: S.current.createAssembleInputTip,
            showSuffixIcon: true,
            maxLength: 16,
            onChanged: (value) {
              controller.inputTeamName.value = textEditingController.text;
            },
            suffixIconOnTap: () {
              controller.inputTeamName.value = "";
            },
          ),
        ),
      );
    });
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      enabled: true,
      topMargin: 15.h,
      title: S.current.startMatch,
      onTap: () {
        if (!GlobalFloatingManager().currentIsShowMiniWindow()) {
          controller.startMatching();
        }
      },
    );
  }

  Widget _buildBottomTipLabel() {
    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: 20.h),
      child: Text(
        "忙碌之余，游戏放松一下可以让身心更健康哦~",
        style: TextStyles.common(12.sp, AppColors.colorFF999999),
      ),
    );
  }

  void setNormalRoomName() {
    String roomName = "集结处";
    int random = Random().nextInt(100000) + 100000;
    roomName += random.toString();
    textEditingController.text = roomName;
    controller.inputTeamName.value = textEditingController.text;
  }
}
