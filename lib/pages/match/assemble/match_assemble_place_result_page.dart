import 'package:dada/common/values/colors.dart';
import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/match/assemble/match_assemble_place_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MatchAssemblePlaceResultPage extends StatefulWidget {
  const MatchAssemblePlaceResultPage({super.key});

  @override
  State<MatchAssemblePlaceResultPage> createState() =>
      _MatchAssemblePlaceResultPageState();
}

class _MatchAssemblePlaceResultPageState
    extends State<MatchAssemblePlaceResultPage> {
  final controller = Get.find<MatchAssemblePlaceController>();

  @override
  void initState() {
    super.initState();

    controller.startMatching();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          GradientWidget(
            width: ScreenUtil().screenWidth,
            height: ScreenUtil().screenHeight,
            stops: const [0, 0.45],
            colors: const [AppColors.colorFFADF0B0, AppColors.colorFFF5F5F5],
          ),
          _buildContentWidget(),
        ],
      ),
    );
  }

  Widget _buildContentWidget() {
    return GetBuilder(
      init: controller,
      builder: (controller) {
        if (controller.pageState == PageState.loading ||
            controller.pageState == PageState.start) {
          return const LoadingWidget();
        }
        return EmptyWidget(
          content: S.current.matchTeamEmptyTip,
          image: Assets.imagesMatchTeamEmptyImg,
          imageWidth: 220.w,
          imageHeight: 220.h,
          child: Padding(
            padding: EdgeInsets.only(top: 30.h),
            child: GestureDetector(
              onTap: () {
                Get.toNamed(GetRouter.assembleHall);
              },
              child: ImageUtils.getImage(
                  Assets.imagesMatchAssembleHallBtn, 140.w, 30.h),
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    super.dispose();

    controller.pageState = PageState.start;
    controller.loadingAnimationFinished = false;
  }
}
