import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/log.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_add_opt_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

class MatchAssemblePlaceController extends BasePageController {
  RxList<String> subLabelList = <String>[].obs;
  late List<String> peopleNumberList;
  final RxInt selectedNumberIndex = 0.obs;
  final RxBool isCreate = false.obs;
  final RxString inputTeamName = "".obs;

  bool loadingAnimationFinished = false;
  MatchTeamResultEntity? matchTeamResult;

  @override
  void onInit() {
    super.onInit();

    peopleNumberList = ["2", "3", "4", "5", "6"];
  }

  void startMatching() {
    createTeamOrMatchTeam();
  }

  Future<void> createTeamOrMatchTeam() async {
    ToastUtils.showLoading();
    int teamNumber = int.parse(peopleNumberList[selectedNumberIndex.value]);
    if (isCreate.value == true) {
      matchTeamResult = await ApiService().createTeam(
        teamName: inputTeamName.value,
        teamNumber: teamNumber,
        labels: subLabelList,
      );
      if (matchTeamResult != null) {
        /// 创建群组，之所以选择创建 Meeting 类型，是因为 Meeting 类型群组进去不需要审核，
        /// 而且可以转让群主，AVChatRoom 不支持转让群主. 但 AVChatRoom 不支持设置群组 attribute 属性，
        /// Team 房间内东西变更暂定用 TRTC sendCustomMsg 来实现群组内容变更通知远端
        String? groupID = await ChatIMManager.sharedInstance.createGroup(
          groupType: GroupType.Meeting,
          groupName: matchTeamResult!.teamName!,
          groupID: matchTeamResult!.teamNo!.toString(),
          faceUrl: matchTeamResult!.teamImg!,
          addOptTypeEnum: GroupAddOptTypeEnum.V2TIM_GROUP_ADD_ANY,
        );
        Log.d(groupID);
      }
    } else {
      matchTeamResult = await ApiService().matchTeam(labels: subLabelList);
    }

    if (matchTeamResult != null) {
      V2TimConversation conversation =
          await ChatIMManager.sharedInstance.getConversation(
        type: 2,
        groupID: matchTeamResult!.teamNo.toString(),
        groupName: matchTeamResult!.teamName!,
        groupFaceUrl: matchTeamResult!.teamImg!,
      );
      bool success = true;
      if (isCreate.value == false) {
        success = await ChatIMManager.sharedInstance.joinGroup(
            groupType: GroupType.Meeting,
            groupID: matchTeamResult!.teamNo.toString(),
            message: "");
      }
      if (success == false) {
        ToastUtils.showToast("加入小队失败，请稍后再试");
        return;
      }
      ToastUtils.hideLoading();
      Get.toNamed(
        GetRouter.teamDetail,
        parameters: {"teamId": matchTeamResult!.teamId!.toString()},
        arguments: {"conversation": conversation},
      );
    } else {
      ToastUtils.hideLoading();
      if (isCreate.value == false) {
        ToastUtils.showDialog(
          hideTopImg: true,
          content: "暂未找到精准匹配，是否前往集结队大厅",
          onConfirm: () {
            Get.toNamed(GetRouter.assembleHall);
          },
        );
      }
    }
  }

  void startJumpToTeam() {
    if (matchTeamResult != null) {
    } else {
      update();
    }
  }
}
