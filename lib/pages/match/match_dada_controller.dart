import 'dart:ui';
import 'package:dada/generated/l10n.dart';
import 'package:get/get.dart';

class MatchDadaController extends GetxController {
  late List<String> habits;
  late List<Color> habitsColors;
  final RxInt selectedHabitIndex = 1.obs;

  late List<String> sexSelections;
  final RxInt selectedSexIndex = 0.obs;

  RxList<String> subLabelList = <String>[].obs;
  RxList<String> otherLabelList = <String>[].obs;

  @override
  void onInit() {
    super.onInit();

    sexSelections = [S.current.unlimited, S.current.boy, S.current.girl];
  }
}
