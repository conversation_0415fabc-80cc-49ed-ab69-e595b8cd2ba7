import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/fonts_family.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/radio_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/dada_match_result_entity.dart';
import 'package:dada/pages/match/dada/match_dada_list_controller.dart';
import 'package:dada/pages/match/match_dada_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MatchDadaPage extends StatefulWidget {
  const MatchDadaPage({super.key});

  @override
  State<MatchDadaPage> createState() => _MatchDadaPageState();
}

class _MatchDadaPageState extends State<MatchDadaPage> {
  final controller = Get.put(MatchDadaController());
  RxBool showBottomTip = true.obs;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFFFFEEE,
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
      ),
      extendBodyBehindAppBar: true,
      body: Column(
        children: [
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                Stack(
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesDadaMatchTopBg, 375.w, 250.h,
                        fit: BoxFit.cover),
                    _buildContentWidget(),
                  ],
                ),
              ],
            ),
          ),
          _buildBottomTip(),
        ],
      ),
    );
  }

  Widget _buildContentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.only(
              top: 44.h + ScreenUtil().statusBarHeight, left: 35.w),
          child:
              ImageUtils.getImage(Assets.imagesMatchTopTitleImg, 195.w, 80.h),
        ),
        Container(
          margin: EdgeInsets.only(
            top: 76.h,
            left: 15.w,
            right: 15.w,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(
              color: AppColors.colorFFABEEE3,
            ),
            color: Colors.white,
          ),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
            child: Column(
              children: [
                _buildSexSelectionWidget(),
                _buildLabelsWidget(),
                _buildOtherLabelsWidget(),
              ],
            ),
          ),
        ),
        _buildBottomBtn(),
        SizedBox(height: 10.h),
        _buildBottomText(),
      ],
    );
  }

  Widget _buildSexSelectionWidget() {
    return Container(
      height: 40.h,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        color: AppColors.colorFFEBFCE0,
      ),
      child: Row(
        children: controller.sexSelections.map((e) {
          return Obx(() {
            int index = controller.sexSelections.indexOf(e);
            bool selected = index == controller.selectedSexIndex.value;
            Color normalColor = AppColors.colorFF999999;
            Color selectedColor = AppColors.colorFF62CE67;
            return Padding(
              padding: EdgeInsets.only(right: 20.w),
              child: RadioButton(
                text: e,
                selected: selected,
                normalColor: normalColor,
                selectedColor: selectedColor,
                normalTextColor: AppColors.colorFF3D3D3D,
                selectedTextColor: selectedColor,
                onTap: () {
                  controller.selectedSexIndex.value = index;
                },
              ),
            );
          });
        }).toList(),
      ),
    );
  }

  ///标签
  Widget _buildLabelsWidget({bool? isOther}) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 5.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        color: AppColors.colorFFEBFCE0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 12.5.h, left: 20.w, bottom: 10.h),
            child: Text(
              isOther == true
                  ? S.current.matchOtherLabels
                  : S.current.matchLabels,
              style: TextStyles.normal(18.sp, f: FontsFamily.youSheBiaoTiHei),
            ),
          ),
          Obx(() {
            List<String> list = [];
            if (isOther == true) {
              list.addAll(controller.otherLabelList);
            } else {
              list.addAll(controller.subLabelList);
            }
            if (list.isEmpty) {
              if (!list.contains("")) {
                list.add("");
              }
            } else if (list.length < 4) {
              if (!list.contains("")) {
                list.add("");
              }
            } else if (list.length > 4) {
              list = list.sublist(0, 4);
            }
            return Container(
              padding: EdgeInsets.only(bottom: 12.h, left: 18.w),
              constraints: BoxConstraints(
                minHeight: 35.w,
              ),
              child: Wrap(
                spacing: 10.w,
                runSpacing: 10.w,
                children: _buildSubLabelListChildren(list, isOther: isOther),
              ),
            );
          }),
        ],
      ),
    );
  }

  List<Widget> _buildSubLabelListChildren(List<String> data, {bool? isOther}) {
    List<Widget> list = <Widget>[];
    for (int i = 0; i < data.length; i++) {
      String text = data[i];
      list.add(_buildSubLabelItem(text, i, isOther: isOther));
    }
    return list;
  }

  Widget _buildSubLabelItem(String text, int index, {bool? isOther}) {
    if (text.isEmpty) {
      return LabelItemWidget(
        text: S.current.addLabel,
        fontSize: 14.sp,
        height: 30.h,
        isAddItem: true,
        textColor: AppColors.colorFF23AF28,
        bgColor: Colors.transparent,
        borderColor: AppColors.colorFF23AF28,
        addAction: (text) {
          if (text.isNotEmpty) {
            if (isOther == true) {
              if (controller.otherLabelList.isNotEmpty) {
                controller.otherLabelList
                    .insert(controller.subLabelList.length - 1, text);
              } else {
                controller.otherLabelList.add(text);
              }
            } else {
              if (controller.subLabelList.isNotEmpty) {
                controller.subLabelList
                    .insert(controller.subLabelList.length - 1, text);
              } else {
                controller.subLabelList.add(text);
              }
            }
          }
        },
      );
    }

    return LabelItemWidget(
      text: text,
      textColor: AppColors.colorFF23AF28,
      padding: EdgeInsets.only(left: 8.w, right: 6.w),
      fontSize: 14.sp,
      textMaxLength: 4,
      closeBtnSize: 16.w,
      bgColor: AppColors.colorFF23AF28.withOpacity(0.2),
      borderColor: Colors.transparent,
      deleteAction: () {
        if (isOther == true) {
          controller.otherLabelList.removeAt(index);
        } else {
          controller.subLabelList.removeAt(index);
        }
      },
    );
  }

  ///其它标签
  Widget _buildOtherLabelsWidget() {
    return _buildLabelsWidget(isOther: true);
  }

  Widget _buildBottomBtn() {
    // return Obx(() {
    //   bool enabled = controller.subLabelList.isNotEmpty;
     // bool enabled = true;
      return CommonGradientBtn(
        topMargin: 15.h,
        title: S.current.startMatch,
        onTap: () async {
          //if (enabled) {
            MatchDadaListController matchDadaListController =
                MatchDadaListController();
            matchDadaListController.type =
                controller.selectedSexIndex.value.toString();
            matchDadaListController.labels = controller.subLabelList;
            matchDadaListController.subLabels = controller.otherLabelList;

            DadaMatchResultEntity? data =
                await matchDadaListController.loadData();
            if (data != null) {
              Get.toNamed(
                GetRouter.matchDadaResult,
                arguments: {
                  'type': controller.selectedSexIndex.value.toString(),
                  "labels": controller.subLabelList,
                  "subLabels": controller.otherLabelList,
                  'data': data,
                },
              );
            } else {
              ToastUtils.showToast("暂未匹配到数据，请修改条件后再试");
            }
        },
      );
   // });
  }

  Widget _buildBottomTip() {
    return Obx(
      () => Container(
        height: showBottomTip.value == true ? 40.h : 0,
        margin: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
        padding: EdgeInsets.only(left: 15.w, right: 20.w),
        color: AppColors.colorFFEBFCE0,
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.current.matchBottomTip,
              style: TextStyles.normal(14.sp),
            ),
            GestureDetector(
              child: Icon(
                Icons.close_outlined,
                size: 15.w,
                color: AppColors.colorFF3D3D3D,
              ),
              onTap: () {
                showBottomTip.value = false;
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _buildBottomText() {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w), // 添加边距
      child: Text(
        "系统会根据你设置的标签为您匹配好友哦，游戏、综艺、喜好、地区等都可以哦！比如：王者荣耀、聊天、星座、旅游、成都等。",
        style: TextStyles.common(14.sp, AppColors.colorFF87958A).copyWith(height: 1.5), // 设置行高
        softWrap: true,
        maxLines: 3,
      ),
    );
  }
}
