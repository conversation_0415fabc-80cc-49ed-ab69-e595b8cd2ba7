import 'dart:math' as math;
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/sex_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/dada_match_result_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/model/user_label_entity.dart';
import 'package:dada/pages/match/dada/dialog/match_dada_list_label_detail_dialog.dart';
import 'package:dada/pages/match/dada/match_dada_list_controller.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/pages/small_room/small_room_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

class MatchDadaListPage extends StatefulWidget {
  const MatchDadaListPage({super.key});

  @override
  State<MatchDadaListPage> createState() => _MatchDadaListPageState();
}

class _MatchDadaListPageState extends State<MatchDadaListPage>
    with TickerProviderStateMixin {
  MatchDadaListController controller = Get.put(MatchDadaListController());

  @override
  void initState() {
    super.initState();

    String type = Get.parameters["type"] ?? "0";
    List<String>? labels = Get.arguments['labels'];
    List<String>? subLabels = Get.arguments['subLabels'];

    DadaMatchResultEntity data = Get.arguments['data'];
    controller.data = data;
    if (labels != null && labels.isNotEmpty) {
      controller.labels = labels;
    }
    controller.subLabels = subLabels;
    controller.type = type;

    String? alertTip;
    if (data.type == 0) {
      alertTip = "呼，可把搭吖累坏了~这里为你找到了些你可能会感兴趣的其他小伙伴哦~";
    }

    if (alertTip != null && alertTip.isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 500), () {
        ToastUtils.showToast("$alertTip");
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
        title: S.current.dadaTabTitle,
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          GradientWidget(
            width: ScreenUtil().screenWidth,
            height: ScreenUtil().screenHeight,
            stops: const [0, 0.45],
            colors: const [AppColors.colorFFADF0B0, AppColors.colorFFF5F5F5],
          ),
          _buildContentWidget(),
        ],
      ),
    );
  }

  Widget _buildContentWidget() {
    return GetBuilder(
      init: controller,
      builder: (controller) {
        return Container(
          margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 56),
          child: !(controller.data?.matchDada?.isNotEmpty == true)
              ? const ListPageEmptyWidget()
              : ListView.separated(
                  padding: EdgeInsets.zero,
                  itemBuilder: (context, index) {
                    UserInfoEntity entity = controller.data!.matchDada![index];
                    return _buildListItem(entity);
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 10.h,
                    );
                  },
                  itemCount: controller.data?.matchDada?.length ?? 0,
                ),
        );
      },
    );
  }

  Widget _buildListItem(UserInfoEntity entity) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Container(
        padding:
            EdgeInsets.only(left: 10.w, right: 10.w, top: 10.h, bottom: 15.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCellHeaderWidget(entity),
            _buildLabelsWidget(entity),
          ],
        ),
      ),
    );
  }

  Widget _buildCellHeaderWidget(UserInfoEntity entity) {
    return Stack(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: AvatarWidget(
                size: 50.w,
                url: entity.avatar,
                showPlaceholder: true,
                onTap: () {
                  Get.toNamed(GetRouter.userProfile,
                      parameters: {"userId": entity.id!});
                },
              ),
            ),
            SizedBox(
              width: 5.w,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 5.h),
                  child: Row(
                    children: [
                      Container(
                        constraints: BoxConstraints(maxWidth: 120.w),
                        child: Text(
                          entity.nickname ?? "",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyles.medium(16.sp),
                        ),
                      ),
                      SizedBox(
                        width: 5.w,
                      ),
                      SexAgeWidget(sex: entity.sex ?? 0, age: entity.age ?? 0),
                    ],
                  ),
                ),
                _buildAudioSignatureWidget(entity),
                Visibility(
                  visible: entity.txtSignature?.isNotEmpty == true ||
                      entity.recentState?.isNotEmpty == true,
                  child: Container(
                    padding: EdgeInsets.only(top: 5.h),
                    constraints: BoxConstraints(
                      maxWidth:
                          ScreenUtil().screenWidth - 15.w * 2 - 67.w - 10.w,
                    ),
                    child: Text(
                      entity.recentState ?? entity.txtSignature ?? "",
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyles.common(14.sp, AppColors.colorFF666666,
                          h: 1.3),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        _buildRightActionWidget(entity),
      ],
    );
  }

  Widget _buildAudioSignatureWidget(UserInfoEntity entity) {
    int index = controller.data!.matchDada!.indexOf(entity);
    SVGAAnimationController animationController;
    if (controller.svgaControllerList.containsKey(index)) {
      animationController = controller.svgaControllerList[index]!;
    } else {
      animationController = SVGAAnimationController(vsync: this);
    }
    return Visibility(
      visible: entity.voiceSignature != null &&
          entity.voiceLength != null &&
          entity.voiceLength != 0,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          controller.stopCurrentAndPlayNewAudio(index: index);
        },
        child: Container(
          width: 76.w,
          height: 20.h,
          margin: EdgeInsets.only(top: 3.h),
          decoration: BoxDecoration(
            color: AppColors.colorFF4DC151,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: ImageUtils.getImage(
                    animationController.isAnimating
                        ? Assets.imagesMatchDadaListAudioPause
                        : Assets.imagesMatchDadaListAudioPlay,
                    15.w,
                    15.w),
              ),
              Container(
                width: 13.w,
                height: 10.h,
                margin: EdgeInsets.only(left: 5.w),
                child: Stack(
                  children: [
                    Visibility(
                      visible: !animationController.isAnimating,
                      child: ImageUtils.getImage(
                          Assets.imagesMatchDadaListAudioVolume, 13.w, 10.h,
                          fit: BoxFit.cover),
                    ),
                    Visibility(
                      visible: !animationController.isAnimating,
                      child: SVGAImage(animationController),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 8.w),
                child: Text(
                  "${entity.voiceLength ?? "0"}s",
                  style: TextStyles.common(14.sp, Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRightActionWidget(UserInfoEntity entity) {
    return Positioned(
      top: 2.h,
      right: 0,
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              Get.to(() => SmallRoomPage(userId: entity.id!));
            },
            child:
                ImageUtils.getImage(Assets.imagesMatchDadaListRoom, 30.w, 30.w),
          ),
          Visibility(
            visible: entity.isFriend == 0,
            child: GestureDetector(
              onTap: () {
                Get.toNamed(GetRouter.addFriendSend, parameters: {
                  "userId": entity.id!,
                  "avatar": entity.avatar!,
                  "nickname": entity.nickname!,
                });
              },
              child: Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: ImageUtils.getImage(
                    Assets.imagesMatchDadaListAddFriend, 30.w, 30.w),
              ),
            ),
          ),
          GestureDetector(
            onTap: () async {
              if (!UserService().checkIsMonthCardUser()) {
                ToastUtils.showBottomDialog(
                    const MonthCardExpiredLimitChatDialog());
              } else {
                V2TimConversation conversation = await ChatIMManager
                    .sharedInstance
                    .getConversation(type: 1, userID: entity.id!);
                Get.toNamed(GetRouter.chatDetail, arguments: conversation);
              }
            },
            child: Padding(
              padding: EdgeInsets.only(left: 5.w),
              child: ImageUtils.getImage(
                  Assets.imagesMatchDadaListChat, 30.w, 30.w),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLabelsWidget(UserInfoEntity entity) {
    int index = controller.data!.matchDada!.indexOf(entity);

    bool unfold = false;
    if (controller.labelsUnfoldList.length <= index) {
      controller.labelsUnfoldList[index] = false;
    } else {
      unfold = controller.labelsUnfoldList[index] ?? false;
    }
    bool showMore = entity.labels != null && entity.labels!.length > 3;
    List<UserLabelEntity>? labelsList = entity.labels;
    if (showMore && unfold == false) {
      labelsList = entity.labels!.sublist(0, 3);
    } else {
      labelsList = entity.labels;
    }

    return Padding(
      padding: EdgeInsets.only(left: 5.w, top: 10.h),
      child: !(entity.labels != null && entity.labels!.isNotEmpty == true)
          ? Container(
              alignment: Alignment.center,
              child: Column(
                children: [
                  ImageUtils.getImage(
                      Assets.imagesUserLabelDetailDialogSublistEmpty,
                      46.w,
                      35.h),
                  Padding(
                    padding: EdgeInsets.only(top: 5.h),
                    child: Text(
                      "这个家伙很懒，没有添加标签哦",
                      style: TextStyles.normal(14.sp),
                    ),
                  ),
                ],
              ),
            )
          : Column(
              children: [
                Wrap(
                  spacing: 8.w,
                  runSpacing: 5.w,
                  children: labelsList!.map(
                    (e) {
                      int index = labelsList!.indexOf(e);
                      return _buildLabelItem(e, index);
                    },
                  ).toList(),
                ),
                Visibility(
                  visible: showMore && unfold == false,
                  child: Padding(
                    padding: EdgeInsets.only(top: 15.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            controller.labelsUnfoldList[index] = true;
                            controller.refreshWidget();
                          },
                          child: ImageUtils.getImage(
                              Assets.imagesUserProfileLabelsMore, 20.w, 20.w),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildLabelItem(UserLabelEntity labelEntity, int index) {
    int randomInt = _getRandomIndex();
    String bgImage = _getRandomLabelBgImage(randomInt);
    Color textColor = _getRandomLabelTextColor(randomInt);
    Color textShadowColor = _getRandomLabelTextShadowColor(randomInt);
    return GestureDetector(
      onTap: () {
        ToastUtils.showDialog(
          dialog: MatchDadaListLabelDetailDialog(
            labelEntity: labelEntity,
            bgImageIndex: randomInt,
            isMySelf: false,
            callback: () {},
          ),
        );
      },
      child: Container(
        width: 100.w,
        height: 36.h,
        decoration: BoxDecoration(
          image: DecorationImage(image: AssetImage(bgImage), fit: BoxFit.fill),
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 1.5.h),
                  constraints: BoxConstraints(maxWidth: 65.w),
                  child: Text(
                    labelEntity.labelName ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                      color: textColor,
                      shadows: [
                        Shadow(
                          color: textShadowColor,
                          offset: const Offset(0, 0.5),
                          blurRadius: 0.5,
                        ),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: labelEntity.labelImgs != null &&
                      labelEntity.labelImgs!.isNotEmpty,
                  child: Padding(
                    padding: EdgeInsets.only(left: 5.w, top: 2.h),
                    child: ImageUtils.getImage(
                        Assets.imagesUserProfileLabelImg, 13.w, 11.h),
                  ),
                ),
              ],
            ),
            Positioned(
              bottom: 1.h,
              child: ImageUtils.getImage(
                  Assets.imagesUserProfileLabelHasSubTag, 10.w, 13.h),
            ),
          ],
        ),
      ),
    );
  }

  int _getRandomIndex() {
    var random = math.Random();
    int randomInt = random.nextInt(2);
    return randomInt;
  }

  String _getRandomLabelBgImage(int index) {
    List<String> images = [
      Assets.imagesProfileTagLabelRandomBg1,
      Assets.imagesProfileTagLabelRandomBg2,
      Assets.imagesProfileTagLabelRandomBg3,
    ];
    return images[index];
  }

  Color _getRandomLabelTextColor(int index) {
    List<Color> bgColors = [
      const Color(0xFFFF17BA),
      const Color(0xFF23AF28),
      const Color(0xFFF9781D),
    ];
    return bgColors[index];
  }

  Color _getRandomLabelTextShadowColor(int index) {
    List<Color> list = [
      const Color(0xFFAF83A2),
      const Color(0xFF87B07D),
      const Color(0xFFB09A5B)
    ];
    return list[index];
  }
}
