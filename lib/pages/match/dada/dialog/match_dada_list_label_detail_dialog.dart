import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_label_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MatchDadaListLabelDetailDialog extends StatelessWidget {
  final UserLabelEntity labelEntity;
  final int bgImageIndex;
  final bool isMySelf;
  final Function() callback;

  const MatchDadaListLabelDetailDialog(
      {super.key,
      required this.labelEntity,
      required this.bgImageIndex,
      required this.isMySelf,
      required this.callback});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 320.w,
          constraints: BoxConstraints(
            minHeight: 350.h,
          ),
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(getBgImageAssetName()),
              scale: 3,
              fit: BoxFit.fill,
              centerSlice: Rect.fromLTWH(0, 100.h, 320.w, 200.h),
            ),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 25.h),
                    child: Center(
                      child: Text(
                        labelEntity.labelName ?? "",
                        style: TextStyles.medium(18.sp),
                      ),
                    ),
                  ),
                  _buildSubLabelListWidget(),
                  _buildLabelImagesWidget(),
                ],
              ),
              Visibility(
                visible: isMySelf,
                child: Positioned(
                  top: 12.5.h,
                  right: 12.5.w,
                  child: GestureDetector(
                    onTap: () {
                      Get.back();
                      Get.toNamed(GetRouter.userLabelAdd,
                          parameters: {"title": S.current.editLabel},
                          arguments: labelEntity)
                          ?.then((value) {
                        if (value != null) {
                          callback.call();
                        }
                      });
                    },
                    child: ImageUtils.getImage(
                        Assets.imagesUserLabelDetailDialogEdit, 30.w, 30.w),
                  ),
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 25.h),
          child: GestureDetector(
            onTap: () {
              Get.back();
            },
            child:
                ImageUtils.getImage(Assets.imagesDialogBottomClose, 40.w, 40.w),
          ),
        )
      ],
    );
  }

  Widget _buildSubLabelListWidget() {
    bool listEmpty =
        labelEntity.labelText?.isEmpty == true || labelEntity.labelText == null;
    return Container(
      width: 300.w,
      margin: EdgeInsets.only(top: 9.h, left: 10.w, right: 10.w),
      constraints: BoxConstraints(
        minHeight: 160.h,
        maxHeight: 160.h,
      ),
      decoration: BoxDecoration(
        color: AppColors.colorFFADC1A3.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: IntrinsicHeight(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 10.h, left: 10.w),
              child: Text(
                "子标签",
                style: TextStyles.normal(16.sp),
              ),
            ),
            listEmpty
                ? Container(
                    padding: EdgeInsets.only(top: 15.h),
                    alignment: Alignment.center,
                    child: ImageUtils.getImage(
                        Assets.imagesUserLabelDetailDialogSublistEmpty,
                        53.w,
                        40.h),
                  )
                : Expanded(
                    child: SingleChildScrollView(
                      padding:
                          EdgeInsets.only(left: 10.w, top: 10.h, bottom: 10.h),
                      child: Wrap(
                        spacing: 5.w,
                        runSpacing: 5.h,
                        children: labelEntity.labelText!
                            .map((e) => _buildSubLabelItem(e))
                            .toList(),
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubLabelItem(String text) {
    if (text.isEmpty) {
      return Container();
    }
    return LabelItemWidget(
      text: text,
      fontSize: 14.sp,
      textColor: AppColors.colorFF666666,
      editable: false,
      padding: EdgeInsets.only(left: 14.w, right: 14.w),
      minWidth: 56.w,
      bgColor: AppColors.colorFFEAECF1,
      borderColor: Colors.transparent,
      alignment: MainAxisAlignment.center,
      textMaxLength: 15,
      borderRadius: 15.r,
    );
  }

  Widget _buildLabelImagesWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 10.w, top: 15.h),
          child: Text(
            "相关截图",
            style: TextStyles.normal(16.sp),
          ),
        ),
        _buildLabelImageList(),
      ],
    );
  }

  Widget _buildLabelImageList() {
    if (labelEntity.labelImgs?.isNotEmpty == true) {
      labelEntity.labelImgs?.removeWhere((e) => e.isEmpty);
    }
    if (labelEntity.labelImgs == null ||
        labelEntity.labelImgs?.isEmpty == true) {
      return Container(
        alignment: Alignment.center,
        height: 114.h,
        child: ImageUtils.getImage(
            Assets.imagesUserLabelDetailDialogSublistEmpty, 53.w, 40.h),
      );
    }
    return Container(
      padding:
          EdgeInsets.only(left: 10.w, right: 10.w, top: 10.h, bottom: 15.h),
      constraints: BoxConstraints(minHeight: 70.h),
      child: Wrap(
        spacing: 5.w,
        runSpacing: 5.w,
        children:
            labelEntity.labelImgs!.map((e) => _buildLabelImageItem(e)).toList(),
      ),
    );
  }

  Widget _buildLabelImageItem(String url) {
    return Container(
      width: 96.w,
      height: 54.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Stack(
        alignment: Alignment.topRight,
        children: [
          GestureDetector(
            onTap: () {
              ImageUtils.showImageBrowser(
                ImageBrowserArgs(
                  [HeroTagName.labelImg.of(url)],
                  [url],
                ),
              );
            },
            child: Hero(
              tag: HeroTagName.labelImg.of(url),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5.r),
                child: ImageUtils.getImage(
                  url,
                  95.w,
                  56.h,
                  fit: BoxFit.cover,
                  color: AppColors.colorFFADC1A3.withOpacity(0.1),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String getBgImageAssetName() {
    switch (bgImageIndex) {
      case 0:
        return Assets.imagesUserLabelDetailDialogBg1;
      case 1:
        return Assets.imagesUserLabelDetailDialogBg2;
      case 2:
        return Assets.imagesUserLabelDetailDialogBg3;
      default:
        return Assets.imagesUserLabelDetailDialogBg1;
    }
  }
}
