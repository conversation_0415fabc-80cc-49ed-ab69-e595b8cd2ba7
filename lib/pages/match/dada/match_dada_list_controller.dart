import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/model/dada_match_result_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:svgaplayer_flutter/player.dart';

class MatchDadaListController extends BasePageController {
  bool loadingAnimationFinished = false;
  final AudioPlayerUtils audioPlayer = AudioPlayerUtils();
  final Map<int, SVGAAnimationController> svgaControllerList = {};
  final Map<int, bool> labelsUnfoldList = <int, bool>{};
  late String type;
  late List<String> labels;
  late List<String>? subLabels;
  DadaMatchResultEntity? data;

  Future<DadaMatchResultEntity?> loadData() async {
    String sexType = type == "0" ? "-1" : (type == "1" ? "0" : "1");
    DadaMatchResultEntity? result = await ApiService().getMatchDadaList(
        type: int.parse(sexType), labels: labels, subLabels: subLabels);
    data = result;
    return result;
  }

  void refreshWidget() {
    update();
  }

  void stopCurrentAndPlayNewAudio({int? index}) {
    if (audioPlayer.state == AudioPlayerState.playing) {
      audioPlayer.stop();
    }

    if (index != null) {
      ///跟当前播放的是同一条语音
      SVGAAnimationController? currentSvgaController =
          svgaControllerList[index];
      if (currentSvgaController != null && currentSvgaController.isAnimating) {
        currentSvgaController.stop();
        currentSvgaController.reset();
        return;
      }
      UserInfoEntity itemEntity = data!.matchDada![index];
      audioPlayer.setUrl(itemEntity.voiceSignature!);
      audioPlayer.play();
    }

    playNewAndStopOtherAnimation(index: index);
  }

  void playNewAndStopOtherAnimation({int? index}) {
    if (index != null) {
      SVGAAnimationController? currentSvgaController =
          svgaControllerList[index];
      currentSvgaController?.repeat();
    }

    stopOtherAnimation(index: index);
  }

  void stopOtherAnimation({int? index}) {
    svgaControllerList.forEach((key, svgaController) {
      if (index != null && key != index) {
        svgaController.stop();
        svgaController.reset();
      }
    });
  }

  Future<bool> startKnown(String userId) async {
    return await ApiService().startKnown(userId: userId);
  }

  @override
  void dispose() {
    audioPlayer.stop();
    audioPlayer.cancelPlayerSubscriptions();
    stopOtherAnimation();
    super.dispose();
  }
}
