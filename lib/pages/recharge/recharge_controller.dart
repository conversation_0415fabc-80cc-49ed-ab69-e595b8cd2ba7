import 'package:dada/model/account_balance_entity.dart';
import 'package:dada/model/goods_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class RechargeController extends GetxController {
  List<GoodsEntity> goodsList = [];
  RxInt currentSelectedIndex = 0.obs;
  Rx<AccountBalanceEntity?> accountBalance = Rx(null);

  int type = 1;

  @override
  void onReady() {
    super.onReady();

    loadData();
    if (type == 1) {
      loadUserStainsCount();
    }
  }

  void loadData() async {
    List<GoodsEntity>? list = await ApiService().getGoodsList(type: type);
    goodsList = list ?? [];
    update();
  }

  void loadUserStainsCount() async {
    AccountBalanceEntity? entity = await ApiService().getUserAccountBalance();
    accountBalance.value = entity;
  }
}
