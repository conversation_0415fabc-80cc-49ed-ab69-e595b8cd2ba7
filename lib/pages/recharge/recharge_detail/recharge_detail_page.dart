import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/recharge_detail_list_item_entity.dart';
import 'package:dada/pages/recharge/recharge_detail/recharge_detail_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class RechargeDetailPage extends StatefulWidget {
  const RechargeDetailPage({super.key});

  @override
  State<RechargeDetailPage> createState() => _RechargeDetailPageState();
}

class _RechargeDetailPageState extends State<RechargeDetailPage> {
  RechargeDetailController controller = RechargeDetailController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        title: "充值明细",
      ),
      body: Padding(
        padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 44.h),
        child: GetBuilder<RechargeDetailController>(
          init: controller,
          id: controller.refreshId,
          builder: (controller) {
            return RefreshWidget.build(
              refreshController: controller.refreshController,
              onRefresh: () => controller.refreshData(),
              onLoadMore: () => controller.loadMoreData(),
              child: controller.data.isEmpty
                  ? const ListPageEmptyWidget()
                  : ListView.separated(
                      padding: EdgeInsets.only(top: 10.h, bottom: 25.h),
                      itemBuilder: (context, index) {
                        RechargeDetailListItemEntity itemEntity =
                            controller.data[index];
                        return _buildListItemWidget(itemEntity);
                      },
                      separatorBuilder: (context, index) {
                        return Container(
                          margin: EdgeInsets.symmetric(horizontal: 15.w),
                          color: AppColors.colorFFF5F6F7,
                          height: 1,
                        );
                      },
                      itemCount: controller.data.length,
                    ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildListItemWidget(RechargeDetailListItemEntity itemEntity) {
    String rightIconName = "";
    double rightIconWidth = 0;
    double rightIconHeight = 0;
    if (itemEntity.type == 1) {
      rightIconName = Assets.imagesRechargeDaCoin;
      rightIconWidth = 20.w;
      rightIconHeight = 20.w;
    } else if (itemEntity.type == 2) {
      rightIconName = Assets.imagesRechargeDaBang;
      rightIconWidth = 18.w;
      rightIconHeight = 23.h;
    } else if (itemEntity.type == 3) {
      rightIconName = Assets.imagesRechargeSkin;
      rightIconWidth = 20.w;
      rightIconHeight = 20.w;
    }
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: itemEntity.createdDate != null &&
                          itemEntity.createdDate!.split(" ").first.isNotEmpty
                      ? itemEntity.createdDate!.split(" ").first
                      : "",
                  style: TextStyles.normal(16.sp),
                ),
                TextSpan(
                  text:
                      "  ${itemEntity.createdDate != null && itemEntity.createdDate!.split(" ").length > 1 ? itemEntity.createdDate!.split(" ").last : ""}",
                  style: TextStyles.common(12.sp, AppColors.colorFF999999),
                ),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 5.h),
                child: Text(
                  itemEntity.content ?? "",
                  style: TextStyles.normal(16.sp),
                ),
              ),
              (rightIconName.isNotEmpty)
                  ? Row(
                      children: [
                        ImageUtils.getImage(
                            rightIconName, rightIconWidth, rightIconHeight),
                        Padding(
                          padding: EdgeInsets.only(left: 5.w),
                          child: Text(
                            itemEntity.billType == 1?"+${itemEntity.billNo}":"-${itemEntity.billNo}",
                            style: TextStyles.common(
                              20.sp,
                              itemEntity.billType == 1
                                  ? AppColors.colorFF29782D
                                  : AppColors.colorFFE72020,
                              w: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    )
                  : Container(),
            ],
          ),
        ],
      ),
    );
  }
}
