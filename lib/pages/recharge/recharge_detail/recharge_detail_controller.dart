import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/recharge_detail_list_item_entity.dart';
import 'package:dada/services/network/api_service.dart';

class RechargeDetailController extends ListPageController<
    RechargeDetailListItemEntity, RechargeDetailController> {
  @override
  Future<List<RechargeDetailListItemEntity>?> loadData(int page) async {
    List<RechargeDetailListItemEntity>? list =
        await ApiService().getRechargeDetailList(page: page);
    return list;
  }
}
