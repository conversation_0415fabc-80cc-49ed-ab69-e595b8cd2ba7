import 'dart:io';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/goods_entity.dart';
import 'package:dada/pages/recharge/recharge_controller.dart';
import 'package:dada/pages/store/pay_order_info_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class RechargePage extends StatefulWidget {
  const RechargePage({super.key});

  @override
  State<RechargePage> createState() => _RechargePageState();
}

class _RechargePageState extends State<RechargePage> {
  RechargeController controller = Get.put(RechargeController());

  @override
  void initState() {
    super.initState();

    controller.type = 1;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
        title: "充值搭币",
        rightWidgets: [
          GestureDetector(
            onTap: () {
              Get.toNamed(GetRouter.rechargeDetail);
            },
            child: Padding(
              padding: EdgeInsets.only(right: 15.w),
              child: Text(
                "查看明细",
                style: TextStyles.common(14.sp, AppColors.colorFF666666),
              ),
            ),
          ),
        ],
      ),
      body: Container(
        padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 44.h),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [AppColors.colorFFADF0B0, AppColors.colorFFE4FFBA],
            stops: [0, 1],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            _buildBalanceInfoWidget(),
            _buildRechargeListContainer(),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceInfoWidget() {
    double itemWidth = (ScreenUtil().screenWidth - 15.w * 2 - 1.w) / 2;
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
      height: 90.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
        color: AppColors.colorFFF5FFF0,
      ),
      child: Row(
        children: [
          SizedBox(
            width: itemWidth,
            height: 90.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ImageUtils.getImage(Assets.imagesRechargeDaCoin, 32.w, 32.w),
                Padding(
                  padding: EdgeInsets.only(left: 5.w),
                  child: Text(
                    "搭币：",
                    style: TextStyles.common(16.sp, AppColors.colorFF6FB573,
                        w: FontWeight.w500),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 1.w),
                  child: Obx(
                    () => Text(
                      "${controller.accountBalance.value?.daCoinNum ?? 0}",
                      style: TextStyles.common(
                        30.sp,
                        AppColors.colorFF29782D,
                        w: FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 1.w,
            height: 20.h,
            color: AppColors.colorFFDDF8CF,
          ),
          SizedBox(
            width: itemWidth,
            height: 90.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ImageUtils.getImage(Assets.imagesRechargeDaBang, 22.w, 32.h),
                Padding(
                  padding: EdgeInsets.only(left: 5.w),
                  child: Text(
                    "搭棒：",
                    style: TextStyles.common(16.sp, AppColors.colorFF6FB573,
                        w: FontWeight.w500),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 1.w),
                  child: Obx(
                    () => Text(
                      "${controller.accountBalance.value?.daStickNum ?? 0}",
                      style: TextStyles.common(
                        30.sp,
                        AppColors.colorFF29782D,
                        w: FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRechargeListContainer() {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
              color: AppColors.colorFFD1EAD2,
              offset: Offset(0, -2),
              blurRadius: 8,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: GetBuilder(
                init: controller,
                builder: (controller) {
                  if (controller.goodsList.isEmpty) {
                    return EmptyWidget();
                  }
                  return GridView.extent(
                    padding:
                        EdgeInsets.only(left: 15.w, right: 15.w, top: 15.h),
                    maxCrossAxisExtent: 110.w,
                    mainAxisSpacing: 15.h,
                    crossAxisSpacing: 6.8.w,
                    childAspectRatio: 110.w / 140.h,
                    children: controller.goodsList.map((e) {
                      int index = controller.goodsList.indexOf(e);
                      return _buildRechargeListItemWidget(e, index);
                    }).toList(),
                  );
                },
              ),
            ),
            _buildBottomBtn(),
          ],
        ),
      ),
    );
  }

  Widget _buildRechargeListItemWidget(GoodsEntity itemEntity, int index) {
    return Obx(() {
      bool selected = controller.currentSelectedIndex.value == index;
      String bgImage = Assets.imagesRechargeListItemBg1;
      bool isFirstRecharge = false;
      if (itemEntity.goodsType == 0) {
        isFirstRecharge = UserService().user?.isFirstRecharge == 1;
      } else if (itemEntity.goodsType == 1) {
        isFirstRecharge = false;
        bgImage = Assets.imagesRechargeListItemBg2;
      }

      return GestureDetector(
        onTap: () {
          controller.currentSelectedIndex.value = index;
        },
        child: Container(
          width: 110.w,
          height: 140.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(13.r),
            border: Border.all(
              color: selected ? AppColors.colorFF23AF28 : Colors.transparent,
              width: 3.w,
            ),
            image:
                DecorationImage(image: AssetImage(bgImage), fit: BoxFit.cover),
          ),
          child: itemEntity.goodsType == 1
              ? _buildGiftPackItemContainer(itemEntity)
              : Column(
                  children: [
                    Expanded(
                      child: Stack(
                        clipBehavior: Clip.none,
                        alignment: Alignment.center,
                        children: [
                          itemEntity.goodsType != 1
                              ? Padding(
                                  padding: EdgeInsets.only(
                                    top: 5.h,
                                  ),
                                  child: ImageUtils.getImage(
                                      itemEntity.image ?? "", 110.w, 60.h),
                                )
                              : Container(),
                          Visibility(
                            visible: isFirstRecharge,
                            child: Positioned(
                              right: 0,
                              top: 0,
                              child: ImageUtils.getImage(
                                  Assets.imagesRechargeListItemFirstPurchase,
                                  45.w,
                                  47.h),
                            ),
                          ),
                          Positioned(
                            bottom: 0.h,
                            child: GestureDetector(
                              onTap: () {},
                              child: Text(
                                itemEntity.goodsDetailsList?.first.num != null
                                    ? "${Platform.isIOS ? (itemEntity.goodsDetailsList!.first.num! * 0.85).ceil() : itemEntity.goodsDetailsList!.first.num}个"
                                    : "",
                                style: TextStyles.normal(16.sp),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      alignment: Alignment.center,
                      height: 28.h,
                      padding: EdgeInsets.only(top: 5.h),
                      child: Text(
                        itemEntity.goodsType == 0 || itemEntity.goodsType == 2
                            ? "￥${itemEntity.price}"
                            : "",
                        style: TextStyles.bold(16.sp, w: FontWeight.w500),
                      ),
                    ),
                  ],
                ),
        ),
      );
    });
  }

  ///礼包购买
  Widget _buildGiftPackItemContainer(GoodsEntity itemEntity) {
    String endTimeStr = "";
    if (itemEntity.endTime != null) {
      final dateTime = DateTime.parse(itemEntity.endTime!);
      endTimeStr = "${dateTime.month}/${dateTime.day}";
    }

    return Column(
      children: [
        SizedBox(
          height: 112.h,
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r)),
                child: ImageUtils.getImage(itemEntity.image ?? "", 110.w, 112.h,
                    fit: BoxFit.cover),
              ),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 85.w,
                  height: 25.h,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(
                          Assets.imagesRechargeListItemGiftPackTimeBg),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Text(
                    "截止到$endTimeStr",
                    style: TextStyles.common(12.sp, AppColors.colorFF666666),
                  ),
                ),
              ),
              Positioned(
                top: 40.h,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 5.h),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.r),
                      bottomLeft: Radius.circular(12.r),
                    ),
                    color: AppColors.colorFFFF6464,
                  ),
                  child: Text(
                      "限\n购\n${itemEntity.isBuy ?? 0}/${itemEntity.buyLimit}",
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 11.sp, color: Colors.white)),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Container(
            alignment: Alignment.center,
            child: Text(
              "￥${itemEntity.price}",
              style: TextStyles.bold(16.sp, w: FontWeight.w500),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBtn() {
    return GetBuilder(
      init: controller,
      filter: (controller) => controller.goodsList.length,
      builder: (controller) {
        if (controller.goodsList.isEmpty) {
          return Container();
        }
        return Obx(() {
          GoodsEntity goodsEntity =
              controller.goodsList[controller.currentSelectedIndex.value];
          bool enabled = true;
          if (goodsEntity.isBuy != null &&
              goodsEntity.buyLimit != null &&
              goodsEntity.isBuy! >= goodsEntity.buyLimit!) {
            enabled = false;
          }
          return CommonGradientBtn(
            horizontalMargin: 35.w,
            topMargin: 10.h,
            bottomMargin: ScreenUtil().bottomBarHeight + 10.h,
            title: "立即充值",
            enabled: enabled,
            disabledImage: Assets.imagesGradientBtnDisabledBg1,
            onTap: () {
              if (!enabled) {
                return;
              }
              GoodsEntity goodsEntity =
                  controller.goodsList[controller.currentSelectedIndex.value];
              Get.to(() => PayOrderInfoPage(goodsEntity: goodsEntity))
                  ?.then((value) async {
                if (value == "1") {
                  await UserService().refresh();
                  controller.loadUserStainsCount();
                  controller.loadData();
                }
              });
            },
          );
        });
      },
    );
  }
}
