import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MonthCardExpiredLimitChatDialog extends StatelessWidget {
  const MonthCardExpiredLimitChatDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
      height: 270.h,
      stops: const [0, 0.35],
      title: "",
      isShowCloseBtn: false,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 5.h),
            child: Text(
              "您的月卡到期啦~",
              style: TextStyles.medium(18.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Text(
              "续费月卡，继续使用聊天等所有功能！",
              style: TextStyles.normal(18.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: RichText(
                text: TextSpan(
                    text: '(在小屋-',
                    style: TextStyles.normal(15.sp),
                    children: [
                  TextSpan(text: '任务', style: TextStyles.medium(15.sp)),
                  TextSpan(text: '中，也可以', style: TextStyles.normal(15.sp)),
                  TextSpan(text: '免费获得月卡', style: TextStyles.medium(15.sp)),
                  TextSpan(text: '权益哦~)', style: TextStyles.normal(15.sp)),
                ])),
          ),
          Padding(
            padding: EdgeInsets.only(top: 40.h, bottom: 30.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    width: 100.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(17.5.r),
                      border: Border.all(
                        color: const Color(0xFF999999),
                      ),
                    ),
                    child: Text(
                      "知道啦",
                      style: TextStyles.common(16.sp, AppColors.colorFF666666),
                    ),
                  ),
                ),
                SizedBox(
                  width: 9.w,
                ),
                CommonGradientBtn(
                  width: 100.w,
                  height: 40.h,
                  normalImage: Assets.imagesCommonGradientBtnBg103w44h,
                  title: "开通月卡",
                  onTap: () {
                    Get.back();
                    Get.toNamed(GetRouter.rechargeMonthCard);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
