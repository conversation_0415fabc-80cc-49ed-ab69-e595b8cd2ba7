import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/goods_entity.dart';
import 'package:dada/pages/store/pay_order_info_page.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class RechargeMonthCardBottomDialog extends StatefulWidget {
  final GoodsEntity goodsItem;
  final Function(String result) callback;

  const RechargeMonthCardBottomDialog(
      {super.key, required this.goodsItem, required this.callback});

  @override
  State<RechargeMonthCardBottomDialog> createState() =>
      _RechargeMonthCardBottomDialogState();
}

class _RechargeMonthCardBottomDialogState
    extends State<RechargeMonthCardBottomDialog> {
  TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();

    textEditingController.text = "1";
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: ScreenUtil().screenWidth,
          height: 350.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
            gradient: const LinearGradient(
              colors: [
                AppColors.colorFFD2F6C0,
                Colors.white,
              ],
              stops: [0, 0.35],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              _buildContainer(),
              _buildCloseBtn(),
            ],
          ),
        ),
        Positioned(
          top: -62.h,
          child: ImageUtils.getImage(
              Assets.imagesRechargeMonthCardBottomDialogTopImage, 187.w, 133.h),
        ),
      ],
    );
  }

  Widget _buildContainer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 83.5.h),
          child: Text(
            "大礼包套餐",
            style: TextStyles.normal(16.sp),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 20.h),
          child: Wrap(
            spacing: 18.63.w,
            runSpacing: 0,
            children: widget.goodsItem.goodsDetailsList!
                .map((e) => _buildRewardsItemWidget(e))
                .toList(),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 42.h),
          child: Row(
            children: [
              Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: 29.w,
                    ),
                    child: Text(
                      "礼包数量：",
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                  SizedBox(
                    width: 95.w,
                    height: 30.h,
                    child: Stack(
                      alignment: Alignment.centerLeft,
                      children: [
                        Container(
                          margin: EdgeInsets.only(left: 21.w, right: 21.w),
                          padding: EdgeInsets.only(bottom: 2.h),
                          width: 62.w,
                          height: 36.h,
                          color: AppColors.colorFFE1E4E0,
                          child: CustomTextField.build(
                            contentPadding:
                                EdgeInsets.symmetric(horizontal: 10.w),
                            controller: textEditingController,
                            textAlign: TextAlign.center,
                            style: TextStyles.normal(16.sp),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp("[0-9]"))
                            ],
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            if (textEditingController.text.isNotEmpty) {
                              int value = int.parse(textEditingController.text);
                              value--;
                              if (value < 0) {
                                value = 0;
                              }
                              textEditingController.text = value.toString();
                            } else {
                              textEditingController.text = 0.toString();
                            }
                          },
                          child: ImageUtils.getImage(
                              Assets.imagesRechargeMonthCardCountReduce,
                              30.w,
                              30.w),
                        ),
                        Positioned(
                          right: 0,
                          child: GestureDetector(
                            onTap: () {
                              if (textEditingController.text.isNotEmpty) {
                                int value =
                                    int.parse(textEditingController.text);
                                value++;
                                textEditingController.text = value.toString();
                              } else {
                                textEditingController.text = 1.toString();
                              }
                            },
                            child: ImageUtils.getImage(
                                Assets.imagesRechargeMonthCardCountAdd,
                                30.w,
                                30.w),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Padding(
                padding: EdgeInsets.only(right: 20.w),
                child: CommonGradientBtn(
                  title: "购买",
                  width: 103.w,
                  height: 44.h,
                  normalImage: Assets.imagesCommonGradientBtnBg44h,
                  onTap: () {
                    widget.goodsItem.count =
                        int.parse(textEditingController.text);
                    Get.to(
                      () => PayOrderInfoPage(goodsEntity: widget.goodsItem),
                    )?.then((result) {
                      if (result == "1") {
                        Get.back(result: "1");
                      }
                    });
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRewardsItemWidget(GoodsRewardEntity itemEntity) {
    double imageWidth = 34.w;
    double imageHeight = 34.w;

    if (itemEntity.prop?.propType == PropType.daBang.index) {
      imageWidth = 33.w;
      imageHeight = 50.h;
    } else if (itemEntity.prop?.propType == PropType.monthCard.index) {
      imageWidth = 72.w;
      imageHeight = 43.h;
    } else {
      imageHeight = 50.w;
      imageWidth = 50.w;
    }

    return GradientWidget(
      width: 90.w,
      height: 100.h,
      colors: const [
        Colors.white,
        AppColors.colorFFF5FFF0,
      ],
      cornerRadius: 10.r,
      border: Border.all(color: AppColors.colorFFDDF8CF),
      child: Column(
        children: [
          Expanded(
            child: Container(
              alignment: Alignment.center,
              child: ImageUtils.getImage(
                itemEntity.prop?.url ?? "",
                imageWidth,
                imageHeight,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 5.h, left: 5.w, right: 5.w),
            child: Text(
              textAlign: TextAlign.center,
              "${itemEntity.prop?.propName ?? ""} x ${itemEntity.num ?? ""}",
              maxLines: 2,
              style: TextStyles.normal(14.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCloseBtn() {
    return Positioned(
      right: 15.w,
      top: 10.h,
      child: GestureDetector(
        onTap: () {
          Get.back();
        },
        child: Icon(
          Icons.close,
          size: 25.w,
          color: AppColors.colorFF666666,
        ),
      ),
    );
  }
}
