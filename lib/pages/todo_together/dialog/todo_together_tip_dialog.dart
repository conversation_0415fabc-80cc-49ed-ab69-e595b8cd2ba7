import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherTipDialog extends StatelessWidget {
  final String content;
  const TodoTogetherTipDialog({super.key, required this.content});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 266.w,
        height: 291.h,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF8E8ED3).withOpacity(0.9),
                  Color(0xFF5555A8).withOpacity(0.9)
                ])),
        child: Stack(
          children: [
            Positioned.fill(
                child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Column(
                children: [
                  Text(
                    'Tips',
                    style: TextStyles.medium(16.sp,
                        c: Colors.white, h: 22.sp / 16.sp),
                  ),
                  SizedBox(height: 8.h),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Text(
                        content,
                        style: TextStyles.common(14.sp, Colors.white,
                            h: 20.sp / 14.sp),
                      ),
                    ),
                  )
                ],
              ),
            )),
            Positioned(
                right: 0,
                child: IconButton(
                    onPressed: () {
                      Get.back();
                    },
                    icon: Icon(Icons.close_rounded,
                        size: 15, color: Colors.white.withOpacity(0.8))))
          ],
        ),
      ),
    );
  }
}
