import 'package:dada/common/values/text_styles.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_tip_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TodoTogetherRichText extends StatelessWidget {
  final String content;
  final String tipContent;
  const TodoTogetherRichText({
    super.key,
    required this.content,
    required this.tipContent,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            children: [
              TextSpan(
                  text: content,
                  style:
                      TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp)),
              if (tipContent.isNotEmpty)
                WidgetSpan(
                    child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (_) {
                          return TodoTogetherTipDialog(content: tipContent);
                        });
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 3.w),
                    child:
                        Icon(Icons.help_outline, color: Colors.white, size: 13),
                  ),
                )),
            ],
          )),
    );
  }
}
