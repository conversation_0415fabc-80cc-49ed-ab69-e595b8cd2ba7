import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherStopDialog extends StatelessWidget {
  const TodoTogetherStopDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        child: Material(
          color: Colors.transparent,
          child: Column(children: [
            SizedBox(height: 183.h),
            Container(
              width: ScreenUtil().screenWidth,
              margin: EdgeInsets.symmetric(horizontal: 32.w),
              height: 315.h,
              decoration: const BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage(Assets.imagesTodoTogetherDialogBac))),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(height: 110.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 30.w),
                    child: Text('有人终止了一起做件事，此次事件终止，希望你们有缘再会哦~',
                        textAlign: TextAlign.center,
                        style: TextStyles.medium(18.sp,
                            c: Colors.white, h: 22.sp / 18.sp)),
                  ),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 0, vertical: 20.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        GestureDetector(
                          onTap: () {
                            Get.until((router) =>
                                Get.currentRoute == GetRouter.todoTogether);
                          },
                          child: Container(
                            width: 103.w,
                            height: 40.h,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xFF7A71C7),
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(
                                  color: const Color(0xFFE3E3E3), width: 1),
                            ),
                            child: Text(
                              '确认',
                              style: TextStyles.normal(16.sp, c: Colors.white),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ]),
        ));
  }
}
