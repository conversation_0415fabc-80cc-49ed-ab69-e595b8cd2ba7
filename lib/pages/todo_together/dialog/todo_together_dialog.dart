import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TodoTogetherDialog extends StatelessWidget {
  final String? title;
  final String? content;
  final String? leftBtnTitle;
  final String? rightBtnTitle;
  final Function()? onLeftBtnTap;
  final Function()? onRightBtnTap;

  const TodoTogetherDialog(
      {super.key,
      this.title,
      this.content,
      this.leftBtnTitle,
      this.rightBtnTitle,
      this.onLeftBtnTap,
      this.onRightBtnTap});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Column(children: [
        SizedBox(height: 183.h),
        Container(
          width: ScreenUtil().screenWidth,
          margin: EdgeInsets.symmetric(horizontal: 32.w),
          height: 315.h,
          decoration: const BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(Assets.imagesTodoTogetherDialogBac))),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(height: 76.h + 16.h),
              if (title != null && content != null)
                Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Text(
                        title!,
                        style: TextStyles.medium(18.sp,
                            c: Colors.white, h: 22.sp / 18.sp),
                      ),
                    ),
                    SizedBox(height: 22.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 30.w),
                      child: Text(
                        content!,
                        textAlign: TextAlign.center,
                        style: title == null
                            ? TextStyles.medium(18.sp,
                                c: Colors.white, h: 22.sp / 18.sp)
                            : TextStyles.normal(15.sp,
                                c: Colors.white, h: 20.sp / 15.sp),
                      ),
                    ),
                  ],
                )
              else
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: title != null
                      ? Text(title!,
                          textAlign: TextAlign.center,
                          style: TextStyles.medium(18.sp,
                              c: Colors.white, h: 22.sp / 18.sp))
                      : (content!.contains("免费获得月卡权益")
                          ? RichText(
                              text: TextSpan(children: [
                              TextSpan(
                                  text: content!.split("免费")[0],
                                  style: TextStyles.normal(18.sp,
                                      c: Colors.white, h: 22.sp / 18.sp)),
                              TextSpan(
                                  text: "免费",
                                  style: TextStyles.bold(18.sp, c: Colors.red)),
                              TextSpan(
                                  text: content!.split("免费")[1],
                                  style: TextStyles.normal(18.sp,
                                      c: Colors.white, h: 22.sp / 18.sp)),
                            ]))
                          : Text(content!,
                              textAlign: TextAlign.center,
                              style: TextStyles.medium(18.sp,
                                  c: Colors.white, h: 22.sp / 18.sp))),
                ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 0, vertical: 20.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    GestureDetector(
                      onTap: onLeftBtnTap,
                      child: Container(
                        width: 103.w,
                        height: 40.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: const Color(0xFF7A71C7),
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(
                              color: const Color(0xFFE3E3E3), width: 1),
                        ),
                        child: Text(
                          leftBtnTitle ?? "",
                          style: TextStyles.normal(16.sp, c: Colors.white),
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: onRightBtnTap,
                      child: Container(
                        width: 103.w,
                        height: 40.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(
                              color: const Color(0xFF9D93DB), width: 1),
                        ),
                        child: Text(
                          rightBtnTitle ?? "",
                          style: TextStyles.normal(16.sp, c: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ]),
    );
  }
}
