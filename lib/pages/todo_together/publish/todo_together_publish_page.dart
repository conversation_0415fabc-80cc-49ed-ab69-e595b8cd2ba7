import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_tip_dialog.dart';
import 'package:dada/pages/todo_together/publish/todo_together_publish_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherPublishPage extends StatefulWidget {
  const TodoTogetherPublishPage({super.key});

  @override
  State<TodoTogetherPublishPage> createState() =>
      _TodoTogetherPublishPageState();
}

class _TodoTogetherPublishPageState extends State<TodoTogetherPublishPage> {
  TextEditingController _textEditingController = TextEditingController();
  final controller = Get.put(TodoTogetherPublishController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: ScreenUtil().screenWidth,
        height: ScreenUtil().screenHeight,
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage(Assets.imagesTodoTogetherPublishBac),
                fit: BoxFit.cover)),
        child: Stack(
          children: [
            Align(
              child: Container(
                width: ScreenUtil().screenWidth,
                decoration: const BoxDecoration(
                    image: DecorationImage(
                        image: AssetImage(
                            Assets.imagesTodoTogetherPublishInputBac))),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(height: 88.h),
                    RichText(
                        text: TextSpan(
                      children: [
                        TextSpan(
                            text: '那么接下来，你想来个人和你一起',
                            style: TextStyles.bold(16.sp,
                                c: const Color(0xFFFF84E9))),
                        WidgetSpan(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            showDialog(
                                context: context,
                                builder: (_) {
                                  return const TodoTogetherTipDialog(
                                      content:
                                          '随手发个一起来做事，相投、合适的小伙伴就来咯~ \n这里没有拘束，小到一起吃个苹果，或者一起开把王者，也可以一起上个自习，你只要知道，在世界上的某个时空，会有个人和你一起，在做这件事，就好咯~\n时间也很自由，小到几分钟，多到很多天，都随你掌控\n不必局促、无需交流，在开始的时光，你们都是在静静的做这件事。\n你们，从这里相识~   发布，启航！');
                                });
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 3.w, vertical: 2.h),
                            child: Icon(Icons.help_outline,
                                color: Color(0xFFFF84E9), size: 13),
                          ),
                        )),
                      ],
                    )),
                    SizedBox(height: 13.h),
                    Container(
                      width: 247.w,
                      height: 48.h,
                      padding: const EdgeInsets.all(1),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        gradient: const LinearGradient(colors: [
                          Color(0xFFFFA9D2),
                          Color(0xFFF064DB),
                          Color(0xFFF064DB)
                        ]),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.r)),
                        child: CustomTextField.build(
                          contentPadding: EdgeInsets.only(left: 7.w),
                          controller: _textEditingController,
                          maxLength: 20,
                          hintText: "吃个苹果",
                          hintStyle:
                              TextStyles.common(16.sp, const Color(0xFFD3CECE)),
                          showSuffixIcon: true,
                          // focusNode: controller.focusNode,
                          textInputAction: TextInputAction.search,
                          suffixIconOnTap: () {},
                          style:
                              TextStyles.common(16.sp, AppColors.colorFF333333),
                          onChanged: (value) {},
                          onSubmitted: (value) {},
                        ),
                      ),
                    ),
                    SizedBox(height: 24.h),
                    SizedBox(
                      width: 240.w,
                      child: Text(
                        '想找',
                        style:
                            TextStyles.common(14.sp, const Color(0xFF585456)),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Obx(
                      () => SizedBox(
                        width: 240.w,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _checkBtn(
                                title: '不限',
                                img: Assets.imagesTodoTogetherMaleFemale),
                            _checkBtn(
                                title: '男生',
                                img: Assets.imagesTodoTogetherMale),
                            _checkBtn(
                                title: '女生',
                                img: Assets.imagesTodoTogetherFemale),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 17.h),
                    GestureDetector(
                      onTap: () {
                        controller.publish(_textEditingController.text);
                      },
                      child: ImageUtils.getImage(
                          Assets.imagesTodoTogetherPublishPublishBtn,
                          171.w,
                          50.h),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: ScreenUtil().statusBarHeight,
              child: IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: Image(
                      image: const AssetImage(Assets.imagesNaviBackWhite),
                      width: 12.w,
                      height: 20.h)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _checkBtn({String? title, String? img}) {
    Color textColor =
        title == '女生' ? const Color(0xFFFF79CC) : const Color(0xFF5B5B5B);
    Color borderColor = const Color(0xFFE7E7E7);
    Color bacColor = const Color(0xFFFFFFFF);
    if (title == controller.selectedGender.value) {
      if (title == '不限') {
        bacColor = const Color(0xFF989CF9).withOpacity(0.2);
        borderColor = const Color(0xFF989CF9);
      } else if (title == '男生') {
        bacColor = const Color(0xFF21CDFF).withOpacity(0.1);
        borderColor = const Color(0xFF21CDFF);
      } else {
        bacColor = const Color(0xFFFF79CC).withOpacity(0.2);
        borderColor = const Color(0xFFFF79CC);
      }
    }
    return GestureDetector(
      onTap: () {
        if (!UserService().checkIsMonthCardUser()) {
          controller.showStopDialog(context,
              content: '此功能需要月卡权益，您的支持我们将全用来将产品做的更好哦~\n（在小屋-任务中，也可以免费获得月卡权益~)',
              rightText: "开通月卡", onRightBtnTap: () {
            Get.back();
            Get.toNamed(GetRouter.rechargeMonthCard);
          });
        } else {
          controller.selectedGender.value = title;
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(color: borderColor, width: 1.w),
          color: bacColor,
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 13.w, vertical: 7.h),
          child: Row(
            children: [
              Image.asset(img!, height: 15.h, fit: BoxFit.fitHeight),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  title!,
                  style: TextStyles.common(12.sp, textColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
