import 'package:dada/pages/todo_together/dialog/todo_together_dialog.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TodoTogetherPublishController extends GetxController {
  RxString selectedGender = '不限'.obs;

  void publish(String? content) async {
    if (content == null || content.isEmpty) {
      ToastUtils.showToast('请输入内容');
      return;
    }
    int sexInt;
    switch (selectedGender.value) {
      case '不限':
        sexInt = 2;
        break;
      case '男生':
        sexInt = 0;
        break;
      case '女生':
        sexInt = 1;
        break;
      default:
        sexInt = 0;
    }
    bool? result =
        await ApiService().todoTogetherPublish(content: content, sex: sexInt);
    if (result) {
      Get.back();
    } else {
      ToastUtils.showToast('发布失败');
    }
  }

  void showStopDialog(BuildContext c,
      {void Function()? onRightBtnTap, String? content, String? rightText}) {
    showDialog(
        context: c,
        builder: (_) {
          return TodoTogetherDialog(
            content: content ?? '此操作将终止事件哦,确定这么做吗?',
            onRightBtnTap: onRightBtnTap,
            onLeftBtnTap: () {
              Get.back();
            },
            rightBtnTitle: rightText ?? '终止',
            leftBtnTitle: '取消',
          );
        });
  }
}
