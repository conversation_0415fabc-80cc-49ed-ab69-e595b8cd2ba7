import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherDetailProposeCheck extends StatefulWidget {
  const TodoTogetherDetailProposeCheck({super.key});

  @override
  State<TodoTogetherDetailProposeCheck> createState() =>
      _TodoTogetherDetailProposeCheckState();
}

class _TodoTogetherDetailProposeCheckState
    extends State<TodoTogetherDetailProposeCheck> {
  bool get isCreater =>
      Get.find<TodoTogetherListController>(
              tag:
                  'TodoTogetherListController_${TodoTogetherListType.all.toString()}')
          .currentEntity
          .type ==
      1;

  final controller = Get.find<TodoTogetherDetailController>();

  TodoTogetherDetailStage8? stage8;

  @override
  void initState() {
    super.initState();
    stage8 = controller.detail?.stage8;
    if (!isCreater) {
      controller.end();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 34.w),
          child: Text(
            isCreater
                ? '这是双方的再一起做件事提案哦,可以选择其中一件开始~'
                : '这是双方的再一起做件事提案哦,请等待对方选择其中一件开始~对方选择后,一起做件事列表会有相应的更新,请关注',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 64.h),
        _buildProposeRow('提案1: 我想我们一起', stage8?.myPropose, () {
          controller.proposeSelect(stage8?.myPropose);
        }),
        SizedBox(height: 22.h),
        _buildProposeRow('提案2: 我想我们一起', stage8?.otherPropose, () {
          controller.proposeSelect(stage8?.otherPropose);
        }),
        SizedBox(height: 77.h),
      ],
    );
  }

  Widget _buildProposeRow(
      String title, String content, void Function()? onTap) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(left: 31.w),
          child: Text(title,
              style: TextStyles.common(14.sp, Colors.white, h: 20.sp / 14.sp)),
        ),
        Row(
          children: [
            Container(
              margin: EdgeInsets.only(left: 31.w),
              alignment: Alignment.centerLeft,
              width: isCreater ? 248.w : ScreenUtil().screenWidth - 62.w,
              height: 48.h,
              decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.57),
                  border: Border.all(width: 1, color: Colors.white),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r),
                    bottomLeft: Radius.circular(12.r),
                  )),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Text(
                    content,
                    maxLines: 1,
                    style: TextStyles.common(13.sp, Colors.white,
                        h: 18.sp / 13.sp),
                  ),
                ),
              ),
            ),
            if (isCreater && controller.fromType == TodoTogetherListType.all)
              SizedBox(width: 12.w),
            if (isCreater && controller.fromType == TodoTogetherListType.all)
              GestureDetector(
                onTap: onTap,
                child: ImageUtils.getImage(
                    Assets.imagesTodoTogetherDetailCheck, 65.w, 41.h),
              ),
          ],
        ),
      ],
    );
  }
}
