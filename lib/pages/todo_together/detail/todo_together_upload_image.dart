import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/pages/todo_together/detail/bottom_buttons.dart';
import 'package:dada/pages/todo_together/detail/bottom_tips.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class TodoTogetherUploadImage extends StatelessWidget {
  const TodoTogetherUploadImage({super.key});

  @override
  Widget build(BuildContext context) {
    final TodoTogetherDetailController controller =
        Get.find<TodoTogetherDetailController>();
    final TodoTogetherDetailStage2? model = controller.detail?.stage2;
    return Column(
      children: [
        BottomTips(
            verticalPadding: 50.h,
            content:
                '这是你们一起做的第${(controller.detail?.countCommonTasks ?? 1) + 1}件事',
            visible: controller.detail?.beforeTaskId != null),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 34.w),
          child: Text(
            '启航!你们要开始一起${controller.detail?.content}了哦,作为开始做这件事的证明,你们可以各自上传一张准备做这件事的图片哦~\n如果你想在事件完成后再发，或这个事件无需发图，也可以选择跳过哦~',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 64.h),
        GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              if (model?.myImg != null) {
                return;
              }
              ToastUtils.showBottomSheet(
                [S.current.album, S.current.camera],
                onTap: (index) async {
                  bool res = await ImagePickerUtil.checkPermission(
                      index == 0 ? 1 : 2,
                      index == 0 ? "相册权限说明" : "相机权限说明",
                      index == 0
                          ? "获取图片用于上传自己一起做件事图片"
                          : "拍摄图片用于上传自己一起做件事图片, 拍摄后的图片将存放在系统照片中");

                  if (!res) return;

                  /*  XFile? imageFile = await ImagePicker().pickImage(
                      source: index == 0
                          ? ImageSource.gallery
                          : ImageSource.camera); */
                  AssetEntity? asset;
                  if (index == 0) {
                    List<AssetEntity>? assets =
                        await ImagePickerUtil.selectAsset(
                            maxAssets: 1, isImage: true);
                    if (assets != null) {
                      asset = assets.first;
                    }
                  } else {
                    asset = await ImagePickerUtil.takeAsset();
                  }
                  String? imagePath =
                      await ImagePickerUtil.getEntityPath(asset);
                  if (imagePath != null && imagePath.isNotEmpty) {
                    controller.uploadImg.value = imagePath;
                  }
                },
              );
            },
            child: Obx(
              () => DottedBorder(
                color: const Color(0xFFFBFBFB),
                dashPattern: const [8, 8],
                radius: Radius.circular(8.r),
                strokeWidth: 2,
                child: SizedBox(
                  width: 200,
                  height: 200,
                  child: controller.uploadImg.value.isEmpty &&
                          model?.myImg == null
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              Assets.imagesTodoTogetherDetailAddPicture,
                              color: Colors.white,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              '快来上传图片吧~',
                              style: TextStyles.common(14.sp, Colors.white),
                            )
                          ],
                        )
                      : (model?.myImg != null
                          ? ImageUtils.getImage(model?.myImg ?? '', 200, 200)
                          : ImageUtils.getImage(
                              controller.uploadImg.value, 200, 200)),
                ),
              ),
            )),
        BottomTips(
            content: '已确认,请等待对方一下吧~',
            visible: model?.myImg != null && controller.detail?.stage == 2),
        BottomButtons(
          visible: controller.detail?.stage == 2 &&
              model?.myImg == null &&
              controller.fromType == TodoTogetherListType.all,
          leftImg: Assets.imagesTodoTogetherDetailToStart,
          rightImg: Assets.imagesTodoTogetherDetailSend,
          leftOnTap: () {
            controller.skip();
          },
          rightOnTap: () {
            controller.uploadImgMyself();
          },
        ),
        if (!(controller.detail?.stage == 2 && model?.myImg == null))
          SizedBox(height: 77.h),
      ],
    );
  }
}
