import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/pages/todo_together/detail/bottom_buttons.dart';
import 'package:dada/pages/todo_together/detail/bottom_tips.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherDetailPropose extends StatelessWidget {
  const TodoTogetherDetailPropose({super.key});

  @override
  Widget build(BuildContext context) {
    final TextEditingController textEditingController = TextEditingController();
    final TodoTogetherDetailController controller =
        Get.find<TodoTogetherDetailController>();
    final TodoTogetherDetailStage8? model8 = controller.detail?.stage8;
    textEditingController.text = model8?.myPropose ??
        (textEditingController.text.isEmpty ? "" : textEditingController.text);
    return Column(
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 34.w),
          child: Text(
            '可以提议本次一起做件什么事哦\n我想我们一起: ',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 64.h),
        Container(
          width: 310.w,
          height: 48.h,
          decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.57),
              border: Border.all(width: 1, color: Colors.white),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
                bottomLeft: Radius.circular(12.r),
              )),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: CustomTextField.build(
                controller: textEditingController,
                hintText: '输入文字',
                hintStyle: TextStyles.common(13.sp, Colors.white),
                style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp)
                    .copyWith(overflow: TextOverflow.ellipsis),
                enabled: model8?.myPropose == null,
                maxLines: 1,
                maxLength: 20)
            /* TextField(
              controller: textEditingController,
              enabled: model8?.myPropose == null,
              maxLines: 1,
              style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp)
                  .copyWith(overflow: TextOverflow.ellipsis),
              cursorColor: Colors.white,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: '输入文字',
                hintStyle: TextStyles.common(13.sp, Colors.white),
              ),
            ) */
            ,
          ),
        ),
        BottomTips(
          visible: model8?.myPropose != null && controller.detail?.stage == 6,
          content: '请等待对方发布一起做的事吧~',
        ),
        BottomButtons(
          visible: model8?.myPropose == null &&
              controller.fromType == TodoTogetherListType.all,
          centerImg: Assets.imagesTodoTogetherDetailPropose,
          centerOnTap: () {
            if (model8?.myPropose != null) {
              Get.find<TodoTogetherDetailController>().getDetailData();
            } else {
              Get.find<TodoTogetherDetailController>()
                  .propose(textEditingController.text);
            }
          },
        )
      ],
    );
  }
}
