import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BottomTips extends StatelessWidget {
  final String content;
  final bool visible;
  final double? verticalPadding;
  const BottomTips(
      {super.key,
      required this.content,
      required this.visible,
      this.verticalPadding});

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: visible,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
            horizontal: 34.w, vertical: verticalPadding ?? 77.h),
        child: Text(
          content,
          textAlign: TextAlign.center,
          style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
        ),
      ),
    );
  }
}
