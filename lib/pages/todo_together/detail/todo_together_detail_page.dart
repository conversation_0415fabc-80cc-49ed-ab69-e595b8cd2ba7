import 'dart:async';

import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/home/<USER>';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_des.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_other_img.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_propose_check.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_send_msg.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_time.dart';
import 'package:dada/pages/todo_together/detail/todo_together_start.dart';
import 'package:dada/pages/todo_together/detail/todo_together_upload_image.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_stop_dialog.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:dada/pages/todo_together/todo_together_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

class TodoTogetherDetailPage extends StatefulWidget {
  const TodoTogetherDetailPage({super.key});

  @override
  State<TodoTogetherDetailPage> createState() => _TodoTogetherDetailPageState();
}

class _TodoTogetherDetailPageState extends State<TodoTogetherDetailPage> {
  final controller = Get.put(TodoTogetherDetailController());

  String bacImg = Assets.imagesTodoTogetherDetailHomeBac;

  late StreamSubscription myEventSub;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      myEventSub = EventBusEngine.eventBus.on().listen((event) {
        EventEntity entity = event;
        if (entity.event == BusEvent.receiveTodoTogetherMsg) {
          controller.getDetailData();
        } else if (entity.event == BusEvent.receiveTodoTogetherStop) {
          if (entity.message == controller.userTaskId) {
            if (mounted) {
              showDialog(
                  context: context,
                  builder: (_) {
                    return const TodoTogetherStopDialog();
                  });
            }
          }
        }
      });
    });
  }

  @override
  void dispose() {
    myEventSub.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop:
            controller.fromType == TodoTogetherListType.history ? true : false,
        onPopInvokedWithResult: (didPop, result) {
          if (controller.fromType == TodoTogetherListType.all) {
            String? currentId = controller.userTaskId;
            try {
              String unReadId =
                  Get.find<TodoTogetherController>().unReadTaskId.value;
              if (unReadId == currentId) {
                Get.find<TodoTogetherController>().unReadTaskId.value = "";
                Get.find<HomeController>().todoTogetherUnreadId.value = "";
              }
            } catch (e) {
              String unReadId =
                  Get.find<HomeController>().todoTogetherUnreadId.value;
              if (unReadId == currentId) {
                Get.find<HomeController>().todoTogetherUnreadId.value = "";
              }
            }
            if (!didPop) {
              Get.back();
            }
          }
        },
        child: Scaffold(
            body: GetBuilder(
                init: controller,
                builder: (c) {
                  return Stack(
                    children: [
                      ImageUtils.getImage(bacImg, ScreenUtil().screenWidth,
                          ScreenUtil().screenHeight,
                          fit: BoxFit.cover),
                      Positioned.fill(
                        child: Column(
                          children: [
                            SizedBox(height: ScreenUtil().statusBarHeight),
                            _naviBuilder(),
                            /* Container(
                    margin: EdgeInsets.only(right: 16.w),
                    width: ScreenUtil().screenWidth,
                    alignment: Alignment.centerRight,
                    child: GestureDetector(
                      onTap: () {},
                      child: Container(
                        child: ImageUtils.getImage(
                            Assets.imagesTodoTogetherDetailMsg, 84.w, 28.h),
                      ),
                    ),
                  ), */
                            Obx(() => Visibility(
                                visible:
                                    controller.otherUserId.value.isNotEmpty,
                                child: Row(
                                  children: [
                                    const Spacer(),
                                    BadgeWidget(
                                      text: controller
                                                  .msgBoxUnreadCount.value ==
                                              0
                                          ? null
                                          : '${controller.msgBoxUnreadCount.value}',
                                      child: GestureDetector(
                                        onTap: () async {
                                          V2TimConversation? conversation =
                                              await ChatIMManager.sharedInstance
                                                  .getConversation(
                                                      userID: controller
                                                          .otherUserId.value,
                                                      type: 1);
                                          Get.toNamed(
                                              GetRouter.todoTogetherMsgBox,
                                              arguments: conversation);
                                        },
                                        child: ImageUtils.getImage(
                                            Assets.imagesTodoTogetherDetailMsg,
                                            84.w,
                                            28.h),
                                      ),
                                    ),
                                    SizedBox(width: 16.w),
                                  ],
                                ))),
                            Expanded(
                                child: ListView.builder(
                              controller: c.scrollController,
                              itemBuilder: (c, i) {
                                return itemBuilder(c, i);
                              },
                              itemCount: c.contents.length,
                            ))
                          ],
                        ),
                      )
                    ],
                  );
                })));
  }

  Widget _naviBuilder() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
            onPressed: () => Get.back(),
            icon: Image(
                image: const AssetImage(Assets.imagesTodoTogetherDetailBack),
                width: 12.w,
                height: 20.h)),
        Text(
          '一起要做的事',
          style: TextStyles.medium(18.sp, c: Colors.white),
        ),
        controller.fromType == TodoTogetherListType.all && !controller.isLook
            ? TextButton(
                onPressed: () {
                  controller.showStopDialog(context, onRightBtnTap: () {
                    controller.stop();
                  });
                },
                child: Text(
                  '终止',
                  style:
                      TextStyles.common(16.sp, Colors.white.withOpacity(0.8)),
                ))
            : Container(),
      ],
    );
  }

  Widget itemBuilder(BuildContext context, int index) {
    final model = controller.contents[index];
    int itemIndex = index;
    /* if (controller.detail?.beforeTaskId != null) {
      itemIndex = index + 1;
    } */
    Widget item;
    if (itemIndex == 0) {
      item = TodoTogetherStart();
    } else if (itemIndex == 1) {
      item = TodoTogetherUploadImage();
    } else if (itemIndex == 2) {
      item = TodoTogetherDetailOtherImg();
    } else if (itemIndex == 3) {
      item = TodoTogetherDetailTime();
    } else if (itemIndex == 4) {
      item = TodoTogetherDetailSendMsg();
    } else if (itemIndex == 5) {
      item = TodoTogetherDetailSendMsg(isSend: false);
    } else if (itemIndex == 6) {
      item = TodoTogetherDetailDes(model: model);
    } else if (itemIndex == 7) {
      item = TodoTogetherDetailProposeCheck();
    } else {
      item = Container();
    }
    return AutoScrollTag(
      key: ValueKey(index),
      controller: controller.scrollController,
      index: index,
      child: item,
    );
  }
}
