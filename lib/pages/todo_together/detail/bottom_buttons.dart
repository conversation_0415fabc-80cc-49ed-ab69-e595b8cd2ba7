import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BottomButtons extends StatelessWidget {
  final bool visible;
  final double? verticalPadding;
  final String? leftImg;
  final String? centerImg;
  final String? rightImg;
  final String? leftDes;
  final String? centerDes;
  final String? rightDes;
  final Function()? leftOnTap;
  final Function()? centerOnTap;
  final Function()? rightOnTap;

  const BottomButtons(
      {super.key,
      required this.visible,
      this.leftImg,
      this.centerImg,
      this.rightImg,
      this.leftDes,
      this.centerDes,
      this.rightDes,
      this.leftOnTap,
      this.centerOnTap,
      this.rightOnTap,
      this.verticalPadding});

  @override
  Widget build(BuildContext context) {
    List<Widget> buttons = [];
    final double buttonWidth = 105.w;
    if (leftImg != null) {
      buttons.add(
        Column(
          children: [
            if (leftDes != null)
              SizedBox(
                width: buttonWidth,
                child: Text(
                  leftDes!,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style:
                      TextStyles.common(12.sp, Colors.white, h: 17.sp / 12.sp),
                ),
              ),
            if (leftDes != null) SizedBox(height: 5.h),
            GestureDetector(
              onTap: () => leftOnTap?.call(),
              child: Image.asset(leftImg!, width: buttonWidth),
            ),
          ],
        ),
      );
    }

    if (centerImg != null) {
      buttons.add(
        Column(
          children: [
            if (centerDes != null)
              SizedBox(
                width: buttonWidth,
                child: Text(
                  centerDes!,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style:
                      TextStyles.common(12.sp, Colors.white, h: 17.sp / 12.sp),
                ),
              ),
            if (centerDes != null) SizedBox(height: 5.h),
            GestureDetector(
              onTap: () => centerOnTap?.call(),
              child: Image.asset(centerImg!, width: buttonWidth),
            ),
          ],
        ),
      );
    }

    if (rightImg != null) {
      buttons.add(
        Column(
          children: [
            if (rightDes != null)
              SizedBox(
                width: buttonWidth,
                child: Text(
                  rightDes!,
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style:
                      TextStyles.common(12.sp, Colors.white, h: 17.sp / 12.sp),
                ),
              ),
            if (rightDes != null) SizedBox(height: 5.h),
            GestureDetector(
              onTap: () => rightOnTap?.call(),
              child: Image.asset(rightImg!, width: buttonWidth),
            ),
          ],
        ),
      );
    }

    return Visibility(
        visible: visible,
        child: Container(
          width: ScreenUtil().screenWidth,
          padding: EdgeInsets.symmetric(
              horizontal: buttons.length == 3 ? 20.w : 48.w,
              vertical: verticalPadding ?? 77.h),
          child: Row(
            mainAxisAlignment: buttons.length == 1
                ? MainAxisAlignment.center
                : MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: buttons.isEmpty
                ? []
                : buttons.length == 1
                    ? buttons
                    : buttons.length == 2
                        ? [
                            buttons[0],
                            buttons[1],
                          ]
                        : [
                            buttons[0],
                            buttons[1],
                            buttons[2],
                          ],
          ),
        ));
  }
}
