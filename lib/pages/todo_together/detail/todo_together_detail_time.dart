import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/pages/todo_together/detail/bottom_buttons.dart';
import 'package:dada/pages/todo_together/detail/bottom_tips.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherDetailTime extends StatelessWidget {
  const TodoTogetherDetailTime({super.key});

  List<int> splitToTenAndUnit(int num) {
    int ten = num ~/ 10;
    int unit = num % 10;
    return [ten, unit];
  }

  @override
  Widget build(BuildContext context) {
    final TodoTogetherDetailController controller =
        Get.find<TodoTogetherDetailController>();
    final TodoTogetherDetailStage4? model = controller.detail?.stage4;
    String startTimeStr = controller.detail?.createdDate ?? "";
    int days = 0, hours = 0;
    if (startTimeStr.isNotEmpty) {
      final DateTime startTime = DateTime.parse(startTimeStr);
      final DateTime now = DateTime.now();
      final diff = now.difference(startTime);
      int totalHours = diff.inSeconds <= 0 ? 0 : (diff.inSeconds / 3600).ceil();
      days = totalHours ~/ 24;
      hours = totalHours % 24;
    }
    final dayParts = splitToTenAndUnit(days);
    final hourParts = splitToTenAndUnit(hours);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Text(
            '你们开始一起${controller.detail?.content}了',
            textAlign: TextAlign.center,
            style: TextStyles.medium(16.sp, c: Colors.white, h: 22.sp / 16.sp),
          ),
        ),
        SizedBox(height: 28.h),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 24.w),
          height: 44.h,
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.34),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Text(
              '${controller.detail?.content}',
              style: TextStyles.medium(16.sp, c: Colors.white),
            ),
          ),
        ),
        SizedBox(height: 34.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Text(
            '随着时间的流淌,自由自在做你想做的事吧~不过你还知道,有一个人,在某个时空在与你做着相同的事哦\n\n节奏自己把握好,当下就做,或者慢慢做,都随你心意哦~当然啦,不同的处事态度都是你自己各方面的体现哦～默默的做自己就好~\n\n你们已经在一起做这件事这么久了哦,enjoy it~',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 35.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _buildTimeItem(dayParts[0]),
            SizedBox(width: 9.w),
            _buildTimeItem(dayParts[1]),
            SizedBox(width: 4.w),
            Text(
              '天',
              style: TextStyles.common(12.sp, Colors.white),
            ),
            SizedBox(width: 4.w),
            _buildTimeItem(hourParts[0]),
            SizedBox(width: 9.w),
            _buildTimeItem(hourParts[1]),
            SizedBox(width: 4.w),
            Text(
              '小时',
              style: TextStyles.common(12.sp, Colors.white),
            ),
          ],
        ),
        BottomButtons(
          visible: controller.detail?.stage == 4 &&
              controller.fromType == TodoTogetherListType.all,
          verticalPadding: (model?.isOtherChuo == true &&
                  model?.isOtherChuoResponse == false)
              ? 30.h
              : 50.h,
          leftImg: Assets.imagesTodoTogetherDetailChuo,
          leftDes: '(喂，你还在吗)',
          rightImg: model?.isMyComplete != null && model?.isMyComplete == 0
              ? Assets.imagesTodoTogetherDetailFinished
              : null,
          rightDes: model?.isMyComplete != null && model?.isMyComplete == 0
              ? "我感觉做的差不多了~开始下一步"
              : null,
          leftOnTap: () {
            controller.poke();
          },
          rightOnTap: () {
            controller.complete();
          },
        ),
        Visibility(
            visible: model?.isOtherChuo == true &&
                model?.isOtherChuoResponse == false,
            child: const TodoTogetherDetailTimeAnswer()),
        BottomTips(
            verticalPadding: 10.h,
            content: '对方回应了你：嗯，我在！~',
            visible: controller.detail?.stage == 4 &&
                model?.isChuoStateResponse == true),
        BottomTips(
            verticalPadding: 10.h,
            content: '你戳了一下对方，等等看对方有没有回应吧？~',
            visible:
                controller.detail?.stage == 4 && model?.isChuoState == true),
        BottomTips(
            content: '对方已经完成了这件事,如果你也完成了,记得点击我已完成按钮哦',
            visible: controller.detail?.stage == 4 &&
                model?.isMyComplete == 0 &&
                model?.isOtherComplete == 1),
        BottomTips(
            content: '对方还没有完成这件事哦,咱们等等TA吧~',
            visible: controller.detail?.stage == 4 && model?.isMyComplete == 1),
      ],
    );
  }

  Widget _buildTimeItem(int title) {
    return Container(
      width: 37.w,
      height: 40.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.4),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        '$title',
        style: TextStyles.bold(20.sp, c: Colors.white),
      ),
    );
  }
}

class TodoTogetherDetailTimeAnswer extends StatelessWidget {
  const TodoTogetherDetailTimeAnswer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        SizedBox(height: 17.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w),
          child: Text(
            '对方戳了你一下，确认下～你还在吗？要记得做这件事哦～',
            textAlign: TextAlign.center,
            style: TextStyles.common(11.sp, Colors.white, h: 16.sp / 11.sp),
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          margin: EdgeInsets.only(right: 60.w),
          child: GestureDetector(
            onTap: () {
              Get.find<TodoTogetherDetailController>().respond();
            },
            child: Image.asset(Assets.imagesTodoTogetherDetailAnswer,
                width: 82.w, fit: BoxFit.fitWidth),
          ),
        ),
      ],
    );
  }
}
