import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/todo_together/detail/bottom_buttons.dart';
import 'package:dada/pages/todo_together/detail/bottom_tips.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_propose.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherDetailDes extends StatefulWidget {
  final Map model;
  const TodoTogetherDetailDes({super.key, required this.model});

  @override
  State<TodoTogetherDetailDes> createState() => _TodoTogetherDetailDesState();
}

class _TodoTogetherDetailDesState extends State<TodoTogetherDetailDes> {
  late Map model;
  late TodoTogetherDetailDesType type;
  final TodoTogetherDetailController controller =
      Get.find<TodoTogetherDetailController>();

  @override
  void initState() {
    super.initState();
    model = widget.model;
    if (controller.detail?.stage8?.myPropose != null) {
      type = TodoTogetherDetailDesType.propose;
    } else {
      if (model['mySelect'] == 1 || model['otherSelect'] == 1) {
        type = TodoTogetherDetailDesType.exit;
        controller.end();
      } else if (model['mySelect'] == 2 && model['otherSelect'] == 2) {
        type = TodoTogetherDetailDesType.addFriend;
        if (controller.fromType == TodoTogetherListType.all) {
          ApiService()
              .sendAddFriendRequest(
                  userFriendId: controller.detail!.otherUserId!)
              .then((v) {
            ChatIMManager.sharedInstance.sendTextMessage(
                text:
                    "风起回首云尚在，当时共我赏花人。咱们在一起做件事中，经历了一起${controller.detail?.content}哦~江湖山河，幸甚有你。",
                toUserID: controller.detail?.otherUserId);
          });
        }
        controller.end();
      } else if (model['mySelect'] == 3 && model['otherSelect'] == 3) {
        type = TodoTogetherDetailDesType.propose;
      } else if (model['mySelect'] == 3 && model['otherSelect'] == 2) {
        type = TodoTogetherDetailDesType.propose;
      } else {
        type = TodoTogetherDetailDesType.againConfirm;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (type == TodoTogetherDetailDesType.exit) _buildExit(),
        if (type == TodoTogetherDetailDesType.addFriend) _buildAddFriend(),
        if (type == TodoTogetherDetailDesType.againConfirm)
          _buildAgain(context),
        if (type == TodoTogetherDetailDesType.propose)
          TodoTogetherDetailPropose()
      ],
    );
  }

  Widget _buildExit() {
    return BottomTips(
      verticalPadding: 100.h,
      visible: true,
      content:
          '这是一次令人难忘的旅程~不过天下无不散的筵席,因为有一方终结了事件,所以你们的故事到此完结了哦。\n与君千里终须一别,你们的故事完整落幕,就让这段经历随风而去吧~其中的美好会继续伴随你左右~',
    );
  }

  Widget _buildAddFriend() {
    return BottomTips(
        content: '心有灵犀!你们都对对方表达了认可,恭喜你们找到了志同道合的小伙伴!你们双方成为好友了哦!可以在聊天界面查看!',
        visible: true,
        verticalPadding: 100.h);
  }

  Widget _buildAgain(BuildContext c) {
    return Column(
      children: [
        BottomTips(
            content: '载乐载言,其兴至也。过程很让人开心,但对方觉得此时加好友还为时尚早,所以还想再继续来一次一起做件事哦,你同意吗?',
            visible: true,
            verticalPadding: 50.h),
        BottomButtons(
          visible: model['myCheck'] == null,
          leftImg: Assets.imagesTodoTogetherDetailNoStop,
          rightImg: Assets.imagesTodoTogetherDetailAgreeContinue,
          leftOnTap: () {
            controller.showStopDialog(c, content: '确定结束一起做吗?',
                onRightBtnTap: () {
              controller.stop();
            });
          },
          rightOnTap: () {
            setState(() {
              type = TodoTogetherDetailDesType.propose;
            });
          },
        ),
      ],
    );
  }
}
