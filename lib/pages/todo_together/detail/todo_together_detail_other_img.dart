import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/pages/todo_together/detail/bottom_buttons.dart';
import 'package:dada/pages/todo_together/detail/bottom_tips.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherDetailOtherImg extends StatelessWidget {
  const TodoTogetherDetailOtherImg({super.key});

  @override
  Widget build(BuildContext context) {
    final TodoTogetherDetailController controller =
        Get.find<TodoTogetherDetailController>();
    final TodoTogetherDetailStage3? model = controller.detail?.stage3;
    return Column(
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 34.w),
          child: Text(
            '这是对方的准备开始的图哦，是否继续~',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 64.h),
        model?.otherImg != null && model!.otherImg!.isNotEmpty
            ? Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                            color: const Color(0xFFFBFBFB), width: 1)),
                    child: GestureDetector(
                      onTap: () {
                        ImageUtils.showImageBrowser(ImageBrowserArgs(
                          [
                            HeroTagName.todoTogetherOtherImg.of(
                                model.otherImg ??
                                    'todo_together_detail_otherImg'),
                          ],
                          [model.otherImg ?? ''],
                        ));
                      },
                      child: Hero(
                        tag: HeroTagName.todoTogetherOtherImg.of(
                            model.otherImg ?? 'todo_together_detail_otherImg'),
                        child: ImageUtils.getImage(
                          model.otherImg ?? "",
                          200,
                          200,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 14.w),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      Get.to(() => ReportPage(
                          reportType: ReportType.chatMsg,
                          userId: controller.detail?.otherUserId));
                    },
                    child: Container(
                        padding: EdgeInsets.only(bottom: 3.h),
                        decoration: const BoxDecoration(
                          border: Border(
                              bottom:
                                  BorderSide(color: Colors.white, width: 1)),
                        ),
                        child: Text('举报',
                            style: TextStyles.common(14.sp, Colors.white))),
                  ),
                ],
              )
            : Container(
                width: 200,
                height: 200,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border:
                        Border.all(color: const Color(0xFFFBFBFB), width: 1)),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesTodoTogetherDetailNoPicture, 111, 96),
                    SizedBox(height: 17.h),
                    Text(
                      '我准备好做这件事啦~',
                      style: TextStyles.common(14.sp, Colors.white),
                    )
                  ],
                )),
        BottomTips(
            content: "已确认, 请等待对方选择~",
            visible: controller.detail?.stage == 3 && model?.Continue == true),
        BottomButtons(
          visible: controller.detail?.stage == 3 &&
              model?.Continue == false &&
              controller.fromType == TodoTogetherListType.all,
          leftImg: Assets.imagesTodoTogetherDetailSuanle,
          rightImg: Assets.imagesTodoTogetherDetailContinue,
          leftOnTap: () {
            controller.showStopDialog(context, onRightBtnTap: () {
              controller.stop();
            });
          },
          rightOnTap: () {
            controller.continueOtherImg();
          },
        ),
        if (controller.detail?.stage != 3) SizedBox(height: 77.h),
      ],
    );
  }
}
