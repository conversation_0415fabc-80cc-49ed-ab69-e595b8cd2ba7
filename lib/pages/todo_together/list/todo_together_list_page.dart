import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/pages/todo_together/list/todo_together_cell.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherListPage extends StatefulWidget {
  final TodoTogetherListType type;
  const TodoTogetherListPage({super.key, required this.type});

  @override
  State<TodoTogetherListPage> createState() => _TodoTogetherListPageState();
}

class _TodoTogetherListPageState extends State<TodoTogetherListPage> {
  late TodoTogetherListController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(
      TodoTogetherListController(widget.type),
      tag: 'TodoTogetherListController_${widget.type.toString()}',
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TodoTogetherListController>(
      tag: 'TodoTogetherListController_${widget.type.toString()}',
      builder: (controller) {
        return RefreshWidget.build(
            refreshController: controller.refreshController,
            onRefresh: () => controller.refreshData(),
            onLoadMore: () => controller.loadMoreData(),
            child: controller.data.isEmpty
                ? ListPageEmptyWidget(naviBarHeight: 150.h)
                : ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: controller.data.length,
                    itemBuilder: (c, i) {
                      final model = controller.data[i];
                      return TodoTogetherCell(
                          model: model, type: controller.type);
                    },
                  ));
      },
      id: controller.refreshId,
    );
  }

  @override
  void dispose() {
    Get.delete<TodoTogetherListController>(
      tag: 'TodoTogetherListController_${widget.type.toString()}',
    );
    super.dispose();
  }
}
