import 'package:dada/common/values/enums.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_page.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherHistoryPage extends StatefulWidget {
  const TodoTogetherHistoryPage({super.key});

  @override
  State<TodoTogetherHistoryPage> createState() =>
      _TodoTogetherHistoryPageState();
}

class _TodoTogetherHistoryPageState extends State<TodoTogetherHistoryPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage(Assets.imagesTodoTogetherBac),
                fit: BoxFit.cover)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: ScreenUtil().statusBarHeight),
            IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: Image(
                    image: const AssetImage(Assets.imagesNaviBackWhite),
                    width: 12.w,
                    height: 20.h)),
            Center(
              child: ImageUtils.getImage(
                  Assets.imagesTodoTogetherBanner, 328.w, 80.h,
                  fit: BoxFit.fitWidth),
            ),
            SizedBox(height: 18.h - 7.w),
            Expanded(
                child: TodoTogetherListPage(type: TodoTogetherListType.history))
          ],
        ),
      ),
    );
  }
}
