import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/model/contact_group_list_item_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

import '../../common/values/text_styles.dart';

class ShareSelectFriendPage extends StatefulWidget {
  const ShareSelectFriendPage({super.key});

  @override
  State<ShareSelectFriendPage> createState() => _ShareSelectFriendPageState();
}

class _ShareSelectFriendPageState extends State<ShareSelectFriendPage>
    with TickerProviderStateMixin {
  late TabController tabController;
  late PageController pageController;
  List<String> tabItemTitles = ["最近", "搭友"];

  List<V2TimConversation?>? recentConversationList;
  List<FriendUserInfoEntity>? daziFriendList;
  bool isLoadingDaziList = false;
  String? btnTitle;
  String? pageTitle;

  @override
  void initState() {
    super.initState();

    btnTitle = Get.parameters["btnTitle"];
    pageTitle = Get.parameters["btnTitle"];

    tabController = TabController(length: 2, vsync: this, initialIndex: 1);
    pageController = PageController(initialPage: 1);

    recentConversationList = Get.arguments;

    if (Get.isRegistered<ContactsController>()) {
      ContactsController contactsController = ContactsController();
      if (contactsController.contactsGroupList?.isNotEmpty == true) {
        ContactGroupListItemEntity daZiGroupEntity = contactsController
            .contactsGroupList!
            .where((value) => value.groupType == ContactsGroupType.dazi.index)
            .toList()
            .first;
        daziFriendList = daZiGroupEntity.daziList;
      }
    } else {
      loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: pageTitle?.isNotEmpty == true ? pageTitle : "分享至",
      ),
      body: Column(
        children: [
          _buildTabBar(),
          _buildPageView(),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 51.h,
      alignment: Alignment.centerLeft,
      child: TabBar(
        padding: EdgeInsets.only(left: 15.w, top: 0.h),
        tabAlignment: TabAlignment.start,
        controller: tabController,
        tabs: tabItemTitles.map((e) => Tab(text: e)).toList(),
        dividerColor: Colors.transparent,
        isScrollable: true,
        labelPadding: EdgeInsets.only(right: 15.w),
        labelStyle: TextStyle(fontSize: 16.sp),
        labelColor: AppColors.colorFF333333,
        unselectedLabelColor: AppColors.colorFF666666,
        unselectedLabelStyle: TextStyle(fontSize: 16.sp),
        indicatorWeight: 4.h,
        indicatorPadding:
            EdgeInsets.only(bottom: 15.h, top: 24.h, left: 5.w, right: 5.w),
        indicator: BoxDecoration(
          color: Theme.of(context)
              .bottomNavigationBarTheme
              .selectedLabelStyle
              ?.color,
          borderRadius: BorderRadius.circular(2.r),
        ),
        onTap: (index) {
          pageController.animateToPage(index,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInCubic);
        },
      ),
    );
  }

  Widget _buildPageView() {
    return Expanded(
      child: PageView(
        controller: pageController,
        onPageChanged: (index) {},
        children: tabItemTitles.map((e) {
          if (e == "最近") {
            return _buildRecentConversationList();
          }
          return _buildDaziList();
        }).toList(),
      ),
    );
  }

  Widget _buildRecentConversationList() {
    if (!(recentConversationList?.isNotEmpty == true)) {
      if (isLoadingDaziList) {
        return const LoadingWidget();
      }
      return EmptyWidget();
    }
    return ListView.separated(
      itemBuilder: (context, index) {
        V2TimConversation? conversation = recentConversationList?[index];
        if (conversation != null && conversation.userID != null) {
          return _buildCommonListItem(conversation.userID!,
              conversation.faceUrl ?? "", conversation.showName ?? "");
        }
        return Container();
      },
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 14.h,
        );
      },
      itemCount: recentConversationList!.length,
    );
  }

  Widget _buildDaziList() {
    if (!(daziFriendList?.isNotEmpty == true)) {
      return EmptyWidget();
    }
    return ListView.separated(
      itemBuilder: (context, index) {
        FriendUserInfoEntity userInfoEntity = daziFriendList![index];
        if (userInfoEntity.userFriendId != null) {
          return _buildCommonListItem(
              userInfoEntity.userFriendId!,
              userInfoEntity.avatar ?? "",
              userInfoEntity.daName ?? userInfoEntity.nickname ?? "");
        }
        return Container();
      },
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 14.h,
        );
      },
      itemCount: daziFriendList!.length,
    );
  }

  Widget _buildCommonListItem(String userId, String avatar, String nickname) {
    return Row(
      children: [
        Row(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 18.w),
              child: ClipOval(
                child:
                    ImageUtils.getImage(avatar, 40.w, 40.w, fit: BoxFit.cover),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 18.w),
              child: Text(
                nickname,
                style: TextStyles.normal(16.sp),
              ),
            ),
          ],
        ),
        const Spacer(),
        GestureDetector(
          onTap: () {
            Get.back(result: userId);
          },
          child: Padding(
            padding: EdgeInsets.only(right: 13.w),
            child: Container(
              width: 60.w,
              height: 25.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.5.r),
                gradient: const LinearGradient(
                  colors: [AppColors.colorFFA0F6A5, AppColors.colorFF58C75D],
                ),
              ),
              child: Text(
                btnTitle?.isNotEmpty == true ? btnTitle! : "分享",
                style: TextStyles.normal(12.sp),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void loadData() async {
    isLoadingDaziList = true;
    ContactsController contactsController = ContactsController();
    List<ContactGroupListItemEntity>? groupList =
        await contactsController.loadData();
    if (groupList != null) {
      ContactGroupListItemEntity daZiGroupEntity = contactsController
          .contactsGroupList!
          .where((value) => value.groupType == ContactsGroupType.dazi.index)
          .toList()
          .first;
      setState(() {
        isLoadingDaziList = false;
        daziFriendList = daZiGroupEntity.daziList;
      });
    }
  }
}
