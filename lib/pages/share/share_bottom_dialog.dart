import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat/conversation/chat_conversation_list_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

class ShareBottomDialog extends StatefulWidget {
  final Function(String userId) callback;
  final String? dialogTitle;
  final String? btnTitle;
  final String? pageTitle;

  const ShareBottomDialog(
      {super.key,
      required this.callback,
      this.dialogTitle,
      this.pageTitle,
      this.btnTitle});

  @override
  State<ShareBottomDialog> createState() => _ShareBottomDialogState();
}

class _ShareBottomDialogState extends State<ShareBottomDialog> {
  List<V2TimConversation?>? recentConversationList;

  @override
  void initState() {
    super.initState();

    if (Get.isRegistered<ChatConversationListController>()) {
      if (Get.find<ChatConversationListController>()
              .conversationList
              ?.isNotEmpty ==
          true) {
        recentConversationList =
            Get.find<ChatConversationListController>().conversationList;
      }
    } else {
      loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
      ),
      child: GradientWidget(
        width: ScreenUtil().screenWidth,
        height: 281.h,
        stops: const [0, 0.25],
        colors: const [
          AppColors.colorFFD2F6C0,
          Colors.white,
        ],
        child: Stack(
          children: [
            Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 16.h),
                  child: Text(
                    widget.dialogTitle ?? "分享至好友",
                    style: TextStyles.normal(16.sp),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 23.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: 15.w,
                        ),
                        child: Text(
                          "最近聊天",
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 10.h),
                        width: ScreenUtil().screenWidth,
                        height: 75.h,
                        child: recentConversationList?.isNotEmpty == true
                            ? ListView.separated(
                                padding: EdgeInsets.symmetric(horizontal: 15.w),
                                scrollDirection: Axis.horizontal,
                                itemBuilder: (context, index) {
                                  V2TimConversation? conversation =
                                      recentConversationList?[index];
                                  return _buildListItemWidget(conversation);
                                },
                                itemCount: recentConversationList!.length,
                                separatorBuilder: (context, index) {
                                  return SizedBox(
                                    width: 15.w,
                                  );
                                },
                              )
                            : Container(
                                height: 70.h,
                                alignment: Alignment.center,
                                child: Text(
                                  "暂无最近聊天~",
                                  style: TextStyles.common(
                                      16.sp, AppColors.colorFF666666),
                                ),
                              ),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.only(top: 15.h, left: 15.w, right: 15.w),
                        child: Container(
                          color: AppColors.colorFFF5F6F7,
                          height: 1.h,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Get.toNamed(GetRouter.shareSelectUser,
                        arguments: recentConversationList,
                        parameters: {
                          "btnTitle": widget.btnTitle ?? "",
                          "pageTitle": widget.pageTitle ?? ""
                        })?.then(
                      (userId) {
                        if (userId != null) {
                          Get.back();
                          widget.callback(userId);
                        }
                      },
                    );
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 14.h),
                        child: Column(
                          children: [
                            ImageUtils.getImage(
                                Assets.imagesShareBottomDialogDaziFriend,
                                50.w,
                                50.w),
                            Text(
                              "搭友",
                              style: TextStyles.normal(13.sp),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              top: 15.h,
              right: 15.w,
              child: GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Icon(
                  Icons.close,
                  color: AppColors.colorFF666666,
                  size: 20.w,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListItemWidget(V2TimConversation? conversation) {
    if (conversation == null) {
      return Container();
    }
    return GestureDetector(
      onTap: () {
        Get.back();
        if (conversation.userID != null) {
          widget.callback(conversation.userID!);
        }
      },
      behavior: HitTestBehavior.opaque,
      child: Column(
        children: [
          ClipOval(
            child: ImageUtils.getImage(conversation.faceUrl ?? "", 50.w, 50.w,
                fit: BoxFit.cover),
          ),
          Padding(
            padding: EdgeInsets.only(top: 5.h),
            child: Container(
              constraints: BoxConstraints(maxWidth: 50.w),
              child: Text(
                conversation.showName ?? "",
                overflow: TextOverflow.ellipsis,
                style: TextStyles.normal(12.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void loadData() async {
    ChatConversationListController conversationListController =
        ChatConversationListController();
    List<V2TimConversation?>? list =
        await conversationListController.loadData(nexSeq: 0);
    setState(() {
      recentConversationList = list?.where((e) {
        if (e != null && e.type == 1 && e.userID != "1") {
          return true;
        }
        return false;
      }).toList();
    });
  }
}
