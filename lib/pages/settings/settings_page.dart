import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/common_setting_list_item_widget.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/utils/login_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "设置",
      ),
      body: Column(
        children: [
          CommonSettingListItemWidget(
            title: "关于搭吖",
            subTitle: "",
            onTap: () {
              Get.toNamed(GetRouter.about);
            },
          ),
          const Spacer(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      title: "退出登录",
      topMargin: 10.h,
      bottomMargin: ScreenUtil().bottomBarHeight + 20.h,
      onTap: () {
        ToastUtils.showDialog(
          content: "确定要退出吗",
          confirmBtnTitle: "确定",
          onConfirm: () {
            if (GlobalFloatingManager().isShowMiniWindow.value == true) {
              GlobalFloatingManager().closeMiniWindow();
            }
            LoginUtils.logOut();
          },
        );
      },
    );
  }
}
