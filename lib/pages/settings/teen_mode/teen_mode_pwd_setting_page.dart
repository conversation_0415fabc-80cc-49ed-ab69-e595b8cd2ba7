import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/verification_box/verification_box.dart';
import 'package:dada/components/widgets/verification_box/verification_box_item.dart';
import 'package:dada/pages/settings/teen_mode/teen_mode_pwd_ensure_pwd_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TeenModePwdSettingPage extends StatefulWidget {
  const TeenModePwdSettingPage({super.key});

  @override
  State<TeenModePwdSettingPage> createState() => _TeenModePwdSettingPageState();
}

class _TeenModePwdSettingPageState extends State<TeenModePwdSettingPage> {
  int inputNumber = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "",
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Text(
              "设置独立密码开启模式",
              style: TextStyles.normal(16.sp),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 85.h, left: 85.w, right: 85.w),
            height: 45.h,
            child: VerificationBox(
              count: 4,
              itemWidget: 40.w,
              borderWidth: 1.h,
              type: VerificationBoxItemType.underline,
              textStyle: TextStyles.normal(16.sp),
              focusBorderColor: AppColors.colorFF23AF28,
              onSubmitted: (value) {
                inputNumber = int.parse(value);
                Get.to(() => TeenModePwdEnsurePwdPage(pwd: inputNumber));
              },
            ),
          ),
        ],
      ),
    );
  }
}
