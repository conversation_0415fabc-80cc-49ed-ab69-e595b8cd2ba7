import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/verification_box/verification_box.dart';
import 'package:dada/components/widgets/verification_box/verification_box_item.dart';
import 'package:dada/model/teen_mode_status_entity.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class TeenModePwdEnsurePwdPage extends StatefulWidget {
  final int pwd;

  const TeenModePwdEnsurePwdPage({super.key, required this.pwd});

  @override
  State<TeenModePwdEnsurePwdPage> createState() =>
      _TeenModePwdEnsurePwdPageState();
}

class _TeenModePwdEnsurePwdPageState extends State<TeenModePwdEnsurePwdPage> {
  int inputNumber = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "",
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Text(
              "确认密码",
              style: TextStyles.normal(16.sp),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 85.h, left: 85.w, right: 85.w),
            height: 45.h,
            child: VerificationBox(
              count: 4,
              itemWidget: 40.w,
              borderWidth: 1.h,
              type: VerificationBoxItemType.underline,
              textStyle: TextStyles.normal(16.sp),
              focusBorderColor: AppColors.colorFF23AF28,
              onSubmitted: (value) {
                inputNumber = int.parse(value);
                if (inputNumber == widget.pwd) {
                  openTeenMode();
                } else {
                  ToastUtils.showToast("密码输入不一致，请重新输入");
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  void openTeenMode() async {
    bool success = await ApiService()
        .changeTeenModeStatus(isOpen: "1", password: inputNumber.toString());
    if (success) {
      ToastUtils.showToast("青少年模式开启成功");
      Get.find<MainController>().teenModeStatusEntity = TeenModeStatusEntity()
        ..toggle = "1"
        ..password = inputNumber.toString();
      Get.offNamedUntil(
        GetRouter.teenModeSetting,
        ModalRoute.withName(GetRouter.main),
        parameters: {"isOpen": "1", "pwd": inputNumber.toString()},
      );
    }
  }
}
