import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/verification_box/verification_box.dart';
import 'package:dada/components/widgets/verification_box/verification_box_item.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/pages/settings/teen_mode/teen_mode_forget_pwd_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TeenModeClosePwdInputPage extends StatefulWidget {
  final int pwd;
  final String? comeFrom;

  const TeenModeClosePwdInputPage(
      {super.key, required this.pwd, this.comeFrom});

  @override
  State<TeenModeClosePwdInputPage> createState() =>
      _TeenModeClosePwdInputPageState();
}

class _TeenModeClosePwdInputPageState extends State<TeenModeClosePwdInputPage> {
  int inputNumber = 0;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "",
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Text(
              "请输入密码确认关闭模式",
              style: TextStyles.normal(16.sp),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 85.h, left: 85.w, right: 85.w),
            height: 45.h,
            child: VerificationBox(
              count: 4,
              itemWidget: 40.w,
              borderWidth: 1.h,
              editingController: _controller,
              type: VerificationBoxItemType.underline,
              textStyle: TextStyles.normal(16.sp),
              focusBorderColor: AppColors.colorFF23AF28,
              onSubmitted: (value) {
                inputNumber = int.parse(value);
                if (inputNumber == widget.pwd) {
                  closeTeenMode();
                } else {
                  ToastUtils.showToast("密码错误，请重新输入");
                  _controller.clear();
                }
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 36.h),
            child: GestureDetector(
              onTap: () {
                Get.to(() => const TeenModeForgetPwdPage());
              },
              child: Text(
                "忘记密码?",
                style: TextStyle(
                  color: AppColors.colorFF23AF28,
                  fontSize: 16.sp,
                  decoration: TextDecoration.underline,
                  decorationColor: AppColors.colorFF23AF28,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void closeTeenMode() async {
    bool success = await ApiService().changeTeenModeStatus(isOpen: "0");
    if (success) {
      Get.find<MainController>().teenModeStatusEntity = null;
      if (widget.comeFrom == "main") {
        Get.until((route) => !(Get.isDialogOpen!));
      } else {
        Get.offNamedUntil(
          GetRouter.teenModeSetting,
          ModalRoute.withName(GetRouter.main),
          parameters: {"isOpen": "0"},
        );
      }
    }
  }
}
