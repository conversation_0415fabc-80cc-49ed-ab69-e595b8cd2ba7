import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TeenModePage extends StatefulWidget {
  const TeenModePage({super.key});

  @override
  State<TeenModePage> createState() => _TeenModePageState();
}

class _TeenModePageState extends State<TeenModePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
      ),
      body: Stack(
        children: [
          ImageUtils.getImage(Assets.imagesTeenModePageBg,
              ScreenUtil().screenWidth, ScreenUtil().screenHeight),
          Positioned(
            top: 282.h + ScreenUtil().statusBarHeight,
            child: Column(
              children: [
                Container(
                  width: 345.w,
                  height: 270.h,
                  margin: EdgeInsets.only(left: 15.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Column(
                    children: [
                      Container(
                        height: 60.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: AppColors.colorFFEDF8FA,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(10.r),
                          ),
                        ),
                        child: Text(
                          "青少年模式",
                          style: TextStyles.normal(18.sp),
                        ),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.only(top: 25.h, left: 25.w, right: 25.w),
                        child: Text(
                          "为呵护未成年人健康成长，搭吖特别推出青少年模式，该模式下部分功能无法正常使用。请监护人主动选择，并设置监护密码。",
                          style: TextStyles.common(
                            16.sp,
                            AppColors.colorFF666666,
                            h: 1.5,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Padding(
                        padding: EdgeInsets.only(bottom: 30.h),
                        child: GestureDetector(
                          onTap: () {
                            Get.toNamed(GetRouter.teenModeSetting);
                          },
                          child: Text(
                            "设置青少年模式",
                            style: TextStyles.common(
                              16.sp,
                              AppColors.colorFF23AF28,
                              w: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 70.h),
                  child: CommonGradientBtn(
                    height: 40.h,
                    width: 150.w,
                    title: "我知道了",
                    normalImage: Assets.imagesCommonGradientBtnBg,
                    onTap: () {
                      Get.back();
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
