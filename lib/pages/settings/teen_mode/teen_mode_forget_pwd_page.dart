import 'dart:ui' as ui show PlaceholderAlignment;
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TeenModeForgetPwdPage extends StatefulWidget {
  const TeenModeForgetPwdPage({super.key});

  @override
  State<TeenModeForgetPwdPage> createState() => _TeenModeForgetPwdPageState();
}

class _TeenModeForgetPwdPageState extends State<TeenModeForgetPwdPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "重置密码",
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 6.h),
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: "若您需要重置时间锁或青少年模式的密码，请您发送邮件至",
                      style: TextStyles.normal(16.sp),
                    ),
                    TextSpan(
                      text: "<EMAIL> ",
                      style: TextStyles.bold(
                        16.sp,
                      ),
                    ),
                    WidgetSpan(
                      alignment: ui.PlaceholderAlignment.middle,
                      child: GestureDetector(
                        onTap: () {
                          Clipboard.setData(
                              const ClipboardData(text: "<EMAIL>"));
                          ToastUtils.showToast("已复制到剪贴板");
                        },
                        child: Padding(
                          padding: EdgeInsets.only(right: 2.w),
                          child: ImageUtils.getImage(
                              Assets.imagesCopyTextBtnGreen, 16.w, 16.w),
                        ),
                      ),
                    ),
                    TextSpan(
                      text: "点击复制",
                      style: TextStyles.common(16.sp, AppColors.colorFF23AF28,
                          h: 1.2),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Clipboard.setData(
                              const ClipboardData(text: "<EMAIL>"));
                          ToastUtils.showToast("已复制到剪贴板");
                        },
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 25.h),
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: "主题命名为",
                      style: TextStyles.normal(16.sp),
                    ),
                    TextSpan(
                      text: "【注册手机号+青少模式密码重置】",
                      style: TextStyles.bold(
                        16.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 25.h),
              child: Text(
                "邮件中请您上传本人手持身份证和写有“仅用于 青少年模式密码重置”纸张的照片，要求本人、 身份证和纸张在同一照片中，且身份证、自己清 晰可辨。",
                style: TextStyles.normal(16.sp),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 25.h),
              child: Text(
                "您的资料仅用于密码重置申诉，搭吖不会泄露您 的个人信息，并会尽快为您处理。",
                style: TextStyles.normal(16.sp),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
