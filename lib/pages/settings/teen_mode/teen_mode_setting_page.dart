import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/settings/teen_mode/teen_mode_close_pwd_input_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TeenModeSettingPage extends StatefulWidget {
  final String? openState;
  final String? pwd;
  final String? comFrom;
  final bool? hideBack;

  const TeenModeSettingPage(
      {super.key, this.openState, this.pwd, this.comFrom, this.hideBack});

  @override
  State<TeenModeSettingPage> createState() => _TeenModeSettingPageState();
}

class _TeenModeSettingPageState extends State<TeenModeSettingPage> {
  bool isOpen = false;
  String? password;

  @override
  void initState() {
    super.initState();

    String? isOpenState = Get.parameters["isOpen"];
    isOpen = isOpenState == "1";
    if (widget.openState != null) {
      isOpen = widget.openState == "1";
    }
    String? pwd = Get.parameters["pwd"];
    password = pwd;
    if (widget.pwd != null) {
      password = widget.pwd;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !(widget.openState != null &&
          isOpen == true &&
          widget.hideBack == true),
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: CustomAppBar(
          backgroundColor: Colors.transparent,
          backAction: () {
            if (widget.openState != null &&
                isOpen == true &&
                widget.hideBack == true) {
              return;
            }
            Get.back();
          },
          leftWidget: widget.hideBack == true ? Container() : null,
        ),
        body: Stack(
          children: [
            ImageUtils.getImage(Assets.imagesTeenModePageBg,
                ScreenUtil().screenWidth, ScreenUtil().screenHeight),
            Positioned(
              top: 282.h + ScreenUtil().statusBarHeight,
              child: Column(
                children: [
                  Container(
                    width: 345.w,
                    height: 270.h,
                    margin: EdgeInsets.only(left: 15.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Column(
                      children: [
                        Container(
                          height: 60.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.colorFFEDF8FA,
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(10.r),
                            ),
                          ),
                          child: Text(
                            "青少年模式",
                            style: TextStyles.normal(18.sp),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              top: 23.h, left: 25.w, right: 25.w),
                          child: Column(
                            children: [
                              _buildCommonLine("每日累计使用时间不超过40分钟"),
                              _buildCommonLine("每日晚10:00至次日早6:00限制使用"),
                              _buildCommonLine("凑热闹、搭圈等不分功能不对青少年 开放，并关闭充值功能"),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 70.h),
                    child: CommonGradientBtn(
                      height: 40.h,
                      width: 150.w,
                      title: isOpen ? "关闭青少年模式" : "开启青少年模式",
                      normalImage: Assets.imagesCommonGradientBtnBg,
                      onTap: () {
                        if (isOpen) {
                          if (password?.isNotEmpty == true) {
                            Get.to(
                              () => TeenModeClosePwdInputPage(
                                pwd: int.parse(password!),
                                comeFrom: widget.comFrom,
                              ),
                            );
                          }
                        } else {
                          Get.toNamed(GetRouter.teenModePwdSetting);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommonLine(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 18.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 7.h, right: 8.w),
            child: Container(
              width: 6.w,
              height: 6.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3.r),
                color: AppColors.colorFF23AF28,
              ),
            ),
          ),
          SizedBox(
            width: 262.w,
            child: Text(
              title,
              maxLines: 2,
              style: TextStyles.common(16.sp, AppColors.colorFF666666, h: 1.3),
            ),
          ),
        ],
      ),
    );
  }
}
