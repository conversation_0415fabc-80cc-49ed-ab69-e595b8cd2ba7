import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AboutPage extends StatefulWidget {
  const AboutPage({super.key});

  @override
  State<AboutPage> createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {
  final RxString version = "".obs;
  final RxString buildNumber = "".obs;

  @override
  void initState() {
    super.initState();

    _getVersion();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "关于",
      ),
      body: Container(
        alignment: Alignment.center,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 30.h),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: ImageUtils.getImage(Assets.imagesAppIcon, 85.w, 85.w),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 10.h),
              child: Text(
                "搭吖",
                style: TextStyles.normal(16.sp),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 15.h),
              child: Obx(
                () => Text(
                  "当前版本：${version.value} (${buildNumber.value})",
                  style: TextStyles.normal(16.sp),
                ),
              ),
            ),
            // Padding(
            //   padding: EdgeInsets.only(top: 20.h),
            //   child: CommonSettingListItemWidget(
            //     title: "官方网址",
            //     subTitle: "",
            //     onTap: () {
            //       Get.toNamed(GetRouter.webView,
            //           parameters: {"url": AppConfig.officialUrl});
            //     },
            //   ),
            // ),
            const Spacer(),
            Padding(
              padding: EdgeInsets.only(bottom: 40.h),
              child: Column(
                children: [
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: "《隐私政策》",
                          style: TextStyle(
                            color: AppColors.colorFF6265FF,
                            fontSize: 14.sp,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              Get.toNamed(GetRouter.webView,
                                  parameters: {"url": AppConfig.privacyUrl});
                            },
                        ),
                        TextSpan(
                          text: "《用户协议》",
                          style: TextStyle(
                            color: AppColors.colorFF6265FF,
                            fontSize: 14.sp,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              Get.toNamed(GetRouter.webView, parameters: {
                                "url": AppConfig.userAgreementUrl
                              });
                            },
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 5.h),
                    child: Text(
                      "客服联系方式：<EMAIL>",
                      style: TextStyles.common(14.sp, AppColors.colorFF999999),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _getVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    version.value = packageInfo.version;
    buildNumber.value = packageInfo.buildNumber;
  }
}
