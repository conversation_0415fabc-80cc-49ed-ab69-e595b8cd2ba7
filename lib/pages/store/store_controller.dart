import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/store_goods_item_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

import 'package:dada/model/account_balance_entity.dart';

class StoreController
    extends ListPageController<StoreGoodsItemEntity, StoreController> {
  int shopType = 1;
  int? listType;
  Rx<AccountBalanceEntity?> accountBalance = Rx(null);

  RxBool exchangeStoreItemUnfold = false.obs;
  RxBool storeItemUnfold = false.obs;
  RxString selectedIndex = "".obs;

  @override
  Future<List<StoreGoodsItemEntity>?> loadData(int page) async {
    List<StoreGoodsItemEntity>? list = await ApiService()
        .getStoreGoodsList(shopType: shopType, propType: listType);
    return list;
  }

  Future<void> loadUserStainsCount() async {
    AccountBalanceEntity? entity = await ApiService().getUserAccountBalance();
    accountBalance.value = entity;
  }
}
