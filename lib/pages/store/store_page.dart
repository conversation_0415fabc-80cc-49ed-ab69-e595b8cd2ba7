import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/store_goods_item_entity.dart';
import 'package:dada/pages/store/store_controller.dart';
import 'package:dada/pages/store/store_goods_detail_dialog.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class StorePage extends StatefulWidget {
  const StorePage({super.key});

  @override
  State<StorePage> createState() => _StorePageState();
}

class _StorePageState extends State<StorePage> {
  final StoreController controller = StoreController();

  @override
  void initState() {
    super.initState();
    controller.loadUserStainsCount();
    controller.shopType = 1;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        title: "商城",
        backgroundColor: Colors.transparent,
      ),
      body: Container(
        padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 44.h),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [AppColors.colorFFADF0B0, AppColors.colorFFF5F5F5],
            stops: [0, 0.35],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: () {},
                  child: Container(
                    height: 36.h,
                    margin: EdgeInsets.only(right: 15.w),
                    child: Stack(
                      children: [
                        Obx(() {
                          return Container(
                            height: 25.h,
                            margin: EdgeInsets.only(top: 9.h, left: 11.w),
                            padding: EdgeInsets.only(left: 17.w, right: 15.w),
                            alignment: Alignment.center,
                            constraints: BoxConstraints(minWidth: 50.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(25.h / 2),
                                  bottomRight: Radius.circular(25.h / 2)),
                              color: Colors.white,
                            ),
                            child: Text(
                              "${controller.accountBalance.value?.daCoinNum ?? 0}",
                              style: TextStyles.normal(16.sp),
                            ),
                          );
                        }),
                        Padding(
                          padding: EdgeInsets.only(top: 6.h),
                          child: ImageUtils.getImage(
                            Assets.imagesRechargeDaCoin,
                            26.w,
                            28.h,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {},
                  child: Container(
                    height: 36.h,
                    margin: EdgeInsets.only(right: 15.w),
                    child: Stack(
                      children: [
                        Obx(() {
                          return Container(
                            height: 25.h,
                            margin: EdgeInsets.only(top: 9.h, left: 11.w),
                            padding: EdgeInsets.only(left: 17.w, right: 15.w),
                            alignment: Alignment.center,
                            constraints: BoxConstraints(minWidth: 50.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(25.h / 2),
                                  bottomRight: Radius.circular(25.h / 2)),
                              color: Colors.white,
                            ),
                            child: Text(
                              "${controller.accountBalance.value?.daStickNum ?? 0}",
                              style: TextStyles.normal(16.sp),
                            ),
                          );
                        }),
                        Padding(
                          padding: EdgeInsets.only(top: 5.h),
                          child: ImageUtils.getImage(
                            Assets.imagesRechargeDaBang,
                            23.w,
                            36.h,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {},
                  child: Container(
                    height: 36.h,
                    margin: EdgeInsets.only(right: 15.w),
                    child: Stack(
                      children: [
                        Obx(() {
                          return Container(
                            height: 25.h,
                            margin: EdgeInsets.only(top: 9.h, left: 11.w),
                            padding: EdgeInsets.only(left: 17.w, right: 15.w),
                            alignment: Alignment.center,
                            constraints: BoxConstraints(minWidth: 50.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(25.h / 2),
                                  bottomRight: Radius.circular(25.h / 2)),
                              color: Colors.white,
                            ),
                            child: Text(
                              "${controller.accountBalance.value?.skinNum ?? 0}",
                              style: TextStyles.normal(16.sp),
                            ),
                          );
                        }),
                        Padding(
                          padding: EdgeInsets.only(top: 5.h),
                          child: ImageUtils.getImage(
                            Assets.imagesRechargeSkin,
                            26.w,
                            30.h,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(top: 10.h, right: 10.w),
                child: _buildBottomContainer(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomContainer() {
    return Row(
      children: [
        _buildSelectCategoryListWidget(),
        Expanded(
          child: _buildStoreListWidget(),
        ),
      ],
    );
  }

  Widget _buildSubCategoryListWidget(int shopType) {
    List<int> listTypes = [0, 7, 11, 8];
    return ListView.builder(
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return Obx(
          () {
            int listType = listTypes[index];
            String title = "";
            if (listType == 0) {
              title = "全部";
            } else if (listType == 7) {
              title = "头像框";
            } else if (listType == 11) {
              title = "外观";
            } else if (listType == 8) {
              title = "聊天框";
            }
            return GestureDetector(
              onTap: () {
                if (controller.selectedIndex.value == "$shopType-$listType") {
                  return;
                }
                controller.selectedIndex.value = "$shopType-$listType";
                controller.shopType = shopType;
                controller.listType = listType;
                controller.refreshData();
              },
              child: Container(
                width: 80.w,
                height: 35.h,
                alignment: Alignment.center,
                color: controller.selectedIndex.value == "$shopType-$listType"
                    ? AppColors.colorFF89E15C
                    : AppColors.color00E4FFBA,
                child: Text(
                  title,
                  style: TextStyles.common(14.sp, AppColors.colorFF2D6D0B),
                ),
              ),
            );
          },
        );
      },
      itemCount: listTypes.length,
    );
  }

  Widget _buildSelectCategoryListWidget() {
    return Container(
      width: 85.w,
      color: AppColors.colorFFF2F3F6,
      child: Column(
        children: [
          GestureDetector(
            onTap: () async {
              controller.storeItemUnfold.value =
                  !controller.storeItemUnfold.value;
              if (controller.storeItemUnfold.value == true &&
                  controller.selectedIndex.value == "") {
                controller.selectedIndex.value = "2-0";
                controller.shopType = 2;
                controller.listType = 0;
                await controller.refreshData();
              }
            },
            child: Obx(
              () => Container(
                width: 85.w,
                height: 35.h,
                padding: EdgeInsets.only(left: 8.w),
                alignment: Alignment.centerLeft,
                color: controller.storeItemUnfold.value == true
                    ? AppColors.colorFFD7DED0
                    : AppColors.color00E4FFBA,
                child: Row(
                  children: [
                    ImageUtils.getImage(
                      controller.storeItemUnfold.value == true
                          ? Assets.imagesMallCategoryArrowDown
                          : Assets.imagesMallCategoryArrowRight,
                      6.w,
                      6.w,
                      fit: BoxFit.contain,
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 4.w),
                      child: Text(
                        "商城",
                        style:
                            TextStyles.common(14.sp, AppColors.colorFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Obx(
            () => Visibility(
              visible: controller.storeItemUnfold.value == true,
              child: _buildSubCategoryListWidget(2),
            ),
          ),
          GestureDetector(
            onTap: () {
              controller.exchangeStoreItemUnfold.value =
                  !controller.exchangeStoreItemUnfold.value;
              if (controller.exchangeStoreItemUnfold.value == true &&
                  controller.selectedIndex.value == "") {
                controller.selectedIndex.value = "1-0";
              }
            },
            child: Obx(
              () => Container(
                width: 85.w,
                height: 35.h,
                padding: EdgeInsets.only(left: 8.w),
                alignment: Alignment.centerLeft,
                color: controller.exchangeStoreItemUnfold.value == true
                    ? AppColors.colorFFD7DED0
                    : AppColors.color00E4FFBA,
                child: Row(
                  children: [
                    ImageUtils.getImage(
                      controller.exchangeStoreItemUnfold.value == true
                          ? Assets.imagesMallCategoryArrowDown
                          : Assets.imagesMallCategoryArrowRight,
                      6.w,
                      6.w,
                      fit: BoxFit.contain,
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 3.w),
                      child: Text(
                        "兑换商城",
                        style:
                            TextStyles.common(14.sp, AppColors.colorFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Obx(
            () => Visibility(
              visible: controller.exchangeStoreItemUnfold.value == true,
              child: _buildSubCategoryListWidget(1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoreListWidget() {
    return GetBuilder(
      init: controller,
      id: controller.refreshId,
      builder: (ctrl) {
        if (controller.pageState == PageState.loading) {
          return const LoadingWidget();
        }
        if (controller.data.isEmpty) {
          return const ListPageEmptyWidget();
        }
        return GridView.extent(
          padding: EdgeInsets.only(bottom: 35.w),
          maxCrossAxisExtent: 90.w,
          childAspectRatio: 90.w / 137.5.w,
          crossAxisSpacing: 5.w,
          mainAxisSpacing: 10.w,
          children: controller.data.map((e) => _buildListItem(e)).toList(),
        );
      },
    );
  }

  Widget _buildListItem(StoreGoodsItemEntity itemEntity) {
    String coinAssetName = Assets.imagesRechargeDaCoin;
    double coinImageWidth = 16.w;
    double coinImageHeight = 16.w;
    if (itemEntity.type == 1) {
      coinAssetName = Assets.imagesRechargeDaCoin;
      coinImageWidth = 16.w;
      coinImageHeight = 16.w;
    } else if (itemEntity.type == 2) {
      coinAssetName = Assets.imagesRechargeDaBang;
      coinImageWidth = 13.w;
      coinImageHeight = 20.h;
    } else if (itemEntity.type == 3) {
      coinAssetName = Assets.imagesRechargeSkin;
      coinImageWidth = 16.w;
      coinImageHeight = 16.w;
    }
    bool enable = !(itemEntity.limitNo == 1 && itemEntity.isHave == 1);
    return GestureDetector(
      onTap: () {
        if (!enable) {
          return;
        }
        ToastUtils.showBottomDialog(
          StoreGoodsDetailDialog(goodsItemEntity: itemEntity),
          showClose: true,
        ).then((result) async {
          if (result == "1") {
            await controller.refreshData();
            await controller.loadUserStainsCount();
            ToastUtils.showToast("兑换成功");
          }
        });
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(5.r),
        child: Container(
          width: 90.w,
          height: 137.5.w,
          color: Colors.white,
          child: Column(
            children: [
              Container(
                width: 90.w,
                height: 67.5.w,
                alignment: Alignment.center,
                color: Colors.white,
                child: ImageUtils.getImage(itemEntity.url ?? "", 90.w, 67.5.h,
                    fit: BoxFit.contain),
              ),
              Container(
                margin: EdgeInsets.only(top: 5.h),
                constraints: BoxConstraints(
                  maxWidth: 90.w - 8.w * 2,
                ),
                child: Text(
                  itemEntity.propName ?? "",
                  overflow: TextOverflow.ellipsis,
                  style: TextStyles.normal(12.sp),
                ),
              ),
              Visibility(
                // visible: !(itemEntity.limitNo == 1 && itemEntity.isHave == 1),
                child: Padding(
                  padding: EdgeInsets.only(top: 8.h),
                  child: Container(
                    width: 62.w,
                    height: 25.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: !enable
                          ? AppColors.colorFFD4D4D4
                          : AppColors.colorFF23AF28,
                      borderRadius: BorderRadius.circular(25.h / 2),
                      gradient: !enable ? null : const LinearGradient(
                        colors: [
                          AppColors.colorFFA0F6A5,
                          AppColors.colorFF58C75D
                        ],
                      ),
                      border: !enable
                          ? null
                          : Border.all(color: AppColors.colorFF23AF28),
                    ),
                    child: !enable
                        ? Text(
                            "已拥有",
                            style: TextStyles.common(
                              14.sp,
                              AppColors.colorFF999999,
                            ),
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ImageUtils.getImage(coinAssetName, coinImageWidth,
                                  coinImageHeight),
                              Padding(
                                padding: EdgeInsets.only(left: 5.w),
                                child: Text(
                                  "${itemEntity.price ?? 0}",
                                  textAlign: TextAlign.center,
                                  style: TextStyles.normal(14.sp),
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
