import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui show PlaceholderAlignment;
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/radio_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/goods_entity.dart';
import 'package:dada/model/pay_info.dart';
import 'package:dada/pages/store/pay_status_bottom_dialog.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/pay_utils.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PayOrderInfoPage extends StatefulWidget {
  final GoodsEntity goodsEntity;

  const PayOrderInfoPage({super.key, required this.goodsEntity});

  @override
  State<PayOrderInfoPage> createState() => _PayOrderInfoPageState();
}

class _PayOrderInfoPageState extends State<PayOrderInfoPage> {
  Timer? _timer;
  RxInt countDownTime = (60 * 60).obs;
  Rx<PayType?> selectedPayType = Rx(null);

  @override
  void initState() {
    super.initState();

    if (Platform.isIOS) {
      selectedPayType.value = PayType.apple;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "支付订单",
      ),
      body: Column(
        children: [
          _buildCountDownWidget(),
          _buildPriceWidget(),
          _buildPayTypeListWidget(),
          const Spacer(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  ///倒计时
  Widget _buildCountDownWidget() {
    _startTimer();
    return Padding(
      padding: EdgeInsets.only(top: 20.h),
      child: Obx(() {
        String countDownTimeStr = TimeUtils.countdownTime(countDownTime.value,
            showDay: false, showHour: false);
        if (countDownTime.value == 60 * 60) {
          countDownTimeStr = "60:00";
        } else if (countDownTime.value <= 0) {
          countDownTimeStr = "00:00";
        }
        return Text(
          "支付剩余时间 $countDownTimeStr",
          style: TextStyles.common(14.sp, AppColors.colorFF666666),
        );
      }),
    );
  }

  ///价格
  Widget _buildPriceWidget() {
    double price = widget.goodsEntity.price ?? 0.00;
    return Padding(
      padding: EdgeInsets.only(top: 10.h),
      child: Text.rich(
        TextSpan(
          children: [
            WidgetSpan(
              child: Padding(
                padding: EdgeInsets.only(bottom: 6.h),
                child: Text(
                  "￥",
                  style: TextStyles.normal(16.sp),
                ),
              ),
            ),
            WidgetSpan(
              alignment: ui.PlaceholderAlignment.bottom,
              child: Padding(
                padding: EdgeInsets.only(left: 3.w),
                child: Text(
                  price.toStringAsFixed(2),
                  style: TextStyles.bold(30.sp, w: FontWeight.w700),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///支付选项
  Widget _buildPayTypeListWidget() {
    return Visibility(
      visible: Platform.isAndroid,
      child: Container(
        margin: EdgeInsets.only(top: 77.h),
        child: Column(
          children: [
            Platform.isAndroid
                ? _buildPayTypeListItemWidget(
                    PayType.alipay, Assets.imagesPayOrderPayTypeAlipay, "支付宝支付")
                : Container(),
            Platform.isAndroid
                ? _buildPayTypeListItemWidget(
                    PayType.wechat, Assets.imagesPayOrderPayTypeWechat, "微信支付")
                : Container(),
            // Platform.isIOS
            //     ? _buildPayTypeListItemWidget(
            //         PayType.apple, Assets.imagesPayOrderPayTypeApple, "苹果支付")
            //     : Container(),
          ],
        ),
      ),
    );
  }

  ///支付单个选项
  Widget _buildPayTypeListItemWidget(
      PayType payType, String imageName, String title) {
    return FutureBuilder<bool>(
      future: PayUtils().checkInstalled(payType),
      builder: (context, snapshot) {
        if (snapshot.data == true) {
          return Obx(() {
            selectedPayType.value ??= payType;
            bool selected = selectedPayType.value == payType;
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                selectedPayType.value = payType;
              },
              child: Container(
                height: 50.h,
                margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 10.h),
                decoration: BoxDecoration(
                  color: AppColors.colorFFF5F5F5,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 10.w),
                          child: ImageUtils.getImage(imageName, 20.w, 20.w),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 5.w),
                          child: Text(
                            title,
                            style: TextStyles.normal(14.sp),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: 10.w),
                      child: RadioButton(
                        selected: selected,
                      ),
                    ),
                  ],
                ),
              ),
            );
          });
        }
        return Container();
      },
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      bottomMargin: 35.h,
      title: "确认支付",
      onTap: () async {
        if (selectedPayType.value != null) {
          PayInfo? payInfo = await ApiService().createOrder(
            goodsId: widget.goodsEntity.goodsId!,
            payType: selectedPayType.value!,
            buyNum: widget.goodsEntity.count ?? 1,
            payAmount:
                widget.goodsEntity.price! * (widget.goodsEntity.count ?? 1),
          );
          if (payInfo == null) {
            ToastUtils.showToast("创建订单失败");
            Get.back();
            return;
          }
          ToastUtils.showLoading();
          PayUtils.sharedInstance.pay(
            payType: selectedPayType.value!,
            payInfo: payInfo,
            callback: (success) {
              ToastUtils.hideLoading();
              if (success) {
                ToastUtils.showBottomDialog(
                  PayStatusBottomDialog(
                    payResultStatus: PayResultStatus.success,
                    payType: selectedPayType.value!,
                    price: widget.goodsEntity.price!,
                  ),
                ).then((result) {
                  Get.back(result: "1");
                });
              } else {
                ToastUtils.showToast("支付失败");
              }
            },
          );
        } else {
          ToastUtils.showToast("请先安装微信或支付宝客户端才能进行支付");
        }
      },
    );
  }

  void _startTimer() {
    if (_timer != null) {
      return;
    }
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      countDownTime--;
      if (countDownTime <= 0) {
        _timer?.cancel();
        _timer = null;
        Get.back();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }
}
