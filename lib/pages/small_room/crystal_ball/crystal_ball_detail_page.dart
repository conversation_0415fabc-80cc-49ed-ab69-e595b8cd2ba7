import 'dart:io';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/crystal_ball_life_record_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class CrystalBallDetailPage extends StatefulWidget {
  const CrystalBallDetailPage({super.key});

  @override
  State<CrystalBallDetailPage> createState() => _CrystalBallDetailPageState();
}

class _CrystalBallDetailPageState extends State<CrystalBallDetailPage> {
  late String userId;
  late int year;
  Rx<CrystalBallLifeRecordEntity?> lifeRecordEntity = Rx(null);
  TextEditingController textEditingController = TextEditingController();
  CrystalBallLifeRecordEntity? originalLifeRecordEntity;
  RxBool isEditing = false.obs;
  Function()? callback;
  bool hasChanged = false;

  @override
  void initState() {
    super.initState();

    userId = Get.parameters["userId"] ?? "";
    year = int.parse(Get.parameters["year"] ?? "0");
    callback = Get.arguments;
    loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        resizeToAvoidBottomInset: true,
        extendBodyBehindAppBar: true,
        appBar: CustomAppBar(
          backgroundColor: Colors.transparent,
          title: "$year",
        ),
        body: GradientWidget(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight,
          child: Container(
            margin: EdgeInsets.only(
                bottom: ScreenUtil().bottomBarHeight,
                top: ScreenUtil().statusBarHeight + kToolbarHeight),
            decoration: const BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(
                      Assets.imagesCrystalBallLifeRecordDetailContentBg),
                  fit: BoxFit.fill),
            ),
            child: SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildEditingStateWidget(),
                  _buildListView(),
                  _buildSeparatorLine(),
                  _buildBottomTextField(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEditingStateWidget() {
    return Container(
      margin: EdgeInsets.only(top: 40.h, left: 30.w, right: 40.w),
      height: 30.h,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 130.w,
            height: 20.h,
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(top: 5.h),
            padding: EdgeInsets.only(left: 12.w),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image:
                    AssetImage(Assets.imagesCrystalBallLifeRecordDetailTitleBg),
              ),
            ),
            child: Text(
              "$year 生活记录",
              style: TextStyles.common(14.sp, Colors.white),
            ),
          ),
          Visibility(
            visible: UserService().user!.id == userId,
            child: GestureDetector(
              onTap: () {
                isEditing.value = !isEditing.value;
                if (isEditing.value == false) {
                  saveData();
                }
              },
              child: Obx(() {
                if (isEditing.value == false) {
                  return Padding(
                    padding: EdgeInsets.only(top: 7.h),
                    child: ImageUtils.getImage(
                        Assets.imagesCommonEditBtnGreen, 16.w, 16.w),
                  );
                }
                return Container(
                  width: 56.w,
                  height: 30.h,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: AppColors.colorFF89E15C,
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  child: Text(
                    "完成",
                    style: TextStyles.normal(14.sp),
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return Container(
      margin: EdgeInsets.only(top: 27.h, left: 31.w),
      child: Wrap(
        spacing: 10.w,
        runSpacing: 14.h,
        children: [0, 1, 2, 3].map((e) {
          String url = "";
          if (lifeRecordEntity.value?.imgUrls?.length != null &&
              lifeRecordEntity.value!.imgUrls!.length > e) {
            url = lifeRecordEntity.value!.imgUrls![e];
          }
          return GestureDetector(
            onTap: () {
              if (isEditing.value) {
                if (url.isEmpty) {
                  ToastUtils.showBottomSheet(
                    [S.current.album, S.current.camera],
                    onTap: (index) async {
                      if (index == 0) {
                        List<AssetEntity>? assets =
                            await ImagePickerUtil.selectAsset();
                        if (assets?.isNotEmpty == true) {
                          insertImageToList(assets!.first, e);
                        }
                      } else {
                        AssetEntity? asset = await ImagePickerUtil.takeAsset(
                            enableRecording: false);
                        if (asset != null) {
                          insertImageToList(asset, e);
                        }
                      }
                    },
                  );
                }
              } else {
                if (url.isNotEmpty) {
                  String tag = HeroTagName.crystalBall
                      .of("$e");
                  ImageUtils.showImageBrowser(
                    ImageBrowserArgs(
                      [tag],
                      [url],
                      index: e,
                    ),
                  );
                }
              }
            },
            child: Container(
              width: 144.4.w,
              height: 194.4.h,
              padding: EdgeInsets.only(
                  top: 10.h, left: 10.w, right: 8.w, bottom: 15.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5.r),
                color: Colors.white,
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.colorFFBEA8A4,
                    blurRadius: 5,
                  ),
                ],
              ),
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                        Assets.imagesCrystalBallLifeRecordDetailItemEmptyBg),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Stack(
                  children: [
                    url.isNotEmpty
                        ? ImageUtils.getImage(url, 127.w, 168.h,
                            fit: BoxFit.cover, radius: 2.r)
                        : Container(),
                    Visibility(
                      visible: isEditing.value == true && url.isNotEmpty,
                      child: Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                            onTap: () {
                              if (lifeRecordEntity.value?.imgUrls != null &&
                                  lifeRecordEntity.value!.imgUrls!.length > e) {
                                lifeRecordEntity.value!.imgUrls!.removeAt(e);
                                hasChanged = lifeRecordEntity
                                        .value!.imgUrls!.length ==
                                    originalLifeRecordEntity?.imgUrls?.length;
                                setState(() {});
                              }
                            },
                            child: Container(
                              width: 20.w,
                              height: 18.h,
                              alignment: Alignment.center,
                              child: ImageUtils.getImage(
                                  Assets.imagesUserLabelImgDelete, 20.w, 18.h),
                            )),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSeparatorLine() {
    return Container(
      margin: EdgeInsets.only(left: 25.w, right: 42.w, top: 12.h),
      child: ImageUtils.getImage(
          Assets.imagesCrystalBallLifeRecordDetailSeparator, 307.w, 17.h),
    );
  }

  Widget _buildBottomTextField() {
    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        constraints: BoxConstraints( // 关键约束
            minHeight: 100.h,    // 最小高度
            maxHeight: constraints.maxHeight * 0.5 // 最大不超过可用空间的50%
        ),
        margin:
            EdgeInsets.only(left: 31.w, right: 37.w, top: 5.h, bottom: 25.h),
        child: Obx(
          () => Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
              border: isEditing.value
                  ? Border.all(color: AppColors.colorFFD0C1BE)
                  : null,
            ),
            child: CustomTextField.build(
                contentPadding: EdgeInsets.only(
                    left: 15.w, right: 15.w, top: 10.h, bottom: 10.h),
                controller: textEditingController,
                readOnly: !isEditing.value,
                textInputAction: TextInputAction.done,
                maxLines: 5,
                maxLength: 2000,
                style:
                    TextStyles.common(14.sp, AppColors.colorFF84635C, h: 1.5),
                onChanged: (value) {
                  if (originalLifeRecordEntity?.content != null &&
                      originalLifeRecordEntity?.content != value) {
                    hasChanged = true;
                  } else if (originalLifeRecordEntity?.content == null) {
                    hasChanged = true;
                  }
                }),
          ),
        ),
      );
    });
  }

  void insertImageToList(AssetEntity asset, int index) async {
    File? file = await asset.originFile;
    if (file != null) {
      String url = file.path;
      String? uploadUrl = await ApiService().uploadFile(url);
      if (uploadUrl != null) {
        if (lifeRecordEntity.value?.imgUrls != null &&
            lifeRecordEntity.value!.imgUrls!.length > index) {
          lifeRecordEntity.value!.imgUrls!.insert(index, uploadUrl);
        } else {
          lifeRecordEntity.value?.imgUrls ??= [];
          lifeRecordEntity.value!.imgUrls!.add(uploadUrl);
        }
        hasChanged = true;
        setState(() {});
      }
    }
  }

  void loadData() async {
    lifeRecordEntity.value = await ApiService()
        .getCrystalBallLifeRecordDetail(userId: userId, year: year);
    lifeRecordEntity.value ??= CrystalBallLifeRecordEntity()..year = year;
    originalLifeRecordEntity = lifeRecordEntity.value;
    textEditingController.text = lifeRecordEntity.value?.content ?? "";
  }

  void saveData() async {
    if (!hasChanged) {
      return;
    }
    bool? result = await ApiService().publishCrystalBallLifeRecord(
      userId: userId,
      year: year,
      content: textEditingController.text,
      imgUrls: lifeRecordEntity.value?.imgUrls ?? [],
    );
    if (result == true) {
      callback?.call();
      loadData();
    }
  }
}
