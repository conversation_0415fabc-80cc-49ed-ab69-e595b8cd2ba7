import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/fonts_family.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/crystal_ball_life_record_entity.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CrystalBallPage extends StatefulWidget {
  final String tag;

  const CrystalBallPage({super.key, required this.tag});

  @override
  State<CrystalBallPage> createState() => _CrystalBallPageState();
}

class _CrystalBallPageState extends State<CrystalBallPage> {
  final RxDouble sliverAppBarOpacity = 0.0.obs;
  final RxDouble scrollPositionY = 0.0.obs;
  final double sliverAppBarMaxOffsetY =
      281.h - ScreenUtil().statusBarHeight - kBottomNavigationBarHeight;
  late ScrollController _scrollController;
  final RxList<CrystalBallLifeRecordEntity> _lifeRecordList =
      <CrystalBallLifeRecordEntity>[].obs;
  late String userId;
  late String userName;

  @override
  void initState() {
    super.initState();

    userId = Get.arguments["userId"] ?? UserService().user?.id ?? "";
    userName = Get.arguments["userName"] ?? UserService().user?.nickname ?? "";

    _scrollController = ScrollController();
    _scrollController.addListener(() {
      Log.i('Scrolling updated. OffsetY: ${_scrollController.position.pixels}');
      if (_scrollController.position.pixels >= 0) {
        Log.i(
            'Scrolling updated. OffsetY: ${_scrollController.position.pixels}');
        scrollPositionY.value = _scrollController.position.pixels;
        sliverAppBarOpacity.value =
            scrollPositionY.value / sliverAppBarMaxOffsetY;
        sliverAppBarOpacity.value = max(0, sliverAppBarOpacity.value);
        sliverAppBarOpacity.value = min(1, sliverAppBarOpacity.value);
        Log.i('SliverAppBarOpacity value: ${sliverAppBarOpacity.value}');
      }
    });

    loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      List<Widget> widgets = [];
      int cycles = _lifeRecordList.length ~/ 8;
      for (int i = 0; i < cycles; i++) {
        widgets.add(
          _buildCyclingListWidget(_lifeRecordList.sublist(i * 8, (i + 1) * 8)),
        );
      }
      if (_lifeRecordList.length % 8 != 0) {
        widgets.add(
          _buildCyclingListWidget(
              _lifeRecordList.sublist(cycles * 8, _lifeRecordList.length)),
        );
      }
      if (widgets.isEmpty) {
        widgets.add(_buildCyclingListWidget([]));
      }
      return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: CustomAppBar(
          title: '记录生活',
          backgroundColor: sliverAppBarOpacity.value < 0.99
              ? Colors.black.withOpacity(0.01)
              : Colors.white.withOpacity(sliverAppBarOpacity.value),
          rightWidgets: [
            Visibility(
            visible: UserService().user!.id == userId,
              child: _buildSwitchButton(),
            ),
            //_buildSwitchButton(),
          ],
        ),
        body: ListView(
          padding: EdgeInsets.zero,
          controller: _scrollController,
          physics: const ClampingScrollPhysics(),
          children: [
            Container(
              width: ScreenUtil().screenWidth,
              height: 281.h,
              alignment: Alignment.topCenter,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(Assets.imagesCrystalBallTopBgImage),
                  fit: BoxFit.cover,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 210.w,
                    height: 90.h,
                    margin: EdgeInsets.only(top: 125.h),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Hi~$userName,",
                          style: TextStyles.common(25.sp, AppColors.colorFF907149,
                              w: FontWeight.w400, f: FontsFamily.youSheBiaoTiHei),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 5.h),
                          child: Text(
                            "欢迎来到时光机！",
                            style: TextStyles.common(
                              20.sp,
                              AppColors.colorFF907149,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 在这里添加一段文字
                  Padding(
                    padding: EdgeInsets.only(top: 20.h),
                    child: Text(
                      "每年都要有记录哦，成长的感觉！",
                      style: TextStyles.common(
                        14.sp,
                        AppColors.colorFF907149,
                        w: FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            ...widgets,
          ],
        ),
      );
    });
  }

  Widget _buildCyclingListWidget(List<CrystalBallLifeRecordEntity> recordList) {
    List<Widget> widgets = [];
    for (int i = 0; i < recordList.length; i++) {
      CrystalBallLifeRecordEntity record = recordList[i];
      widgets.add(
        Positioned(
          left: _recordPositions()[i].dx,
          top: _recordPositions()[i].dy,
          child: GestureDetector(
            onTap: () {
              Get.toNamed(GetRouter.crystalBallLifeDetail, parameters: {
                "userId": userId,
                "year": record.year.toString()
              }, arguments: () {
                loadData();
              });
            },
            child: Container(
              width: 80.w,
              height: 73.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(record.isExist == true
                      ? Assets.imagesCrystalBallRecordListItemBg
                      : Assets.imagesCrystalBallRecordListItemEmptyBg),
                  fit: BoxFit.cover,
                ),
              ),
              child: Text(
                "${record.year ?? ""}",
                style: TextStyles.common(
                  18.sp,
                  record.isExist == true
                      ? Colors.white
                      : Colors.white.withOpacity(0.5),
                  w: FontWeight.w900,
                ),
              ),
            ),
          ),
        ),
      );
    }
    return Container(
      width: ScreenUtil().screenWidth,
      height: 531.h,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.imagesCrystalBallCycleListBgImage),
          fit: BoxFit.fill,
        ),
      ),
      child: Stack(
        children: widgets,
      ),
    );
  }

  Widget _buildSwitchButton() {
    SmallRoomController controller =
        Get.find<SmallRoomController>(tag: widget.tag);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(right: 5.w),
          child: Text(
            "公开",
            style: TextStyles.common(14.sp, Colors.white),
          ),
        ),
        GestureDetector(
          onTap: () async {
            int state = controller.roomInfo?.room?.roomLifeState ?? 1;
            bool success = await controller.updateRoomInfo(
                roomLifeState: state == 1 ? 0 : 1);
            if (success) {
              setState(() {
                controller.roomInfo?.room?.roomLifeState = state == 1 ? 0 : 1;
              });
            }
          },
          child: Padding(
            padding: EdgeInsets.only(right: 10.w),
            child: ImageUtils.getImage(
                controller.roomInfo?.room?.roomLifeState == 1
                    ? Assets.imagesSmallRoomSwitchBtnOpen
                    : Assets.imagesSmallRoomSwitchBtnClose,
                40.w,
                20.h),
          ),
        ),
      ],
    );
  }

  List<Offset> _recordPositions() {
    return [
      Offset(24.w, 0),
      Offset(165.w, 27.h),
      Offset(285.w, 88.h),
      Offset(167.w, 153.h),
      Offset(27.w, 189.h),
      Offset(144.w, 270.h),
      Offset(276.w, 352.h),
      Offset(144.w, 412.h),
    ];
  }

  void loadData() async {
    _lifeRecordList.value =
        await ApiService().getCrystalBallLifeRecordList(userId: userId) ?? [];
  }
}
