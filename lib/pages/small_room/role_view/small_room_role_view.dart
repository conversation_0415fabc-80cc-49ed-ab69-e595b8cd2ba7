import 'dart:async';
import 'dart:math' as math;
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';
import 'package:dada/pages/chat/invite/chat_invite_game_sheet.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/pages/small_room/function_view/bubble_word/small_room_bubble_word_edit_bottom_dialog.dart';
import 'package:dada/pages/small_room/function_view/other/small_room_relation_setting_bottom_dialog.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/pages/small_room/small_room_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/dress_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/svgaplayer_flutter.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tolyui/tolyui.dart';

class SmallRoomRoleView extends StatefulWidget {
  final String tag;
  final double originX;
  final double originY;
  final double imageScaleX;
  final FriendUserInfoEntity userInfo;
  final double imageWidth;
  final double imageHeight;
  final String roleImage;
  final String? roleImageStatus;

  const SmallRoomRoleView(
      {super.key,
      required this.tag,
      required this.userInfo,
      required this.originX,
      required this.originY,
      required this.imageWidth,
      required this.imageHeight,
      required this.roleImage,
      required this.imageScaleX,
      this.roleImageStatus});

  @override
  State<SmallRoomRoleView> createState() => _SmallRoomRoleViewState();
}

class _SmallRoomRoleViewState extends State<SmallRoomRoleView>
    with TickerProviderStateMixin {
  late SmallRoomController controller;
  late double roleWidth;
  late double roleHeight;
  bool hideApplyEndBubble = false;
  late SVGAAnimationController svgaAnimationController;
  Timer? _timer;
  RxInt applyLeftHours = 0.obs;
  final PopoverController popoverController = PopoverController();
  final PopoverController popoverController1 = PopoverController();

  ///气泡语相关
  RxInt currentShowBubbleWordIndex = 0.obs;
  RxBool isShowingBubbleWord = false.obs;
  RxString currentShowBubbleWord = "".obs;

  late String currentRoleImageName;
  late double currentImageScaleX;

  /// 新增的拖动位置变量
  double _dragOffsetX = 0.0;
  double _dragOffsetY = 0.0;

  late MainController mainController;
  bool isInSmallRoom = true;
  bool isPanning = false;

  @override
  void initState() {
    super.initState();

    roleWidth = widget.imageWidth;
    roleHeight = widget.imageHeight;
    currentRoleImageName = widget.roleImage;

    debugPrint(
        "2. 用户${widget.userInfo.nickname}, 形象:${widget.roleImageStatus},  width: $roleWidth, height:$roleHeight");

    controller = Get.find<SmallRoomController>(tag: widget.tag);

    svgaAnimationController = SVGAAnimationController(vsync: this);
    mainController = Get.find<MainController>();
    // 监听页面切换事件
    ever(mainController.currentIndex, (index) {
      if (mainController.previousIndex.value == 2 && index != 2) {
        isInSmallRoom = false;
        svgaAnimationController.stop();
        // svgaAnimationController.clear();
        // svgaAnimationController.dispose();
      } else if (index == 2) {
        isInSmallRoom = true;
        if (mounted) {
          // svgaAnimationController = SVGAAnimationController(vsync: this);
        }
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.userInfo.userFriendId == null ||
          (widget.userInfo.userFriendId == UserService().user?.id &&
              controller.isMyRoom())) {
        return;
      }
    });

    currentImageScaleX = widget.imageScaleX;
  }

  @override
  void didUpdateWidget(covariant SmallRoomRoleView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.imageWidth != oldWidget.imageWidth) {
      roleWidth = widget.imageWidth;
    }
    if (widget.imageHeight != oldWidget.imageHeight) {
      roleHeight = widget.imageHeight;
    }
    if (widget.roleImage != oldWidget.roleImage) {
      currentRoleImageName = widget.roleImage;
    }
  }

  @override
  Widget build(BuildContext context) {
    checkShouldShowBubbleWord();
    return Transform.translate(
      offset: Offset(_dragOffsetX, _dragOffsetY),
      child: GestureDetector(
        onPanUpdate: (details) {
          if (widget.roleImageStatus !=
              UserRoleImageStatus.playComputer.value) {
            if (svgaAnimationController.isAnimating) {
              isPanning = true;
              svgaAnimationController.stop();
            }
            setState(() {
              _dragOffsetX += details.delta.dx;
              _dragOffsetY += details.delta.dy;
            });
          }
        },
        onPanEnd: (details) {
          setState(() {
            isPanning = false;
          });
        },
        child: _buildRoleActionPopover(
          popoverContent: _buildRolePopoverContent(),
          actionItemsCount: getRolePopoverActionItemsCount(),
          child: SizedBox(
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.topCenter,
              children: [
                _buildCountDownOrRemarkWidget(),
                _buildDaziNameWidget(),
                _buildRoleImageWidget(),
                _buildApplyDaziTimeEndPopover(),
                _buildRoleBubbleWordWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCountDownOrRemarkWidget() {
    if (widget.userInfo.daUserId != null &&
        !controller.isMyRoom() &&
        widget.userInfo.daUserId == UserService().user?.id) {
      DateTime endDateTime;
      int day = 0;
      int hour = 0;
      bool countTimeEnd = false; //倒计时已结束
      if (widget.userInfo.expreDate != null) {
        countTimeEnd =
            DateTime.now().isAfter(DateTime.parse(widget.userInfo.expreDate!));
        DateTime nowTime = DateTime.now();
        endDateTime = DateTime.parse(widget.userInfo.expreDate!);
        if (nowTime.isBefore(endDateTime)) {
          day = nowTime.difference(endDateTime).inDays.abs();
          int inHours = nowTime.difference(endDateTime).inHours.abs();
          int inMinutes = nowTime.difference(endDateTime).inMinutes.abs();
          if (inMinutes == 0 && day == 0 && inMinutes > 0) {
            inMinutes = 1;
          }
          applyLeftHours.value = inHours;
          hour = inHours - day * Duration.hoursPerDay;
          if (day > 0 || hour > 0) {
            startCountDownTimer();
          } else {
            cancelCountDownTimer();
          }
        } else {
          cancelCountDownTimer();
        }
      }

      if (countTimeEnd) {
        ///我的房间内其它申请搭子的人倒计时结束气泡，已到三天
        return Positioned(
          top: -50.h,
          child: _buildCountDownEndWidget(),
        );
      } else {
        ///倒计时
        return Positioned(
          top: -55.h,
          child: _buildCountDownWidget(day, hour),
        );
      }
    }
    return _buildDaNameRemarkWidget();
  }

  Widget _buildCountDownWidget(int day, int hour) {
    return Container(
      height: 36.h,
      alignment: Alignment.center,
      padding: EdgeInsets.only(left: 8.w, right: 6.w),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.imagesSmallRoomDaziApplyLeftTimeBg),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildTimeTextLabel("$day"),
              Text(
                "天",
                style: TextStyle(
                  color: AppColors.colorFFD1D1D1,
                  fontSize: 12.sp,
                ),
              ),
              _buildTimeTextLabel("$hour"),
              Text(
                "时",
                style: TextStyle(
                  color: AppColors.colorFFD1D1D1,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(left: 6.w, bottom: 3.h),
            child: ImageUtils.getImage(
              Assets.imagesSmallRoomDaziApplyTimeIcon,
              15.w,
              20.h,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountDownEndWidget() {
    return Container(
      width: 80.w,
      height: 30.h,
      decoration: BoxDecoration(
        color: AppColors.colorFFF2F6E1,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 2.5.w),
            child: ImageUtils.getImage(
              Assets.imagesSmallRoomDaziApplyTimeEndIcon,
              33.w,
              27.h,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 4.w),
            child: Text(
              "熟友",
              style: TextStyles.common(14.sp, AppColors.colorFF986328),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDaNameRemarkWidget() {
    String bgImage = "";
    if (widget.userInfo.friendType == 1) {
      //纯搭
      bgImage = Assets.imagesSmallRoomRoleDaNameBg1;
    } else if (widget.userInfo.friendType == 2) {
      bgImage = Assets.imagesSmallRoomRoleDaNameBg2;
    } else if (widget.userInfo.friendType == 3) {
      bgImage = Assets.imagesSmallRoomRoleDaNameBg3;
    } else if (widget.userInfo.friendType == 4) {
      bgImage = Assets.imagesSmallRoomRoleDaNameBg4;
    }
    return Positioned(
      top: -37.5.h - 22.h,
      child: Visibility(
        visible: !(controller.userID == widget.userInfo.userFriendId &&
                controller.isMyRoom()) &&
            widget.userInfo.daName != null,
        child: Container(
          height: 37.5.h,
          width: 100.w,
          padding:
              EdgeInsets.only(top: widget.userInfo.friendType == 1 ? 6.h : 0),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(bgImage),
              fit: BoxFit.fill,
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Text(
                "${widget.userInfo.daName ?? ""} 搭子",
                style: TextStyle(
                  fontSize: 14.sp,
                  foreground: Paint()
                    ..style = PaintingStyle.stroke
                    ..strokeWidth = 0.5
                    ..color = AppColors.colorFF70311C,
                ),
              ),
              Text(
                "${widget.userInfo.daName ?? ""} 搭子",
                style: TextStyle(fontSize: 14.sp, color: Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDaziNameWidget() {
    String showName = widget.userInfo.nickname ?? "";
    Color textColor = Colors.white;
    bool showNameBg = false;
    if (widget.userInfo.userFriendId == controller.userID) {
      if (controller.isMyRoom()) {
        showName = "我";
      } else {
        showName = widget.userInfo.sex == 0 ? "他" : "她";
      }
      showNameBg = true;
    }
    return Positioned(
      top: -22.h,
      child: Padding(
        padding: EdgeInsets.only(top: 2.h),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Visibility(
              visible: showNameBg,
              child: ImageUtils.getImage(
                  Assets.imagesSmallRoomMineRoleNicknameBg, 53.w, 22.h),
            ),
            Text(
              showName,
              style: TextStyle(
                fontSize: 14.sp,
                foreground: Paint()
                  ..style = PaintingStyle.stroke
                  ..strokeWidth = 0.2
                  ..color = AppColors.colorFF70311C,
              ),
            ),
            Text(
              showName,
              style: TextStyle(
                fontSize: 14.sp,
                color: textColor,
                shadows: [
                  Shadow(
                    color: AppColors.colorFF9C3F22,
                    blurRadius: 4.r,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleImageWidget() {
    if (widget.roleImageStatus == UserRoleImageStatus.sitPlayPhone.value) {
      roleWidth = 106.w;
      roleHeight = 110.h;
    } else if (widget.roleImageStatus ==
        UserRoleImageStatus.playComputer.value) {
      roleWidth = 85.w;
      roleHeight = 152.h;
    } else {
      roleWidth = 110.w;
      roleHeight = 210.h;
    }
    _setRoleAssetImage();
    if (widget.roleImageStatus == UserRoleImageStatus.playComputer.value) {
      currentImageScaleX = 1.0;
    }
    return RepaintBoundary(
      child: Transform.scale(
        scaleX: currentImageScaleX,
        child: SizedBox(
          width: roleWidth,
          height: roleHeight,
          child: Stack(
            children: [
              Container(
                color: Colors.transparent,
                // color: Colors.blueAccent,
                width: roleWidth,
                height: roleHeight,
              ),
              SVGAImage(
                svgaAnimationController,
                fit: BoxFit.contain,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeTextLabel(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 1.h, right: 3.w, left: 3.w),
      child: Stack(
        children: [
          Text(
            text,
            style: TextStyle(
              fontSize: 18.sp,
              foreground: Paint()
                ..style = PaintingStyle.stroke
                ..strokeWidth = 0.2
                ..color = AppColors.colorFF70311C,
            ),
          ),
          Text(
            text,
            style: TextStyle(
              fontSize: 18.sp,
              color: AppColors.colorFFFDE484,
              shadows: const [
                Shadow(
                  color: AppColors.colorFF42CE48,
                  offset: Offset(0.5, 0.5),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///角色点击弹出气泡内容
  Widget _buildRolePopoverContent() {
    return Container(
      width: 150.w,
      constraints: BoxConstraints(
        minHeight: 125.h,
      ),
      padding: EdgeInsets.only(left: 10.w, right: 8.w, top: 8.h, bottom: 10.h),
      decoration: BoxDecoration(
        image: DecorationImage(
          scale: 3,
          image: const AssetImage(Assets.imagesSmallRoomBgActionPopoverBubble),
          centerSlice: Rect.fromLTRB(0.w, 40.h, 100.w, 50.h),
          fit: BoxFit.fill,
        ),
      ),
      child: _buildPopoverActionListWidget(),
    );
  }

  ///角色点击弹出气泡设置
  Widget _buildRoleActionPopover({
    required Widget popoverContent,
    required int actionItemsCount,
    required Widget child,
  }) {
    return TolyPopover(
      controller: popoverController,
      maxWidth: 150.w,
      gap: 3.h,
      offsetCalculator: (calculator) {
        if (calculator.placement == Placement.bottom) {
          if (widget.originY < 200.h) {
            if (widget.originY < 100.h) {
              if (actionItemsCount > 5) {
                return Offset(0.w, -300.h);
              } else if (actionItemsCount > 4) {
                return Offset(0.w, -320.h);
              } else if (actionItemsCount > 3) {
                return Offset(0.w, -250.h);
              } else if (actionItemsCount > 2) {
                return Offset(0.w, -230.h);
              }
              return Offset(0.w, -280.h);
            } else {
              if (actionItemsCount >= 4) {
                return Offset(0.w, -360.h);
              } else if (actionItemsCount > 3) {
                return Offset(0.w, -270.h);
              }
              return Offset(0.w, 0.h);
            }
          }
        }
        return Offset(0.w, 10.h);
      },
      placement: Placement.top,
      overlay: popoverContent,
      overlayDecorationBuilder: (_) => const BoxDecoration(
        color: Colors.transparent,
      ),
      builder: (_, ctrl, __) {
        return GestureDetector(
          onTap: () {
            popoverController.open();
          },
          child: child,
        );
      },
    );
  }

  ///角色点击弹出气泡事件列表
  Widget _buildPopoverActionListWidget() {
    bool showToSmallRoom = actionBubbleCheckShouldShowToSmallRoom();
    bool showRelationSetting = actionBubbleCheckShouldShowRelationSetting();
    bool showToChat = actionBubbleCheckShouldShowToChat();
    bool showInvite = actionBubbleCheckShouldShowToInvite();
    bool showToProfile = actionBubbleCheckShouldShowToProfile();
    bool showBubbleWordSetting =
        actionBubbleCheckShouldShowToBubbleWordSetting();
    bool showLetOutRoom = actionBubbleCheckShouldShowToLetOut();
    bool shouldShowPoke = actionBubbleCheckShouldShowToPoke();
    bool showExitRoom = actionBubbleCheckShouldShowToExit();
    int actionItemsCount = getRolePopoverActionItemsCount();
    double actionItemMargin = actionItemsCount < 3 ? 10.h : 2.h;
    return Padding(
      padding: EdgeInsets.only(
          top: actionItemsCount < 3 ? 10.h : 15.h,
          left: 4.w,
          right: 4.w,
          bottom: 15.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Visibility(
            visible: showToSmallRoom,
            child: _buildRolePopoverActionItemWidget(
              "去对方小屋",
              Assets.imagesSmallRoomRoleActionToRoom,
              () {
                if (widget.userInfo.userFriendId != null) {
                  popoverController.close();
                  Get.to(() =>
                      SmallRoomPage(userId: widget.userInfo.userFriendId!));
                } else if (widget.userInfo.daUserId != null) {
                  popoverController.close();
                  Get.to(
                      () => SmallRoomPage(userId: widget.userInfo.daUserId!));
                }
              },
            ),
          ),
          Visibility(
            visible: showRelationSetting,
            child: Padding(
              padding: EdgeInsets.only(top: actionItemMargin),
              child: _buildRolePopoverActionItemWidget(
                "关系设置",
                Assets.imagesSmallRoomRoleActionRelationSetting,
                () {
                  ToastUtils.showBottomDialog(
                    SmallRoomRelationSettingBottomDialog(
                      userFriendId: widget.userInfo.userFriendId!,
                      userFriendType: widget.userInfo.friendType!,
                      friendDaziRemark: widget.userInfo.daName,
                      callback: (value) {
                        if (value != null) {
                          int? friendType = value["friendType"];
                          String? daName = value["daName"];
                          if (friendType != null) {
                            widget.userInfo.friendType = friendType;
                          }
                          if (daName != null) {
                            widget.userInfo.daName = daName;
                          }
                          setState(() {});
                          controller.updateDaziRelation(
                              userFriendId: widget.userInfo.userFriendId!,
                              friendType: friendType,
                              daName: daName);
                        }
                      },
                    ),
                  );
                },
              ),
            ),
          ),
          Visibility(
            visible: showToChat,
            child: Padding(
              padding: EdgeInsets.only(top: actionItemMargin),
              child: _buildRolePopoverActionItemWidget(
                "去聊天",
                Assets.imagesSmallRoomRoleActionToChat,
                () async {
                  popoverController.close();
                  if (!UserService().checkIsMonthCardUser()) {
                    ToastUtils.showBottomDialog(
                        const MonthCardExpiredLimitChatDialog());
                  } else {
                    String? userID = widget.userInfo.userFriendId ??
                        widget.userInfo.daUserId;
                    if (userID?.isNotEmpty == true) {
                      V2TimConversation conversation = await ChatIMManager
                          .sharedInstance
                          .getConversation(userID: userID, type: 1);
                      Get.toNamed(GetRouter.chatDetail,
                          arguments: conversation);
                    }
                  }
                },
              ),
            ),
          ),
          Visibility(
            visible: showInvite,
            child: Padding(
              padding: EdgeInsets.only(top: actionItemMargin),
              child: _buildRolePopoverActionItemWidget(
                "邀请去玩",
                Assets.imagesSmallRoomRoleActionToInvite,
                () {
                  if (widget.userInfo.userFriendId != null) {
                    ToastUtils.showBottomDialog(
                      ChatInviteGameSheet(
                          daUserId: widget.userInfo.userFriendId!),
                    );
                  }
                },
              ),
            ),
          ),
          Visibility(
            visible: showToProfile,
            child: Padding(
              padding: EdgeInsets.only(top: actionItemMargin),
              child: _buildRolePopoverActionItemWidget(
                "个人主页",
                Assets.imagesSmallRoomRoleActionToProfile,
                () {
                  if (widget.userInfo.userFriendId != null) {
                    popoverController.close();
                    Get.toNamed(GetRouter.userProfile,
                        parameters: {"userId": widget.userInfo.userFriendId!});
                  } else if (widget.userInfo.daUserId != null) {
                    popoverController.close();
                    Get.toNamed(GetRouter.userProfile,
                        parameters: {"userId": widget.userInfo.daUserId!});
                  }
                },
              ),
            ),
          ),
          Visibility(
            visible: showLetOutRoom,
            child: Padding(
              padding: EdgeInsets.only(top: actionItemMargin),
              child: _buildRolePopoverActionItemWidget(
                "请出小屋",
                Assets.imagesSmallRoomRoleActionToOut,
                () {
                  popoverController.close();
                  ToastUtils.showDialog(
                    content: "确定要将 ${widget.userInfo.nickname ?? ""} 请出小屋？",
                    onConfirm: () {
                      controller.kickOutRoomUser(widget.userInfo);
                    },
                  );
                },
              ),
            ),
          ),
          Visibility(
            visible: showBubbleWordSetting,
            child: Padding(
              padding: EdgeInsets.only(top: actionItemMargin),
              child: _buildRolePopoverActionItemWidget(
                "泡泡语设置",
                Assets.imagesSmallRoomRoleActionToBubble,
                () {
                  if (widget.userInfo.userFriendId != null ||
                      widget.userInfo.daUserId != null) {
                    ToastUtils.showBottomDialog(
                      SmallRoomBubbleWordEditBottomDialog(
                        tag: widget.tag,
                        userId: widget.userInfo.userFriendId ??
                            widget.userInfo.daUserId ??
                            "",
                        bubbleDTOList: widget.userInfo.bubbleDTOList,
                        changedCallback: (words) {
                          widget.userInfo.bubbleDTOList = words;
                          if (controller.isMyRoom() &&
                              widget.userInfo.userFriendId ==
                                  UserService().user?.id) {
                            controller.roomInfo?.bubbleDTOList = words;
                            widget.userInfo.bubbleDTOList = words;
                          }
                          if (words.isNotEmpty &&
                              isShowingBubbleWord.value == false) {
                            repeatShowRoleBubbleWord();
                          }
                          controller
                              .update([SmallRoomController.KRoleListBuildID]);
                        },
                      ),
                    );
                  }
                },
              ),
            ),
          ),

          ///戳一下
          Visibility(
            visible: shouldShowPoke,
            child: Padding(
              padding: EdgeInsets.only(top: actionItemMargin),
              child: _buildRolePopoverActionItemWidget(
                "戳一下",
                Assets.imagesSmallRoomRoleActionToPoke,
                () {
                  popoverController.close();
                  changeRoleImage();
                  controller.checkCanPlayAudio(widget.userInfo.bubbleDTOList);
                  controller.pokeUser(widget.userInfo.userFriendId ??
                      widget.userInfo.daUserId ??
                      "");
                },
              ),
            ),
          ),

          ///离开小屋
          Visibility(
            visible: showExitRoom,
            child: Padding(
              padding: EdgeInsets.only(top: actionItemMargin),
              child: _buildRolePopoverActionItemWidget(
                "离开小屋",
                Assets.imagesSmallRoomRoleActionToOut,
                () {
                  popoverController.close();
                  ToastUtils.showDialog(
                    content: "确定要离开他的小屋？",
                    onConfirm: () async {
                      String userID = controller.userID ?? "";
                      bool success =
                          await ApiService().leaveFromOtherRoom(userID);
                      if (success) {
                        Get.back();
                      }
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///角色点击弹出气泡事件单项
  Widget _buildRolePopoverActionItemWidget(
    String title,
    String icon,
    Function() onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            title,
            style: TextStyles.common(16.sp, Colors.white, w: FontWeight.w500),
          ),
          Padding(
            padding: EdgeInsets.only(left: 0.w),
            child: ImageUtils.getImage(icon, 30.w, 30.w),
          ),
        ],
      ),
    );
  }

  ///获取角色形象素材（性别_衣服主题_衣服颜色）
  void _setRoleAssetImage({String? roleImage}) async {
    if (isPanning) return;
    final videoItem = await SVGAParser.shared
        .decodeFromAssets(roleImage ?? currentRoleImageName);
    if (!isInSmallRoom) return;
    svgaAnimationController.videoItem = videoItem;
    debugPrint(
        "3. 用户${widget.userInfo.nickname}, 形象:${widget.roleImageStatus}, roleImage: ${roleImage ?? currentRoleImageName}, width: $roleWidth, height:$roleHeight");
    var random = math.Random();
    int delay = random.nextInt(5);
    Future.delayed(Duration(seconds: delay), () {
      if (isInSmallRoom) {
        svgaAnimationController.repeat();
      }
    });
  }

  ///开始了解倒计时结束气泡显示
  Widget _buildApplyDaziTimeEndPopover() {
    bool leftTimeEnd = widget.userInfo.expreDate != null &&
        widget.userInfo.daUserId != null &&
        (DateTime.now().isAfter(DateTime.parse(widget.userInfo.expreDate!)));
    bool showDaziApplyEndBubble = !controller.isMyRoom() &&
        widget.userInfo.daUserId == UserService().user?.id &&
        leftTimeEnd;
    if (!showDaziApplyEndBubble || hideApplyEndBubble == true) {
      return Container();
    }

    return TolyPopover(
      maxWidth: 175.w,
      gap: 30.h,
      barrierDismissible: false,
      controller: popoverController1,
      offsetCalculator: (calculator) {
        if (widget.originY < 100) {
          return Offset(0.w, -calculator.gap + 30.h);
        }
        return Offset(0.w, -calculator.gap);
      },
      overlay: Container(
        width: 175.w,
        height: 70.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesSmallRoomDaziApplyEndTimeBubble),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 12.w, top: 11.h),
              child: Text(
                "相熟后，就可以申\n请搭子了哦！",
                style:
                    TextStyles.common(14.sp, AppColors.colorFF986328, h: 1.3),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 6.w, top: 8.h),
              child: GestureDetector(
                onTap: () {
                  hideApplyEndBubble = true;
                  popoverController1.close();
                },
                child: Icon(
                  Icons.close,
                  color: AppColors.colorFF986328,
                  size: 20.w,
                ),
              ),
            ),
          ],
        ),
      ),
      overlayDecorationBuilder: (_) => const BoxDecoration(
        color: Colors.transparent,
      ),
      builder: (_, ctrl, __) {
        Future.delayed(const Duration(milliseconds: 300), () {
          if (ctrl.isOpen != true) {
            ctrl.open();
          }
        });
        return Container(
          width: roleWidth,
          height: roleHeight,
          color: Colors.transparent,
        );
      },
    );
  }

  ///气泡语气泡展示
  Widget _buildRoleBubbleWordWidget() {
    if (widget.userInfo.bubbleDTOList?.isNotEmpty == true) {
      return Obx(() {
        if (currentShowBubbleWord.isNotEmpty == true) {
          Size bubbleWordSize = StringUtils.calculateTextSize(
            currentShowBubbleWord.value,
            maxWidth: 150.w - 20.w,
            textStyle: TextStyles.normal(14.sp, h: 1.3),
          );
          double bubbleWidth = bubbleWordSize.width + 20.w;
          double bubbleHeight = bubbleWordSize.height + 8.h + 6.h;
          double offsetX = (roleWidth - bubbleWidth) / 2;
          double blowRight = 0.w;
          if (bubbleWidth > roleWidth) {
            if (widget.originX + (bubbleWidth - roleWidth) / 2 >
                ScreenUtil().screenWidth) {
              offsetX = ScreenUtil().screenWidth - (bubbleWidth - roleWidth);
            } else if (widget.originX - (bubbleWidth - roleWidth) / 2 < 0) {
              offsetX = -(bubbleWidth - roleWidth) / 2 + 15.w;
              blowRight = 35.w;
            }
          }

          double offsetY = widget.userInfo.daName?.isNotEmpty == true
              ? (-bubbleHeight - 15.h)
              : (-bubbleHeight - 10.h);
          if (widget.originY < 100) {
            if (widget.originY < bubbleHeight) {
              offsetY = offsetY + 25.h;
            } else {
              offsetY = offsetY + 15.h;
            }
          }
          return Positioned(
            top: offsetY,
            left: offsetX,
            child: Visibility(
              visible: isShowingBubbleWord.value,
              child: SizedBox(
                width: bubbleWidth + 20.w,
                height: bubbleHeight + 3.h,
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: bubbleWidth + 20.w,
                      height: bubbleHeight + 3.h,
                      margin: EdgeInsets.only(bottom: 5.h),
                      padding: EdgeInsets.only(
                          left: 10.w, right: 10.w, top: 6.h, bottom: 6.h),
                      decoration: BoxDecoration(
                        color: AppColors.colorFFF2F6E1,
                        borderRadius: BorderRadius.circular(5.w),
                      ),
                      child: Text(
                        currentShowBubbleWord.value,
                        maxLines: 10,
                        style: TextStyles.common(14.sp, AppColors.colorFF986328,
                            h: 1.3),
                      ),
                    ),
                    Positioned(
                      bottom: 3.h,
                      child: Padding(
                        padding: EdgeInsets.only(right: blowRight),
                        child: ImageUtils.getImage(
                            Assets.imagesSmallRoomRoleBubbleWordBgBrow,
                            8.w,
                            5.h),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
        return Container();
      });
    }
    return Container();
  }

  void repeatShowRoleBubbleWord() {
    if (isShowingBubbleWord.value == true) {
      return;
    }
    isShowingBubbleWord.value = true;
    List<SmallRoomBubbleWordEntity> bubbleAudioList =
        widget.userInfo.bubbleDTOList!.where((e) => e.type == 1).toList();
    if (currentShowBubbleWordIndex.value > bubbleAudioList.length - 1) {
      currentShowBubbleWordIndex.value = 0;
    }
    if (bubbleAudioList.isEmpty == true) {
      isShowingBubbleWord.value = false;
      return;
    }
    currentShowBubbleWord.value =
        bubbleAudioList[currentShowBubbleWordIndex.value].content!;

    Future.delayed(const Duration(seconds: 9), () {
      isShowingBubbleWord.value = false;
      currentShowBubbleWordIndex.value++;
      var random = math.Random();
      int milliseconds = random.nextInt(5000) + 3000;
      Future.delayed(Duration(milliseconds: milliseconds), () {
        repeatShowRoleBubbleWord();
      });
    });
  }

  void startCountDownTimer() {
    if (_timer != null) {
      return;
    }
    _timer = Timer.periodic(const Duration(hours: 1), (_) {
      if (mounted) {
        setState(() {
          applyLeftHours--;
          if (applyLeftHours.value <= 0) {
            _timer?.cancel();
            _timer = null;
          }
        });
      }
    });
  }

  void cancelCountDownTimer() {
    _timer?.cancel();
    _timer = null;
  }

  ///点击人物气泡是否显示去对方小屋
  bool actionBubbleCheckShouldShowToSmallRoom() {
    bool show = false;
    if (controller.isMyRoom() == true &&
        widget.userInfo.userFriendId != UserService().user?.id) {
      show = true;
    }
    return show;
  }

  ///点击人物气泡是否显示关系设置
  bool actionBubbleCheckShouldShowRelationSetting() {
    bool show = false;
    if (controller.isMyRoom() == true &&
        widget.userInfo.knowId == null &&
        widget.userInfo.userFriendId != null &&
        widget.userInfo.userFriendId != UserService().user?.id) {
      ///在我小屋且是搭子关系
      show = true;
    }
    return show;
  }

  ///点击人物气泡是否显示去聊天
  bool actionBubbleCheckShouldShowToChat() {
    bool show = false;
    if (controller.isMyRoom() == true &&
        widget.userInfo.userFriendId != UserService().user?.id &&
        widget.userInfo.knowId == null &&
        widget.userInfo.userFriendId != null) {
      ///我的小屋，点击搭子
      show = true;
    } else if (controller.isMyRoom() == false &&
        ((widget.userInfo.userFriendId != UserService().user?.id &&
                widget.userInfo.userFriendId != null) ||
            (widget.userInfo.daUserId != UserService().user?.id &&
                widget.userInfo.daUserId != null))) {
      ///他人小屋，点击除我以外的角色
      show = true;
    }
    return show;
  }

  ///点击人物气泡是否显示邀请去玩
  bool actionBubbleCheckShouldShowToInvite() {
    bool show = false;
    if (controller.isMyRoom() == true &&
        widget.userInfo.userFriendId != UserService().user?.id &&
        widget.userInfo.knowId == null &&
        widget.userInfo.isDaziRelation()) {
      ///我的小屋，点击搭子
      show = true;
    }
    return show;
  }

  ///点击人物气泡是否显示去个人主页
  bool actionBubbleCheckShouldShowToProfile() {
    bool show = true;
    return show;
  }

  ///点击人物气泡是否显示请出小屋
  bool actionBubbleCheckShouldShowToLetOut() {
    bool show = false;
    if (controller.isMyRoom() == true &&
        widget.userInfo.userFriendId == null &&
        widget.userInfo.daUserId != null) {
      ///我的小屋，点击开始了解的人
      show = true;
    }
    return show;
  }

  ///点击人物气泡是否显示泡泡语设置
  bool actionBubbleCheckShouldShowToBubbleWordSetting() {
    bool show = false;
    if (controller.isMyRoom() == true &&
        widget.userInfo.userFriendId == UserService().user?.id) {
      ///我的小屋，点击我自己
      show = true;
    } else if (controller.isMyRoom() == false &&
        (widget.userInfo.userFriendId == UserService().user?.id ||
            widget.userInfo.daUserId == UserService().user?.id)) {
      ///他人小屋，点击我自己
      show = true;
    }
    return show;
  }

  ///点击人物气泡是否显示戳一下
  bool actionBubbleCheckShouldShowToPoke() {
    bool show = true;
    return show;
  }

  ///点击人物气泡是否显示离开小屋
  bool actionBubbleCheckShouldShowToExit() {
    bool show = false;
    if (controller.isMyRoom() == false &&
        widget.userInfo.userFriendId == null &&
        widget.userInfo.daUserId != null &&
        widget.userInfo.daUserId == UserService().user?.id) {
      ///他人小屋，我自己是正在开始了解的状态
      show = true;
    }
    return show;
  }

  ///获取气泡内容按钮数量
  int getRolePopoverActionItemsCount() {
    int count = 0;
    if (actionBubbleCheckShouldShowToSmallRoom()) {
      count++;
    }
    if (actionBubbleCheckShouldShowRelationSetting()) {
      count++;
    }
    if (actionBubbleCheckShouldShowToChat()) {
      count++;
    }
    if (actionBubbleCheckShouldShowToInvite()) {
      count++;
    }
    if (actionBubbleCheckShouldShowToProfile()) {
      count++;
    }
    if (actionBubbleCheckShouldShowToLetOut()) {
      count++;
    }
    if (actionBubbleCheckShouldShowToBubbleWordSetting()) {
      count++;
    }
    if (actionBubbleCheckShouldShowToPoke()) {
      count++;
    }
    if (actionBubbleCheckShouldShowToExit()) {
      count++;
    }
    return count;
  }

  void checkShouldShowBubbleWord() {
    if (widget.userInfo.bubbleDTOList?.isNotEmpty == true &&
        isShowingBubbleWord.value == false) {
      var random = math.Random();
      int milliseconds = random.nextInt(10000) + 5000;
      Future.delayed(Duration(milliseconds: milliseconds), () {
        repeatShowRoleBubbleWord();
      });
    }
  }

  ///改变角色形象
  void changeRoleImage() {
    if (widget.roleImageStatus == UserRoleImageStatus.sitPlayPhone.value ||
        widget.roleImageStatus == UserRoleImageStatus.playComputer.value) {
      return;
    }
    String roleAssetsName = "";
    if (widget.userInfo.userImageNo != null) {
      roleAssetsName = getRandomRoleImage();
      currentRoleImageName = "assets/svga/dress/$roleAssetsName";
    }
    if (roleAssetsName.isNotEmpty) {
      roleAssetsName = "assets/svga/dress/$roleAssetsName";
    }
    _setRoleAssetImage(roleImage: roleAssetsName);
  }

  String getRandomRoleImage() {
    String roleImageAssetName = '';
    roleImageAssetName = DressUtils().getSvgaAssetNameWithDressNo(
      dressNo: widget.userInfo.userImageNo!,
      isRandomStatus: true,
      status: null,
    );
    if (roleImageAssetName == currentRoleImageName.split('/').last) {
      roleImageAssetName = getRandomRoleImage();
    }
    return roleImageAssetName;
  }

  @override
  void dispose() {
    svgaAnimationController.stop();
    svgaAnimationController.clear();
    super.dispose();
  }
}
