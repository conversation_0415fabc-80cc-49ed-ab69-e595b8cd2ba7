import 'dart:math' as math;
import 'package:dada/common/values/enums.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/small_room/role_view/small_room_role_view.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/dress_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomDaziView extends StatefulWidget {
  final String tag;

  const SmallRoomDaziView({super.key, required this.tag});

  @override
  State<SmallRoomDaziView> createState() => _SmallRoomDaziViewState();
}

class _SmallRoomDaziViewState extends State<SmallRoomDaziView> {
  late SmallRoomController controller;
  List<Offset>? rolePositionList;
  Map<String, Offset>? rolePositionMap;

  @override
  void initState() {
    super.initState();

    controller = Get.find<SmallRoomController>(tag: widget.tag);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SmallRoomController>(
      id: SmallRoomController.KRoleListBuildID,
      tag: widget.tag,
      builder: (controller) {
        if (controller.roomInfo == null) {
          return Container();
        }
        List<FriendUserInfoEntity> roleList = [];
        if (controller.isMyRoom()) {
          roleList.add(
            transformUserInfoToFriendInfoEntity(userInfo: UserService().user),
          );
        } else {
          roleList.add(
            transformUserInfoToFriendInfoEntity(userInfo: controller.userInfo),
          );
        }
        if (controller.roomInfo?.dadaList?.isNotEmpty == true) {
          roleList.addAll(controller.roomInfo!.dadaList!);
        }
        if (controller.roomInfo?.beginKnowList?.isNotEmpty == true) {
          roleList.addAll(controller.roomInfo!.beginKnowList!);
        }

        if (!(rolePositionList?.isNotEmpty == true)) {
          rolePositionList = selectRandomPositions(roleList.length);
          rolePositionMap = getRolePositionMap(rolePositionList!, roleList);
        } else {
          updateRolePositions(roleList);
        }
        rolePositionList!.sort((a, b) => a.dy.compareTo(b.dy));
        updateBgChairShow();
        return Stack(
          children: roleList.map((e) {
            int index = roleList.indexOf(e);
            Offset offset = rolePositionList![index];
            int positionIndex = getDaziRoleOffsetList().indexOf(offset);
            String roleImage = getRoleImageAsset(e, positionIndex);
            String? roleImageStatus = getRoleImageStatus(roleImage);
            Size roleImageSize =
                getRoleImageSize(roleImageStatus, positionIndex);
            double imageScaleX = getRoleImageScaleX(roleImageStatus);
            double positionY = offset.dy;
            if (!controller.isMyRoom() &&
                roleImageStatus == UserRoleImageStatus.playComputer.value) {
              positionY += ScreenUtil().bottomBarHeight;
            }
            debugPrint(
                "1. 用户${e.nickname}, 形象:$roleImageStatus, roleImage:$roleImage, positionIndex:$positionIndex, width: ${roleImageSize.width}, height:${roleImageSize.height}");
            return Positioned(
              left: offset.dx,
              top: positionY,
              child: SmallRoomRoleView(
                tag: widget.tag,
                userInfo: e,
                originX: offset.dx,
                originY: offset.dy - ScreenUtil().statusBarHeight,
                roleImage: roleImage,
                imageWidth: roleImageSize.width,
                imageHeight: roleImageSize.height,
                roleImageStatus: roleImageStatus,
                imageScaleX: imageScaleX,
              ),
            );
          }).toList(),
        );
      },
    );
  }

  List<Offset> getDaziRoleOffsetList() {
    List<Offset> list = [];
    double originY = ScreenUtil().statusBarHeight;
    double bottomBarHeight = ScreenUtil().bottomBarHeight;
    double chairOffsetY = 0.0;
    if (bottomBarHeight == 0) {
      chairOffsetY = 20.h;
      if (!controller.isMyRoom()) {
        chairOffsetY = 36.h;
      }
    } else {
      if (!controller.isMyRoom()) {
        chairOffsetY = -10.h;
      }
    }
    list.add(Offset(55.w, 80.h + originY)); //0.
    list.add(Offset(240.w, 100.h + originY)); //1.
    list.add(Offset(160.w, 64.h + originY + chairOffsetY)); //2.玩电脑固定位置
    list.add(Offset(115.w, 230.h + originY)); //3.
    list.add(Offset(30.w, 200.h + originY)); //4.
    list.add(Offset(254.w, 232.h + originY)); //5.
    list.add(Offset(165.w, 320.h + originY)); //6.
    list.add(Offset(0.w, 440.h + originY)); //7.坐着玩手机固定位置
    list.add(Offset(282.w, 380.h + originY)); //8.
    list.add(Offset(145.w, 436.h + originY)); //9.
    return list;
  }

  List<Offset> selectRandomPositions(int length,
      {List<Offset>? selectedPositionList}) {
    final selectedPositions = selectedPositionList ?? <Offset>[];
    final random = math.Random();

    final list = getDaziRoleOffsetList();
    while (selectedPositions.length < length) {
      final index = random.nextInt(list.length);
      if (!selectedPositions.contains(list[index])) {
        selectedPositions.add(list[index]);
      }
    }
    return selectedPositions;
  }

  Map<String, Offset> getRolePositionMap(
      List<Offset> positions, List<FriendUserInfoEntity> userList) {
    Map<String, Offset> map = {};
    for (int i = 0; i < userList.length; i++) {
      FriendUserInfoEntity friendUserInfo = userList[i];
      if (friendUserInfo.userFriendId != null) {
        map[friendUserInfo.userFriendId!] = positions[i];
      }
    }
    return map;
  }

  void updateRolePositions(List<FriendUserInfoEntity> userList) {
    if (rolePositionList != null &&
        userList.length != rolePositionList!.length) {
      if (userList.length > rolePositionList!.length) {
        selectRandomPositions(userList.length,
            selectedPositionList: rolePositionList!);
      } else {
        rolePositionMap!.removeWhere((userId, offset) {
          int? index = userList.indexWhere((e) => e.userFriendId == userId);
          if (index == -1) {
            rolePositionList!.remove(offset);
            return true;
          }
          return false;
        });
      }
    }
  }

  FriendUserInfoEntity transformUserInfoToFriendInfoEntity(
      {UserInfoEntity? userInfo}) {
    FriendUserInfoEntity friendUserInfoEntity = FriendUserInfoEntity();
    friendUserInfoEntity.userFriendId = userInfo?.id;
    friendUserInfoEntity.nickname = userInfo?.nickname;
    friendUserInfoEntity.sex = userInfo?.sex;
    friendUserInfoEntity.userImageNo = userInfo?.currentDressNo;
    friendUserInfoEntity.bubbleDTOList = controller.roomInfo?.bubbleDTOList;
    return friendUserInfoEntity;
  }

  String getRoleImageAsset(FriendUserInfoEntity userInfo, int index) {
    String roleAssetsName = "";
    if (userInfo.userImageNo != null) {
      bool isRandomStatus = true;
      String? status = "";
      if (index == 7) {
        isRandomStatus = false;
        status = UserRoleImageStatus.sitPlayPhone.value;
      } else if (index == 2) {
        isRandomStatus = false;
        status = UserRoleImageStatus.playComputer.value;
      }
      roleAssetsName = DressUtils().getSvgaAssetNameWithDressNo(
        dressNo: userInfo.userImageNo!,
        isRandomStatus: isRandomStatus,
        status: status,
      );
    }
    if (roleAssetsName.isNotEmpty) {
      roleAssetsName = "assets/svga/dress/$roleAssetsName";
    }
    return roleAssetsName;
  }

  String? getRoleImageStatus(String roleImage) {
    if (roleImage.contains(UserRoleImageStatus.sitPlayPhone.value)) {
      return UserRoleImageStatus.sitPlayPhone.value;
    } else if (roleImage.contains(UserRoleImageStatus.playComputer.value)) {
      return UserRoleImageStatus.playComputer.value;
    }
    return null;
  }

  Size getRoleImageSize(String? imageStatus, int positionIndex) {
    if (imageStatus == UserRoleImageStatus.sitPlayPhone.value) {
      return Size(106.w, 110.w);
    } else if (imageStatus == UserRoleImageStatus.playComputer.value) {
      return Size(85.w, 152.w);
    }
    return Size(110.w, 210.w);
  }

  double getRoleImageScaleX(String? imageStatus) {
    double scaleX = 1.0;
    if (imageStatus == UserRoleImageStatus.sitPlayPhone.value) {
      scaleX = -1.0;
    } else if (imageStatus == UserRoleImageStatus.playComputer.value) {
      scaleX = 1.0;
    } else {
      if (imageStatus == null) {
        final random = math.Random();
        List<double> list = [-1.0, 1.0];
        int index = random.nextInt(list.length);
        scaleX = list[index];
      }
    }
    return scaleX;
  }

  void updateBgChairShow() {
    Future.delayed(const Duration(milliseconds: 300), () {
      Offset chairOffset = getDaziRoleOffsetList()[2];
      if (!rolePositionList!.contains(chairOffset)) {
        controller.isChairShow.value = true;
      } else {
        controller.isChairShow.value = false;
      }
    });
  }
}
