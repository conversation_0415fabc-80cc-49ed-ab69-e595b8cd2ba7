import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/small_room_mail_list_item_entity.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/pages/small_room/mail/small_room_mail_controller.dart';
import 'package:dada/pages/small_room/mail/small_room_mail_large_content_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomMailPage extends StatefulWidget {
  const SmallRoomMailPage({super.key});

  @override
  State<SmallRoomMailPage> createState() => _SmallRoomMailPageState();
}

class _SmallRoomMailPageState extends State<SmallRoomMailPage>
    with TickerProviderStateMixin {
  late TabController tabController;
  late PageController pageController;
  List<String> tabItemTitles = ["系统消息", "弹幕", "搭子申请"];
  final String roomId = Get.parameters["roomId"] ?? "";
  Function()? callback;

  @override
  void initState() {
    super.initState();

    tabController = TabController(length: 3, vsync: this);
    pageController = PageController();

    callback = Get.arguments;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFF5F5F5,
      appBar: CustomAppBar(
        backgroundColor: Colors.white,
        title: "信箱",
      ),
      body: Column(
        children: [
          _buildTabBar(),
          _buildPageView(),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 51.h,
      alignment: Alignment.centerLeft,
      child: TabBar(
        padding: EdgeInsets.only(left: 15.w, top: 0.h),
        tabAlignment: TabAlignment.start,
        controller: tabController,
        tabs: tabItemTitles.map((e) {
          return Obx(() {
            int index = tabItemTitles.indexOf(e);
            String? unreadCount;
            MainController mainController = Get.find<MainController>();
            if (index == 0) {
              unreadCount =
                  mainController.mailSystemUnreadCount.value.toString();
            } else if (index == 1) {
              unreadCount = mainController.mailMsgUnreadCount.value.toString();
            } else if (index == 2) {
              unreadCount = mainController.mailDadaUnreadCount.value.toString();
            }
            if (unreadCount == "0") {
              unreadCount = null;
            }
            return BadgeWidget(
              text: unreadCount,
              offset: Offset(0, 2.h),
              child: Tab(text: e),
            );
          });
        }).toList(),
        dividerColor: Colors.transparent,
        isScrollable: true,
        labelPadding: EdgeInsets.only(right: 15.w),
        labelStyle: TextStyle(fontSize: 16.sp),
        labelColor: AppColors.colorFF333333,
        unselectedLabelColor: AppColors.colorFF666666,
        unselectedLabelStyle: TextStyle(fontSize: 16.sp),
        indicatorWeight: 4.h,
        indicatorPadding:
            EdgeInsets.only(bottom: 15.h, top: 24.h, left: 5.w, right: 5.w),
        indicator: BoxDecoration(
          color: Theme.of(context)
              .bottomNavigationBarTheme
              .selectedLabelStyle
              ?.color,
          borderRadius: BorderRadius.circular(2.r),
        ),
        onTap: (index) {
          pageController.jumpToPage(index);
        },
      ),
    );
  }

  Widget _buildPageView() {
    return Expanded(
      child: PageView(
        controller: pageController,
        onPageChanged: (index) {
          tabController.animateTo(index);
        },
        children: tabItemTitles.map(
          (e) {
            int tabIndex = tabItemTitles.indexOf(e);
            int messageType = tabIndex + 1;
            if (tabIndex == 0) {
              messageType = 2;
            } else if (tabIndex == 1) {
              messageType = 1;
            }
            return GetBuilder(
              init: SmallRoomMailController()
                ..messageType = messageType
                ..roomId = roomId,
              global: false,
              id: SmallRoomMailController().refreshId,
              builder: (controller) {
                if (controller.data.isEmpty) {
                  return EmptyWidget();
                }
                return RefreshWidget.build(
                  triggerAxis: Axis.vertical,
                  refreshController: controller.refreshController,
                  onRefresh: () => controller.refreshData(),
                  onLoadMore: () => controller.loadMoreData(),
                  child: ListView.separated(
                    scrollDirection: Axis.vertical,
                    itemBuilder: (context, index) {
                      SmallRoomMailListItemEntity entity =
                          controller.data[index];
                      return _buildMessageItem(
                          tabIndex: tabIndex,
                          msgIndex: index,
                          message: entity,
                          controller: controller);
                    },
                    separatorBuilder: (context, index) {
                      return Container(
                        height: 10.h,
                      );
                    },
                    itemCount: controller.data.length,
                  ),
                );
              },
            );
          },
        ).toList(),
      ),
    );
  }

  Widget _buildMessageItem(
      {required int tabIndex,
      required int msgIndex,
      required SmallRoomMailListItemEntity message,
      required SmallRoomMailController controller}) {
    String content = tabIndex == 2
        ? _buildDaziApplyMessageContent(message)
        : message.content ?? "";
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      width: ScreenUtil().screenWidth - 15.w * 2,
      padding:
          EdgeInsets.only(left: 15.w, right: 15.w, top: 15.h, bottom: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMessageContentWidget(tabIndex, msgIndex, message, content),
          _buildDaziApplyBtnWidget(
              tabIndex: tabIndex, message: message, controller: controller),
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Visibility(
                      visible: !UserService()
                          .isOfficialAccount(userId: message.userId),
                      child: GestureDetector(
                        onTap: () {
                          if (message.userId != null) {
                            Get.toNamed(GetRouter.userProfile,
                                parameters: {"userId": message.userId!});
                          }
                        },
                        child: ClipOval(
                          child: ImageUtils.getImage(
                              message.avatar ?? "", 25.w, 25.w,
                              color: AppColors.colorFFF5F5F5,
                              fit: BoxFit.cover,
                              showPlaceholder: true),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 5.w),
                      child: Text(
                        message.nickname ?? "",
                        style:
                            TextStyles.common(14.sp, AppColors.colorFF666666),
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      TimeUtils.formatPostDate(message.createdDate ?? "",
                          formats: [yyyy, "-", m, "-", d]),
                      style: TextStyles.common(14.sp, AppColors.colorFF999999),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 10.w),
                      child: GestureDetector(
                        onTap: () {
                          controller.deleteMessage(message);
                        },
                        child: ImageUtils.getImage(
                            Assets.imagesSmallRoomMailMessageDelete,
                            16.w,
                            15.w),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageContentWidget(int tabIndex, int msgIndex,
      SmallRoomMailListItemEntity message, String content) {
    if (tabIndex == 0) {
      double textHeight = StringUtils.calculateTextHeight(
        message.content ?? "",
        maxWidth: ScreenUtil().screenWidth - 15.w * 2 - 30.w,
        textStyle: TextStyles.normal(16.sp),
      );
      if (textHeight < 40.h) {
        return Text(
          message.content ?? "",
          style: TextStyles.normal(16.sp),
        );
      }
      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          ToastUtils.showDialog(
            dialog: SmallRoomMailLargeContentDialog(message: message),
          );
        },
        child: Column(
          children: [
            Text(
              message.content ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.normal(16.sp),
            ),
            Padding(
              padding: EdgeInsets.only(top: 10.h, bottom: 15.h),
              child: ImageUtils.getImage(
                  Assets.imagesUserProfileLabelHasSubTag, 10.w, 13.h),
            ),
          ],
        ),
      );
    }
    return Text(
      content,
      style: TextStyles.normal(16.sp),
    );
  }

  Widget _buildDaziApplyBtnWidget(
      {required int tabIndex,
      required SmallRoomMailListItemEntity message,
      required SmallRoomMailController controller}) {
    if (tabIndex == 2 && message.type != "0") {
      int? daziApplyState = message.state;
      bool btnEnabled = daziApplyState == 0;
      String btnTitle = "同意";
      if (daziApplyState == 1) {
        btnTitle = "已同意";
      } else if (daziApplyState == 2) {
        btnTitle = "已拒绝";
      } else if (daziApplyState == 3) {
        btnTitle = "维持现状";
      }
      return Padding(
        padding: EdgeInsets.only(top: 15.h, bottom: 15.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CommonGradientBtn(
              width: daziApplyState == 3 ? 82.w : 72.w,
              height: 31.h,
              title: btnTitle,
              enabled: btnEnabled,
              normalImage: Assets.imagesSmallRoomMailMessageAgreeBtn,
              disabledImage: Assets.imagesSmallRoomMailMessageAgreedBtn,
              onTap: () {
                if (btnEnabled) {
                  handleDaziApply(message.userId!, 1, controller);
                }
              },
            ),
            btnEnabled
                ? GestureDetector(
                    onTap: () {
                      handleDaziApply(message.userId!, 2, controller);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(left: 5.w),
                      width: 70.w,
                      height: 30.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15.r),
                        border: Border.all(color: AppColors.colorFF999999),
                      ),
                      child: Text(
                        "拒绝",
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF666666),
                      ),
                    ),
                  )
                : Container(),
            btnEnabled
                ? GestureDetector(
                    onTap: () {
                      handleDaziApply(message.userId!, 3, controller);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(left: 5.w),
                      width: 90.w,
                      height: 30.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15.r),
                        border: Border.all(color: AppColors.colorFF999999),
                      ),
                      child: Text(
                        "维持现状",
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF666666),
                      ),
                    ),
                  )
                : Container(),
          ],
        ),
      );
    }
    return Container();
  }

  String _buildDaziApplyMessageContent(SmallRoomMailListItemEntity message) {
    String daziName = "搭子";
    if (message.friendType == 1) {
      daziName = "纯搭";
    } else if (message.friendType == 2) {
      daziName = "浅搭";
    } else if (message.friendType == 3) {
      daziName = "浅搭（随缘）";
    } else if (message.friendType == 4) {
      daziName = "深搭";
    }
    if (message.type! == "2") {
      return "对方申请将与您的搭子关系变更为$daziName？";
    } else if (message.type! == "1") {
      return "对方申请加您为$daziName关系，是否同意？";
    } else {
      return "您已经开始了解TA,并进驻了TA的小屋";
    }
  }

  void handleDaziApply(
      String userId, int state, SmallRoomMailController controller) async {
    bool success =
        await ApiService().handleDaziApply(userId: userId, state: state);
    if (success) {
      if (state == 1) {
        ToastUtils.showToast("搭子申请已接受");
        callback?.call();
      }
      controller.refreshData();
    }
  }
}
