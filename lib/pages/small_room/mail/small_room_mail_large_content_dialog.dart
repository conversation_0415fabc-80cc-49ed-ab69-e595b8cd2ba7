import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/small_room_mail_list_item_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomMailLargeContentDialog extends StatefulWidget {
  final SmallRoomMailListItemEntity message;

  const SmallRoomMailLargeContentDialog({super.key, required this.message});

  @override
  State<SmallRoomMailLargeContentDialog> createState() =>
      _SmallRoomMailLargeContentDialogState();
}

class _SmallRoomMailLargeContentDialogState
    extends State<SmallRoomMailLargeContentDialog> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 345.w,
      height: 462.h,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.imagesSmallRoomMailLargeContentDialogBg),
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            right: 20.w,
            top: 25.h,
            child: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: ImageUtils.getImage(
                  Assets.imagesCommonDialogCloseBtn, 25.w, 25.w),
            ),
          ),
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 21.w, top: 64.h),
                    child: Row(
                      children: [
                        ClipOval(
                          child: ImageUtils.getImage(
                              widget.message.avatar ?? "", 32.w, 32.w),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 5.w),
                          child: Text(
                            widget.message.nickname ?? "",
                            style: TextStyles.common(
                                14.sp, AppColors.colorFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 15.w, top: 64.h),
                    child: Text(
                      TimeUtils.formatPostDate(widget.message.createdDate ?? "",
                          formats: [yyyy, "-", m, "-", d]),
                      style: TextStyles.common(14.sp, AppColors.colorFF666666),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(
                      top: 18.h, left: 30.w, right: 30.w, bottom: 10.w),
                  child: CustomTextField.build(
                    contentPadding: EdgeInsets.zero,
                    controller: TextEditingController()..text = widget.message.content ?? "",
                    maxLines: 100,
                    readOnly: true,
                  ),
                ),
              ),
              CommonGradientBtn(
                normalImage: Assets.imagesCommonGradientBtnBg103w44h,
                bottomMargin: 30.h,
                width: 100.w,
                height: 40.h,
                title: "关闭",
                onTap: () {
                  Get.back();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
