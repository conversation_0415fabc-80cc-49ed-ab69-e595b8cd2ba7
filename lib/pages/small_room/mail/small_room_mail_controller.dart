import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/small_room_mail_list_item_entity.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class SmallRoomMailController extends ListPageController<
    SmallRoomMailListItemEntity, SmallRoomMailController> {
  late int messageType;
  late String roomId;

  @override
  Future<List<SmallRoomMailListItemEntity>?> loadData(int page) async {
    List<SmallRoomMailListItemEntity>? list = await ApiService()
        .getSmallRoomMailMessageList(
            dadaRoomId: roomId, type: messageType, page: page);
    if (list != null) {
      Get.find<MainController>().getDadaTabUnreadCount();
    }
    return list;
  }

  void deleteMessage(SmallRoomMailListItemEntity message) async {
    bool success =
        await ApiService().deleteSmallRoomBarrage(roomMsgId: message.roomMsgId!);
    if (success) {
      data.remove(message);
      update();
    }
  }
}
