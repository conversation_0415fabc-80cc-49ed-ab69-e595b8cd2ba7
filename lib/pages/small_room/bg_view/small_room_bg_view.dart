import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/constants.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/components/widgets/input_text_field_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tolyui/tolyui.dart';

class SmallRoomBgWidget extends StatefulWidget {
  final String tag;

  const SmallRoomBgWidget({super.key, required this.tag});

  @override
  State<SmallRoomBgWidget> createState() => _SmallRoomBgWidgetState();
}

class _SmallRoomBgWidgetState extends State<SmallRoomBgWidget> {
  late SmallRoomController controller;

  @override
  void initState() {
    super.initState();

    controller = Get.find<SmallRoomController>(tag: widget.tag);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SmallRoomController>(
        id: SmallRoomController.KBgViewBuildID,
        tag: widget.tag,
        builder: (controller) {
          bool isOtherRoom = !controller.isSelf;
          double bgHeight = ScreenUtil().screenHeight -
              (!isOtherRoom
                  ? (Constants.kBottomBarHeight + Get.mediaQuery.padding.bottom)
                  : 0);
          return Container(
            width: ScreenUtil().screenWidth,
            height: bgHeight,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(UserService().user?.sex == 0
                    ? Assets.imagesSmallRoomBgBoy
                    : Assets.imagesSmallRoomBgGirl),
                fit: BoxFit.fill,
              ),
            ),
            child: Stack(
              children: [
                ///音乐盒
                Positioned(
                  top: 100.h +
                      ScreenUtil().statusBarHeight +
                      (isOtherRoom ? 15.h : 0),
                  left: 86.w,
                  child: _buildBgActionBubble(
                    size: Size(40.w, 60.h),
                    text:
                        "最近在听 ${controller.roomInfo?.room?.musicContent ?? ""}",
                    maxLines: 2,
                    placement: Placement.topStart,
                    showEdit: controller.isSelf,
                    onEdit: () {
                      ToastUtils.showDialog(
                        dialog: InputTextFieldDialog(
                          title: "请设置最近在听",
                          maxLength: 10,
                          isMultiLine: true,
                          onSubmit: (value) async {
                            bool success = await controller.updateRoomInfo(
                                musicContent: value);
                            if (success) {
                              controller
                                  .update([SmallRoomController.KBgViewBuildID]);
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),

                ///水晶球（点击跳转相册）
                Positioned(
                  top: 70.h +
                      ScreenUtil().statusBarHeight +
                      (isOtherRoom ? 15.h : 0),
                  left: 180.w,
                  child: GestureDetector(
                    onTap: () {
                      controller.checkGoToCrystalBallPage(widget.tag);
                    },
                    child: Container(
                      width: 40.w,
                      height: 60.w,
                      color: Colors.transparent,
                    ),
                  ),
                ),

                ///电脑
                Positioned(
                  top: 56.5.h +
                      ScreenUtil().statusBarHeight +
                      (isOtherRoom ? 15.h : 0),
                  left: 215.5.w,
                  child: _buildBgActionBubble(
                    size: Size(40.w, 90.h),
                    text:
                        "最近在玩 ${controller.roomInfo?.room?.computerContent ?? ""}",
                    maxLines: 2,
                    placement: Placement.top,
                    showEdit: controller.isSelf,
                    onEdit: () {
                      ToastUtils.showDialog(
                        dialog: InputTextFieldDialog(
                          title: "请设置最近在玩",
                          maxLength: 10,
                          isMultiLine: true,
                          onSubmit: (value) async {
                            bool success = await controller.updateRoomInfo(
                                computerContent: value);
                            if (success) {
                              controller
                                  .update([SmallRoomController.KBgViewBuildID]);
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),

                ///书架
                Positioned(
                  top: 50.h +
                      ScreenUtil().statusBarHeight +
                      (isOtherRoom ? 22.h : 0),
                  left: 275.w,
                  child: _buildBgActionBubble(
                    maxLines: 2,
                    size: Size(100.w, 150.h),
                    offsetY: 60.h,
                    placement: Placement.topEnd,
                    text:
                        "最近在看 ${controller.roomInfo?.room?.bookContent ?? ""}",
                    showEdit: controller.isSelf,
                    onEdit: () {
                      ToastUtils.showDialog(
                        dialog: InputTextFieldDialog(
                          title: "请设置最近在看",
                          maxLength: 10,
                          isMultiLine: true,
                          onSubmit: (value) async {
                            bool success = await controller.updateRoomInfo(
                                bookContent: value);
                            if (success) {
                              controller
                                  .update([SmallRoomController.KBgViewBuildID]);
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),

                ///椅子
                _buildChairWidget(),

                ///信箱
                _buildMailWidget(),
              ],
            ),
          );
        });
  }

  Widget _buildMailWidget() {
    return Visibility(
      visible: controller.isSelf,
      child: Obx(
        () => Positioned(
          left: 52.w,
          top: 414.h + ScreenUtil().statusBarHeight - 25.h,
          child: Container(
            padding: EdgeInsets.only(left: 30.w),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 25.h),
                  child: GestureDetector(
                    onTap: () {
                      if (controller.roomInfo?.room?.dadaRoomId != null) {
                        Get.toNamed(
                          GetRouter.smallRoomMail,
                          parameters: {
                            "roomId": controller.roomInfo!.room!.dadaRoomId!
                          },
                          arguments: () {
                            controller.getRoomInfo(showLoading: false);
                          },
                        )?.then((value) {
                          Get.find<MainController>().getDadaTabUnreadCount();
                        });
                      }
                    },
                    child: ImageUtils.getImage(
                        Get.find<MainController>().mailUnreadCount.value > 0
                            ? Assets.imagesSmallRoomMailBoxNonempty
                            : Assets.imagesSmallRoomMailBoxEmpty,
                        79.w,
                        98.h),
                  ),
                ),
                Positioned(
                  top: 12.h + 25.h,
                  left: 39.w,
                  child: BadgeWidget(
                    text: Get.find<MainController>().dadaTabBadgeCount.value,
                  ),
                ),
                // _buildMailBubble(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChairWidget() {
    return Obx(
      () {
        double offsetY = 0;
        if (!controller.isMyRoom()) {
          offsetY = 25.h;
          if (ScreenUtil().bottomBarHeight == 0) {
            offsetY = 35.h;
          }
        }
        return Visibility(
          visible: controller.isChairShow.value && controller.roomInfo != null,
          child: Positioned(
            left: 160.w,
            top: 120.h + ScreenUtil().statusBarHeight + offsetY,
            child: ImageUtils.getImage(Assets.imagesSmallRoomChair, 66.w, 94.h),
          ),
        );
      },
    );
  }

  Widget _buildBgActionBubble(
      {required String text,
      Placement? placement,
      double? offsetX,
      double? offsetY,
      int? maxLines,
      bool? showEdit,
      Size? size,
      Function()? onEdit}) {
    return TolyPopover(
      maxWidth: 178.w,
      gap: 5.h,
      offsetCalculator: (calculator) {
        return placement == Placement.top
            ? Offset(offsetX ?? -2.w, calculator.gap + 13.h + (offsetY ?? 0))
            : placement == Placement.topStart
                ? Offset(0.w, calculator.gap + 13.h + (offsetY ?? 0))
                : placement == Placement.topEnd
                    ? Offset(0, calculator.gap + 13.h + (offsetY ?? 0))
                    : Offset(0, calculator.gap + 6.h + (offsetY ?? 0));
      },
      placement: placement ?? Placement.top,
      overlay: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 5.h),
            padding:
                EdgeInsets.only(left: 10.w, right: 10.w, top: 8.h, bottom: 6.h),
            decoration: BoxDecoration(
              image: DecorationImage(
                scale: 3,
                image: const AssetImage(Assets.imagesSmallRoomRoleBubbleWordBg),
                centerSlice: Rect.fromLTRB(10.w, 10.h, 20.w, 20.h),
                fit: BoxFit.fill,
              ),
            ),
            child: RichText(
              maxLines: maxLines,
              overflow: TextOverflow.ellipsis,
              text: TextSpan(
                text: text,
                style:
                    TextStyles.common(14.sp, AppColors.colorFF986328, h: 1.2),
                children: showEdit == true
                    ? <InlineSpan>[
                        WidgetSpan(
                          child: Padding(
                            padding: EdgeInsets.only(bottom: 2.5.h, left: 8.w),
                            child: GestureDetector(
                              onTap: onEdit,
                              child: ImageUtils.getImage(
                                  Assets.imagesSmallRoomActionBubbleEdit,
                                  13.w,
                                  13.w),
                            ),
                          ),
                        ),
                      ]
                    : null,
              ),
            ),
          ),
          Positioned(
            bottom: 0.h,
            left: placement == Placement.topStart ? 15.w : null,
            right: placement == Placement.topEnd ? 16.w : null,
            child: ImageUtils.getImage(
                Assets.imagesSmallRoomRoleBubbleWordBgBrow, 8.w, 5.h),
          ),
        ],
      ),
      overlayDecorationBuilder: (_) => const BoxDecoration(
        color: Colors.transparent,
      ),
      builder: (_, ctrl, __) {
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: ctrl.open,
          child: Container(
            width: size?.width ?? 40.w,
            height: size?.height ?? 40.w,
            color: Colors.transparent,
          ),
        );
      },
    );
  }
}
