import 'dart:math';

import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';
import 'package:dada/model/small_room_detail_info_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/pages/small_room/crystal_ball/crystal_ball_page.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/extensions/list_extension.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';

class SmallRoomController extends GetxController {
  static const String KBgViewBuildID = "small_room_bg_view";
  static const String KFuncViewBuildID = "small_room_func_view";
  static const String KRoleListBuildID = "small_room_role_list_view";

  String? userID;
  UserInfoEntity? userInfo;
  SmallRoomDetailInfoEntity? roomInfo;

  ///搭子申请开关
  RxBool isReceiveDaziApply = true.obs;

  ///椅子是否显示
  RxBool isChairShow = true.obs;

  RxList<UserInfoEntity> allFriendsList = <UserInfoEntity>[].obs;

  final AudioPlayerUtils audioPlayer = AudioPlayerUtils();
  String? currentPlayAudioUrl;

  get isSelf {
    if (userID == UserService().user?.id) {
      return true;
    }
    return false;
  }

  @override
  void onInit() {
    super.onInit();

    if (!(userID?.isNotEmpty == true)) {
      userID = UserService().user?.id;
    }
    addAudioPlayerListener();
  }

  @override
  void onReady() async {
    super.onReady();

    await getUserInfo();
    await getRoomInfo();
    getUserFriendList();
    doTask();
  }

  void checkShouldReload() {
    getRoomInfo(showLoading: false);
  }

  Future<void> getUserInfo({bool? showLoading}) async {
    if (isSelf) {
      if (UserService().user != null) {
        userInfo = UserService().user;
        isReceiveDaziApply.value = userInfo?.isAccept == 1;
        return;
      }
    }
    userInfo = await ApiService()
        .getUserInfo(userID!, showLoading: showLoading ?? true);
    isReceiveDaziApply.value = userInfo?.isAccept == 1;
  }

  Future<void> getRoomInfo({bool? showLoading}) async {
    roomInfo = await ApiService()
        .getSmallRoomDetailRoomInfo(userId: userID!, showLoading: showLoading);
    update([KRoleListBuildID, KFuncViewBuildID, KBgViewBuildID]);
  }

  bool isMyRoom() {
    if (roomInfo != null) {
      return roomInfo!.room?.userId == UserService().user?.id;
    }
    return true;
  }

  Future<bool> updateRoomInfo(
      {String? musicContent,
      String? computerContent,
      String? bookContent,
      int? roomLifeState}) async {
    if (roomInfo?.room?.dadaRoomId != null) {
      bool success = await ApiService().updateRoomInfo(
        roomId: roomInfo!.room!.dadaRoomId!,
        musicContent: musicContent,
        computerContent: computerContent,
        bookContent: bookContent,
        roomLifeState: roomLifeState,
      );
      if (success) {
        if (musicContent != null) {
          roomInfo!.room!.musicContent = musicContent;
        }
        if (computerContent != null) {
          roomInfo!.room!.computerContent = computerContent;
        }
        if (bookContent != null) {
          roomInfo!.room!.bookContent = bookContent;
        }
      }
      return success;
    }
    return false;
  }

  void updateDaziRelation(
      {required String userFriendId, int? friendType, String? daName}) {
    roomInfo?.dadaList?.map((e) {
      if (e.userFriendId == userFriendId) {
        if (friendType != null) {
          e.friendType = friendType;
        }
        if (daName != null) {
          e.daName = daName;
        }
      }
    });
  }

  void kickOutRoomUser(FriendUserInfoEntity userInfo) async {
    bool success =
        await ApiService().kickOutUserFromSmallRoom(userId: userInfo.daUserId!);
    if (success) {
      roomInfo?.beginKnowList
          ?.removeWhere((e) => e.daUserId == userInfo.daUserId);
      update([SmallRoomController.KRoleListBuildID]);
    }
  }

  void updateMyBubbleWord(List<SmallRoomBubbleWordEntity> list) {
    FriendUserInfoEntity? userInfoEntity = roomInfo?.dadaList
        ?.where((e) => e.userFriendId == UserService().user!.id)
        .toList()
        .first;
    if (userInfoEntity != null) {
      userInfoEntity.bubbleDTOList = list;
    }
  }

  bool isDaziRelation(String? userId) {
    if (roomInfo?.dadaList.isNullOrEmpty == true ||
        roomInfo == null ||
        userId == null) {
      return false;
    }
    FriendUserInfoEntity? userInfoEntity;
    List<FriendUserInfoEntity>? list =
        roomInfo!.dadaList!.where((e) => e.userFriendId == userId).toList();
    if (list.isNotEmpty == true) {
      userInfoEntity = list.first;
    }
    return userInfoEntity?.isDaziRelation() == true;
  }

  void addAudioPlayerListener() {
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.complete) {
        currentPlayAudioUrl = null;
      }
    });
  }

  void getUserFriendList() async {
    if (isMyRoom()) {
      ContactsController contactsController = ContactsController();
      allFriendsList.value = await contactsController.getFriendsAndDaziList();
    }
  }

  void doTask() {
    if (!isSelf) {
      ApiService().doTask(taskId: "3");
    }
  }

  void checkCanPlayAudio(List<SmallRoomBubbleWordEntity>? bubbleList) {
    if (bubbleList == null) {
      return;
    }
    List<SmallRoomBubbleWordEntity> audioList =
        bubbleList.where((e) => e.type == 2).toList();
    if (audioList.isEmpty) {
      return;
    }
    int randomIndex = Random().nextInt(audioList.length);
    String url = audioList[randomIndex].content!;
    if (currentPlayAudioUrl == url) {
      return;
    } else {
      stopAudio();
    }
    currentPlayAudioUrl = url;
    playAudio(url);
  }

  void playAudio(String url) {
    audioPlayer.setUrl(url);
    audioPlayer.play();
  }

  void stopAudio() {
    audioPlayer.stop();
  }

  void checkGoToCrystalBallPage(String widgetTag) {
    if (userInfo != null) {
      if (roomInfo?.room?.roomLifeState == 1 || isMyRoom()) {
        Get.to(() => CrystalBallPage(tag: widgetTag),
            arguments: {"userId": userID!, "userName": userInfo?.nickname});
      } else {
        ToastUtils.showToast("该小屋主人水晶球暂未开放");
      }
    }
  }

  void pokeUser(String userId) async {
    if (userId.isNotEmpty && userId != UserService().user?.id) {
      bool success = await ApiService().smallRoomPoke(userId: userId);
      if (success) {
        ChatIMManager.sharedInstance
            .sendTextMessage(text: "我看到小屋里的你蛮可爱的，忍不住戳了一下~", toUserID: userId);
      }
    }
  }
}
