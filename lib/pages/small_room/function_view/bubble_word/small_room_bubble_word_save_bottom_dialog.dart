import 'package:dada/common/values/colors.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomBubbleWordSaveDialog extends StatefulWidget {
  final String tag;
  final String userId;
  final SmallRoomBubbleWordEntity? bubbleEntity;
  final Function(SmallRoomBubbleWordEntity) callback;

  const SmallRoomBubbleWordSaveDialog(
      {super.key,
      required this.tag,
      required this.userId,
      required this.callback,
      this.bubbleEntity});

  @override
  State<SmallRoomBubbleWordSaveDialog> createState() =>
      _SmallRoomBubbleWordSaveDialogState();
}

class _SmallRoomBubbleWordSaveDialogState
    extends State<SmallRoomBubbleWordSaveDialog> {
  TextEditingController editingController = TextEditingController();
  late SmallRoomController controller;

  @override
  void initState() {
    super.initState();

    controller = Get.find<SmallRoomController>(tag: widget.tag);
    if (widget.bubbleEntity != null) {
      editingController.text = widget.bubbleEntity!.content!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
      height: 267.h,
      title: "泡泡语",
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(top: 20.h, left: 15.w, right: 15.w),
            height: 120.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
              color: AppColors.colorFFF5F5F5,
            ),
            child: CustomTextField.build(
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
              controller: editingController,
              hintText: "请输入想说的话",
              maxLines: 1000,
              maxLength: 40,
              showLeftLength: true,
            ),
          ),
          CommonGradientBtn(
            topMargin: 15.h,
            horizontalMargin: 116.w,
            title: "保存",
            height: 44.h,
            onTap: () {
              saveOrEditBubbleContent();
            },
            normalImage: Assets.imagesCommonGradientBtnBg,
          ),
        ],
      ),
    );
  }

  void saveOrEditBubbleContent() async {
    String? userId = widget.userId;
    String? dadaRoomId = controller.roomInfo?.room?.dadaRoomId;
    String? bubbleId;
    if (widget.bubbleEntity != null) {
      bubbleId = widget.bubbleEntity!.bubbleId;
    }
    if (dadaRoomId != null) {
      SmallRoomBubbleWordEntity? bubbleWordEntityEntity =
          await ApiService().saveSmallRoomBubbleWord(
        type: 1,
        userId: userId,
        dadaRoomId: dadaRoomId,
        bubbleId: bubbleId,
        content: editingController.text,
      );
      if (bubbleWordEntityEntity != null) {
        widget.callback(bubbleWordEntityEntity);
        Get.back();
      }
    }
  }
}
