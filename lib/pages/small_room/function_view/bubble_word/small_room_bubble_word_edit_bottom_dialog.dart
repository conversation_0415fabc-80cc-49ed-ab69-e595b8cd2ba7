import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/custom_tab_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';
import 'package:dada/pages/small_room/function_view/bubble_word/small_room_bubble_word_save_bottom_dialog.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/extensions/list_extension.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomBubbleWordEditBottomDialog extends StatefulWidget {
  final String tag;
  final String userId;
  final List<SmallRoomBubbleWordEntity>? bubbleDTOList;
  final Function(List<SmallRoomBubbleWordEntity>) changedCallback;

  const SmallRoomBubbleWordEditBottomDialog(
      {super.key,
      required this.changedCallback,
      required this.tag,
      required this.userId,
      this.bubbleDTOList});

  @override
  State<SmallRoomBubbleWordEditBottomDialog> createState() =>
      _SmallRoomBubbleWordEditBottomDialogState();
}

class _SmallRoomBubbleWordEditBottomDialogState
    extends State<SmallRoomBubbleWordEditBottomDialog>
    with TickerProviderStateMixin {
  late SmallRoomController controller;
  late TabController tabController;
  late PageController pageController;
  Rx<SmallRoomBubbleWordEntity?> currentPlayAudio = Rx(null);
  final audioPlayer = AudioPlayerUtils();
  final RxBool audioPlaying = false.obs;
  final RxDouble audioProgress = 0.0.obs;

  AnimationController? _animationController;
  Animation<double>? _progressAnimation;

  ///泡泡语
  List<SmallRoomBubbleWordEntity> bubbleWordsList = [];
  RxList<SmallRoomBubbleWordEntity> bubbleWordsTextList =
      <SmallRoomBubbleWordEntity>[].obs;
  RxList<SmallRoomBubbleWordEntity> bubbleWordsAudioList =
      <SmallRoomBubbleWordEntity>[].obs;

  @override
  void initState() {
    super.initState();

    tabController = TabController(length: 2, vsync: this);
    pageController = PageController(initialPage: 0);
    if (Get.isRegistered<SmallRoomController>(tag: widget.tag)) {
      controller = Get.find<SmallRoomController>(tag: widget.tag);
      bubbleWordsList = widget.bubbleDTOList ?? [];
      bubbleWordsTextList.value =
          bubbleWordsList.where((e) => e.type == 1).toList();
      bubbleWordsAudioList.value =
          bubbleWordsList.where((e) => e.type == 2).toList();
    }
    initAudioPlayer();
  }

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
      title: "",
      height: 296.h,
      titleLeftBtn: Container(
        height: 32.h,
        width: 200.w,
        alignment: Alignment.centerLeft,
        child: CustomTabBar(
          tabAlignment: TabAlignment.start,
          isScrollable: true,
          labelPadding: EdgeInsets.only(right: 15.w),
          indicatorPadding:
              EdgeInsets.only(left: 5.w, right: 5.w, top: 18.h, bottom: 8.h),
          controller: tabController,
          onTap: (index) {
            pageController.animateToPage(index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.ease);
          },
          tabs: const [
            Tab(
              text: "泡泡语",
            ),
            Tab(
              text: "互动语音",
            ),
          ],
        ),
      ),
      child: Expanded(
        child: Padding(
          padding: EdgeInsets.only(top: 0.h, bottom: 20.h),
          child: PageView(
            onPageChanged: (index) {
              tabController.animateTo(index);
            },
            controller: pageController,
            children: [
              _buildBubbleTextPage(),
              _buildBubbleAudioPage(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBubbleTextPage() {
    return Column(
      children: [
        _buildAddBubbleWidget(1),
        Expanded(
          child: _buildTextListContent(),
        ),
      ],
    );
  }

  Widget _buildBubbleAudioPage() {
    return Column(
      children: [
        _buildAddBubbleWidget(2),
        Expanded(
          child: _buildAudioListContent(),
        ),
      ],
    );
  }

  Widget _buildAddBubbleWidget(int type) {
    return Obx(() {
      bool showAddBubble = false;
      if ((type == 1 && bubbleWordsTextList.length < 5) ||
          (type == 2 && bubbleWordsAudioList.length < 5)) {
        showAddBubble = true;
      }
      return Visibility(
        visible: showAddBubble,
        child: GestureDetector(
          onTap: () async {
            if (type == 1) {
              ToastUtils.showBottomDialog(
                SmallRoomBubbleWordSaveDialog(
                  tag: widget.tag,
                  userId: widget.userId,
                  callback: (entity) {
                    bubbleWordsTextList.insert(0, entity);
                    updateCallbackData();
                  },
                ),
              );
            } else {
              goToAddBubbleAudioWord();
            }
          },
          child: Container(
            margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 10.h),
            height: 45.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFFEFFF8,
              borderRadius: BorderRadius.circular(5.r),
              border: Border.all(color: AppColors.colorFF58C75D),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ImageUtils.getImage(
                    Assets.imagesSmallRoomBubbleWordAdd, 16.w, 16.w),
                SizedBox(
                  width: 5.w,
                ),
                Text(
                  type == 1 ? "添加泡泡语" : "添加互动语音",
                  style: TextStyles.common(16.sp, AppColors.colorFF168C1A),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildTextListContent() {
    return Obx(() {
      if (bubbleWordsTextList.isEmpty) {
        return EmptyWidget(
          padding: EdgeInsets.only(top: 0.h),
          image: Assets.imagesSmallRoomBubbleWordDialogEmpty,
          imageWidth: 98.w,
          imageHeight: 98.w,
          content: "还没泡泡语，快来添加吧！",
          imageTextSpacing: 6.h,
          contentNormalTextStyle:
              TextStyles.common(16.sp, AppColors.colorFF2E5A2F),
        );
      }
      return ListView.separated(
        padding: EdgeInsets.zero,
        itemCount: bubbleWordsTextList.length,
        itemBuilder: (context, index) {
          SmallRoomBubbleWordEntity entity = bubbleWordsTextList[index];
          return _buildTextListItemWidget(entity, index);
        },
        separatorBuilder: (context, index) {
          return SizedBox(
            height: 5.h,
          );
        },
      );
    });
  }

  Widget _buildTextListItemWidget(
      SmallRoomBubbleWordEntity bubbleWordEntity, int index) {
    double textH = StringUtils.calculateTextHeight(bubbleWordEntity.content!,
        maxWidth: 273.w);
    double topPadding = textH > 30.h ? 10.h : 12.5.h;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      padding: EdgeInsets.only(left: 10.w, top: topPadding, bottom: topPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: AppColors.colorFFFDFDFD,
        border: Border.all(color: AppColors.colorFFC3CEB7),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: 273.w,
            ),
            child: Text(
              bubbleWordEntity.content!,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.common(14.sp, AppColors.colorFF666666,
                  h: textH > 30.h ? 1.5 : 1.0),
            ),
          ),
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(right: 10.w),
                child: InkWell(
                  onTap: () {
                    ToastUtils.showBottomDialog(
                      SmallRoomBubbleWordSaveDialog(
                        tag: widget.tag,
                        userId: widget.userId,
                        bubbleEntity: bubbleWordEntity,
                        callback: (entity) {
                          bubbleWordsTextList
                              .replaceRange(index, index + 1, [entity]);
                          updateCallbackData();
                        },
                      ),
                    );
                  },
                  child: ImageUtils.getImage(
                      Assets.imagesSmallRoomBubbleWordItemEdit, 13.w, 13.w),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(right: 5.w),
                child: GestureDetector(
                  onTap: () {
                    deleteBubbleWord(bubbleWordEntity, 1);
                  },
                  child: ImageUtils.getImage(
                      Assets.imagesSmallRoomBubbleWordItemDelete, 14.w, 14.w),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAudioListContent() {
    return Obx(() {
      if (bubbleWordsAudioList.isEmpty) {
        return EmptyWidget(
          padding: EdgeInsets.only(top: 15.h),
          image: Assets.imagesSmallRoomBubbleWordDialogEmpty,
          imageWidth: 98.w,
          imageHeight: 98.w,
          content: "还没泡泡语，快来添加吧！",
          imageTextSpacing: 6.h,
          contentNormalTextStyle:
              TextStyles.common(16.sp, AppColors.colorFF2E5A2F),
        );
      }
      return ListView.separated(
        itemCount: bubbleWordsAudioList.length,
        itemBuilder: (context, index) {
          SmallRoomBubbleWordEntity entity = bubbleWordsAudioList[index];
          return Container(
            height: 45.h,
            margin: EdgeInsets.symmetric(horizontal: 15.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: AppColors.colorFFFDFDFD,
              border: Border.all(color: AppColors.colorFFC3CEB7),
            ),
            child: Row(
              children: [
                _buildAudioPlayWidget(entity),
                const Spacer(),
                Padding(
                  padding: EdgeInsets.only(right: 5.w),
                  child: GestureDetector(
                    onTap: () {
                      deleteBubbleWord(entity, 2);
                    },
                    child: ImageUtils.getImage(
                        Assets.imagesSmallRoomBubbleWordItemDelete, 14.w, 14.w),
                  ),
                ),
              ],
            ),
          );
        },
        separatorBuilder: (context, index) {
          return SizedBox(
            height: 5.h,
          );
        },
      );
    });
  }

  Widget _buildAudioPlayWidget(SmallRoomBubbleWordEntity audioEntity) {
    return Obx(() {
      String btnImage = Assets.imagesSmallRoomBubbleWordAudioPlayBtn;
      double progressWidth = 220.w;
      int audioDuration = audioEntity.voiceLength ?? 0;
      if (audioEntity.bubbleId == currentPlayAudio.value?.bubbleId) {
        btnImage = audioPlaying.value
            ? Assets.imagesSmallRoomBubbleWordAudioPauseBtn
            : Assets.imagesSmallRoomBubbleWordAudioPlayBtn;
        progressWidth =
            audioPlaying.value ? (220.w * audioProgress.value) : 220.w;
      } else {
        btnImage = Assets.imagesSmallRoomBubbleWordAudioPlayBtn;
      }

      _progressAnimation =
          Tween<double>(begin: 0.0, end: audioProgress.value).animate(
        CurvedAnimation(
          parent: _animationController!,
          curve: Curves.linear,
        ),
      );
      return Row(
        children: [
          GestureDetector(
            onTap: () {
              if (audioPlaying.value == true) {
                if (audioEntity.bubbleId == currentPlayAudio.value?.bubbleId) {
                  audioPlayer.stop();
                  _animationController?.stop();
                } else {
                  audioPlayer.stop();
                  startPlayAudio(audioEntity);
                }
              } else {
                startPlayAudio(audioEntity);
              }
            },
            child: Padding(
              padding: EdgeInsets.only(left: 9.w),
              child: ImageUtils.getImage(btnImage, 30.w, 30.w),
            ),
          ),
          SizedBox(
            width: 15.w,
          ),
          Stack(
            children: [
              Container(
                width: 220.w,
                height: 3.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFBCBCBC),
                  borderRadius: BorderRadius.circular(1.5.h),
                ),
              ),
              Visibility(
                visible: audioPlaying.value == true &&
                    currentPlayAudio.value?.bubbleId == audioEntity.bubbleId,
                child: AnimatedBuilder(
                  animation: _progressAnimation!,
                  builder: (context, child) {
                    return Container(
                      width: audioPlaying.value == true
                          ? _progressAnimation!.value * progressWidth
                          : progressWidth,
                      height: 3.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFF65D06A),
                        borderRadius: BorderRadius.circular(1.5.h),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          SizedBox(
            width: 15.w,
          ),
          Text(
            "${audioDuration}s",
            style: TextStyles.common(16.sp, AppColors.colorFF666666),
          ),
        ],
      );
    });
  }

  void deleteBubbleWord(
      SmallRoomBubbleWordEntity bubbleEntity, int type) async {
    bool success = await ApiService()
        .deleteSmallRoomBubbleWord(bubbleId: bubbleEntity.bubbleId!);
    if (success) {
      if (type == 1) {
        bubbleWordsTextList.remove(bubbleEntity);
      } else {
        bubbleWordsAudioList.remove(bubbleEntity);
      }
      updateCallbackData();
    }
  }

  void updateCallbackData() {
    bubbleWordsList = [...bubbleWordsTextList, ...bubbleWordsAudioList];
    widget.changedCallback(bubbleWordsList);
  }

  ///添加语音
  void goToAddBubbleAudioWord() {
    Get.toNamed(GetRouter.audioRecord,
            parameters: {"title": "添加语音", "maxLength": "7", "minLength": "3"})
        ?.then((result) async {
      if (result != null) {
        int duration = result["duration"] ?? 0;
        String audioPath = result["filePath"] ?? "";
        String? audioUrl = await ApiService().uploadFile(audioPath);
        if (audioUrl != null) {
          SmallRoomBubbleWordEntity? bubbleWordEntity =
              await ApiService().saveSmallRoomBubbleWord(
            userId: widget.userId,
            type: 2,
            content: audioUrl,
            voiceLength: duration,
            dadaRoomId: controller.roomInfo?.room?.dadaRoomId ?? "",
          );
          if (bubbleWordEntity != null) {
            bubbleWordsAudioList.insert(0, bubbleWordEntity);
            updateCallbackData();
          }
        }
      }
    });
  }

  void startPlayAudio(SmallRoomBubbleWordEntity audioEntity) {
    currentPlayAudio.value = audioEntity;
    audioPlayer.setUrl(audioEntity.content!);
    audioPlayer.play();
    _animationController?.forward();
  }

  void initAudioPlayer() {
    audioPlayer.onInit();
    audioPlayer.durationStream?.listen((event) {
      if (currentPlayAudio.value?.voiceLength != null) {
        audioProgress.value =
            (event ?? 0) / (currentPlayAudio.value!.voiceLength!);
        audioProgress.value = min(1.0, audioProgress.value);
      }
    });
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.playing) {
        audioPlaying.value = true;
      } else {
        audioPlaying.value = false;
      }
    });

    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController?.stop();
    audioPlayer.stop();
    audioPlayer.dispose();
    super.dispose();
  }
}
