import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/components/widgets/sex_widget.dart';
import 'package:dada/components/widgets/user_medal_tag_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/small_room_see_me_entity.dart';
import 'package:dada/pages/small_room/function_view/see_me/small_room_see_me_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tolyui/tolyui.dart';

class SmallRoomSeeMeDialog extends StatefulWidget {
  const SmallRoomSeeMeDialog({super.key});

  @override
  State<SmallRoomSeeMeDialog> createState() => _SmallRoomSeeMeDialogState();
}

class _SmallRoomSeeMeDialogState extends State<SmallRoomSeeMeDialog> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<SmallRoomSeeMeController>(
      init: SmallRoomSeeMeController(),
      global: false,
      builder: (controller) {
        return GradientWidget(
          width: 320.w,
          height: 460.h,
          cornerRadius: 20.r,
          colors: const [AppColors.colorFFD2F6C0, Colors.white],
          stops: const [0, 0.56],
          child: Column(
            children: [
              _buildNavView(),
              Expanded(
                child: _buildListView(controller),
              )
            ],
          ),
        );
      },
      id: SmallRoomSeeMeController().refreshId,
    );
  }

  Widget _buildNavView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
            onPressed: null,
            icon: Icon(
              Icons.access_time,
              color: Colors.transparent,
              size: 22.w,
            )),
        Text(
          '小屋访客',
          style: TextStyles.common(18.sp, AppColors.colorFF3D3D3D),
        ),
        IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(
              Icons.close,
              size: 22.w,
              weight: 500,
              color: AppColors.colorFF666666,
            ))
      ],
    );
  }

  Widget _buildListView(SmallRoomSeeMeController controller) {
    return RefreshWidget.build(
        refreshController: controller.refreshController,
        onRefresh: () => controller.refreshData(),
        onLoadMore: () => controller.loadMoreData(),
        child: controller.data.isEmpty
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  EmptyWidget(
                    content: '访客正在路上哦~',
                    contentNormalTextStyle:
                        TextStyles.normal(16.sp, c: AppColors.colorFF666666),
                  ),
                  SizedBox(height: 40.h)
                ],
              )
            : ListView.builder(
                itemCount: controller.data.length,
                itemBuilder: (c, i) => _itemBuilder(c, i, controller)));
  }

  Widget _itemBuilder(BuildContext cxt, int i, SmallRoomSeeMeController c) {
    SmallRoomSeeMeEntity entity = c.data[i];
    final formatter = DateFormat('yyyy-MM-dd HH:mm');
    String visitTime = entity.visitTime == null
        ? ''
        : formatter.format(DateTime.parse('${entity.visitTime}'));

    return GestureDetector(
      onTap: () {
        Get.toNamed(GetRouter.userProfile,
            parameters: {"userId": entity.userId!});
      },
      child: Container(
        margin: EdgeInsets.only(
            left: 10.w, right: 10.w, top: i == 0 ? 12.h : 0.h, bottom: 10.h),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 15.h),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: const [
              BoxShadow(
                  offset: Offset(0, 1),
                  spreadRadius: 0,
                  blurRadius: 2,
                  color: Color(0xFF5F6F58))
            ]),
        child: Row(
          children: [
            AvatarWidget(size: 50, url: entity.avatar),
            SizedBox(width: 5.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '${entity.nickname}',
                      style: TextStyles.common(16.sp, AppColors.colorFF3D3D3D,
                          h: 21.sp / 16.sp),
                    ),
                    SizedBox(width: 5.w),
                    SexAgeWidget(sex: entity.sex ?? 0, age: entity.age ?? 0),
                    SizedBox(width: 5.w),
                    Padding(
                      padding: EdgeInsets.only(top: 2.5.h, bottom: 2.5.h),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Visibility(
                            visible: entity.isPioneer == 1,
                            child: TolyTooltip(
                              gap: 10.h,
                              triggerMode: TooltipTriggerMode.tap,
                              placement: Placement.top,
                              message: "乘风破浪的先行者。可以在各种活动中获得额外福利！",
                              child: ImageUtils.getImage(
                                  Assets.imagesUserTagXianXing, 20.w, 20.w),
                            ),
                          ),

                          Visibility(
                            visible: entity.isInitUser == 1,
                            child: Padding(
                              padding: EdgeInsets.only(left: 5.w),
                              child: TolyTooltip(
                                gap: 10.h,
                                triggerMode: TooltipTriggerMode.tap,
                                placement: Placement.top,
                                message: "我们珍视每一位元老，会增加好运哦！",
                                child: ImageUtils.getImage(
                                    Assets.imagesUserTagYuanLao, 20.w, 20.w),
                              ),
                            ),
                          ),

                          ///传火者勋章
                          Visibility(
                            visible: entity.isFireKeeper == 1,
                            child: Padding(
                              padding: EdgeInsets.only(left: 5.w),
                              child: TolyTooltip(
                                gap: 10.h,
                                triggerMode: TooltipTriggerMode.tap,
                                placement: Placement.top,
                                message: "传火者，在后续活动中可以获得特效奖励！",
                                child: ImageUtils.getImage(
                                    Assets.imagesUserTagHuo, 20.w, 20.w),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5.h),
                Text(
                  visitTime,
                  style: TextStyles.normal(12.sp,
                      c: AppColors.colorFF999999, h: 20.sp / 12.sp),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
