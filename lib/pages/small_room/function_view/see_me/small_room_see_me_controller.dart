import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/small_room_see_me_entity.dart';
import 'package:dada/services/network/api_service.dart';

class SmallRoomSeeMeController
    extends ListPageController<SmallRoomSeeMeEntity, SmallRoomSeeMeController> {
  SmallRoomSeeMeController();

  @override
  Future<List<SmallRoomSeeMeEntity>?> loadData(int page) async {
    List<SmallRoomSeeMeEntity>? list =
        await ApiService().getSmallRoomSeeMeList(pageIndex: pageIndex);
    return list;
  }
}
