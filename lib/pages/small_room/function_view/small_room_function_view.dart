import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/small_room_barrage_item_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/pages/small_room/function_view/barrage/small_room_barrage_widget.dart';
import 'package:dada/pages/small_room/function_view/other/small_room_card_box_bottom_dialog.dart';
import 'package:dada/pages/small_room/function_view/other/small_room_dazi_apply_bottom_dialog.dart';
import 'package:dada/pages/small_room/function_view/barrage/small_room_send_barrage_dialog.dart';
import 'package:dada/pages/small_room/function_view/other/small_room_social_habit_bottom_dialog.dart';
import 'package:dada/pages/small_room/function_view/other/small_room_task_bottom_dialog.dart';
import 'package:dada/pages/small_room/function_view/see_me/small_room_see_me_dialog.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/pages/small_room/small_room_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:sp_util/sp_util.dart';

class SmallRoomFunctionView extends StatefulWidget {
  final String tag;

  const SmallRoomFunctionView({super.key, required this.tag});

  @override
  State<SmallRoomFunctionView> createState() => _SmallRoomFunctionViewState();
}

class _SmallRoomFunctionViewState extends State<SmallRoomFunctionView> {
  late SmallRoomController controller;
  // final _barrageKey = GlobalKey<SmallRoomBarrageWidgetState>();
  RxBool isOpenFriendListDialog = false.obs;
  RxBool closedMailMsgBubble = false.obs;
  Rx<UserInfoEntity?> userInfo = Rx<UserInfoEntity?>(null);
  final GlobalKey _one = GlobalKey();
  bool hasShow = SpUtil.getBool("showcase_small_room") ?? false;

  @override
  void initState() {
    super.initState();

    controller = Get.find<SmallRoomController>(tag: widget.tag);

    if (!hasShow) {
      ShowcaseView.register(scope: "small_room_function_view");
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ShowcaseView.getNamed("small_room_function_view")
            .startShowCase([_one], delay: const Duration(milliseconds: 200));
        SpUtil.putBool("showcase_small_room", true);
      });
    }

    EventBusEngine.eventBus.on().listen((event) {
      EventEntity entity = event;
      if (entity.event == BusEvent.userInfoUpdate) {
        userInfo.value = UserService().user;
        controller.userInfo = userInfo.value;
      } else if (entity.event == BusEvent.smallRoomMailHasNewMsg) {
        closedMailMsgBubble.value = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SmallRoomController>(
      init: controller,
      id: SmallRoomController.KFuncViewBuildID,
      tag: widget.tag,
      builder: (controller) {
        userInfo.value = controller.userInfo;
        return Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    _buildUserInfoView(),
                    // _buildBarrageWidget(),
                    _leftAndRightFunctionMenuWidget(),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildBottomLeftBtn(),
                    _buildBottomRightBtn(),
                  ],
                ),
              ],
            ),
            // _buildNoticeWidget(),

            ///信箱气泡
            _buildMailBubble(),
          ],
        );
      },
    );
  }

  ///用户信息
  Widget _buildUserInfoView() {
    return Padding(
      padding: EdgeInsets.only(top: 45.h, left: 15.w, right: 15.w),
      child: Row(
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  Get.toNamed(GetRouter.userProfile,
                      parameters: {"userId": controller.userID!});
                },
                child: Obx(
                  () => ImageUtils.getImage(
                      userInfo.value?.avatar ?? "", 28.w, 28.w,
                      fit: BoxFit.cover,
                      showPlaceholder: true,
                      placeholder: Assets.imagesAvatarPlaceholder,
                      radius: 14.w,
                      showBorder: true),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 4.w),
                child: GestureDetector(
                  onTap: () {
                    if (controller.allFriendsList.isNotEmpty == true) {
                      isOpenFriendListDialog.value =
                          !isOpenFriendListDialog.value;
                      if (isOpenFriendListDialog.value == true) {
                        showFriendListDialog();
                      }
                    }
                  },
                  child: Row(
                    children: [
                      Obx(
                        () => Text(
                          userInfo.value?.nickname ?? "",
                          style: TextStyles.common(16.sp, Colors.white),
                        ),
                      ),
                      Obx(
                        () => Visibility(
                          visible: controller.isMyRoom() &&
                              controller.allFriendsList.isNotEmpty,
                          child: Padding(
                            padding: EdgeInsets.only(left: 6.w),
                            child: Transform.rotate(
                              angle: isOpenFriendListDialog.value == true
                                  ? -pi / 2
                                  : pi / 2,
                              child: ImageUtils.getImage(
                                  Assets.imagesSmallRoomOwnerNicknameArrow,
                                  10.w,
                                  10.w),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const Spacer(),
          _buildUserInfoRightBtn(),
        ],
      ),
    );
  }

  Widget _buildUserInfoRightBtn() {
    if (!controller.isSelf) {
      return GestureDetector(
        onTap: () {
          Get.back();
        },
        child: Row(
          children: [
            ImageUtils.getImage(Assets.imagesSmallRoomExitBtn, 20.w, 20.w),
            Padding(
              padding: EdgeInsets.only(left: 5.w),
              child: Text(
                "返回",
                style: TextStyles.common(14.sp, Colors.white),
              ),
            ),
          ],
        ),
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(right: 5.w),
            child: Text(
              "允许小屋访客",
              style: TextStyles.common(14.sp, Colors.white),
            ),
          ),
          Obx(
            () => GestureDetector(
              onTap: () async {
                bool success = await ApiService().updateRoomDaziApply(
                    isAccept:
                        controller.isReceiveDaziApply.value == true ? 0 : 1);
                if (success) {
                  controller.isReceiveDaziApply.value =
                      !controller.isReceiveDaziApply.value;
                }
              },
              child: ImageUtils.getImage(
                  controller.isReceiveDaziApply.value
                      ? Assets.imagesSmallRoomSwitchBtnOpen
                      : Assets.imagesSmallRoomSwitchBtnClose,
                  40.w,
                  20.h),
            ),
          ),
        ],
      );
    }
  }

  /* Widget _buildBarrageWidget() {
    List<SmallRoomBarrageItemEntity> list = [];
    if (controller.roomInfo?.msgList?.isNotEmpty == true) {
      list.addAll(controller.roomInfo!.msgList!);
    }
    return Visibility(
      visible: list.isNotEmpty,
      child: SmallRoomBarrageWidget(key: _barrageKey, barrageList: list),
    );
  } */

  Widget _leftAndRightFunctionMenuWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _leftFunctionMenuWidget(),
        _rightFunctionMenuWidget(),
      ],
    );
  }

  Widget _leftFunctionMenuWidget() {
    if (controller.userInfo == null) {
      return Container();
    }
    int? relation = controller.userInfo?.ties;
    bool isEndTime = false;
    if (relation == 5) {
      isEndTime = controller.userInfo?.beginKnowdDate != null &&
          DateTime.now()
              .isAfter(DateTime.parse(controller.userInfo!.beginKnowdDate!));
    }
    return controller.isSelf
        ? Container(
            width: 50.w,
            height: 135.h,
            margin: EdgeInsets.only(top: 10.h, left: 15.w),
            padding: EdgeInsets.symmetric(vertical: 10.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(60.r),
              color: Colors.black.withOpacity(0.3),
            ),
            child: Column(
              children: [
                _buildFunctionMenuBtn(
                  title: "任务",
                  icon: Assets.imagesSmallRoomMenuTaskIcon,
                  onTap: () {
                    if (controller.roomInfo?.room?.dadaRoomId != null) {
                      ToastUtils.showBottomDialog(SmallRoomTaskBottomDialog(
                          roomId: controller.roomInfo!.room!.dadaRoomId!));
                    }
                  },
                ),
                SizedBox(
                  height: 10.h,
                ),
                _buildFunctionMenuBtn(
                  title: "背包",
                  icon: Assets.imagesSmallRoomMenuBackpackIcon,
                  onTap: () {
                    Get.toNamed(GetRouter.backpack)?.then((value) {
                      if (value != null) {
                        controller
                            .update([SmallRoomController.KRoleListBuildID]);
                      }
                    });
                  },
                ),
              ],
            ),
          )
        : controller.userInfo?.isDaziRelation() == true ||
                (relation == 5 && isEndTime == false)
            ? Container()
            : Container(
                margin: EdgeInsets.only(top: 10.h, left: 12.w),
                height: (relation == 5 && isEndTime == true) ? 58.h : 69.h,
                width: 64.w,
                child: GestureDetector(
                  onTap: () async {
                    if (controller.userInfo != null) {
                      if (relation == 5 && isEndTime == true) {
                        ToastUtils.showBottomDialog(
                          SmallRoomDaziApplyDialog(
                              userId: controller.userInfo!.id!,
                              userFriendType:
                                  controller.userInfo!.socialState ?? 2),
                        );
                      } else {
                        bool success = await ApiService()
                            .startKnown(userId: controller.userInfo!.id!);
                        if (success) {
                          ToastUtils.showToast("开始了解发送成功");
                          await controller.getUserInfo(showLoading: false);
                          await controller.getRoomInfo(showLoading: false);
                        }
                      }
                    }
                  },
                  child: Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      ImageUtils.getImage(
                          Assets.imagesSmallRoomDaziApplyBtn, 42.w, 42.w),
                      Positioned(
                        bottom: 0,
                        child: Container(
                          width: 64.w,
                          height: (relation == 5 && isEndTime) ? 20.h : 30.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: AppColors.colorFFF2F6E1,
                          ),
                          child: Text(
                            (relation == 5 && isEndTime == true)
                                ? "申请搭子"
                                : "脸熟一下", //进驻小屋\n(开始了解)
                            textAlign: TextAlign.center,
                            style: TextStyles.common(
                                12.sp, AppColors.colorFF986328,
                                h: 1.3),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
  }

  Widget _rightFunctionMenuWidget() {
    bool isSelf = controller.isSelf;
    return Container(
      width: 50.w,
      margin: EdgeInsets.only(top: 10.h, right: 15.w),
      padding: EdgeInsets.symmetric(vertical: 15.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(60.r),
        color: Colors.black.withOpacity(0.3),
      ),
      child: Column(
        children: [
          if (isSelf)
            _buildFunctionMenuBtn(
              title: "习惯",
              icon: Assets.imagesSmallRoomMenuHabitIcon,
              onTap: () {
                ToastUtils.showBottomDialog(
                  SmallRoomSocialHabitBottomDialog(
                      userInfoEntity: controller.userInfo!),
                );
              },
              padding: EdgeInsets.only(bottom: 10.h),
            ),
          isSelf
              ? _buildFunctionMenuBtn(
                  title: "探索",
                  icon: Assets.imagesSmallRoomMenuExploreIcon,
                  onTap: () {
                    Get.toNamed(GetRouter.explorePage);
                  },
                  padding: EdgeInsets.only(bottom: 10.h),
                )
              : Container(),
          if (hasShow)
            _buildFunctionMenuBtn(
              title: "卡片盒",
              icon: Assets.imagesSmallRoomMenuCardBoxIcon,
              onTap: () {
                if (controller.userInfo != null) {
                  ToastUtils.showBottomDialog(
                    SmallRoomCardBoxBottomDialog(
                        userInfoEntity: controller.userInfo!),
                  );
                }
              },
            ),
          if (!hasShow)
            Showcase(
                key: _one,
                description:
                    "如果你也偶尔玩游戏，可以在这里保存记录你心爱的外观、战绩等，也可以创建一个角色信息卡，大家会对你的了解更为直观哦！~",
                child: _buildFunctionMenuBtn(
                  title: "卡片盒",
                  icon: Assets.imagesSmallRoomMenuCardBoxIcon,
                  onTap: () {
                    if (controller.userInfo != null) {
                      ToastUtils.showBottomDialog(
                        SmallRoomCardBoxBottomDialog(
                            userInfoEntity: controller.userInfo!),
                      );
                    }
                  },
                )),
          _buildFunctionMenuBtn(
            title: "水晶球",
            icon: Assets.imagesSmallRoomMenuCrystalBallIcon,
            padding: EdgeInsets.only(top: 10.h),
            onTap: () {
              controller.checkGoToCrystalBallPage(widget.tag);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomLeftBtn() {
    bool isSelf = controller.isSelf;
    return Column(
      children: [
        if (isSelf)
          Padding(
            padding: EdgeInsets.only(
              left: 15.w,
              bottom: 20.h,
            ),
            child: _buildBottomBtn(
              icon: Assets.imagesSmallRoomSeeme,
              title: "谁看过我",
              onTap: () {
                ToastUtils.showDialog(
                    dialog: const SmallRoomSeeMeDialog(),
                    barrierDismissible: false);
              },
            ),
          ),
        /* Padding(
          padding: EdgeInsets.only(
            left: 15.w,
            bottom: 10.h + (isSelf ? 0 : ScreenUtil().bottomBarHeight),
          ),
          child: _buildBottomBtn(
            icon: Assets.imagesSmallRoomDanmu,
            title: "弹幕",
            onTap: () {
              if (controller.roomInfo?.room?.dadaRoomId != null) {
                ToastUtils.showDialog(
                  dialog: SmallRoomSendBarrageDialog(
                      roomId: controller.roomInfo!.room!.dadaRoomId!),
                ).then((value) async {
                  if (value != null) {
                    if (!(controller.roomInfo?.msgList?.isNotEmpty == true)) {
                      SmallRoomBarrageItemEntity barrage =
                          SmallRoomBarrageItemEntity();
                      barrage.content = value;
                      barrage.avatar = UserService().user?.avatar;
                      controller.roomInfo?.msgList = [barrage];
                    }
                    controller.update([SmallRoomController.KFuncViewBuildID]);
                    EventBusEngine.fire(
                        event: BusEvent.smallRoomUpdateBarrage, ext: value);
                  }
                });
              }
            },
          ),
        ) */
      ],
    );
  }

  Widget _buildBottomRightBtn() {
    bool isSelf = controller.isSelf;
    if (!isSelf) {
      return Container();
    }
    return Padding(
      padding: EdgeInsets.only(
        right: 15.w,
        bottom: 10.h + (isSelf ? 0 : ScreenUtil().bottomBarHeight),
      ),
      child: Column(
        children: [
          _buildBottomBtn2(
            icon: Assets.imagesSmallRoomDaziMatchBtn,
            title: "扩列",
            onTap: () {
              Get.toNamed(GetRouter.matchDada);
            },
          ),
          // SizedBox(
          //   height: 15.h,
          // ),
          // _buildBottomBtn(
          //   icon: Assets.imagesSmallRoomDaziTeamMatchBtn,
          //   title: "搭搭",
          //   onTap: () {
          //     Get.toNamed(GetRouter.matchAssemblePlace);
          //   },
          // ),
        ],
      ),
    );
  }

  ///---------------------------------------------------------------------------

  Widget _buildFunctionMenuBtn(
      {required String icon,
      required String title,
      required Function() onTap,
      EdgeInsets? padding}) {
    return Container(
      padding: padding,
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          children: [
            ImageUtils.getImage(icon, 34.w, 34.w),
            SizedBox(
              height: 2.h,
            ),
            Text(
              title,
              style: TextStyles.common(14.sp, Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBtn(
      {required String icon, required String title, Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          ImageUtils.getImage(icon, 45.w, 45.h),
          SizedBox(
            height: 5.h,
          ),
          Text(
            title,
            style: TextStyles.common(14.sp, Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBtn2(
      {required String icon, required String title, Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          ImageUtils.getImage(icon, 60.w, 56.h),
          SizedBox(
            height: 5.h,
          ),
          Text(
            title,
            style: TextStyles.common(14.sp, Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildMailBubble() {
    return Obx(
      () {
        return Positioned(
          left: 92.w,
          top: 414.h + ScreenUtil().statusBarHeight - 25.h,
          child: Visibility(
            visible: Get.find<MainController>().mailUnreadCount.value > 0 &&
                closedMailMsgBubble.value == false &&
                controller.isSelf,
            child: Padding(
              padding: EdgeInsets.only(left: 7.w),
              child: Container(
                padding: EdgeInsets.only(
                    left: 12.w, right: 10.w, top: 10.h, bottom: 16.h),
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Assets.imagesSmallRoomBubbleLeftBrew),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      "您有新的邮件",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyles.common(14.sp, AppColors.colorFF986328,
                          h: 1.2),
                    ),
                    SizedBox(
                      width: 3.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        closedMailMsgBubble.value = true;
                      },
                      child: Icon(
                        Icons.close,
                        size: 16.w,
                        color: AppColors.colorFFAC9A86,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void showFriendListDialog() {
    Get.dialog(
      useSafeArea: false,
      barrierColor: Colors.transparent,
      Stack(
        children: [
          Positioned(
            left: 15.w,
            top: 26.h + ScreenUtil().statusBarHeight,
            child: Visibility(
              visible: controller.allFriendsList.isNotEmpty &&
                  isOpenFriendListDialog.value == true,
              child: Container(
                width: 128.w,
                height: 162.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  color: Colors.black.withOpacity(0.5),
                ),
                child: ListView.separated(
                  padding: EdgeInsets.only(left: 10.w, top: 10.h, bottom: 15.h),
                  itemBuilder: (context, index) {
                    UserInfoEntity userInfo = controller.allFriendsList[index];
                    return GestureDetector(
                      onTap: () {
                        Get.to(() => SmallRoomPage(userId: userInfo.id!));
                      },
                      child: Container(
                        height: 30.h,
                        alignment: Alignment.centerLeft,
                        child: Row(
                          children: [
                            ImageUtils.getImage(
                              userInfo.avatar ?? "",
                              30.w,
                              30.w,
                              radius: 15.w,
                              borderColor: Colors.white,
                              showBorder: true,
                              showPlaceholder: true,
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 4.w),
                              constraints: BoxConstraints(maxWidth: 80.w),
                              child: Text(
                                userInfo.nickname ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyles.common(16.sp, Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 5.h,
                    );
                  },
                  itemCount: controller.allFriendsList.length,
                ),
              ),
            ),
          ),
        ],
      ),
    ).then((value) {
      isOpenFriendListDialog.value = false;
    });
  }
}
