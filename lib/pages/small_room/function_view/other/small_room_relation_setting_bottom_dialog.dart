import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/radio_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomRelationSettingBottomDialog extends StatefulWidget {
  final String userFriendId;
  final int userFriendType;
  final String? friendDaziRemark;
  final Function(dynamic)? callback;

  const SmallRoomRelationSettingBottomDialog(
      {super.key,
      required this.userFriendId,
      required this.userFriendType,
      this.friendDaziRemark,
      this.callback});

  @override
  State<SmallRoomRelationSettingBottomDialog> createState() =>
      _SmallRoomRelationSettingBottomDialogState();
}

class _SmallRoomRelationSettingBottomDialogState
    extends State<SmallRoomRelationSettingBottomDialog> {
  TextEditingController editingController = TextEditingController();
  FocusNode focusNode = FocusNode();
  RxBool editable = false.obs;
  RxInt selectedIndex = 0.obs;

  @override
  void initState() {
    super.initState();

    editingController.text = widget.friendDaziRemark?.isNotEmpty == true
        ? widget.friendDaziRemark!
        : "";
    selectedIndex.value = widget.userFriendType - 1;
  }

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
      title: "关系设置",
      height: 380.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDaziRemarkAddWidget(),
          _buildDaziSelectWidget(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildDaziRemarkAddWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => Container(
            alignment: Alignment.center,
            height: 40.h,
            margin: EdgeInsets.only(top: 12.h, left: 50.w, right: 45.w),
            child: widget.friendDaziRemark?.isNotEmpty == true ||
                    editable.value == true
                ? _buildEditRemarkWidget()
                : _buildAddRemarkWidget(),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 5.h, left: 50.w),
          child: Text(
            "如：王者搭子、购物搭子（最多输入两个字）",
            style: TextStyles.common(14.sp, AppColors.colorFF999999),
          ),
        ),
      ],
    );
  }

  Widget _buildEditRemarkWidget() {
    return Container(
      height: 40.h,
      alignment: Alignment.center,
      child: editable.value == true
          ? Row(
              children: [
                Container(
                  width: 230.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: AppColors.colorFFFDFDFD,
                    borderRadius: BorderRadius.circular(5.r),
                    border: Border.all(color: AppColors.colorFFC3CEB7),
                  ),
                  child: CustomTextField.build(
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                    controller: editingController,
                    showSuffixIcon: true,
                    focusNode: focusNode,
                    maxLength: 2,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 8.w),
                  child: Text(
                    "搭子",
                    style: TextStyles.normal(16.sp),
                  ),
                ),
              ],
            )
          : Container(
              height: 40.h,
              decoration: BoxDecoration(
                color: AppColors.colorFFFDFDFD,
                borderRadius: BorderRadius.circular(5.r),
                border: Border.all(color: AppColors.colorFFC3CEB7),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10.w),
                    child: Text(
                      widget.friendDaziRemark ?? "",
                      style: TextStyles.common(
                        14.sp,
                        AppColors.colorFF666666,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 10.w),
                    child: GestureDetector(
                      onTap: () {
                        editable.value = true;
                        focusNode.requestFocus();
                      },
                      child: ImageUtils.getImage(
                          Assets.imagesCommonEditBtnGreen, 13.w, 13.w),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildAddRemarkWidget() {
    return Container(
      height: 45.h,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.colorFF58C75D),
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: GestureDetector(
        onTap: () {
          editable.value = true;
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ImageUtils.getImage(
                Assets.imagesSmallRoomDaziSettingBottomDialogAdd, 16.w, 16.w),
            SizedBox(
              width: 5.w,
            ),
            Text(
              "添加搭子名称",
              style: TextStyles.common(16.sp, AppColors.colorFF168C1A),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDaziSelectWidget() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 18.5.h),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: 8.h), // 添加间距
                  child: Row(
                    children: [
                      Obx(
                            () => _buildRadioBtnWidget(
                          title: "纯搭",
                          content: "纯纯的某领域伙伴，啥都不带掺的~哈哈",
                          selected: selectedIndex.value == 0,
                          onTap: () {
                            selectedIndex.value = 0;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 8.h), // 添加间距
                  child: Row(
                    children: [
                      Obx(
                            () => _buildRadioBtnWidget(
                          title: "浅搭",
                          content: "淡淡的伙伴关系，点到即止",
                          selected: selectedIndex.value == 1,
                          onTap: () {
                            selectedIndex.value = 1;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 8.h), // 添加间距
                  child: Row(
                    children: [
                      Obx(
                            () => _buildRadioBtnWidget(
                          title: "浅搭(随缘)",
                          content: "淡淡的伙伴关系，随心所欲",
                          selected: selectedIndex.value == 2,
                          onTap: () {
                            selectedIndex.value = 2;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 8.h), // 添加间距
                  child: Row(
                    children: [
                      Obx(
                            () => _buildRadioBtnWidget(
                          title: "深搭",
                          content: "俺们关系很好咯，嘿嘿",
                          selected: selectedIndex.value == 3,
                          onTap: () {
                            selectedIndex.value = 3;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
  }

  Widget _buildRadioBtnWidget(
      {required String title,
        required String content,
        required bool selected,
        required Function() onTap}) {
    return Container(
      padding: EdgeInsets.only(left: 50.w),
      child: Row(
        mainAxisSize: MainAxisSize.min, // 确保内容紧挨
        children: [
          RadioButton(
            text: title,
            radioSize: 18.w,
            spacing: 6.w,
            selected: selected,
            onTap: onTap,
          ),
          const SizedBox(width: 8), // 添加间距
          Text(content, style: const TextStyle(color: Colors.grey, fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      height: 44.h,
      topMargin: 15.h,
      horizontalMargin: 116.w,
      bottomMargin: 10.h,
      title: "确定",
      normalImage: Assets.imagesCommonGradientBtnBg,
      onTap: () async {
        String? daName;
        if (editingController.text.isNotEmpty) {
          daName = editingController.text;
        }
        bool success = await ApiService().updateDaziRelation(
          userFriendId: widget.userFriendId,
          friendType: selectedIndex.value + 1,
          daName: daName,
        );
        if (success) {
          //Map<String, dynamic> result = {"friendType": selectedIndex.value + 1};
          Map<String, dynamic> result = {};
          if (daName?.isNotEmpty == true) {
            result["daName"] = daName;
          }
          if (widget.callback != null) {
            widget.callback!.call(result);
          }
          if(widget.userFriendType - 1!=selectedIndex.value){
            //提示发送成功
            ToastUtils.showToast("发送请求成功,请等待对方同意！");
          }else{
            ToastUtils.showToast("修改成功！");
          }
          Get.back();
        }
      },
    );
  }
}
