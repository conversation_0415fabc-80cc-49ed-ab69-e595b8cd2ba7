import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_card_box_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomCardBoxBottomDialog extends StatefulWidget {
  final UserInfoEntity userInfoEntity;

  const SmallRoomCardBoxBottomDialog({super.key, required this.userInfoEntity});

  @override
  State<SmallRoomCardBoxBottomDialog> createState() =>
      _SmallRoomCardBoxBottomDialogState();
}

class _SmallRoomCardBoxBottomDialogState
    extends State<SmallRoomCardBoxBottomDialog> {
  RxList<UserCardBoxEntity> cardBoxList = <UserCardBoxEntity>[].obs;

  @override
  void initState() {
    super.initState();

    loadUserCardBoxList();
  }

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
      height: 256.h,
      title: "${widget.userInfoEntity.nickname} 卡片盒",
      child: Padding(
        padding: EdgeInsets.only(top: 20.h),
        child: Obx(() {
          int idCardNum = 0;
          int shaiouCardNum = 0;
          int dressCardNum = 0;
          int strengthCardNum = 0;

          List<UserCardBoxEntity?>? idCardList =
              cardBoxList.where((e) => e.cardType == "1").toList();
          List<UserCardBoxEntity?>? shaiouCardList =
              cardBoxList.where((e) => e.cardType == "2").toList();
          List<UserCardBoxEntity?>? dressCardList =
              cardBoxList.where((e) => e.cardType == "3").toList();
          List<UserCardBoxEntity?>? strengthCardList =
              cardBoxList.where((e) => e.cardType == "4").toList();
          if (idCardList.isNotEmpty == true) {
            UserCardBoxEntity idCard = idCardList.first!;
            idCardNum = idCard.cardNum ?? 0;
          }
          if (shaiouCardList.isNotEmpty == true) {
            UserCardBoxEntity shaiouCard = shaiouCardList.first!;
            shaiouCardNum = shaiouCard.cardNum ?? 0;
          }
          if (dressCardList.isNotEmpty == true) {
            UserCardBoxEntity dressCard = dressCardList.first!;
            dressCardNum = dressCard.cardNum ?? 0;
          }
          if (strengthCardList.isNotEmpty == true) {
            UserCardBoxEntity strengthCard = strengthCardList.first!;
            strengthCardNum = strengthCard.cardNum ?? 0;
          }
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              ///身份卡
              _cardItemWidget(
                size: Size(75.w, 110.h),
                margin: EdgeInsets.only(
                    left: 3.5.w, right: 1.5.w, top: 10.h, bottom: 8.h),
                image: Assets.imagesUserProfileCardIdentity,
                onTap: () {
                  Get.toNamed(GetRouter.userIdCard,
                      arguments: widget.userInfoEntity,
                      parameters: {"cardType": "1"});
                },
                cardName: "身份卡",
                cardNum: idCardNum,
              ),

              ///晒欧卡
              _cardItemWidget(
                size: Size(88.w, 124.h),
                margin: EdgeInsets.only(bottom: 4.h),
                image: shaiouCardNum == 0
                    ? Assets.imagesUserProfileCardShaiouEmpty
                    : Assets.imagesUserProfileCardShaiou,
                onTap: () {
                  Get.toNamed(
                    GetRouter.otherCard,
                    arguments: widget.userInfoEntity,
                    parameters: {"title": S.current.goodCard, "cardType": "2"},
                  );
                },
                cardName: S.current.goodCard,
                cardNum: shaiouCardNum,
              ),

              ///穿搭卡
              _cardItemWidget(
                size: Size(82.w, 118.h),
                margin: EdgeInsets.only(top: 6.h, bottom: 4.h),
                image: dressCardNum == 0
                    ? Assets.imagesUserProfileCardDressEmpty
                    : Assets.imagesUserProfileCardDress,
                onTap: () {
                  Get.toNamed(
                    GetRouter.otherCard,
                    arguments: widget.userInfoEntity,
                    parameters: {"title": S.current.dressCard, "cardType": "3"},
                  );
                },
                cardName: S.current.dressCard,
                cardNum: dressCardNum,
              ),

              ///高光卡
              _cardItemWidget(
                size: Size(82.w, 124.h),
                margin: EdgeInsets.only(top: 3.h, left: 2.w),
                image: strengthCardNum == 0
                    ? Assets.imagesUserProfileCardStrengthEmpty
                    : Assets.imagesUserProfileCardStrength,
                onTap: () {
                  Get.toNamed(
                    GetRouter.otherCard,
                    arguments: widget.userInfoEntity,
                    parameters: {
                      "title": S.current.strengthCard,
                      "cardType": "4"
                    },
                  );
                },
                cardName: S.current.strengthCard,
                cardNum: strengthCardNum,
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _cardItemWidget(
      {required Size size,
      required EdgeInsets margin,
      required String image,
      required Function() onTap,
      required String cardName,
      required int cardNum}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            margin: margin,
            width: size.width,
            height: size.height,
            child: ImageUtils.getImage(image, size.width, size.height,
                fit: BoxFit.fill),
          ),
          Padding(
            padding: EdgeInsets.only(top: 2.h),
            child: Text(
              "$cardName($cardNum)",
              style: TextStyles.common(14.sp, AppColors.colorFF2D6D0B),
            ),
          ),
        ],
      ),
    );
  }

  void loadUserCardBoxList() async {
    cardBoxList.value =
        await ApiService().getUserCardBoxCardCount(widget.userInfoEntity.id!) ??
            [];
  }
}
