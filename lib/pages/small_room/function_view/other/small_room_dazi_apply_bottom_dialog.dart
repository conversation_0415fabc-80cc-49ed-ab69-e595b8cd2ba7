import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/radio_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomDaziApplyDialog extends StatefulWidget {
  final String userId;
  final int userFriendType;
  final Function(bool)? callback;

  const SmallRoomDaziApplyDialog(
      {super.key,
      required this.userId,
      required this.userFriendType,
      this.callback});

  @override
  State<SmallRoomDaziApplyDialog> createState() =>
      _SmallRoomDaziApplyDialogState();
}

class _SmallRoomDaziApplyDialogState extends State<SmallRoomDaziApplyDialog> {
  RxInt selectedIndex = 0.obs;

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
        height: 337.h, title: "搭子申请", child: _buildContentWidget());
  }

  Widget _buildContentWidget() {
    return Container(
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 12.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.r),
          topRight: Radius.circular(15.r),
        ),
        color: Colors.white,
      ),
      child: Column(
        children: [
          _buildDaziSelectWidget(),
          Visibility(
            visible: widget.userFriendType < 3 && selectedIndex.value > 1,
            child: Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Text(
                "对方比较慢热，还是从纯搭、浅搭开始吧。",
                style: TextStyles.common(14.sp, AppColors.colorFFEE4141),
              ),
            ),
          ),
          CommonGradientBtn(
            title: "发送申请",
            height: 44.h,
            topMargin: 10.h,
            horizontalMargin: 101.w,
            normalImage: Assets.imagesCommonGradientBtnBg,
            onTap: () {
              sendApplyDazi();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDaziSelectWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 12.5.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 8.h), // 添加间距
            child: Row(
              children: [
                Obx(
                  () => _buildRadioBtnWidget(
                    title: "纯搭",
                    content: "纯纯的某领域伙伴，啥都不带掺的~哈哈",
                    selected: selectedIndex.value == 0,
                    onTap: () {
                      selectedIndex.value = 0;
                      setState(() {});
                    },
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 8.h), // 添加间距
            child: Row(
              children: [
                Obx(
                  () => _buildRadioBtnWidget(
                    title: "浅搭",
                    content: "淡淡的伙伴关系，点到即止",
                    selected: selectedIndex.value == 1,
                    onTap: () {
                      selectedIndex.value = 1;
                      setState(() {});
                    },
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 8.h), // 添加间距
            child: Row(
              children: [
                Obx(
                  () => _buildRadioBtnWidget(
                    title: "浅搭(随缘)",
                    content: "淡淡的伙伴关系，随心所欲",
                    selected: selectedIndex.value == 2,
                    onTap: () {
                      selectedIndex.value = 2;
                      setState(() {});
                    },
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 8.h), // 添加间距
            child: Row(
              children: [
                Obx(
                  () => _buildRadioBtnWidget(
                    title: "深搭",
                    content: "俺们关系很好咯，嘿嘿",
                    selected: selectedIndex.value == 3,
                    onTap: () {
                      selectedIndex.value = 3;
                      setState(() {});
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadioBtnWidget(
      {required String title,
      required String content,
      required bool selected,
      required Function() onTap}) {
    return Container(
      padding: EdgeInsets.only(left: 16.w, right: 5.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // 确保内容紧挨
        children: [
          RadioButton(
            text: title,
            radioSize: 18.w,
            spacing: 6.w,
            selected: selected,
            onTap: onTap,
          ),
          const SizedBox(width: 8), // 添加间距
          Container(
            constraints: BoxConstraints(maxWidth: 250.w),
            child: Text(
              content,
              maxLines: 2,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  void sendApplyDazi() async {
    int friendType = UserRelationType.simpleDa.index;
    if (selectedIndex.value == 0) {
      friendType = UserRelationType.pureDa.index;
    } else if (selectedIndex.value == 1) {
      friendType = UserRelationType.simpleDa.index;
    } else if (selectedIndex.value == 2) {
      friendType = UserRelationType.moreDa.index;
    } else if (selectedIndex.value == 3) {
      friendType = UserRelationType.closeDa.index;
    }
    bool success = await ApiService()
        .applyDazi(userId: widget.userId, friendType: friendType);
    if (success) {
      widget.callback?.call(success);
      ToastUtils.showToast("搭子申请发送成功，请耐心等待对方处理请求");
      Get.back();
    }
  }
}
