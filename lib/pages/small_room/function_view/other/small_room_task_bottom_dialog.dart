import 'dart:io';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/small_room_task_entity.dart';
import 'package:dada/services/ad/ad_plugin_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomTaskBottomDialog extends StatefulWidget {
  final String roomId;

  const SmallRoomTaskBottomDialog({super.key, required this.roomId});

  @override
  State<SmallRoomTaskBottomDialog> createState() =>
      _SmallRoomTaskBottomDialogState();
}

class _SmallRoomTaskBottomDialogState extends State<SmallRoomTaskBottomDialog> {
  List<SmallRoomTaskEntity> taskList = [];
  late AdPluginListener _listener;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      loadData();
      if (Platform.isIOS) {
        ///注释上写着仅 android 端获取权限，然而 android 端不获取权限就能播放广告，iOS 端却不可以，iOS 端加完权限就能播
        bool result = await FlutterGromoreAds.requestPermissionIfNecessary;
        debugPrint("requestPermission: $result");
      }
    });

    addAdListener();
  }

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
      title: "做任务 赚礼物",
      height: 481.h,
      stops: const [0, 0.35],
      child: Expanded(
        child: taskList.isEmpty
            ? EmptyWidget()
            : ListView.separated(
                padding: EdgeInsets.only(
                    top: 21.h, bottom: 20.h + ScreenUtil().bottomBarHeight),
                itemBuilder: (context, index) {
                  SmallRoomTaskEntity taskEntity = taskList[index];
                  return _buildTaskListItemWidget(taskEntity);
                },
                separatorBuilder: (context, index) {
                  return Container(
                    height: 10.h,
                  );
                },
                itemCount: taskList.length,
              ),
      ),
    );
  }

  Widget _buildTaskListItemWidget(SmallRoomTaskEntity taskEntity) {
    return Container(
      height: 80.h,
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: AppColors.colorFFFDFDFD,
        border: Border.all(color: AppColors.colorFFC3CEB7),
      ),
      child: Stack(
        children: [
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(10.r),
                  bottomLeft: Radius.circular(10.r),
                ),
                color: const Color(0xFFE5EEE5),
              ),
              padding: const EdgeInsets.all(4.0),
              child: Text(
                taskEntity.type == 1 ? '每日任务' : "周任务",
                style: const TextStyle(
                  color: Color(0xFF8F9588),
                  fontSize: 12.0,
                ),
              ),
            ),
          ),
          Row(
            children: [
              Container(
                margin: EdgeInsets.only(left: 15.w),
                child: Stack(
                  children: [
                    ClipOval(
                      child: Container(
                        width: 40.w,
                        height: 40.w,
                        color: AppColors.colorFFD8D8D8,
                      ),
                    ),
                    ClipOval(
                      child: ImageUtils.getImage(
                        taskEntity.taskUrl ?? "",
                        40.w,
                        40.w,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: 190.w,
                      ),
                      child: Text(
                        "${taskEntity.taskName ?? ""}(${taskEntity.taskProgress}/${taskEntity.taskTotalProgress})",
                        maxLines: 2,
                        style: TextStyles.medium(16.sp),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(top: 5.h),
                      constraints: BoxConstraints(
                        maxWidth: 190.w,
                      ),
                      child: Text(
                        taskEntity.taskText ?? "",
                        style:
                            TextStyles.common(13.sp, AppColors.colorFF849F85),
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Padding(
                padding: EdgeInsets.only(right: 15.w, top: 5.h),
                child: CommonGradientBtn(
                  width: 65.w,
                  height: 33.h,
                  textStyle:
                      TextStyles.common(14.sp, AppColors.colorFF3D3D3D, h: 1.6),
                  title: taskEntity.completed == 2
                      ? "已领取"
                      : taskEntity.completed == 1
                          ? "领取"
                          : "去完成",
                  enabled: taskEntity.completed != 2,
                  normalImage: Assets.imagesSmallRoomTaskListBtnBg,
                  disabledImage: Assets.imagesSmallRoomTaskRightBtnClaimed,
                  onTap: () async {
                    if (taskEntity.completed == 1) {
                      bool success = await ApiService()
                          .getTaskReward(taskId: taskEntity.roomTaskId!);
                      if (success) {
                        ToastUtils.showToast("领取成功");
                        loadData(showLoading: false);
                        if (taskEntity.roomTaskId == "1") {
                          UserService().refresh();
                        }
                      }
                    } else if (taskEntity.completed == 0) {
                      if (taskEntity.roomTaskId == "1") {
                        showRewardAd();
                      } else {
                        Get.back();
                      }
                    } else {
                      Get.back();
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void loadData({bool? showLoading}) async {
    taskList = await ApiService()
            .getTaskList(daRoomId: widget.roomId, showLoading: showLoading) ??
        [];
    // preLoadAd();
    setState(() {});
  }

  void addAdListener() {
    _listener = AdPluginListener(
      onAdSkip: (adId) {
        debugPrint("Ad Plugin onAdSkip");
      },
      onAdClicked: (adId) {
        debugPrint("Ad Plugin onAdClicked");
      },
      onAdClosed: (adId) {
        loadData(showLoading: false);
      },
      onAdReward: (event) async {
        if (event.rewardVerify) {
          await ApiService().doTask(taskId: "1");
          loadData(showLoading: false);
        }
      },
    );
    AdPluginManager.instance.addListener(_listener);
  }

  void preLoadAd() async {
    AdPluginManager.instance.preLoadRewardVideoAd(Platform.isIOS
        ? AppConfig.gromoreRewardAdsID_iOS
        : AppConfig.gromoreRewardAdsID_Android);
  }

  void showRewardAd() async {
    AdPluginManager.instance.showRewardVideoAd(Platform.isIOS
        ? AppConfig.gromoreRewardAdsID_iOS
        : AppConfig.gromoreRewardAdsID_Android);
  }

  @override
  void dispose() {
    AdPluginManager.instance.removeListener(_listener!);
    super.dispose();
  }
}
