import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class SmallRoomSendInviteMsgBottomDialog extends StatefulWidget {
  final String userId;

  const SmallRoomSendInviteMsgBottomDialog({super.key, required this.userId});

  @override
  State<SmallRoomSendInviteMsgBottomDialog> createState() =>
      _SmallRoomSendInviteMsgBottomDialogState();
}

class _SmallRoomSendInviteMsgBottomDialogState
    extends State<SmallRoomSendInviteMsgBottomDialog> {
  TextEditingController editingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BottomGradientDialog(
      title: "邀请去玩",
      height: 220.h,
      child: Column(
        children: [
          Container(
            height: 49.h,
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(top: 25.h, left: 47.5.h, right: 47.5.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.5.h),
              color: AppColors.colorFFF5F5F5,
              border: Border.all(
                color: AppColors.colorFFE1E1E1,
              ),
            ),
            child: CustomTextField.build(
              style: TextStyles.common(16.sp, AppColors.colorFF999999),
              controller: editingController,
              contentPadding:
                  EdgeInsets.only(left: 14.w, top: 10.h, bottom: 10.h),
              hintText: "请输入游戏名字",
              maxLength: 10,
            ),
          ),
          CommonGradientBtn(
            title: "确定",
            topMargin: 29.h,
            horizontalMargin: 116.w,
            normalImage: Assets.imagesCommonGradientBtnBg,
            onTap: () async {
              if (editingController.text.isNotEmpty) {
                // V2TimMessage? message = await ChatIMManager.sharedInstance
                //     .sendTextMessage(
                //         text: "我邀请你去玩${editingController.text}",
                //         toUserID: widget.userId);
                // if (message != null) {
                //   Get.back();
                // }
                Get.back(result: editingController.text);
              } else {
                ToastUtils.showToast("请输入要邀请的游戏名称");
              }
            },
          ),
        ],
      ),
    );
  }
}
