import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_gradient_dialog.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomSocialHabitBottomDialog extends StatefulWidget {
  final UserInfoEntity userInfoEntity;

  const SmallRoomSocialHabitBottomDialog(
      {super.key, required this.userInfoEntity});

  @override
  State<SmallRoomSocialHabitBottomDialog> createState() =>
      _SmallRoomSocialHabitBottomDialogState();
}

class _SmallRoomSocialHabitBottomDialogState
    extends State<SmallRoomSocialHabitBottomDialog> {
  SmallRoomController controller =
      Get.find<SmallRoomController>(tag: "mine_small_room_tag");
  RxInt selectedHabitIndex = 1.obs;
  late List<String> habits;
  late List<Color> habitsColors;
  late final UserInfoEntity userInfo = widget.userInfoEntity;

  @override
  void initState() {
    super.initState();

    habits = [
      S.current.cool,
      S.current.slow,
      S.current.moderate,
      S.current.alive,
      S.current.hospitable
    ];

    habitsColors = [
      const Color(0xFF2867FA),
      const Color(0xFF11E7FF),
      const Color(0xFFF0D657),
      const Color(0xFFFF7018),
      const Color(0xFFFF065D),
    ];

    if (UserService().user?.socialState != null) {
      selectedHabitIndex.value = UserService().user!.socialState! - 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isSelf = userInfo.id == UserService().user!.id;
    return BottomGradientDialog(
      height: 305.h,
      title: isSelf ? "个人社交习惯" : "TA的社交习惯",
      child: Container(
        margin: EdgeInsets.only(left: 15.w, top: 15.h, bottom: 15.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15.r),
        ),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(top: 17.h),
              child: Obx(
                () => Text(
                  habits[selectedHabitIndex.value],
                  style: TextStyles.normal(16.sp),
                ),
              ),
            ),
            _socialHabits(),
            isSelf
                ? CommonGradientBtn(
                    title: "确定",
                    topMargin: 32.w,
                    horizontalMargin: 101.w,
                    normalImage: Assets.imagesCommonGradientBtnBg,
                    onTap: () {
                      updateUserSocialHabit();
                    },
                  )
                : Container(),
          ],
        ),
      ),
    );
  }

  Widget _socialHabits() {
    return Padding(
      padding: EdgeInsets.only(left: 26.w, right: 26.w, top: 28.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: habits.map((e) => _habitItemWidget(e)).toList(),
      ),
    );
  }

  Widget _habitItemWidget(String title) {
    return Obx(
      () {
        double width = (ScreenUtil().screenWidth - 41.w * 2) / 5;
        int index = habits.indexOf(title);
        bool selected = selectedHabitIndex.value == index;
        Color color = habitsColors[index];
        return GestureDetector(
          onTap: () {
            bool isSelf = userInfo.id == UserService().user!.id;
            if (!isSelf) {
              return;
            }
            selectedHabitIndex.value = index;
          },
          child: Column(
            children: [
              Container(
                height: 39.h,
                width: width,
                padding: EdgeInsets.only(right: 1.w),
                child: Stack(
                  children: [
                    Column(
                      children: [
                        const Spacer(),
                        Container(
                          height: 10.h,
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.only(
                              topLeft: index == 0
                                  ? Radius.circular(2.r)
                                  : Radius.zero,
                              bottomLeft: index == 0
                                  ? Radius.circular(2.r)
                                  : Radius.zero,
                              topRight: index == habits.length - 1
                                  ? Radius.circular(2.r)
                                  : Radius.zero,
                              bottomRight: index == habits.length - 1
                                  ? Radius.circular(2.r)
                                  : Radius.zero,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Container(
                      alignment: Alignment.topRight,
                      padding: EdgeInsets.only(right: 3.w),
                      child: Visibility(
                        visible: selected,
                        child: ImageUtils.getImage(
                            Assets.imagesRegisterHabitSelected, 32.w, 37.h),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 3.h),
              Text(
                title,
                style: TextStyles.normal(14.sp),
              ),
            ],
          ),
        );
      },
    );
  }

  void updateUserSocialHabit() async {
    bool success = await ApiService()
        .editUserInfo(socialState: selectedHabitIndex.value + 1,
        audioSignature: UserService().user?.voiceSignature,
        voiceLength: UserService().user?.voiceLength);
    if (success) {
      UserInfoEntity user = UserService().user!;
      user.socialState = selectedHabitIndex.value + 1;
      UserService().user = user;
      Get.back();
    }
  }
}
