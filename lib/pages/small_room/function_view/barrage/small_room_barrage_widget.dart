import 'package:dada/common/values/text_styles.dart';
import 'package:dada/model/small_room_barrage_item_entity.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_infinite_marquee/flutter_infinite_marquee.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomBarrageWidget extends StatefulWidget {
  final List<SmallRoomBarrageItemEntity>? barrageList;

  const SmallRoomBarrageWidget({super.key, required this.barrageList});

  @override
  State<SmallRoomBarrageWidget> createState() => SmallRoomBarrageWidgetState();
}

class SmallRoomBarrageWidgetState extends State<SmallRoomBarrageWidget> {
  final List<SmallRoomBarrageItemEntity> _loopBarrageList1 = [];
  final List<SmallRoomBarrageItemEntity> _loopBarrageList2 = [];
  int currentIndex1 = 0;
  int currentIndex2 = 0;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();

    EventBusEngine.eventBus.on().listen((event) {
      EventEntity entity = event;
      if (entity.event == BusEvent.smallRoomUpdateBarrage) {
        SmallRoomBarrageItemEntity barrage = SmallRoomBarrageItemEntity();
        barrage.content = entity.message;
        barrage.avatar = UserService().user?.avatar;
        barrage.nickname = UserService().user?.nickname;

        // 检查是否已存在相同内容的弹幕，避免重复
        bool isDuplicate = _loopBarrageList1.any((item) =>
                item.content == barrage.content &&
                item.nickname == barrage.nickname) ||
            _loopBarrageList2.any((item) =>
                item.content == barrage.content &&
                item.nickname == barrage.nickname);

        if (!isDuplicate) {
          // 交替插入两行，确保数据分布均匀
          if (_loopBarrageList1.length <= _loopBarrageList2.length) {
            _loopBarrageList1.insert(0, barrage);
          } else {
            _loopBarrageList2.insert(0, barrage);
          }
          setState(() {});
        }
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeBarrageData();
    });
  }

  void _initializeBarrageData() {
    if (widget.barrageList != null && !_isInitialized) {
      _isInitialized = true;
      Future.delayed(const Duration(seconds: 1), () {
        _distributeBarrageData(widget.barrageList!);
      });
    }
  }

  void _distributeBarrageData(List<SmallRoomBarrageItemEntity> barrageList) {
    setState(() {
      _loopBarrageList1.clear();
      _loopBarrageList2.clear();
      currentIndex1 = 0;
      currentIndex2 = 0;

      // 限制数据量，避免过多数据
      List<SmallRoomBarrageItemEntity> list =
          barrageList.length > 20 ? barrageList.sublist(0, 20) : barrageList;

      // 去重处理
      Set<String> seenContent = <String>{};
      List<SmallRoomBarrageItemEntity> uniqueList = [];

      for (var item in list) {
        String key = "${item.nickname ?? ''}:${item.content ?? ''}";
        if (!seenContent.contains(key)) {
          seenContent.add(key);
          uniqueList.add(item);
        }
      }

      // 智能分配到两行，确保数据分布均匀且不重复
      for (int i = 0; i < uniqueList.length; i++) {
        if (i % 2 == 0) {
          _loopBarrageList1.add(uniqueList[i]);
        } else {
          _loopBarrageList2.add(uniqueList[i]);
        }
      }
    });
  }

  @override
  void didUpdateWidget(covariant SmallRoomBarrageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.barrageList != oldWidget.barrageList &&
        widget.barrageList != null) {
      _distributeBarrageData(widget.barrageList!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 10.h),
          width: ScreenUtil().screenWidth,
          height: 20.h,
          child: InfiniteMarquee(
            initialScrollOffset: -ScreenUtil().screenWidth,
            itemBuilder: (context, index) {
              if (index < 0 || _loopBarrageList1.isEmpty) {
                return Container(
                    width: 50.w, height: 20.h, color: Colors.transparent);
              }

              // 改进循环逻辑，避免重复显示
              if (currentIndex1 >= _loopBarrageList1.length) {
                currentIndex1 = 0;
                // 添加更大的间隔，避免立即重复
                return Container(
                    width: ScreenUtil().screenWidth * 3,
                    height: 20.h,
                    color: Colors.transparent);
              }

              SmallRoomBarrageItemEntity barrage =
                  _loopBarrageList1[currentIndex1];
              currentIndex1++;
              return SmallRoomBarrageItemWidget(
                barrageItem: barrage,
              );
            },
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: 4.h),
          width: ScreenUtil().screenWidth,
          height: 20.h,
          child: InfiniteMarquee(
            initialScrollOffset: -ScreenUtil().screenWidth,
            itemBuilder: (context, index) {
              if (index < 0 || _loopBarrageList2.isEmpty) {
                return Container(
                    width: 50.w, height: 20.h, color: Colors.transparent);
              }

              // 改进循环逻辑，避免重复显示
              if (currentIndex2 >= _loopBarrageList2.length) {
                currentIndex2 = 0;
                // 添加更大的间隔，避免立即重复
                return Container(
                    width: ScreenUtil().screenWidth * 3,
                    height: 20.h,
                    color: Colors.transparent);
              }

              SmallRoomBarrageItemEntity barrage =
                  _loopBarrageList2[currentIndex2];
              currentIndex2++;
              return SmallRoomBarrageItemWidget(
                barrageItem: barrage,
              );
            },
          ),
        ),
      ],
    );
  }

  void addBarrage(SmallRoomBarrageItemEntity barrage) {
    // 检查是否已存在相同内容的弹幕，避免重复
    bool isDuplicate = _loopBarrageList1.any((item) =>
            item.content == barrage.content &&
            item.nickname == barrage.nickname) ||
        _loopBarrageList2.any((item) =>
            item.content == barrage.content &&
            item.nickname == barrage.nickname);

    if (!isDuplicate) {
      // 交替插入两行，确保数据分布均匀
      if (_loopBarrageList1.length <= _loopBarrageList2.length) {
        _loopBarrageList1.insert(0, barrage);
        // 限制列表长度，避免内存过度使用
        if (_loopBarrageList1.length > 50) {
          _loopBarrageList1.removeLast();
        }
      } else {
        _loopBarrageList2.insert(0, barrage);
        // 限制列表长度，避免内存过度使用
        if (_loopBarrageList2.length > 50) {
          _loopBarrageList2.removeLast();
        }
      }
      setState(() {});
    }
  }
}

class SmallRoomBarrageItemWidget extends StatelessWidget {
  final SmallRoomBarrageItemEntity barrageItem;
  final Function()? onDelete;

  const SmallRoomBarrageItemWidget(
      {super.key, required this.barrageItem, this.onDelete});

  @override
  Widget build(BuildContext context) {
    RxBool visible = true.obs;
    return Obx(() {
      return Visibility(
        visible: visible.value,
        child: Container(
          height: 20.h,
          margin: EdgeInsets.only(right: 10.w),
          padding: EdgeInsets.only(right: 10.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Colors.black.withOpacity(0.5),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 2.w),
                child: ClipOval(
                  child: ImageUtils.getImage(
                      barrageItem.avatar ?? "", 16.w, 16.w,
                      fit: BoxFit.cover),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  "${barrageItem.nickname ?? ""}：${barrageItem.content ?? ""}",
                  style: TextStyles.common(12.sp, Colors.white),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
