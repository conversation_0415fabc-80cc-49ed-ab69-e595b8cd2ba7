import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomSendBarrageDialog extends StatefulWidget {
  final String roomId;

  const SmallRoomSendBarrageDialog({super.key, required this.roomId});

  @override
  State<SmallRoomSendBarrageDialog> createState() =>
      _SmallRoomSendBarrageDialogState();
}

class _SmallRoomSendBarrageDialogState
    extends State<SmallRoomSendBarrageDialog> {
  final TextEditingController editingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GradientWidget(
      width: 320.w,
      height: 230.h,
      colors: const [AppColors.colorFFD2F6C0, Colors.white],
      stops: const [0, 0.45],
      cornerRadius: 20.r,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Text(
              "写da",
              style: TextStyles.medium(18.sp),
            ),
          ),
          Container(
            width: 280.w,
            height: 80.h,
            margin: EdgeInsets.only(top: 23.h),
            alignment: Alignment.topLeft,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
              color: AppColors.colorFFF5F5F5,
            ),
            child: CustomTextField.build(
              contentPadding: EdgeInsets.only(
                  left: 10.w, top: 10.h, right: 10.w, bottom: 10.h),
              controller: editingController,
              hintText: "请输入想说的话",
              maxLength: 20,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    width: 80.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(17.5.r),
                      border: Border.all(color: AppColors.colorFF999999),
                    ),
                    child: Text(
                      S.current.cancel,
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                GestureDetector(
                  onTap: () {
                    sendSmallRoomBarrage();
                  },
                  child: Container(
                    width: 80.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [
                          AppColors.colorFFA0F6A5,
                          AppColors.colorFF58C75D
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(17.5.r),
                    ),
                    child: Text(
                      S.current.sure,
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void sendSmallRoomBarrage() async {
    if (editingController.text.isEmpty) {
      ToastUtils.showToast("发送内容不能为空");
      return;
    }
    bool success = await ApiService().addSmallRoomBarrage(
        roomId: widget.roomId, content: editingController.text);
    if (success) {
      Get.back(result: editingController.text);
    }
  }
}
