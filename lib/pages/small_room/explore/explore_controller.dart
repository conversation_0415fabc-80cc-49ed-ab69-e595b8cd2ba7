import 'package:dada/generated/assets.dart';
import 'package:dada/model/prop_entity.dart';
import 'package:dada/model/small_room_explore_info_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'dart:async';

import 'package:dada/common/values/colors.dart';

class ExploreController extends GetxController {
  final RxDouble progress = 0.0.obs;
  final RxString remainTime = "".obs;
  final RxInt state = 0.obs;
  final Rx<SmallRoomExploreInfoExploreProp1> prop1 = SmallRoomExploreInfoExploreProp1().obs;
  final Rx<SmallRoomExploreInfoExploreProp2> prop2 = SmallRoomExploreInfoExploreProp2().obs;
  List<PropEntity> propList = [];

  //DateTime? endTime; // 后台返回的结束时间
  Rx<DateTime?> endTime = Rx<DateTime?>(null);
  Timer? _timer;

  @override
  void onInit() {
    super.onInit();
    fetchExploreInfo();
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }

  // 获取探索信息
  Future<void> fetchExploreInfo() async {
    try {
      SmallRoomExploreInfoEntity? result = await ApiService().getExploreInfo();
      if (result == null) {
        return;
      }
      // 假设接口返回的是ISO格式的时间字符串
      endTime.value = DateTime.parse(result.exploreDate!);
      state.value = result.state ?? 0;
      prop1.value = result.exploreProp1 ?? SmallRoomExploreInfoExploreProp1();
      prop2.value = result.exploreProp2 ?? SmallRoomExploreInfoExploreProp2();
      propList = result.propList!;
      startTimer();
    } catch (e) {
      print('Error fetching explore info: $e');
    }
  }

  // 开始计时器
  void startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      updateTimeAndProgress();
    });
    // 立即更新一次
    updateTimeAndProgress();
  }

  // 更新时间和进度
  void updateTimeAndProgress() {
    if (endTime == null) return;

    final now = DateTime.now();
    if (now.isAfter(endTime.value!)) {
      if (state == 1) {
        progress.value = 1.0;
        remainTime.value = "收获";
      } else {
        progress.value = 0.0;
        remainTime.value = "开始探索";
      }
      _timer?.cancel();
      return;
    }

    // 计算剩余时间
    final remaining = endTime.value!.difference(now);

    // 格式化剩余时间
    if (remaining.inDays > 0) {
      remainTime.value =
          "${remaining.inDays}天${remaining.inHours % 24}时${remaining.inMinutes % 60}分";
    } else if (remaining.inHours > 0) {
      remainTime.value =
          "${remaining.inHours}时${remaining.inMinutes % 60}分${remaining.inSeconds % 60}秒";
    } else {
      remainTime.value = "${remaining.inMinutes}分${remaining.inSeconds % 60}秒";
    }

    // 计算进度 (假设总时长为3天)
    final totalDuration = Duration(hours: 20);
    final elapsed = totalDuration - remaining;
    progress.value =
        (elapsed.inSeconds / totalDuration.inSeconds).clamp(0.0, 1.0);
  }

  Future<void> startExplore() async {
    // 处理开始收获的逻辑
    String? result = await ApiService().startExplore();
    endTime.value = DateTime.parse(result!);
    startTimer();
  }

  Future<void> getExplore() async {
    try {
      // 处理开始收获的逻辑
      SmallRoomExploreInfoEntity? result = await ApiService().getExplore();
      if (result == null) {
        return;
      }
      state.value = result.state ?? 0;
      startTimer();
      prop1.value = result.exploreProp1 ?? prop1.value;
      prop2.value = result.exploreProp2 ?? prop2.value;

      // 弹窗提示收获的奖品
      Get.dialog(
        AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.w),
          ),
          backgroundColor: Colors.transparent,
          contentPadding: EdgeInsets.zero,
          actionsPadding:
              EdgeInsets.only(bottom: 16.w, right: 16.w, left: 16.w),
          content: Stack(
            children: [
              // 文字部分
              ConstrainedBox(
                constraints: const BoxConstraints(
                  minHeight: 350.0, // 设置最小高度为 160 像素
                ),
                child: Container(
                  width: double.infinity,
                  // 设置宽度为和弹窗一样宽
                  padding: EdgeInsets.only(
                      top: 60.w, left: 16.w, right: 16.w, bottom: 16.w),
                  margin: EdgeInsets.only(top: 20.w),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                       begin: Alignment.topCenter,
                       end: Alignment.bottomCenter,
                      colors: [AppColors.colorFFD2F6C0, Colors.white],
                      stops: [0.0, 0.35],
                    ),
                    borderRadius: BorderRadius.circular(16.w),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        color: Colors.grey[100], // 浅灰色背景
                        padding: const EdgeInsets.all(8.0), // 可选：添加内边距
                        child: Text(
                          result.eventDesc ?? "",
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 16.sp,
                          ),
                        ),
                      ),
                      // 添加道具展示
                      Wrap(
                        spacing: 16.w, // 水平间距
                        runSpacing: 16.w, // 垂直间距
                        crossAxisAlignment: WrapCrossAlignment.center, // 对齐方式
                        children: result.propList!.map((prop) {
                          return Container(
                            margin: EdgeInsets.only(top: 16.w),
                            child: Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    border:
                                    Border.all(color: const Color(0xFFDDF8CF), width: 2.w),
                                    borderRadius: BorderRadius.circular(8.w),
                                  ),
                                  child: Image.network(
                                    prop.url!,
                                    width: 60.w,
                                    height: 60.w,
                                  ),
                                ),
                                SizedBox(height: 8.w),
                                Text(
                                  "${prop.propName!}×${prop.description}",
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 30.0), // 固定按钮距离底部10像素
                      IntrinsicWidth(
                        child: Container(
                          width: 100.w,
                          height: 35.h,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFFA0F6A5),
                                Color(0xFF58C75D),
                              ], // 设置渐变颜色
                            ),
                            borderRadius:
                                BorderRadius.circular(35.h / 2), // 设置按钮圆角
                          ),
                          child: ElevatedButton(
                            onPressed: () {
                              Get.back();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent, // 设置按钮背景透明
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(8.0), // 设置按钮圆角
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 26.w, vertical: 4.w), // 设置按钮内边距
                              elevation: 0, // 去掉按钮的阴影
                            ),
                            child: const Text(
                              "确定",
                              style: TextStyle(
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // 覆盖文字部分上方的图片
              Positioned(
                top: -5.0,
                left: 0,
                right: 0,
                child: Image.asset(
                  Assets.imagesSmallRoomExploreGet,
                  width: Get.width * 0.8,
                  fit: BoxFit.cover,
                  height: 80.0,
                ),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      print('Error fetching explore info: $e');
    }
  }
}
