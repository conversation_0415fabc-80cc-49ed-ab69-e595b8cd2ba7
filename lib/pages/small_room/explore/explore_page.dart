import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tolyui/tolyui.dart';
import '../../../utils/toast_utils.dart';
import 'explore_controller.dart';

class ExplorePage extends StatefulWidget {
  const ExplorePage({super.key});

  @override
  _ExplorePageState createState() => _ExplorePageState();
}

class _ExplorePageState extends State<ExplorePage> {
  final ExploreController controller = Get.put(ExploreController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        title: "探索",
        backgroundColor: Colors.transparent,
      ),
      body: Stack(
        fit: StackFit.expand, // 确保 Stack 占据整个可用空
        children: [
          // 背景图
          Positioned.fill(
            child: Image.asset(
              Assets.imagesSmallRoomExploreBg,
              fit: BoxFit.cover,
            ),
          ),
          // 左上角预览
          Positioned(
            top: ScreenUtil().statusBarHeight + 55.h,
            left: 10.h,
            child: GestureDetector(
              onTap: () {
                // 添加点击事件的处理逻辑
                ToastUtils.showDialog(
                  dialog: _showRewardPreview(),
                );
              },
              child: Column(
                children: [
                  Image.asset(
                    Assets.imagesSmallRoomExploreBtn,
                    width: 40, // 根据需要调整宽度
                    height: 40, // 根据需要调整高度
                  ),
                  SizedBox(height: 5.h), // 添加间距
                  Text(
                    "奖励预览",
                    style:
                        TextStyles.normal(12.sp, c: Colors.white), // 根据需要调整样式
                  ),
                ],
              ),
            ),
          ),
          // 左下角图片
          Positioned(
            bottom: 0,
            left: 0,
            child: Obx(() {
              return Image.asset(
                controller.remainTime.value != "开始探索" &&
                        controller.remainTime.value != "收获"
                    ? Assets.imagesSmallRoomExploreXianv2
                    : Assets.imagesSmallRoomExploreXianv,
                width: 150, // 根据需要调整宽度
                height: 200, // 根据需要调整高度
              );
            }),
          ),

          Obx(() {
            return Visibility(
              visible: true,
              child: Positioned(
                bottom: 150,
                left: 118,
                child: Container(
                  width: controller.remainTime.value == "开始探索" ? 240 : controller.remainTime.value == "收获" ? 240 : 240,
                  height: controller.remainTime.value == "开始探索" ? 85 : controller.remainTime.value == "收获" ? 85 : 85,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    image: const DecorationImage(
                      image: AssetImage(
                          'assets/images/small_room_explore_log.webp'),
                      // 替换为你的背景图片路径
                      fit: BoxFit.cover,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
            child: Padding(
            padding: const EdgeInsets.only(bottom: 30.0), // 向上移动
                  child: Text(
                    controller.remainTime.value == "开始探索"
                        ? '快派我去探索吧，我已经\n  等不及要一展身手啦!'
                        : controller.remainTime.value == "收获"
                            ? '有惊无险，嘿嘿，\n 看我带回了什么宝贝？~'
                            : '我出门啦，等我好消息哦！', // 可以根据需要添加更多条件
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black, // 根据背景图片调整文字颜色
                      height: 1.2, // 调整行间距
                    ),
                    textAlign: TextAlign.center,
                  ),
            ),
                ),
              ),
            );
          }),

          // 右上角进度条和倒计时容器
          Positioned(
            top: ScreenUtil().statusBarHeight + 50.h,
            right: 0.w,
            child: Container(
              width: 130.w,
              padding: EdgeInsets.all(10.w),
              alignment: Alignment.centerRight,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 进度条
                  Container(
                    height: 6.h,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3.r),
                      color: Colors.white.withOpacity(0.3),
                    ),
                    child: Obx(() => FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: controller.progress.value / 1,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(3.r),
                              gradient: const LinearGradient(
                                colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
                              ),
                            ),
                          ),
                        )),
                  ),
                  SizedBox(height: 5.h),
                  // 倒计时文本
                  Obx(() {
                    if (controller.remainTime.value == "收获") {
                      return Container(
                        width: 50.w,
                        height: 25.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(35.h / 2),
                          gradient: const LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                Color(0xFFA0F6A5),
                                Color(0xFF58C75D),
                              ]),
                        ),
                        child: GestureDetector(
                          onTap: () {
                            controller.getExplore();
                          },
                          child: Text(
                            "收获",
                            style: TextStyles.normal(12.sp,
                                c: AppColors.colorFF344F3D),
                          ),
                        ),
                      );
                    } else if (controller.remainTime.value == "开始探索") {
                      return Container(
                        width: 70.w,
                        height: 25.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(35.h / 2),
                          gradient: const LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                Color(0xFFA0F6A5),
                                Color(0xFF58C75D),
                              ]),
                        ),
                        child: GestureDetector(
                          onTap: () {
                            controller.startExplore();
                          },
                          child: Text(
                            "开始探索",
                            style: TextStyles.normal(12.sp,
                                c: AppColors.colorFF344F3D),
                          ),
                        ),
                      );
                    } else {
                      return Text(
                        '剩余:${controller.remainTime.value}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.sp,
                        ),
                      );
                    }
                  }),
                ],
              ),
            ),
          ),

          Positioned(
            bottom: 30.h,
            right: 20.w,
            child: Obx(() => Row(
                  children: [
                    TolyTooltip(
                      message:
                          '${controller.prop1.value.explorePropName ?? ''}',
                      placement: Placement.bottom,
                      decorationConfig: DecorationConfig(
                        backgroundColor: Colors.black.withOpacity(0.3),
                        isBubble: true,
                        radius: Radius.circular(10.r),
                        textColor: Colors.white,
                      ),
                      triggerMode: TooltipTriggerMode.tap,
                      maxWidth: 190.w,
                      gap: 5.h,
                      textStyle: TextStyles.common(14.sp, Colors.white, h: 1.3),
                      padding:
                          EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
                      child: Padding(
                        padding: EdgeInsets.only(right: 10.w),
                        child: ImageUtils.getImage(
                          controller.prop1.value.img ??
                              Assets.imagesSmallRoomExploreProp,
                          40.w,
                          40.w,
                          showPlaceholder: true,
                          placeholder: Assets.imagesSmallRoomExploreProp,
                        ),
                      ),
                    ),
                    TolyTooltip(
                      message:
                          '${controller.prop2.value.explorePropName ?? ''}',
                      placement: Placement.bottom,
                      decorationConfig: DecorationConfig(
                        backgroundColor: Colors.black.withOpacity(0.3),
                        isBubble: true,
                        radius: Radius.circular(10.r),
                        textColor: Colors.white,
                      ),
                      triggerMode: TooltipTriggerMode.tap,
                      maxWidth: 190.w,
                      gap: 5.h,
                      textStyle: TextStyles.common(14.sp, Colors.white, h: 1.3),
                      padding:
                          EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
                      child: Padding(
                        padding: EdgeInsets.only(right: 10.w),
                        child: ImageUtils.getImage(
                          controller.prop2.value.img ??
                              Assets.imagesSmallRoomExploreProp,
                          40.w,
                          40.w,
                          showPlaceholder: true,
                          placeholder: Assets.imagesSmallRoomExploreProp,
                        ),
                      ),
                    )
                  ],
                )),
          ),
        ],
      ),
    );
  }

  Widget _showRewardPreview() {
    return GradientWidget(
      height: 360.h,
      width: Get.width * 0.8,
      cornerRadius: 20.r,
      stops: const [0, 0.35],
      colors: const [AppColors.colorFFD2F6C0, Colors.white],
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.w),
            child: IntrinsicHeight(
              child: Stack(
                children: [
                  ///标题
                  Center(
                    child: Text(
                      "奖励预览",
                      style: TextStyles.medium(18.sp),
                    ),
                  ),

                  ///title 右边关闭按钮
                  Visibility(
                    visible: true,
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.close,
                          size: 22.w,
                          weight: 500,
                          color: AppColors.colorFF666666,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 2.h),
          GridView.builder(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              mainAxisSpacing: 0.h,
              crossAxisSpacing: 12.w,
              childAspectRatio: 0.6,
            ),
            itemCount: controller.propList.length,
            itemBuilder: (context, index) {
              final prop = controller.propList[index];
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 带边框的图片容器
                  Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: const Color(0xFFDDF8CF), width: 2),
                        borderRadius: BorderRadius.circular(8.w)),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8.w),
                      child: ImageUtils.getImage(
                        prop.url ?? Assets.imagesSmallRoomExploreProp,
                        80.w,
                        50.w,
                        //showPlaceholder: true,
                        //placeholder: Assets.imagesSmallRoomExploreProp,
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  // 不带边框的文本
                  Text(
                    prop.propName ?? "",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.black
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  )
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
