import 'package:dada/pages/small_room/bg_view/small_room_bg_view.dart';
import 'package:dada/pages/small_room/function_view/small_room_function_view.dart';
import 'package:dada/pages/small_room/role_view/small_room_dazi_view.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SmallRoomPage extends StatefulWidget {
  final String? userId;

  const SmallRoomPage({super.key, this.userId});

  @override
  State<SmallRoomPage> createState() => _SmallRoomPageState();
}

class _SmallRoomPageState extends State<SmallRoomPage> {
  late SmallRoomController controller;
  String tag = "mine_small_room_tag";

  @override
  void initState() {
    super.initState();

    String? userId = widget.userId;
    if (!(userId == UserService().user?.id) && userId?.isNotEmpty == true) {
      tag = "other_small_room_tag_${widget.userId}";
    }
    controller = Get.put(SmallRoomController()..userID = userId, tag: tag);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      body: GetBuilder<SmallRoomController>(
          global: false,
          init: controller,
          tag: tag,
          builder: (controller) {
            return Stack(
              children: [
                SmallRoomBgWidget(tag: tag),
                SmallRoomDaziView(tag: tag),
                SmallRoomFunctionView(tag: tag),
              ],
            );
          }),
    );
  }
}
