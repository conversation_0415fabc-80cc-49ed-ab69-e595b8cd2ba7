import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/global_notice_entity.dart';
import 'package:dada/pages/global_speaker/global_screen_overlay_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class GlobalScreenOverlay extends StatefulWidget {
  const GlobalScreenOverlay({super.key});

  @override
  State<GlobalScreenOverlay> createState() => _GlobalScreenOverlayState();
}

class _GlobalScreenOverlayState extends State<GlobalScreenOverlay>
    with TickerProviderStateMixin {
  final GlobalScreenOverlayController controller =
      Get.put(GlobalScreenOverlayController());

  @override
  void initState() {
    super.initState();

    ///第一阶段动画
    controller.animationController1 = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          Future.delayed(Duration(milliseconds: controller.stayDuration), () {
            controller.animationController2.forward();
          });
        }
      });
    controller.animation1 = Tween<Offset>(
      begin: const Offset(-1, 0),
      end: const Offset(0, 0),
    ).animate(
      CurvedAnimation(
        parent: controller.animationController1,
        curve: Curves.easeInOut,
      ),
    );

    ///第二阶段动画
    controller.animationController2 = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          controller.animationController1.reset();
          controller.animationController2.reset();
          controller.showNextNotice();
        }
      });
    controller.animation2 = Tween<Offset>(
      begin: const Offset(0, 0),
      end: const Offset(1, 0),
    ).animate(
      CurvedAnimation(
        parent: controller.animationController2,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GlobalScreenOverlayController>(
      builder: (controller) {
        return Positioned(
          top: ScreenUtil().statusBarHeight + 10.h,
          left: 0,
          child: SlideTransition(
            position: controller.animation2,
            child: SlideTransition(
              position: controller.animation1,
              child: _buildNoticeContent(controller),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNoticeContent(GlobalScreenOverlayController controller) {
    GlobalNoticeEntity? entity = controller.globalNoticeList.firstOrNull;
    if (entity == null) {
      return Container();
    }
    return IgnorePointer(
      child: Container(
        width: ScreenUtil().screenWidth,
        height: 70.h,
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(Assets.imagesGlobalSpeakerMsgBg),
              fit: BoxFit.fill,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.only(left: 9.w, top: 14.h, right: 9.w),
            child: Text.rich(
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              TextSpan(
                children: [
                  WidgetSpan(
                    child: ImageUtils.getImage(entity.avatar ?? "", 19.w, 19.w,
                        radius: 19.w / 2, showBorder: true, borderColor: Colors.blueAccent),
                  ),
                  WidgetSpan(
                    child: Padding(
                      padding: EdgeInsets.only(left: 5.w),
                      child: Text(
                        "${entity.nickname}：",
                        style: TextStyles.normal(12.sp),
                      ),
                    ),
                  ),
                  TextSpan(
                    text: entity.msg,
                    style: TextStyles.normal(14.sp),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    controller.animationController1.dispose();
    controller.animationController2.dispose();
    super.dispose();
  }
}
