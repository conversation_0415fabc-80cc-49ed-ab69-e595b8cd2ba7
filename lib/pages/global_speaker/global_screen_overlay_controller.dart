import 'package:dada/model/global_notice_entity.dart';
import 'package:flutter/animation.dart';
import 'package:get/get.dart';

class GlobalScreenOverlayController extends GetxController {
  late AnimationController animationController1;
  late AnimationController animationController2;
  late Animation<Offset> animation1;
  late Animation<Offset> animation2;

  bool isAnimation = false;
  List<GlobalNoticeEntity> globalNoticeList = <GlobalNoticeEntity>[];

  ///默认停留时间
  int stayDuration = 1000;

  void startAnimation(GlobalNoticeEntity entity) {
    if (isAnimation) {
      return;
    }
    stayDuration = (entity.time ?? 30) * 1000;
    update();
    animationController1.forward();
    isAnimation = true;
  }

  void showNextNotice() {
    globalNoticeList.removeAt(0);
    isAnimation = false;
    if (globalNoticeList.isEmpty) {
      return;
    }
    GlobalNoticeEntity globalNoticeEntity = globalNoticeList[0];
    startAnimation(globalNoticeEntity);
  }

  void addNotice(GlobalNoticeEntity globalNoticeEntity) {
    globalNoticeList.add(globalNoticeEntity);
    if (!isAnimation) {
      startAnimation(globalNoticeEntity);
    }
  }

  void clearAll () {
    globalNoticeList.clear();
    isAnimation = false;
    update();
  }
}