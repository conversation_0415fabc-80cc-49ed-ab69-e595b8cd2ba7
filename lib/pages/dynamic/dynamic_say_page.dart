import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';

class DynamicSayPage extends StatefulWidget {
  const DynamicSayPage({super.key});

  @override
  State<DynamicSayPage> createState() => _DynamicSayPageState();
}

class _DynamicSayPageState extends State<DynamicSayPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesDynamicSayBg), // 需要添加背景图资源
            fit: BoxFit.cover,
          ),
        ),
        child: Column(
          children: [
            /// 标题
            Padding(
              padding:
                  EdgeInsets.only(top: ScreenUtil().statusBarHeight + 17.h),
              child: Text(
                "随意说",
                style: TextStyles.common(16.sp, Colors.white),
                textAlign: TextAlign.center,
              ),
            ),

            // 中间输入框区域
            Container(
              margin: EdgeInsets.only(
                  top: 350.h - ScreenUtil().bottomBarHeight,
                  left: 13.w,
                  right: 15.w),
              padding: EdgeInsets.only(top: 20.h, left: 15.w),
              height: 200.h,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  Get.toNamed(GetRouter.postPublish);
                },
                child: Text(
                  "我想说...\n与您一样，很多人也在此时此刻发布随意说呢，AI算法会将其中和你同频或契合的人们与你共鸣在一起哦。江湖虽远，于此起缘，随意说说吧。",
                  overflow: TextOverflow.ellipsis,
                  maxLines: 5,
                  style:
                      TextStyles.common(16.sp, AppColors.colorFF999999, h: 1.5),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 40.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 15.w),
                    child: Row(
                      children: [
                        Obx(
                          () => BadgeWidget(
                            text: Get.find<MainController>()
                                .postResonateUnreadCount
                                .value
                                .toString(),
                            child: _buildBottomIcon(
                                Assets.imagesDynamicSayResonance, "共鸣"),
                          ),
                        ),
                        SizedBox(width: 20.w),
                        Obx(
                          () => BadgeWidget(
                            text: Get.find<MainController>()
                                .postMsgUnreadCount
                                .value
                                .toString(),
                            child: _buildBottomIcon(
                                Assets.imagesDynamicSayCell, "广场"),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 15.w),
                    child: _buildBottomIcon(Assets.imagesDynamicSayMine, "我的"),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomIcon(String iconPath, String label) {
    return GestureDetector(
      onTap: () {
        switch (label) {
          case "共鸣":
            Get.toNamed(GetRouter.dynamicResonance)?.then((value) {
              Get.find<MainController>().refreshPostResonateUnreadCount();
            });
            break;
          case "广场":
            Get.toNamed(GetRouter.dynamicList)?.then((value) {
              Get.find<MainController>().refreshPostMsgUnreadCount();
            });
            break;
          case "我的":
            Get.toNamed(GetRouter.mineDynamic);
            break;
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            iconPath,
            width: 40.w,
            height: 40.w,
          ),
          SizedBox(height: 2.h),
          Text(
            label,
            style: TextStyles.common(12.sp, Colors.black),
          ),
        ],
      ),
    );
  }
}
