import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/dynamic_message_item_entity.dart';
import 'package:dada/pages/dynamic/dynamic_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class DynamicMessageController extends ListPageController<
    DynamicMessageItemEntity, DynamicMessageController> {

  @override
  void onReady() {
    super.onReady();

    Get.find<DynamicController>().clearUnreadMsgCount();
  }

  @override
  Future<List<DynamicMessageItemEntity>?> loadData(int page) async {
    List<DynamicMessageItemEntity>? list = await ApiService().getDynamicMessageList(pageIndex: pageIndex);
    return list;
  }

  String getMessageTip(DynamicMessageItemEntity message) {
    String tip = "";
    if (message.type == 1) {
      tip = "赞了你的动态";
    } else if (message.type == 2) {
      tip = "赞了你的评论";
    } else if (message.type == 3) {
      tip = "赞了你的回复";
    } else if (message.type == 4) {
      tip = "评论了你的动态";
    } else if (message.type == 5) {
      tip = "回复了你的评论";
    }
    return tip;
  }
}
