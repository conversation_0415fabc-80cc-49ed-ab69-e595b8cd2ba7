import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/dynamic_message_item_entity.dart';
import 'package:dada/pages/dynamic/message/dynamic_message_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class DynamicMessagePage extends StatefulWidget {
  const DynamicMessagePage({super.key});

  @override
  State<DynamicMessagePage> createState() => _DynamicMessagePageState();
}

class _DynamicMessagePageState extends State<DynamicMessagePage> {
  final controller = Get.put(DynamicMessageController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: S.current.message,
      ),
      body: GetBuilder<DynamicMessageController>(
        init: DynamicMessageController(),
        global: false,
        builder: (controller) {
          return RefreshWidget.build(
            refreshController: controller.refreshController,
            onRefresh: () => controller.refreshData(),
            onLoadMore: () => controller.loadMoreData(),
            child: controller.data.isEmpty
                ? const ListPageEmptyWidget()
                : ListView.separated(
                    padding: EdgeInsets.zero,
                    itemBuilder: (context, index) {
                      DynamicMessageItemEntity message = controller.data[index];
                      return _buildMessageCell(message);
                    },
                    separatorBuilder: (context, index) {
                      return Container(
                        margin: EdgeInsets.only(left: 15.w, right: 15.w),
                        color: AppColors.colorFFF5F6F7,
                        height: 1,
                      );
                    },
                    itemCount: controller.data.length,
                  ),
          );
        },
        id: DynamicMessageController().refreshId,
      ),
    );
  }

  Widget _buildMessageCell(DynamicMessageItemEntity message) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Get.toNamed(GetRouter.postDetail, parameters: {
          "postId": "${message.postId}",
          "autoFocusNode": "0"
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMessageHeader(message),
            _buildMessageContent(message),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageHeader(DynamicMessageItemEntity message) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            SizedBox(
              width: 40.w,
              height: 40.w,
              child: GestureDetector(
                onTap: () {
                  Get.toNamed(GetRouter.userProfile,
                      parameters: {"userId": message.senderId ?? ""});
                },
                child: ClipOval(
                  child: ImageUtils.getImage(message.avatar ?? "", 40.w, 40.w,
                      fit: BoxFit.cover),
                ),
              ),
            ),
            SizedBox(
              width: 5.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message.nickname ?? "",
                  style: TextStyles.normal(16.sp),
                ),
                SizedBox(
                  height: 3.h,
                ),
                Row(
                  children: [
                    Text(
                      controller.getMessageTip(message),
                      style: TextStyles.common(12.sp, AppColors.colorFF666666),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Text(
                      TimeUtils.formatPostDate(message.createdDate ?? ""),
                      style: TextStyles.common(12.sp, AppColors.colorFF666666),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
        Visibility(
          visible: (message.contentType == 2 || message.contentType == 3)
              ? (message.content != null ? true : false)
              : false,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5.r),
            child: Stack(
              alignment: Alignment.center,
              children: [
                ImageUtils.getImage(
                    message.content ?? "",
                    45.w,
                    45.w,
                    fit: BoxFit.cover),
                message.type == 3
                    ? ImageUtils.getImage(
                        Assets.imagesDynamicListCellVideoPlay, 30.w, 30.w)
                    : Container(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMessageContent(DynamicMessageItemEntity message) {
    if (message.postMsg == null) {
      return Container();
    }
    return Padding(
      padding: EdgeInsets.only(left: 45.w, top: 5.h),
      child: Text(
        message.postMsg ?? "",
        style: TextStyles.common(14.sp, AppColors.colorFF666666),
      ),
    );
  }
}
