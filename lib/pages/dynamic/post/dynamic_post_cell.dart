import 'package:dada/common/values/enums.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/pages/dynamic/post/post_cell_bottom_widget.dart';
import 'package:dada/pages/dynamic/post/post_cell_header_widget.dart';
import 'package:dada/pages/dynamic/post/post_image_content_widget.dart';
import 'package:dada/pages/dynamic/post/post_text_content_widget.dart';
import 'package:dada/pages/dynamic/post/post_video_content_widget.dart';
import 'package:dada/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class DynamicPostCell extends StatelessWidget {
  final DynamicType dynamicType;
  final PostEntity postEntity;
  final Function()? callback;
  final Function(PostEntity e)? onLikeChange;

  const DynamicPostCell(
      {super.key,
      required this.postEntity,
      required this.dynamicType,
      this.callback,
      this.onLikeChange});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      overlayColor: WidgetStateProperty.all(Colors.white),
      onTap: () {
        _pushToPostDetail();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCellHeader(),
            _buildCellContent(),
            _buildCellBottom(),
          ],
        ),
      ),
    );
  }

  Widget _buildCellHeader() {
    return PostCellHeaderWidget(
      postEntity: postEntity,
      avatarSize: 50.w,
      showMore: true,
      callback: callback,
    );
  }

  Widget _buildCellContent() {
    return Padding(
      padding: EdgeInsets.only(left: 55.w, bottom: 15.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PostTextContentWidget(
            postEntity: postEntity,
            maxWidth: ScreenUtil().screenWidth - 15.w * 2 - 55.w,
            maxLines: 4,
          ),
          PostImageContentWidget(postEntity: postEntity),
          PostVideoContentWidget(postEntity: postEntity),
        ],
      ),
    );
  }

  Widget _buildCellBottom() {
    return PostCellBottomWidget(
      postEntity: postEntity,
      padding: EdgeInsets.only(left: 55.w),
      callback: () {
        _pushToPostDetail(autoFocusNode: true);
      },
    );
  }

  void _pushToPostDetail({bool? autoFocusNode}) {
    Get.toNamed(GetRouter.postDetail, parameters: {
      "postId": postEntity.postId ?? "",
      "autoFocusNode": autoFocusNode == true ? "1" : "0"
    })?.then((value) {
      if (value is PostEntity) {
        onLikeChange?.call(value);
      } else {
        if (value != null) {
          callback?.call();
        }
      }
    });
  }
}
