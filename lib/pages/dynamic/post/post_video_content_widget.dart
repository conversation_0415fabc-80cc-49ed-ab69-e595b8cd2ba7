import 'package:dada/common/values/enums.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PostVideoContentWidget extends StatelessWidget {
  final PostEntity postEntity;
  final bool? isDetail;

  const PostVideoContentWidget(
      {super.key, required this.postEntity, this.isDetail});

  @override
  Widget build(BuildContext context) {
    if (postEntity.postType != PostType.video.index) {
      return Container();
    }
    if (postEntity.imgUrls == null || postEntity.imgUrls?.isEmpty == true) {
      return Container();
    }

    String videoUrl = postEntity.imgUrls!.first;
    String? videoThumbnail;
    if (videoUrl.isNotEmpty) {
      videoThumbnail = "$videoUrl?x-oss-process=video/snapshot,t_1,m_fast,ar_auto";
    }
    String heroTag = HeroTagName.postImg
        .of("${postEntity.postId!}${isDetail == true ? "_detail" : ""}");
    return Padding(
      padding: EdgeInsets.only(top: 10.h),
      child: Hero(
        tag: heroTag,
        child: GestureDetector(
          onTap: () {
            ///跳转到全屏播放视频
            ImageUtils.showVideoPlayer(
                videoUrl: videoUrl,
                heroTag: HeroTagName.postImg.of(
                    "${postEntity.postId!}${isDetail == true ? "_detail" : ""}"));
          },
          child: Container(
            width: 90.w,
            height: 90.w,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: ImageUtils.getImage(videoThumbnail ?? "", 90.w, 90.w,
                        fit: BoxFit.cover)),
                ImageUtils.getImage(
                    Assets.imagesDynamicListCellVideoPlay, 30.w, 30.w),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
