import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/pages/share/share_bottom_dialog.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PostCellBottomWidget extends StatefulWidget {
  final PostEntity postEntity;
  final EdgeInsets? padding;
  final Function()? callback;
  final Function(PostEntity e)? onLikeChange;

  const PostCellBottomWidget(
      {super.key,
      required this.postEntity,
      this.padding,
      this.callback,
      this.onLikeChange});

  @override
  State<PostCellBottomWidget> createState() => _PostCellBottomWidgetState();
}

class _PostCellBottomWidgetState extends State<PostCellBottomWidget> {
  @override
  Widget build(BuildContext context) {
    RxBool liked = false.obs;
    return SizedBox(
      height: 21.h,
      child: Container(
        padding: widget.padding,
        child: Row(
          children: [
            ///点赞
            Obx(() {
              liked.value = widget.postEntity.likeState ?? false;
              return _buildCellBottomIconBtn(
                Assets.imagesDynamicListCellLike,
                Assets.imagesDynamicListCellLiked,
                widget.postEntity.likeNo,
                liked.value,
                () async {
                  bool success = await _sendPostLikeRequest(!liked.value);
                  if (success) {
                    liked.value = !liked.value;
                    widget.postEntity.likeState = liked.value;
                    if (liked.value == true) {
                      widget.postEntity.likeNo =
                          (widget.postEntity.likeNo ?? 0) + 1;
                    } else {
                      widget.postEntity.likeNo =
                          (widget.postEntity.likeNo ?? 1) - 1;
                    }
                    if (widget.onLikeChange != null) {
                      widget.onLikeChange!(widget.postEntity);
                    }
                  }
                },
              );
            }),

            SizedBox(
              width: 23.w,
            ),

            ///评论
            _buildCellBottomIconBtn(
              Assets.imagesDynamicListCellComment,
              Assets.imagesDynamicListCellComment,
              widget.postEntity.commentNo,
              false,
              () {
                widget.callback?.call();
              },
            ),

            SizedBox(
              width: 23.w,
            ),

            ///转发
            _buildCellBottomIconBtn(
                Assets.imagesDynamicListCellTransmit,
                Assets.imagesDynamicListCellTransmit,
                widget.postEntity.forwardNo,
                false, () {
              ToastUtils.showBottomDialog(
                ShareBottomDialog(
                  callback: (userId) {
                    sendCustomMsgToUser(widget.postEntity, userId);
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildCellBottomIconBtn(String img, String? highlightedImg, int? count,
      bool highlighted, Function() onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          ImageUtils.getImage(
              highlighted ? (highlightedImg ?? "") : img, 16.w, 16.w),
          SizedBox(
            width: 3.w,
          ),
          Text(
            "${count ?? "0"}",
            style: TextStyles.common(
                14.sp,
                highlighted
                    ? AppColors.colorFF4DC151
                    : AppColors.colorFF999999),
          ),
        ],
      ),
    );
  }

  Future<bool> _sendPostLikeRequest(bool liked) async {
    if (liked) {
      return ApiService()
          .postLike(postId: widget.postEntity.postId!, likeType: 1);
    } else {
      return ApiService()
          .postUnLike(postId: widget.postEntity.postId!, likeType: 1);
    }
  }

  void sendCustomMsgToUser(PostEntity postEntity, String toUserId) async {
    bool success = await ChatIMManager.sharedInstance.sendCustomMessage(
        type: ChatImCustomMsgType.ShareDynamicPostMsg,
        data: postEntity.toString(),
        receiver: toUserId,
        groupID: "");
    if (success) {
      ToastUtils.showToast("分享成功");
      sendShareSuccessRequest(postEntity.postId!);
      ApiService().doTask(taskId: "6");
    }
  }

  void sendShareSuccessRequest(String postId) async {
    bool success = await ApiService().sendSharePost(postId: postId);
    if (success) {
      int sharedNo = widget.postEntity.forwardNo!;
      setState(() {
        widget.postEntity.forwardNo = sharedNo + 1;
      });
    }
  }
}
