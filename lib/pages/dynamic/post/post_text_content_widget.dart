import 'package:dada/common/values/colors.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/utils/string_util.dart';
import 'package:expandable_text/expandable_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PostTextContentWidget extends StatelessWidget {
  final PostEntity postEntity;
  final double? maxWidth;
  final int? maxLines;

  const PostTextContentWidget(
      {super.key, required this.postEntity, this.maxWidth, this.maxLines});

  @override
  Widget build(BuildContext context) {
    if (postEntity.content == null || postEntity.content?.isEmpty == true) {
      return Container();
    }
    double textHeight = 0;
    TextStyle textStyle =
        TextStyle(fontSize: 16.sp, height: 1.3, color: AppColors.colorFF333333);
    double oneLineHeight = StringUtils.calculateTextHeight("文本",
        maxWidth: maxWidth ?? ScreenUtil().screenWidth - 15.w * 2,
        textStyle: textStyle);

    ///计算文本高度
    if (postEntity.content?.isNotEmpty == true) {
      textHeight = StringUtils.calculateTextHeight(
        postEntity.content ?? "",
        maxWidth: maxWidth ?? ScreenUtil().screenWidth - 15.w * 2,
        textStyle: textStyle,
      );
    }

    return Padding(
      padding: EdgeInsets.only(top: 10.h),
      child: ExpandableText(
        postEntity.content!,
        style: textStyle,
        maxLines: maxLines != null
            ? (textHeight > oneLineHeight * maxLines! ? maxLines! : 1000)
            : 1000,
        expandText: S.current.unfold,
        linkColor: AppColors.colorFF4DC151,
        onExpandedChanged: (unfold) {},
      ),
    );
  }
}
