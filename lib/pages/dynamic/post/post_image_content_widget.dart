import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PostImageContentWidget extends StatelessWidget {
  final PostEntity postEntity;
  final bool? isDetail;

  const PostImageContentWidget(
      {super.key, required this.postEntity, this.isDetail});

  @override
  Widget build(BuildContext context) {
    if (postEntity.postType != PostType.image.index) {
      return Container();
    }
    if (postEntity.imgUrls == null || postEntity.imgUrls?.isEmpty == true) {
      return Container();
    }
    List<String> tags = [];
    List<Widget> children = [];
    for (int i = 0; i < postEntity.imgUrls!.length; i++) {
      String url = postEntity.imgUrls![i];
      String tag = HeroTagName.postImg
          .of("${postEntity.postId!}${isDetail == true ? "_detail" : ""}_$i");
      tags.add(tag);
      children.add(
        Hero(
          tag: tag,
          child: GestureDetector(
            onTap: () {
              ImageUtils.showImageBrowser(
                ImageBrowserArgs(
                  tags,
                  postEntity.imgUrls!,
                  index: i,
                ),
              );
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: Container(
                color: AppColors.colorFFF5F6F7,
                child: ImageUtils.getImage(url, 90.w, 90.w, fit: BoxFit.cover),
              ),
            ),
          ),
        ),
      );
    }
    return Padding(
      padding: EdgeInsets.only(top: 10.h),
      child: Wrap(
        spacing: 6.w,
        runSpacing: 6.w,
        children: children,
      ),
    );
  }
}
