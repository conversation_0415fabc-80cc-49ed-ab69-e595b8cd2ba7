import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/components/widgets/popup_menu_widget.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PostCellHeaderWidget extends StatelessWidget {
  final PostEntity postEntity;
  final double? avatarSize;
  final bool? showMore;
  final Function()? callback;
  final bool? isDetail;

  const PostCellHeaderWidget({
    super.key,
    required this.postEntity,
    this.avatarSize,
    this.showMore,
    this.callback,
    this.isDetail,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            ///头像
            AvatarWidget(
              heroTag: HeroTagName.postAvatar.of(
                  "${postEntity.postId!}${isDetail == true ? "_detail" : ""}"),
              url: postEntity.avatar ?? "",
              size: 50.w,
              showPlaceholder: true,
              onTap: () {
                Get.toNamed(GetRouter.userProfile,
                    parameters: {"userId": postEntity.userId!});
              },
            ),

            SizedBox(
              width: 5.w,
            ),

            ///名字、发布时间
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  postEntity.nickname ?? "",
                  style: TextStyles.common(16.sp, AppColors.colorFF666666),
                ),
                SizedBox(
                  height: 5.h,
                ),
                Text(
                  TimeUtils.formatPostDate(postEntity.createdDate ?? ""),
                  style: TextStyles.common(14.sp, AppColors.colorFF999999),
                ),
              ],
            ),
          ],
        ),
        Visibility(
          visible: showMore == true
              ? (postEntity.userId == UserService().user?.id)
              : false,
          child: PopupMenuWidget(
            offset: Offset(20.w, 20.w),
            titles: [
              S.current.delete,
              postEntity.isTop == 2 ? "取消置顶" : S.current.top,
              postEntity.state == 1 ? "公开" : S.current.readOnlyMySelf,
            ],
            onSelected: (value) async {
              bool success = false;
              if (value == 0) {
                //删除
                success = await _sendPostDeleteRequest();
              } else if (value == 1) {
                bool top = (postEntity.isTop == 2 ? true : false);
                success = await _sendPostTopRequest(!top);
              } else if (value == 2) {
                bool hidden = postEntity.state == 1 ? true : false;
                success = await _sendPostHideRequest(!hidden);
              }
              if (success == true) {
                callback?.call();
              }
            },
            child: Icon(
              Icons.more_horiz,
              size: 20.w,
              color: AppColors.colorFF999999,
            ),
          ),
        ),
      ],
    );
  }

  Future<bool> _sendPostDeleteRequest() async {
    return ApiService().postDelete(postId: postEntity.postId!);
  }

  Future<bool> _sendPostTopRequest(bool top) async {
    return ApiService().postTop(postId: postEntity.postId!, top: top);
  }

  Future<bool> _sendPostHideRequest(bool hide) async {
    return ApiService().postHide(postId: postEntity.postId!, hide: hide);
  }
}
