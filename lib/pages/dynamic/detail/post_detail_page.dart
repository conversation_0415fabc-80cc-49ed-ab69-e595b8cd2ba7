import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/components/widgets/popup_menu_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/pages/dynamic/comment/comment_all_reply_dialog.dart';
import 'package:dada/pages/dynamic/comment/comment_cell.dart';
import 'package:dada/pages/dynamic/detail/post_detail_controller.dart';
import 'package:dada/pages/dynamic/post/post_cell_bottom_widget.dart';
import 'package:dada/pages/dynamic/post/post_cell_header_widget.dart';
import 'package:dada/pages/dynamic/post/post_image_content_widget.dart';
import 'package:dada/pages/dynamic/post/post_text_content_widget.dart';
import 'package:dada/pages/dynamic/post/post_video_content_widget.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PostDetailPage extends StatefulWidget {
  const PostDetailPage({super.key});

  @override
  State<PostDetailPage> createState() => _PostDetailPageState();
}

class _PostDetailPageState extends State<PostDetailPage> {
  PostDetailController controller = Get.put(PostDetailController());
  final _editingController = TextEditingController();
  bool refreshAfterBack = false; //赋值时，注意changedEntity是否为空
  final FocusNode _textFieldNode = FocusNode();
  final RxString _textFieldPlaceholder = ''.obs;
  bool showFocusNode = false;
  PostEntity? changedEntity;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PostDetailController>(
      init: controller,
      builder: (controller) {
        Widget? stateWidget;
        if (controller.postId == null) {
          stateWidget = Container();
        } else if (controller.postEntity == null) {
          stateWidget = EmptyWidget(
            padding: EdgeInsets.only(
                top: ScreenUtil().screenHeight / 3 -
                    ScreenUtil().statusBarHeight -
                    50.h),
            content: S.current.postDetailNoView,
          );
        }
        return Scaffold(
          backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
          appBar: CustomAppBar(
            title: S.current.postDetail,
            backAction: () {
              if (refreshAfterBack) {
                Get.back(result: changedEntity);
              } else {
                Get.back();
              }
            },
            rightWidgets: [
              _buildRightNaviBarButton(),
            ],
          ),
          body: Column(
            children: [
              Expanded(
                child: RefreshWidget.build(
                  refreshController: controller.refreshController,
                  onRefresh: () => controller.refreshData(),
                  onLoadMore: () => controller.loadMoreData(),
                  child: stateWidget != null
                      ? ListView(
                          children: [stateWidget],
                        )
                      : ListView(
                          children: [
                            _buildPostDetailWidget(),
                            _buildCommentListWidget(),
                          ],
                        ),
                ),
              ),
              _buildTextFieldWidget(),
            ],
          ),
        );
      },
      id: PostDetailController().refreshId,
    );
  }

  Widget _buildRightNaviBarButton() {
    if (controller.pageState != PageState.success ||
        controller.postId == null ||
        controller.postEntity == null) {
      return Container();
    }
    bool isMinePost = controller.postEntity?.userId == UserService().user?.id;
    return Padding(
      padding: EdgeInsets.only(right: 15.w),
      child: isMinePost
          ? PopupMenuWidget(
              offset: Offset(20.w, 20.w),
              titles: [
                S.current.delete,
                controller.postEntity?.isTop == 1 ? "取消置顶" : S.current.top,
                controller.postEntity?.state == 1
                    ? "公开"
                    : S.current.readOnlyMySelf,
              ],
              onSelected: (value) async {
                bool success = false;
                if (value == 0) {
                  //删除
                  success = await controller.sendPostDeleteRequest();
                  if (success) {
                    Get.back(result: 1);
                  }
                } else if (value == 1) {
                  //置顶
                  bool top = (controller.postEntity?.isTop == 2 ? true : false);
                  success = await controller.sendPostTopRequest(!top);
                  if (success) {
                    controller.postEntity?.isTop = top ? 1 : 2;
                    setState(() {});
                  }
                } else if (value == 2) {
                  //仅自己可见
                  bool hidden =
                      controller.postEntity?.state == 1 ? true : false;
                  success = await controller.sendPostHideRequest(!hidden);
                  if (success) {
                    controller.postEntity?.state = hidden ? 0 : 1;
                    setState(() {});
                  }
                }
              },
              child: Icon(
                Icons.more_horiz,
                size: 20.w,
                color: AppColors.colorFF999999,
              ),
            )
          : GestureDetector(
              onTap: () {
                Get.to(() => ReportPage(
                    reportType: ReportType.post, postId: controller.postId));
              },
              child: Row(
                children: [
                  ImageUtils.getImage(
                      Assets.imagesPostDetailReport, 17.w, 16.h),
                  SizedBox(
                    width: 5.w,
                  ),
                  Text(
                    S.current.report,
                    style: TextStyles.common(14.sp, AppColors.colorFF666666),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildPostDetailWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
      child: Column(
        children: [
          _buildPostHeader(),
          _buildPostContent(),
          _buildPostBottom(),
        ],
      ),
    );
  }

  Widget _buildPostHeader() {
    return PostCellHeaderWidget(
      postEntity: controller.postEntity!,
      avatarSize: 40.w,
      isDetail: true,
    );
  }

  Widget _buildPostContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PostTextContentWidget(postEntity: controller.postEntity!),
        PostImageContentWidget(
            postEntity: controller.postEntity!, isDetail: true),
        PostVideoContentWidget(
            postEntity: controller.postEntity!, isDetail: true),
      ],
    );
  }

  Widget _buildPostBottom() {
    return Padding(
      padding: EdgeInsets.only(top: 15.h),
      child: PostCellBottomWidget(
        postEntity: controller.postEntity!,
        callback: () {
          _textFieldNode.requestFocus();
        },
        onLikeChange: (e) {
          refreshAfterBack = true;
          changedEntity = e;
        },
      ),
    );
  }

  Widget _buildCommentListWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 5.h,
          color: AppColors.colorFFE5E5E5,
        ),
        Padding(
          padding: EdgeInsets.only(left: 15.w, top: 15.h),
          child: Text(
            S.current.allComments,
            style: TextStyles.normal(16.sp),
          ),
        ),
        controller.data.isEmpty
            ? EmptyWidget(
                padding: EdgeInsets.only(top: 80.h, bottom: 50.h),
                image: "",
                imageWidth: 140.w,
                imageHeight: 110.h,
                content: S.current.commentEmptyTip,
                contentNormalTextStyle:
                    TextStyles.common(12.h, AppColors.colorFF999999),
              )
            : ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                itemBuilder: (context, index) {
                  CommentItemEntity comment = controller.data[index];
                  return CommentCell(
                    commentItemEntity: comment,
                    callback: (value, name, replyUserId) {
                      if (value.isNotEmpty) {
                        _textFieldNode.requestFocus();
                        controller.commentId = value;
                        controller.replyUserId = replyUserId;
                        if (name.isNotEmpty) {
                          _textFieldPlaceholder.value =
                              "${S.current.reply}$name";
                        }
                      }
                    },
                    showAllReply: () {
                      ToastUtils.showBottomDialog(
                        cornerRadius: 15.r,
                        CommentAllReplyDialog(
                          commentItemEntity: comment,
                          postEntity: controller.postEntity!,
                          callback: () {
                            controller.refreshData();
                          },
                        ),
                      );
                    },
                  );
                },
                separatorBuilder: (context, index) {
                  return Container(
                    margin: EdgeInsets.only(left: 15.w, right: 15.w),
                    color: AppColors.colorFFF5F6F7,
                    height: 1,
                  );
                },
                itemCount: controller.data.length,
              ),
      ],
    );
  }

  Widget _buildTextFieldWidget() {
    if (controller.pageState == PageState.start ||
        controller.pageState == PageState.loading) {
      return Container();
    }
    return Container(
      height: 56.h + ScreenUtil().bottomBarHeight,
      padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              height: 40.h,
              margin: EdgeInsets.only(
                  left: 16.w, right: 16.w, top: 4.h, bottom: 4.h),
              decoration: BoxDecoration(
                color: AppColors.colorFFF5F5F5,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Obx(() {
                Future.delayed(const Duration(milliseconds: 300), () {
                  String? autoFocusNode = Get.parameters["autoFocusNode"];
                  if (showFocusNode == false && autoFocusNode == "1") {
                    _textFieldNode.requestFocus();
                  }
                });
                return CustomTextField.build(
                  focusNode: _textFieldNode,
                  contentPadding: EdgeInsets.only(left: 10.w, right: 10.w),
                  controller: _editingController,
                  hintText: _textFieldPlaceholder.isNotEmpty
                      ? _textFieldPlaceholder.value
                      : S.current.sendCommentPlaceholder,
                  textInputAction: TextInputAction.done,
                  keyboardType: TextInputType.text,
                  style: TextStyles.normal(16.sp),
                  onChanged: (value) {
                    controller.inputText.value = value;
                  },
                  suffixIconOnTap: () {
                    controller.inputText.value = "";
                  },
                  onSubmitted: (value) {
                    sendComment();
                  },
                );
              }),
            ),
          ),
          GestureDetector(
            onTap: () {
              sendComment();
            },
            child: Obx(
              () => Container(
                margin: EdgeInsets.only(left: 5.w, right: 15.w),
                width: 60.w,
                height: 40.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: controller.inputText.value.isNotEmpty
                      ? AppColors.colorFF89E15C
                      : AppColors.colorFFE1E4E0,
                ),
                child: Text(
                  "发送",
                  style: TextStyles.normal(
                    16.sp,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void sendComment() async {
    if (controller.inputText.value.isNotEmpty) {
      bool success = await controller.sendReply(controller.inputText.value);
      if (success) {
        controller.inputText.value = "";
        controller.replyUserId = null;
        controller.commentId = null;
        _editingController.clear();
        _textFieldPlaceholder.value = "";
        _textFieldNode.unfocus();
      }
    }
  }
}
