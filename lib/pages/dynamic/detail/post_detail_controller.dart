import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:get/get.dart';

class PostDetailController
    extends ListPageController<CommentItemEntity, PostDetailController> {
  String? postId = Get.parameters["postId"];
  PostEntity? postEntity;
  RxString inputText = ''.obs;
  String? replyUserId;
  String? commentId;

  ///回复 Post 或者 评论， 如果是post，就是postId, 如果是评论，则是commentsId

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    await loadPostInfo();
  }

  Future<void> loadPostInfo() async {
    if (postId != null) {
      postEntity = await ApiService().getPostDetail(postId: postId!);
      update();
    }
  }

  @override
  Future<List<CommentItemEntity>?> loadData(int page) async {
    List<CommentItemEntity>? list;
    if (postId != null) {
      list = await ApiService()
          .getPostCommentList(postId: postId!, pageIndex: pageIndex);
    }
    return list;
  }

  Future<bool> sendPostDeleteRequest() async {
    return ApiService().postDelete(postId: postEntity!.postId!);
  }

  Future<bool> sendPostTopRequest(bool top) async {
    return ApiService().postTop(postId: postEntity!.postId!, top: top);
  }

  Future<bool> sendPostHideRequest(bool hide) async {
    return ApiService().postHide(postId: postEntity!.postId!, hide: hide);
  }

  Future<bool> sendReply(String? content) async {
    if (content == null || content.isEmpty == true) {
      return false;
    }
    bool success = false;
    bool isAuthor = UserService().user?.id == postEntity?.userId;
    if (commentId?.isNotEmpty == true) {
      success = await ApiService().sendCommentReply(
        commentsId: commentId!,
        replyUserId: replyUserId,
        isAuthor: isAuthor ? 1 : 0,
        content: content,
      );
    } else {
      success = await ApiService()
          .sendPostComment(postId: postEntity!.postId!, content: content);
    }
    if (success) {
      refreshData();
    }
    return success;
  }
}
