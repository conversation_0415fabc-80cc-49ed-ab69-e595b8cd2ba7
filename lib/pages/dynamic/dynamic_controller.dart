import 'dart:convert';

import 'package:dada/common/values/enums.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/services/im/chat_im_callback.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

class DynamicController extends GetxController {
  late List<String> tabTitles;
  late List<DynamicType> dynamicTypes;

  late TabController tabController;
  late PageController pageController;

  RxInt unreadMsgCount = 0.obs;

  @override
  void onInit() {
    super.onInit();

    ChatIMManager.sharedInstance.addListener(
      ChatImListener(
        onReceiveNewMessage: (V2TimMessage msg) {
          if (msg.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM &&
              msg.customElem?.data != null) {
            ChatImCustomMsgEntity customMsgItem =
                ChatImCustomMsgEntity.fromJson(
              jsonDecode(msg.customElem!.data!),
            );
            if (customMsgItem.type ==
                ChatImCustomMsgType.DaCircleMessageNoticeMsg) {
              unreadMsgCount.value++;
            }
          }
        },
      ),
    );
  }

  @override
  void onReady() {
    super.onReady();

    getUnreadMsgCount();
  }

  void getUnreadMsgCount() async {
    int? unreadCount = await ApiService().getDynamicUnreadCount();
    if (unreadCount != null) {
      unreadMsgCount.value = unreadCount;
    }
  }

  void clearUnreadMsgCount() {
    unreadMsgCount.value = 0;
  }
}
