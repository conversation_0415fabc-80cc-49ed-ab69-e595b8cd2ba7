import 'dart:io';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/dynamic/publish/dynamic_publish_controller.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class DynamicPublishPage extends StatefulWidget {
  const DynamicPublishPage({super.key});

  @override
  State<DynamicPublishPage> createState() => _DynamicPublishPageState();
}

class _DynamicPublishPageState extends State<DynamicPublishPage> {
  final controller = Get.put(DynamicPublishController());
  VideoPlayerController? _videoPlayerController;
  final TextEditingController _editingController = TextEditingController();
  final RxBool _videoPlayerInitialized = false.obs;
  static const int kMaxAssetCount = 3;
  static const int kMaxVideoSize = 35 * 1024 * 1024;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        rightWidgets: [
          _buildRightBarButton(),
        ],
        backgroundColor: Colors.transparent,
      ),
      body: Container(
        padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 44.h),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesDynamicPublishBg),
          ),
        ),
        child: Column(
          children: [
            Stack(
              children: [
                Container(
                  margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 25.h),
                  height: 520.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.colorFF8CA77F,
                        offset: Offset(0, 2.h),
                        blurRadius: 6.r,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTextFieldWidget(),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 13.w),
                        height: 1.h,
                        color: AppColors.colorFFE5EEE5,
                      ),
                      _buildAssetListWidget(),
                    ],
                  ),
                ),
                Positioned(
                  top: 12.h,
                  right: 15.w,
                  child: ImageUtils.getImage(
                      Assets.imagesDynamicPublishContainerRightIcon,
                      123.w,
                      28.h),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRightBarButton() {
    return Obx(
      () {
        bool canPublish = false;
        if (controller.selectedAssets.isNotEmpty ||
            controller.inputText.isNotEmpty) {
          canPublish = true;
        }
        return GestureDetector(
          onTap: () {
            if (!canPublish) {
              return;
            }
            controller.publish();
          },
          child: Container(
            width: 56.w,
            height: 30.h,
            margin: EdgeInsets.only(right: 15.w),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30.h / 2),
              color: canPublish
                  ? AppColors.colorFF62CE67
                  : AppColors.colorFFF5F6F7,
            ),
            child: Text(
              S.current.publish,
              style: TextStyles.common(
                  16.sp,
                  canPublish
                      ? AppColors.colorFF333333
                      : AppColors.colorFF999999),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAssetListWidget() {
    return Obx(() {
      List<AssetEntity> assets = <AssetEntity>[];
      if (controller.postType != PostType.video) {
        if (controller.selectedAssets.length < kMaxAssetCount) {
          assets.addAll(controller.selectedAssets);
          assets.insert(
              0, const AssetEntity(id: "0", typeInt: -1, width: 0, height: 0));
        } else {
          assets = controller.selectedAssets;
        }

        List<String> tags = [];
        List<Widget> children = [];
        for (int i = 0; i < assets.length; i++) {
          AssetEntity? asset = assets[i];
          if (asset.typeInt == -1) {
            children.add(
              _buildImageItemWidget(asset, i, "", tags),
            );
            continue;
          }
          String tag = HeroTagName.postImg.of("1000000_$i");
          tags.add(tag);
          children.add(
            _buildImageItemWidget(asset, i - 1, tag, tags),
          );
        }

        return Container(
          height: 268.h,
          padding: EdgeInsets.only(top: 15.h, left: 10.w, right: 10.w),
          child: GridView.extent(
            padding: EdgeInsets.only(bottom: 20.h),
            childAspectRatio: 1.0,
            mainAxisSpacing: 15.h,
            crossAxisSpacing: 5.w,
            maxCrossAxisExtent: 105.w,
            children: children,
          ),
        );
      } else {
        if (controller.selectedAssets.isEmpty) {
          assets.add(
              const AssetEntity(id: "0", typeInt: -1, width: 0, height: 0));
        } else {
          assets = controller.selectedAssets;
        }

        return Container(
          height: 268.h,
          padding: EdgeInsets.only(top: 15.h, left: 10.w, right: 10.w),
          child: ListView(
            padding: EdgeInsets.zero,
            children: [
              Wrap(
                children: [
                  _buildVideoItemWidget(assets.first),
                ],
              ),
            ],
          ),
        );
      }
    });
  }

  Widget _buildImageItemWidget(
      AssetEntity asset, int index, String tag, List<String> tags) {
    if (asset.typeInt == -1) {
      return _commonAddAssetsWidget(PostType.image);
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.r),
      child: Hero(
        tag: tag,
        child: GestureDetector(
          onTap: () async {
            ImageUtils.showImageBrowser(
              ImageBrowserArgs(
                tags,
                [],
                assets: controller.selectedAssets,
                index: index + 1,
              ),
            );
          },
          child: Stack(
            children: [
              Container(
                width: 105.w,
                height: 105.w,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    fit: BoxFit.cover,
                    image: AssetEntityImageProvider(
                      asset,
                      isOriginal: false,
                      thumbnailSize: const ThumbnailSize(105, 105),
                    ),
                  ),
                ),
              ),
              Positioned(
                right: 0,
                top: 0,
                child: GestureDetector(
                  onTap: () {
                    controller.selectedAssets.removeAt(index);
                    if (controller.selectedAssets.isEmpty) {
                      controller.postType = PostType.text;
                    }
                  },
                  child: ImageUtils.getImage(
                      Assets.imagesDynamicPublishAssetsDelete, 20.w, 20.w),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoItemWidget(AssetEntity asset) {
    if (asset.typeInt == -1 || _videoPlayerController == null) {
      initializeVideoPlayerController(asset);
      return _commonAddAssetsWidget(PostType.video);
    }

    String heroTag = HeroTagName.postVideo.name;
    return Hero(
      tag: heroTag,
      child: GestureDetector(
        onTap: () {
          ///跳转到全屏播放视频
          ImageUtils.showVideoPlayer(
              controller: _videoPlayerController!, heroTag: heroTag);
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Obx(
            () {
              return _videoPlayerInitialized.value == true
                  ? Stack(
                      alignment: Alignment.center,
                      children: [
                        SizedBox(
                          width: _videoPlayerController!.value.size.width >
                                  _videoPlayerController!.value.size.height
                              ? null
                              : 120.w,
                          height: _videoPlayerController!.value.size.width <
                                  _videoPlayerController!.value.size.height
                              ? null
                              : 120.h,
                          child: AspectRatio(
                            aspectRatio:
                                _videoPlayerController!.value.aspectRatio,
                            child: VideoPlayer(_videoPlayerController!),
                          ),
                        ),
                        ImageUtils.getImage(
                            Assets.imagesDynamicListCellVideoPlay, 30.w, 30.w),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: GestureDetector(
                            onTap: () {
                              controller.postType = PostType.text;
                              controller.selectedAssets.removeLast();
                              _videoPlayerController = null;
                            },
                            child: ImageUtils.getImage(
                                Assets.imagesDynamicPublishAssetsDelete,
                                20.w,
                                20.w),
                          ),
                        ),
                      ],
                    )
                  : Container();
            },
          ),
        ),
      ),
    );
  }

  Widget _commonAddAssetsWidget(PostType postType) {
    return GestureDetector(
      onTap: () {
        ToastUtils.showBottomSheet(
          [S.current.album, S.current.camera],
          onTap: (index) async {
            if (index == 0) {
              bool res = await ImagePickerUtil.checkPermission(
                  1, "相册权限说明", "获取图片/视频用于发布广场图片/视频");
              if (!res) return;
              List<AssetEntity>? assets = await ImagePickerUtil.selectAssets(
                  selectedAssets: controller.selectedAssets,
                  maxAssets: postType == PostType.image ? kMaxAssetCount : 1);
              if (assets != null) {
                controller.postType = assets.first.type == AssetType.image
                    ? PostType.image
                    : PostType.video;
                if (controller.postType == PostType.video) {
                  AssetEntity videoAsset = assets.first;
                  File? videoFile = await videoAsset.originFile;
                  if (videoFile != null) {
                    int videoSize = await videoFile.length();
                    debugPrint("视频大小：${videoSize / 1024 * 1024} ");
                    if (videoAsset.duration > 30) {
                      ToastUtils.showToast("视频超出最大限制30s，请重新选择");
                      return;
                    }
                    if (videoSize > kMaxVideoSize) {
                      ToastUtils.showToast("视频超出最大限制35M，请重新选择");
                      return;
                    }
                  }
                }
                controller.selectedAssets.value = assets;
              }
            } else {
              bool res = await ImagePickerUtil.checkPermission(
                  2, "相机权限说明", "拍摄图片/视频用于发布广场图片/视频");
              if (!res) return;
              AssetEntity? asset = await ImagePickerUtil.takeAsset(
                  enableRecording:
                      controller.postType == PostType.image ? false : true);
              if (asset != null) {
                controller.postType = asset.type == AssetType.image
                    ? PostType.image
                    : PostType.video;
                if (controller.postType == PostType.video) {
                  File? videoFile = await asset.originFile;
                  if (videoFile != null) {
                    int videoSize = await videoFile.length();
                    debugPrint("视频大小：${videoSize / 1024 * 1024} ");
                    if (videoSize > kMaxVideoSize) {
                      ToastUtils.showToast("视频超出最大限制35M，请重新录制");
                      return;
                    }
                  }
                  controller.selectedAssets.clear();
                }
                controller.selectedAssets.add(asset);
              }
            }
          },
        );
      },
      child: Container(
        width: 105.w,
        height: 105.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: AppColors.colorFFF5F6F7,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child:
            ImageUtils.getImage(Assets.imagesDynamicPublishAdd, 31.5.w, 31.5.w),
      ),
    );
  }

  ///初始化视频播放器
  Future<void> initializeVideoPlayerController(AssetEntity asset) async {
    if (asset.typeInt == -1) {
      return;
    }
    final String? url = await asset.getMediaUrl();
    if (url == null) {
      return;
    }
    final Uri uri = Uri.parse(url);
    if (Platform.isAndroid) {
      _videoPlayerController = VideoPlayerController.contentUri(uri);
    } else {
      _videoPlayerController = VideoPlayerController.file(File.fromUri(uri));
    }
    try {
      await _videoPlayerController?.initialize();
      _videoPlayerInitialized.value = true;
    } catch (e, s) {
      FlutterError.presentError(
        FlutterErrorDetails(
          exception: e,
          stack: s,
          library: "dada",
          silent: true,
        ),
      );
    } finally {
      if (mounted &&
          !context.debugDoingBuild &&
          context.owner?.debugBuilding != true) {
        // ignore: invalid_use_of_protected_member
        setState(() {});
      }
    }
  }

  Widget _buildTextFieldWidget() {
    int maxLength = 2500; //限制输入2500个字
    return Container(
      height: 251.h,
      padding: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
      child: Stack(
        children: [
          Obx(() {
            return Container(
              padding: EdgeInsets.only(
                  bottom: controller.inputText.value.length >= maxLength - 50
                      ? 20.h
                      : 0.h),
              child: CustomTextField.build(
                decoration: InputDecoration(
                  constraints: BoxConstraints(
                    minHeight: 251.h, // 设置最小高度
                    maxHeight: 251.h, // 设置最大高度
                  ),
                  border: InputBorder.none, // 隐藏下划线
                  counterText: '', // 隐藏文字字数展示
                  hintText: "我想说...",
                ),
                controller: _editingController,
                textInputAction: TextInputAction.done,
                keyboardType: TextInputType.text,
                style: TextStyles.normal(16.sp),
                maxLength: maxLength,
                minLines: 1,
                maxLines: 10000,
                onChanged: (value) {
                  controller.inputText.value = value;
                },
                suffixIconOnTap: () {
                  controller.inputText.value = "";
                },
              ),
            );
          }),
          Obx(
            () => Visibility(
              visible: controller.inputText.value.length >= maxLength - 50,
              child: Positioned(
                bottom: 5.h,
                right: 15.w,
                child: Obx(
                  () => Text(
                    S.current.leftInputWord(
                        maxLength - controller.inputText.value.length),
                    style: TextStyles.common(
                        12.sp, AppTheme.themeData.textTheme.labelSmall?.color),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _videoPlayerController = null;
    super.dispose();
  }
}
