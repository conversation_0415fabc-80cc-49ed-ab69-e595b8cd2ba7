import 'dart:io';

import 'package:dada/common/values/enums.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_compress/video_compress.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class DynamicPublishController extends GetxController {
  RxList<AssetEntity> selectedAssets = <AssetEntity>[].obs;
  PostType postType = PostType.text;
  RxString inputText = "".obs;
  static const int kMaxVideoSize = 35 * 1024 * 1024;

  Future<List<String>> getCurrentSelectedAssetsUrls() async {
    List<String> list = <String>[];

    for (int i = 0; i < selectedAssets.length; i++) {
      AssetEntity asset = selectedAssets[i];
      String? url = await asset.getMediaUrl();
      if (url != null) {
        list.add(url);
      }
    }
    return list;
  }

  Future<void> publish() async {
    ToastUtils.showLoading();
    List<String> uploadedUrls = [];
    for (int i = 0; i < selectedAssets.length; i++) {
      AssetEntity asset = selectedAssets[i];
      File? file = await asset.originFile;

      String? assetUrl = file?.path;
      if (postType == PostType.image) {
        if (assetUrl?.isNotEmpty == true) {
          int timestamp = DateTime.now().millisecondsSinceEpoch;
          var tmpDirectory = await getTemporaryDirectory();
          String tmpImgPath = "${tmpDirectory.path}/${timestamp}_$i.jpg";
          var compressedFile = await FlutterImageCompress.compressAndGetFile(
              assetUrl!,
              tmpImgPath,
              quality: 90,
              rotate: 0);
          if (compressedFile?.path.isNotEmpty == true) {
            assetUrl = compressedFile?.path;
          }
        }
      }
      if (assetUrl != null) {
        DateTime before = DateTime.now();
        String? uploadUrl = await ApiService().uploadFile(assetUrl);
        if (uploadUrl?.isNotEmpty == true) {
          uploadedUrls.add(uploadUrl!);
        }
        DateTime after = DateTime.now();
        int duration = after.difference(before).inMilliseconds;
        debugPrint("请求耗时：$duration ms");
      }
    }

    if (uploadedUrls.length != selectedAssets.length) {
      ToastUtils.hideLoading();
      ToastUtils.showToast("发送失败，请稍后再试");
      return;
    }

    bool success = await ApiService().sendPost(
        content: inputText.value,
        postType: postType.index,
        imageUrls: uploadedUrls);
    if (success) {
      ToastUtils.showToast(S.current.publishSuccess);
      var tmpDirectory = await getTemporaryDirectory();
      _deleteDirectory(tmpDirectory);
      VideoCompress.deleteAllCache();
      Future.delayed(const Duration(seconds: 1), () {
        ApiService().doTask(taskId: "5");
      });
      Get.back(result: 1);
    }
  }

  Future<void> _deleteDirectory(Directory dir) async {
    if (await dir.exists()) {
      // 列出目录中的所有文件和子目录
      final List<FileSystemEntity> entities = dir.listSync(recursive: false);
      for (final FileSystemEntity entity in entities) {
        if (entity is Directory) {
          // 递归删除子目录
          await _deleteDirectory(entity);
        } else if (entity is File) {
          // 删除文件
          await entity.delete();
        }
      }
      // 删除空目录
      await dir.delete();
    }
  }
}
