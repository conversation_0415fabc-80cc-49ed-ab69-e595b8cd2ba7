import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/pages/dynamic/list/dynamic_list_controller.dart';
import 'package:dada/pages/dynamic/post/dynamic_post_cell.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class DynamicListWidget extends StatefulWidget {
  final DynamicType dynamicType;
  final String? userId;

  const DynamicListWidget({super.key, required this.dynamicType, this.userId});

  @override
  State<DynamicListWidget> createState() => _DynamicListWidgetState();
}

class _DynamicListWidgetState extends State<DynamicListWidget> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<DynamicListController>(
      init: DynamicListController(
          type: widget.dynamicType, userId: widget.userId),
      global: false,
      builder: (controller) {
        return RefreshWidget.build(
          refreshController: controller.refreshController,
          onRefresh: () => controller.refreshData(),
          onLoadMore: () => controller.loadMoreData(),
          child: controller.data.isEmpty
              ? const ListPageEmptyWidget()
              : ListView.separated(
                  padding: EdgeInsets.zero,
                  itemBuilder: (context, index) {
                    PostEntity post = controller.data[index];
                    return DynamicPostCell(
                      postEntity: post,
                      dynamicType: widget.dynamicType,
                      callback: () {
                        controller.refreshData();
                      },
                      onLikeChange: (e) {
                        controller.updateState(e);
                      },
                    );
                  },
                  separatorBuilder: (context, index) {
                    return Container(
                      margin: EdgeInsets.only(left: 15.w, right: 15.w),
                      color: AppColors.colorFFF5F6F7,
                      height: 1,
                    );
                  },
                  itemCount: controller.data.length,
                ),
        );
      },
      id: DynamicListController(type: DynamicType.recommend).refreshId,
    );
  }
}
