import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/services/network/api_service.dart';

class DynamicListController
    extends ListPageController<PostEntity, DynamicListController> {
  final DynamicType type;
  final String? userId;

  DynamicListController({required this.type, this.userId});

  @override
  Future<List<PostEntity>?> loadData(int page) async {
    List<PostEntity>? list = await ApiService()
        .getDynamicList(type: type.index, pageIndex: pageIndex, userId: userId);
    return list;
  }

  void updateState(PostEntity entity) {
    //刷新当前列表数据状态
    for (var e in data) {
      if (e.postId == entity.postId) {
        e.likeState = entity.likeState;
      }
    }
    update([refreshId]);
  }
}
