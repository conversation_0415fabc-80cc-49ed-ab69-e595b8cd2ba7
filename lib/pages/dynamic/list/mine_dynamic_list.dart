import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/dynamic/list/dynamic_list_page.dart';
import 'package:flutter/material.dart';

class MineDynamicListPage extends StatelessWidget {
  const MineDynamicListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(title: S.current.mine,),
      body: const DynamicListWidget(dynamicType: DynamicType.mine),
    );
  }
}
