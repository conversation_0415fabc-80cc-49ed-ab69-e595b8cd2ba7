import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/model/comment_reply_item_entity.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:get/get.dart';

class CommentReplyController
    extends ListPageController<CommentReplyItemEntity, CommentReplyController> {
  final PostEntity postEntity;
  final CommentItemEntity commentItem;

  CommentReplyController(this.postEntity, this.commentItem);

  RxString inputText = ''.obs;
  String? replyUserId;
  RxInt replyCount = 0.obs;
  RxBool loaded = false.obs;

  @override
  void onInit() {
    super.onInit();

    replyCount.value = commentItem.replyNo ?? 0;
  }

  @override
  Future<List<CommentReplyItemEntity>?> loadData(int page) async {
    List<CommentReplyItemEntity>? list = await ApiService()
        .getCommentReplyList(commentId: commentItem.commentsId!, pageIndex: pageIndex);
    if (loaded.value == false && list?.isNotEmpty == true) {
      loaded.value = true;
    }
    return list;
  }

  Future<bool> sendReply(
    String? content,
  ) async {
    if (content == null || content.isEmpty == true) {
      return false;
    }
    bool success = false;
    bool isAuthor = UserService().user?.id == postEntity.userId;
    if (commentItem.commentsId?.isNotEmpty == true) {
      success = await ApiService().sendCommentReply(
        commentsId: commentItem.commentsId!,
        replyUserId: replyUserId,
        isAuthor: isAuthor ? 1 : 0,
        content: content,
      );
    }
    if (success) {
      replyCount.value ++;
      replyUserId = null;
      refreshData();
    }
    return success;
  }
}
