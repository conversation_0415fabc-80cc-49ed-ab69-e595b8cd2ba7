import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/pages/dynamic/comment/comment_content_widget.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommentCell extends StatelessWidget {
  final CommentItemEntity commentItemEntity;
  final bool? showReply;
  final Function(String, String, String?)? callback;
  final Function()? showAllReply;

  const CommentCell({
    super.key,
    required this.commentItemEntity,
    this.showReply,
    this.callback,
    this.showAllReply,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          CommentContentWidget(
            commentItemEntity: commentItemEntity,
            callback: callback,
          ),
          _buildCommentReply(),
        ],
      ),
    );
  }

  Widget _buildCommentReply() {
    int replyCount = commentItemEntity.replayList?.list != null
        ? commentItemEntity.replayList!.list!.length
        : 0;
    return Padding(
      padding: EdgeInsets.only(left: 35.w, top: 10.h),
      child: Column(
        children: [
          replyCount > 0
              ? CommentContentWidget(
                  replyItemEntity: commentItemEntity.replayList!.list!.first,
                  maxWidth: ScreenUtil().screenWidth - 50.w - 50.w - 35.w,
                  avatarFontSize: 14.sp,
                  callback: callback,
                )
              : Container(),
          replyCount > 1
              ? Padding(
                  padding: EdgeInsets.only(top: 10.h),
                  child: CommentContentWidget(
                    replyItemEntity: commentItemEntity.replayList!.list![1],
                    maxWidth: ScreenUtil().screenWidth - 50.w - 50.w - 35.w,
                    avatarFontSize: 14.sp,
                    callback: callback,
                  ),
                )
              : Container(),
          commentItemEntity.replyNo != null && commentItemEntity.replyNo! > 2
              ? Padding(
                  padding: EdgeInsets.only(top: 15.h, left: 35.w),
                  child: GestureDetector(
                    onTap: showAllReply,
                    child: Row(
                      children: [
                        Text(
                          S.current
                              .totalCountComment(commentItemEntity.replyNo!),
                          style:
                              TextStyles.common(14.sp, AppColors.colorFF23AF28),
                        ),
                        SizedBox(
                          width: 5.w,
                        ),
                        ImageUtils.getImage(
                            Assets.imagesRightArrowGreen, 7.w, 12.h),
                      ],
                    ),
                  ),
                )
              : Container()
        ],
      ),
    );
  }
}
