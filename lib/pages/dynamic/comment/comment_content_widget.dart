import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/model/comment_reply_item_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CommentContentWidget extends StatelessWidget {
  final CommentItemEntity? commentItemEntity;
  final CommentReplyItemEntity? replyItemEntity;
  final double? maxWidth;
  final double? avatarFontSize;
  final double? contentFontSize;
  final double? dateFontSize;
  final bool? showTailReply;
  final Function(String, String, String?)? callback;

  const CommentContentWidget(
      {super.key,
      this.commentItemEntity,
      this.replyItemEntity,
      this.avatarFontSize,
      this.contentFontSize,
      this.dateFontSize,
      this.maxWidth,
      this.showTailReply,
      this.callback})
      : assert(
          !(commentItemEntity == null && replyItemEntity == null),
          'You must set one of [commentItemEntity] or [replyItemEntity], not both null',
        );

  @override
  Widget build(BuildContext context) {
    RxBool liked = false.obs;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: replyItemEntity != null ? 4.h : 6.h),
          child: GestureDetector(
            onTap: () {
              String? userId =
                  commentItemEntity?.userId ?? replyItemEntity?.userId;
              if (userId != null) {
                Get.toNamed(GetRouter.userProfile,
                    parameters: {"userId": userId});
              }
            },
            child: ClipOval(
              child: ImageUtils.getImage(
                  commentItemEntity?.avatar ?? replyItemEntity?.avatar ?? "",
                  30.w,
                  30.w,
                  fit: BoxFit.cover),
            ),
          ),
        ),
        SizedBox(
          width: 5.w,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  commentItemEntity?.nickname ??
                      replyItemEntity?.nickname ??
                      "",
                  style: TextStyles.common(
                      avatarFontSize ?? 16.sp, AppColors.colorFF999999),
                ),
                Visibility(
                  visible: commentItemEntity?.isAuthor ??
                      replyItemEntity?.isAuthor ??
                      false,
                  child: Container(
                    width: 33.w,
                    height: 17.h,
                    margin: EdgeInsets.symmetric(horizontal: 5.w),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.5.h),
                      border:
                          Border.all(color: AppColors.colorFF23AF28, width: 1),
                      color: AppColors.colorFFECF9EC,
                    ),
                    child: Text(
                      S.current.author,
                      style: TextStyles.common(12.sp, AppColors.colorFF23AF28),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: replyItemEntity != null ? 2.h : 3.h,
            ),
            Container(
              constraints: BoxConstraints(
                maxWidth: maxWidth ?? ScreenUtil().screenWidth - 50.w - 50.w,
              ),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: replyItemEntity?.replayUserName != null
                          ? "${S.current.reply}  "
                          : "",
                      style: TextStyles.normal(
                        contentFontSize ?? 14.sp,
                        h: 1.5,
                      ),
                    ),
                    TextSpan(
                      text: replyItemEntity?.replayUserName != null
                          ? "${replyItemEntity?.replayUserName}:  "
                          : "",
                      style: TextStyles.common(
                        contentFontSize ?? 14.sp,
                        AppColors.colorFF999999,
                        h: 1.5,
                      ),
                    ),
                    TextSpan(
                      text: commentItemEntity?.content ??
                          replyItemEntity?.content ??
                          "",
                      style:
                          TextStyles.normal(contentFontSize ?? 14.sp, h: 1.5),
                    ),
                    TextSpan(
                      text:
                          "  ${TimeUtils.formatPostDate(commentItemEntity?.createdDate ?? replyItemEntity?.createdDate ?? "")}",
                      style: TextStyles.common(
                        dateFontSize ?? 14.sp,
                        AppColors.colorFF999999,
                        h: 1.5,
                      ),
                    ),
                    TextSpan(
                      text:
                          showTailReply != false ? "  ${S.current.reply}" : "",
                      style: TextStyles.normal(
                        contentFontSize ?? 14.sp,
                        h: 1.5,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          callback?.call(
                              commentItemEntity?.commentsId ??
                                  replyItemEntity?.commentsId ??
                                  "",
                              commentItemEntity?.nickname ??
                                  replyItemEntity?.nickname ??
                                  "",
                              replyItemEntity?.userId);
                        },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const Spacer(),
        Padding(
          padding: EdgeInsets.only(top: 5.h, right: 0),
          child: Obx(() {
            liked.value = commentItemEntity?.likeState ??
                replyItemEntity?.likeState ??
                false;
            int likeNo =
                commentItemEntity?.likeNo ?? replyItemEntity?.likeNo ?? 0;
            return GestureDetector(
              onTap: () async {
                bool success = await _sendCommentLikeRequest(!liked.value);
                if (success) {
                  liked.value = !liked.value;
                  if (commentItemEntity != null) {
                    commentItemEntity?.likeState = liked.value;
                  } else {
                    replyItemEntity?.likeState = liked.value;
                  }
                  if (liked.value == true) {
                    if (commentItemEntity != null) {
                      commentItemEntity!.likeNo = likeNo + 1;
                    } else {
                      replyItemEntity!.likeNo = likeNo + 1;
                    }
                  } else {
                    if (commentItemEntity != null) {
                      commentItemEntity!.likeNo = max(likeNo - 1, 0);
                    } else {
                      replyItemEntity!.likeNo = max(likeNo - 1, 0);
                    }
                  }
                }
              },
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: 60.w,
                  minWidth: 14.w,
                ),
                child: Column(
                  children: [
                    ImageUtils.getImage(
                        liked.value == true
                            ? Assets.imagesPostCommentLiked
                            : Assets.imagesPostCommentLike,
                        14.w,
                        14.w),
                    Text(
                      likeNo.toString(),
                      style: TextStyles.common(14.sp, AppColors.colorFF999999),
                    ),
                  ],
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Future<bool> _sendCommentLikeRequest(bool liked) async {
    if (commentItemEntity?.commentsId == null &&
        replyItemEntity?.replyId == null) {
      return false;
    }

    int likeType = commentItemEntity != null ? 2 : 3;
    if (liked) {
      return ApiService().postLike(
          postId: commentItemEntity?.commentsId ?? replyItemEntity!.replyId!,
          likeType: likeType);
    } else {
      return ApiService().postUnLike(
          postId: commentItemEntity?.commentsId ?? replyItemEntity!.replyId!,
          likeType: likeType);
    }
  }
}
