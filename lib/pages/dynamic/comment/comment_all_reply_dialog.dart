import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/loading_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/model/comment_reply_item_entity.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/pages/dynamic/comment/comment_content_widget.dart';
import 'package:dada/pages/dynamic/comment/comment_reply_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CommentAllReplyDialog extends StatefulWidget {
  final PostEntity postEntity;
  final CommentItemEntity commentItemEntity;
  final Function()? callback;

  const CommentAllReplyDialog(
      {super.key,
      required this.commentItemEntity,
      required this.postEntity,
      this.callback});

  @override
  State<CommentAllReplyDialog> createState() => _CommentAllReplyDialogState();
}

class _CommentAllReplyDialogState extends State<CommentAllReplyDialog> {
  late CommentReplyController controller;
  final _editingController = TextEditingController();
  bool refreshAfterBack = false;
  final FocusNode _textFieldNode = FocusNode();
  final RxString _textFieldPlaceholder = ''.obs;

  @override
  void initState() {
    super.initState();

    controller =
        CommentReplyController(widget.postEntity, widget.commentItemEntity);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 0.87.sh,
      decoration: BoxDecoration(
        color: AppTheme.themeData.primaryColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          _buildTitleWidget(),
          _buildCommentDetailWidget(),
          _buildCommentReplyListWidget(),
          _buildTextFieldWidget(),
        ],
      ),
    );
  }

  Widget _buildTitleWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            S.current.allReplies,
            style: TextStyles.normal(16.sp),
          ),
          SizedBox(
            width: 3.w,
          ),
          Obx(
            () {
              return Text(
                controller.replyCount.value != 0
                    ? "（${controller.replyCount.value.toString()}）"
                    : "",
                style: TextStyles.common(14.sp, AppColors.colorFF999999),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCommentDetailWidget() {
    return Column(
      children: [
        Padding(
          padding:
              EdgeInsets.only(top: 9.h, left: 15.w, right: 15.w, bottom: 15.h),
          child: CommentContentWidget(
            commentItemEntity: widget.commentItemEntity,
            contentFontSize: 16.sp,
            showTailReply: false,
          ),
        ),
        Container(
          height: 5.h,
          color: AppColors.colorFFF5F6F7,
        ),
      ],
    );
  }

  Widget _buildCommentReplyListWidget() {
    return GetBuilder(
      init: controller,
      global: false,
      id: CommentReplyController(widget.postEntity, widget.commentItemEntity)
          .refreshId,
      builder: (controller) {
        Widget? stateWidget;
        if (controller.pageState == PageState.loading ||
            controller.pageState == PageState.start) {
          stateWidget = Container();
        } else if (controller.data.isEmpty) {
          stateWidget = ListPageEmptyWidget(
            content: S.current.postDetailNoView,
          );
        }
        return Expanded(
          child: RefreshWidget.build(
            refreshController: controller.refreshController,
            onRefresh: () => controller.refreshData(),
            onLoadMore: () => controller.loadMoreData(),
            child: stateWidget ??
                ListView.separated(
                  padding: EdgeInsets.zero,
                  itemBuilder: (context, index) {
                    CommentReplyItemEntity replyItemEntity =
                        controller.data[index];
                    return InkWell(
                      onTap: () {
                        _textFieldNode.requestFocus();
                        _textFieldPlaceholder.value =
                            "${S.current.reply}${replyItemEntity.nickname}";
                        controller.replyUserId = replyItemEntity.userId;
                      },
                      child: Padding(
                        padding: EdgeInsets.all(15.w),
                        child: CommentContentWidget(
                          replyItemEntity: replyItemEntity,
                          contentFontSize: 16.sp,
                          dateFontSize: 14.sp,
                          callback: (value, name, replyUserId) {
                            if (value.isNotEmpty) {
                              _textFieldNode.requestFocus();
                              controller.replyUserId = replyUserId;
                              if (name.isNotEmpty) {
                                _textFieldPlaceholder.value =
                                    "${S.current.reply}$name";
                              }
                            }
                          },
                          showTailReply: false,
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return Container(
                      margin: EdgeInsets.only(left: 15.w, right: 15.w),
                      color: AppColors.colorFFF5F6F7,
                      height: 1,
                    );
                  },
                  itemCount: controller.data.length,
                ),
          ),
        );
      },
    );
  }

  Widget _buildTextFieldWidget() {
    return Obx(() {
      if (controller.loaded.value == false) {
        return Container();
      }
      return Container(
        height: 56.h + ScreenUtil().bottomBarHeight,
        padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
        child: Container(
          height: 40.h,
          margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 8.h),
          decoration: BoxDecoration(
            color: AppColors.colorFFF5F5F5,
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Obx(() {
            return CustomTextField.build(
              focusNode: _textFieldNode,
              contentPadding: EdgeInsets.only(left: 10.w, right: 10.w),
              controller: _editingController,
              hintText: _textFieldPlaceholder.isNotEmpty
                  ? _textFieldPlaceholder.value
                  : S.current.sendCommentPlaceholder,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.text,
              style: TextStyles.normal(16.sp),
              onChanged: (value) {
                controller.inputText.value = value;
              },
              suffixIconOnTap: () {
                controller.inputText.value = "";
              },
              onSubmitted: (value) async {
                bool success = await controller.sendReply(value);
                if (success) {
                  _editingController.clear();
                  refreshAfterBack = true;
                }
              },
            );
          }),
        ),
      );
    });
  }

  @override
  void dispose() {
    if (refreshAfterBack == true) {
      widget.callback?.call();
    }
    super.dispose();
  }
}
