import 'package:carousel_slider/carousel_slider.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

import 'package:dada/components/widgets/common_gradient_btn.dart';

class DynamicResonateListPage extends StatefulWidget {
  const DynamicResonateListPage({super.key});

  @override
  State<DynamicResonateListPage> createState() =>
      _DynamicResonateListPageState();
}

class _DynamicResonateListPageState extends State<DynamicResonateListPage> {
  String? postId = Get.parameters["postId"];
  int? resonateCount = int.parse(Get.parameters["resonateCount"]!);

  RxInt currentPage = 0.obs;
  RxList<PostEntity> postList = <PostEntity>[].obs;

  @override
  void initState() {
    super.initState();

    getDataList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        backgroundColor: Colors.transparent,
      ),
      body: GradientWidget(
        colors: const [
          AppColors.colorFFE0F9D3,
          AppColors.colorFFF7FDF3,
        ],
        child: Column(
          children: [
            _buildPageViewIndexWidget(),
            Container(
              width: 250.w, // 假设宽度与屏幕宽度一致
              padding: EdgeInsets.only(top: 20.h),
              child: Obx(
                () => Text(
                  postList.isNotEmpty
                      ? '遇到喜欢的动态，记得点亮或挠一下哦，退出后共鸣就消失啦，千万别让缘分错过哈~'
                      : '',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF3D3D3D),
                  ),
                ),
              ),
            ),
            _buildPageView(),
            _buildBottomActionBtnWidgets(),
          ],
        ),
      ),
    );
  }

  Widget _buildPageViewIndexWidget() {
    return Padding(
      padding: EdgeInsets.only(top: 20.h + ScreenUtil().statusBarHeight),
      child: Column(
        children: [
          ImageUtils.getImage(
              Assets.imagesDynamicResonateListPageTopIcon, 93.w, 81.h),
          Padding(
            padding: EdgeInsets.only(top: 5.h),
            child: Obx(
              () => Text(
                "${postList.isNotEmpty ? currentPage.value + 1 : 0}/${postList.length}",
                style: TextStyles.normal(16.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageView() {
    return Padding(
      padding: EdgeInsets.only(top: 20.h),
      child: Obx(() {
        if (postList.isEmpty) {
          if (resonateCount! > 0) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 60.h),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 40.w),
                  child: const Text(
                    '共鸣条目查看后即消失，若想恢复查看该共鸣可使用共鸣找回。',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF3D3D3D),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20.h),
                CommonGradientBtn(
                  title: "共鸣找回",
                  height: 40.h,
                  width: 100.w,
                  normalImage: Assets.imagesCommonGradientBtnBg40h,
                  onTap: () async {
                    ToastUtils.showDialog(
                      content: "确定消耗10搭币找回该共鸣吗？",
                      colors: const [
                        AppColors.colorFFE0F9D3,
                        AppColors.colorFFF7FDF3,
                      ],
                      hideTopImg: true,
                      onConfirm: () async {
                        bool result =
                            await ApiService().backPostResonate(postId!);
                        if (result) {
                          ToastUtils.showToast("找回成功");
                          Future.delayed(const Duration(milliseconds: 200), () {
                            Get.back();
                          });
                        }
                      },
                    );
                  },
                ),
              ],
            );
            //return EmptyWidget(content: "共鸣条目查看后即消失，若想恢复查看该 共鸣可使用共鸣找回。",);
          } else {
            return EmptyWidget();
          }
        }
        return CarouselSlider(
          options: CarouselOptions(
            height: 360.h,
            enableInfiniteScroll: false,
            enlargeCenterPage: true,
            viewportFraction: (290.w + 10.w) / 375.w,
            enlargeFactor: 0.2,
            onPageChanged: (index, reason) {
              currentPage.value = index;
              PostEntity postEntity = postList[currentPage.value];
              viewedResonancePost(postEntity.id!);
            },
          ),
          items: postList.map((e) {
            return Builder(builder: (context) {
              return GestureDetector(
                onTap: () {
                  Get.toNamed(GetRouter.postDetail,
                      parameters: {"postId": e.postId!, "autoFocusNode": "0"});
                },
                child: Container(
                  width: 290.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    image: const DecorationImage(
                      image: AssetImage(Assets.imagesDynamicResonateListItemBg),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Column(
                    children: [
                      _buildListItemHeader(e),
                      _buildListItemContainer(e),
                    ],
                  ),
                ),
              );
            });
          }).toList(),
        );
      }),
    );
  }

  Widget _buildListItemHeader(PostEntity itemEntity) {
    return Container(
      height: 50.h,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ClipOval(
            child: ImageUtils.getImage(itemEntity.avatar ?? "", 35.w, 35.w,
                fit: BoxFit.cover),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.w),
            child: Container(
              constraints: BoxConstraints(
                maxWidth: 180.w,
              ),
              child: Text(
                itemEntity.nickname ?? "",
                overflow: TextOverflow.ellipsis,
                style: TextStyles.bold(18.sp),
              ),
            ),
          ),
          Visibility(
            visible: itemEntity.isFriend == true,
            child: Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: Container(
                width: 35.w,
                height: 20.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  border:
                      Border.all(color: AppColors.colorFF23AF28, width: 1.w),
                ),
                child: Text(
                  "好友",
                  style: TextStyles.common(12.sp, AppColors.colorFF23AF28),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItemContainer(PostEntity itemEntity) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.only(bottom: 15.h),
        child: Column(
          children: [
            _buildListItemTextContainer(itemEntity),
            _buildListItemImageContainer(itemEntity),
          ],
        ),
      ),
    );
  }

  Widget _buildListItemTextContainer(PostEntity itemEntity) {
    return Expanded(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
            child: Text(
              itemEntity.content ?? "",
              style: TextStyles.normal(16.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItemImageContainer(PostEntity itemEntity) {
    int count = 0;
    if (itemEntity.imgUrls?.isNotEmpty == true) {
      if (itemEntity.imgUrls!.length > 3) {
        count = 3;
      } else {
        count = itemEntity.imgUrls!.length;
      }
    }
    return Visibility(
      visible: itemEntity.imgUrls?.isNotEmpty == true,
      child: Container(
        height: 130.h,
        padding: EdgeInsets.only(top: 20.h, bottom: 5.h),
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          itemBuilder: (context, index) {
            String imageUrl = itemEntity.imgUrls![index];
            if (itemEntity.postType == 3) {
              ///视频
              imageUrl = "$imageUrl?x-oss-process=video/snapshot,t_1,m_fast";
            }
            return ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  ImageUtils.getImage(imageUrl, 105.w, 105.w,
                      fit: BoxFit.cover),
                  Visibility(
                    visible: itemEntity.postType == 3,
                    child: ImageUtils.getImage(
                        Assets.imagesDynamicListCellVideoPlay, 30.w, 30.w),
                  ),
                ],
              ),
            );
          },
          separatorBuilder: (context, index) {
            return SizedBox(width: 5.w);
          },
          itemCount: count,
        ),
      ),
    );
  }

  Widget _buildBottomActionBtnWidgets() {
    return Obx(() {
      bool show = false;
      if (postList.isNotEmpty) {
        PostEntity postEntity = postList[currentPage.value];
        show = !(postEntity.isChat == true);
      }
      return Visibility(
        visible: show,
        child: Padding(
          padding: EdgeInsets.only(top: 70.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  PostEntity postEntity = postList[currentPage.value];
                  sendCustomMsgToUser(postEntity,
                      ChatImCustomMsgType.DynamicResonateLightMsg, false);
                },
                child: Column(
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesDynamicResonateListLightBtnIcon,
                        63.w,
                        63.w),
                    Padding(
                      padding: EdgeInsets.only(top: 4.h),
                      child: Text(
                        "点亮",
                        style: TextStyles.normal(12.sp),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 50.w,
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  PostEntity postEntity = postList[currentPage.value];
                  sendCustomMsgToUser(postEntity,
                      ChatImCustomMsgType.DynamicResonateLightMsg, true);
                },
                child: Column(
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesDynamicResonateListScratchBtnIcon,
                        63.w,
                        63.w),
                    Padding(
                      padding: EdgeInsets.only(top: 4.h),
                      child: Text(
                        "挠一下",
                        style: TextStyles.normal(12.sp),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      );
    });
  }

  void sendCustomMsgToUser(
      PostEntity postEntity, String msgType, bool isScratch) async {
    ToastUtils.showLoading();
    bool success = await ChatIMManager.sharedInstance.sendCustomMessage(
        type: msgType,
        data: {"isScratch": isScratch, "postEntity": postEntity.toString()},
        receiver: postEntity.resonateUserId!,
        groupID: "");
    ToastUtils.hideLoading();
    if (success) {
      postEntity.isChat = true;
      setState(() {});
      if (!UserService().checkIsMonthCardUser()) {
        ToastUtils.showBottomDialog(const MonthCardExpiredLimitChatDialog());
      } else {
        V2TimConversation conversation = await ChatIMManager.sharedInstance
            .getConversation(userID: postEntity.resonateUserId, type: 1);
        Get.toNamed(GetRouter.chatDetail, arguments: conversation);
      }
    }
  }

  void getDataList() async {
    if (!(postId?.isNotEmpty == true)) {
      return;
    }
    List<PostEntity>? result =
        await ApiService().getResonancePostList(postId: postId!);
    if (result?.isNotEmpty == true) {
      postList.value = result!;
      viewedResonancePost(postList.first.id!);
    }
  }

  ///标记已读
  void viewedResonancePost(String id) async {
    await ApiService().deleteResonancePost(postIds: [id]);
  }
}
