import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/model/dynamic_resonance_entity.dart';
import 'package:dada/pages/dynamic/resonance/dynamic_resonance_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class DynamicResonancePage extends StatefulWidget {
  const DynamicResonancePage({super.key});

  @override
  State<DynamicResonancePage> createState() => _DynamicResonancePageState();
}

class _DynamicResonancePageState extends State<DynamicResonancePage> {
  final DynamicResonanceController controller =
      Get.put(DynamicResonanceController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFF5F5F5, // 添加这一行设置背景色
      appBar: CustomAppBar(
        title: "共鸣",
      ),
      body: GetBuilder<DynamicResonanceController>(
        init: controller,
        id: controller.refreshId,
        builder: (controller) {
          return RefreshWidget.build(
            refreshController: controller.refreshController,
            onRefresh: () => controller.refreshData(),
            onLoadMore: () => controller.loadMoreData(),
            child: controller.data.isEmpty
                ? const ListPageEmptyWidget()
                : ListView.separated(
                    padding: EdgeInsets.only(top: 10.h, bottom: 20.h),
                    itemCount: controller.data.length,
                    itemBuilder: (context, index) {
                      final item = controller.data[index];
                      return _buildListItem(item);
                    },
                    separatorBuilder: (context, index) {
                      return Container(
                        color: AppColors.colorFFF5F5F5,
                        height: 10.h,
                      );
                    },
                  ),
          );
        },
      ),
    );
  }

  Widget _buildListItem(DynamicResonanceEntity item) {
    return GestureDetector(
      onTap: () {
        Get.toNamed(GetRouter.dynamicResonanceList, parameters: {
          "postId": item.postId!,
          "resonateCount": item.resonateCount!.toString()
        })?.then((value) {
          controller.refreshData();
        });
      },
      child: Container(
        padding: EdgeInsets.all(10.w),
        margin: EdgeInsets.only(left: 10.w, right: 10.w),
        decoration: BoxDecoration(
          color: Colors.white, // 设置背景色为白色
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    item.content ?? "",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyles.normal(16.sp),
                  ),
                ),
                Visibility(
                  visible: item.imageUrl?.isNotEmpty == true,
                  child: Padding(
                    padding: EdgeInsets.only(left: 8.w),
                    child: ImageUtils.getImage(
                      item.imageUrl ?? "",
                      40.w,
                      40.w,
                      radius: 5.r,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 添加这行使两端对齐
              children: [
                Text(
                  '共鸣人数：${item.resonateCount}',
                  style: TextStyles.common(12.sp, AppColors.colorFF666666),
                ),
                if (item.notReadResonateCount != null &&
                    item.notReadResonateCount! > 0)
                  BadgeWidget(
                    text: "${item.notReadResonateCount}",
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
