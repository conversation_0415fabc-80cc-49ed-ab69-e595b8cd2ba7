import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/dynamic_resonance_entity.dart';
import 'package:get/get.dart';
import '../../../services/network/api_service.dart';

class DynamicResonanceController extends ListPageController<DynamicResonanceEntity, DynamicResonanceController> {
  final resonanceList = <DynamicResonanceEntity>[].obs;

  @override
  Future<List<DynamicResonanceEntity>?> loadData(int page) async {
    List<DynamicResonanceEntity>? list = await ApiService()
        .getDynamicResonanceList(page: page);
    return list;
  }
}