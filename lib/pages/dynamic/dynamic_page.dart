import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/dynamic/dynamic_controller.dart';
import 'package:dada/pages/dynamic/list/dynamic_list_page.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class DynamicPage extends StatefulWidget {
  const DynamicPage({super.key});

  @override
  State<DynamicPage> createState() => _DynamicPageState();
}

class _DynamicPageState extends State<DynamicPage>
    with TickerProviderStateMixin {
  final controller = Get.put(DynamicController());

  @override
  void initState() {
    super.initState();

    ///初始化显示推荐动态
    int initialIndex = 1;
    controller.tabTitles = [
      S.current.friend,
      "广场",
      S.current.sameCity,
    ];
    controller.dynamicTypes = [
      DynamicType.friend,
      DynamicType.recommend,
      DynamicType.sameCity,
    ];
    controller.tabController = TabController(
        initialIndex: initialIndex,
        length: controller.tabTitles.length,
        vsync: this);
    controller.pageController = PageController(initialPage: initialIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      body: Column(
        children: [
          _buildTabBarWidget(),
          _buildPageView(),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildTabBarWidget() {
    return Container(
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      width: ScreenUtil().screenWidth,
      height: 50.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Obx(() => BadgeWidget(
                    offset: Offset(7, -5),
                    text: Get.find<MainController>()
                        .postResonateUnreadCount
                        .value
                        .toString(),
                    child: Container(
                      margin: EdgeInsets.only(left: 16.w),
                      width: 50.w,
                      height: 30.h,
                      child: TextButton(
                        style: ButtonStyle(
                            padding:
                                const WidgetStatePropertyAll(EdgeInsets.zero),
                            shape:
                                WidgetStatePropertyAll(RoundedRectangleBorder(
                              side: const BorderSide(
                                  color: AppColors.colorFF999999, width: 1),
                              borderRadius: BorderRadius.circular(10.r),
                            ))),
                        onPressed: () {
                          Get.toNamed(GetRouter.dynamicResonance)
                              ?.then((value) {
                            Get.find<MainController>()
                                .refreshPostResonateUnreadCount();
                          });
                        },
                        child: Text(
                          '共鸣',
                          style:
                              TextStyles.common(13.sp, AppColors.colorFF333333),
                        ),
                      ),
                    ),
                  )),
              SizedBox(width: 20.w),
            ],
          ),
          TabBar(
            padding: EdgeInsets.only(left: 15.w, top: 6.h),
            tabAlignment: TabAlignment.start,
            controller: controller.tabController,
            tabs: controller.tabTitles.map((e) => Tab(text: e)).toList(),
            dividerColor: Colors.transparent,
            isScrollable: true,
            labelPadding: EdgeInsets.only(right: 15.w),
            labelStyle: TextStyles.medium(18.sp),
            labelColor: AppTheme.themeData.textTheme.headlineLarge?.color,
            unselectedLabelColor:
                AppTheme.themeData.textTheme.bodyMedium?.color,
            unselectedLabelStyle: TextStyle(fontSize: 16.sp),
            indicatorWeight: 6.h,
            indicatorPadding:
                EdgeInsets.only(bottom: 14.h, top: 24.h, left: 5.w, right: 5.w),
            indicator: BoxDecoration(
              color: Theme.of(context)
                  .bottomNavigationBarTheme
                  .selectedLabelStyle
                  ?.color,
              borderRadius: BorderRadius.zero,
            ),
            onTap: (index) {
              controller.pageController.animateToPage(index,
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInCubic);
            },
          ),
          Row(
            children: [
              IconButton(
                  onPressed: () {
                    Get.toNamed(GetRouter.locationLimit);
                  },
                  icon: const Icon(
                    Icons.settings_outlined,
                    color: Color.fromRGBO(102, 102, 102, 1),
                  )),
              Padding(
                padding: EdgeInsets.only(right: 20.w),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(GetRouter.message)?.then((v) {
                      Get.find<MainController>().refreshPostMsgUnreadCount();
                    });
                  },
                  child: Obx(
                    () => BadgeWidget(
                      offset: Offset(10.w, -4.h),
                      largeSize: 20.h,
                      text: "${controller.unreadMsgCount.value}",
                      child: ImageUtils.getImage(
                          Assets.imagesDynamicMessage, 20.w, 25.w),
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildPageView() {
    return Expanded(
      child: Stack(
        children: [
          ///PageView
          PageView(
            controller: controller.pageController,
            children: controller.dynamicTypes.map((e) {
              return DynamicListWidget(dynamicType: e);
            }).toList(),
            onPageChanged: (index) {
              controller.tabController.animateTo(index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      children: [
        const Expanded(child: SizedBox()),
        _buildActionButton(Icons.add, () {
          Get.toNamed(GetRouter.postPublish);
        }),
        SizedBox(height: 16.h),
        _buildActionButton(Icons.person, () {
          Get.toNamed(GetRouter.mineDynamic);
        }),
      ],
    );
  }

  Widget _buildActionButton(IconData icon, void Function()? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: ClipOval(
        child: Container(
          width: 40,
          height: 40,
          alignment: Alignment.center,
          decoration: const BoxDecoration(
              gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                AppColors.colorFF90E294,
                AppColors.colorFF26BC2D,
              ])),
          child: Icon(icon, color: Colors.white),
        ),
      ),
    );
  }
}
