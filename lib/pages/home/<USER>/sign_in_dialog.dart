import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/sign_in_list_item_entity.dart';
import 'package:dada/pages/home/<USER>/sign_in_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SignInDialog extends StatefulWidget {
  const SignInDialog({super.key});

  @override
  State<SignInDialog> createState() => _SignInDialogState();
}

class _SignInDialogState extends State<SignInDialog> {
  final controller = Get.put(SignInController());

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 320.w,
          height: 470.h,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                Assets.imagesSignInDialogContentBg,
              ),
              fit: BoxFit.fill,
            ),
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 144.h, left: 5.w),
                child: Obx(() {
                  return Wrap(
                    spacing: 6.w,
                    runSpacing: 15.h,
                    children: controller.list.map((e) {
                      int index = controller.list.indexOf(e);
                      return _buildListItem(e, index);
                    }).toList(),
                  );
                }),
              ),
              const Spacer(),
              _buildSignInBtn(),
            ],
          ),
        ),
        Center(
          child: GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Container(
              margin: EdgeInsets.only(top: 22.h),
              child: ImageUtils.getImage(
                  Assets.imagesDialogBottomClose, 40.w, 40.w),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildListItem(SignInListItemEntity itemEntity, int index) {
    LinearGradient gradient = const LinearGradient(
      colors: [
        Color(0xFF8BFFB5),
        Color(0xFF99FF9E),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );

    ///第三天、第六天是组合奖励
    if (index == 2 || index == 5) {
      return Container(
        width: 128.w,
        height: 115.h,
        decoration: BoxDecoration(
          color: itemEntity.isSign == 1 ? AppColors.colorFFE4E4E4 : null,
          borderRadius: BorderRadius.circular(5.r),
          gradient: itemEntity.isSign == 0 ? gradient : null,
        ),
        child: _buildMultiRewardItem(itemEntity, index),
      );
    }
    return Container(
      width: 75.w,
      height: 115.h,
      decoration: BoxDecoration(
        color: itemEntity.isSign == 1 ? AppColors.colorFFE4E4E4 : null,
        borderRadius: BorderRadius.circular(5.r),
        border: itemEntity.isSign == 1
            ? Border.all(color: AppColors.colorFFF5F5F5, width: 1)
            : Border.all(color: Colors.transparent, width: 1),
        gradient: itemEntity.isSign == 0 ? gradient : null,
      ),
      child: _buildSingleRewardItem(itemEntity, index),
    );
  }

  Widget _buildSingleRewardItem(SignInListItemEntity entity, int index) {
    return Column(
      children: [
        Container(
          width: 40.w,
          height: 16.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(5.r),
              bottomRight: Radius.circular(5.r),
            ),
          ),
          child: Text(
            entity.isSign == 1
                ? S.current.signedIn
                : S.current.signInDayIndex(entity.weekDay ?? index + 1),
            style: TextStyles.common(11.sp, Colors.white),
          ),
        ),
        SizedBox(
          height: 15.h,
        ),
        Container(
          width: 50.w,
          height: 42.h,
          padding: EdgeInsets.symmetric(vertical: 3.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.r),
            color: Colors.white,
          ),
          child: ImageUtils.getImage(
              entity.prizesDTOList?.first.imageUrl ?? "", 50.w, 42.h,
              fit: BoxFit.fitHeight),
        ),
        SizedBox(
          height: 5.h,
        ),
        Text(
          entity.prizesDTOList?.first.prizeName ?? "",
          style: TextStyles.common(
            12.sp,
            entity.isSign == 1
                ? AppColors.colorFF999999
                : AppColors.colorFF168C1A,
          ),
        ),
        SizedBox(
          height: 3.h,
        ),
        Text(
          "x ${entity.prizesDTOList?.first.number ?? ""}",
          style: TextStyles.common(
            12.sp,
            entity.isSign == 1
                ? AppColors.colorFF999999
                : AppColors.colorFF168C1A,
          ),
        ),
      ],
    );
  }

  Widget _buildMultiRewardItem(SignInListItemEntity entity, int index) {
    return Column(
      children: [
        Container(
          width: 40.w,
          height: 16.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(5.r),
              bottomRight: Radius.circular(5.r),
            ),
          ),
          child: Text(
            entity.isSign == 1
                ? S.current.signedIn
                : S.current.signInDayIndex(entity.weekDay ?? index + 1),
            style: TextStyles.common(11.sp, Colors.white),
          ),
        ),
        SizedBox(
          height: 6.h,
        ),
        Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 44.w,
                  height: 37.h,
                  padding: EdgeInsets.symmetric(vertical: 3.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5.r),
                    color: Colors.white,
                  ),
                  child: ImageUtils.getImage(
                      entity.prizesDTOList?.first.imageUrl ?? "", 44.w, 37.h,
                      fit: BoxFit.contain),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 2.w),
                  child: ImageUtils.getImage(
                      Assets.imagesSignInDialogPlus, 16.w, 16.w),
                ),
                Container(
                  width: 44.w,
                  height: 37.h,
                  padding: EdgeInsets.symmetric(vertical: 3.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5.r),
                    color: Colors.white,
                  ),
                  child: ImageUtils.getImage(
                      entity.prizesDTOList?[1].imageUrl ?? "", 44.w, 37.h,
                      fit: BoxFit.contain),
                ),
              ],
            ),
            SizedBox(
              height: 7.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Container(
                  width: 64.w,
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Text(
                        entity.prizesDTOList?.first.prizeName ?? "",
                        style: TextStyles.common(
                          12.sp,
                          entity.isSign == 1
                              ? AppColors.colorFF999999
                              : AppColors.colorFF168C1A,
                        ),
                      ),
                      SizedBox(
                        width: 3.h,
                      ),
                      Visibility(
                        visible: entity.prizesDTOList?.first.number != null &&
                            entity.prizesDTOList!.first.number! > 0,
                        child: Text(
                          "x${entity.prizesDTOList?.first.number ?? ""}",
                          maxLines: 2,
                          textAlign: TextAlign.center,
                          style: TextStyles.common(
                            12.sp,
                            entity.isSign == 1
                                ? AppColors.colorFF999999
                                : AppColors.colorFF168C1A,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 64.w,
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Text(
                        entity.prizesDTOList?[1].prizeName ?? "",
                        style: TextStyles.common(
                          12.sp,
                          entity.isSign == 1
                              ? AppColors.colorFF999999
                              : AppColors.colorFF168C1A,
                        ),
                      ),
                      SizedBox(
                        width: 3.h,
                      ),
                      Visibility(
                        visible: entity.prizesDTOList?[1].number != null &&
                            entity.prizesDTOList![1].number! > 0,
                        child: Text(
                          "x${entity.prizesDTOList?[1].number ?? ""}",
                          style: TextStyles.common(
                            12.sp,
                            entity.isSign == 1
                                ? AppColors.colorFF999999
                                : AppColors.colorFF168C1A,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 6.h,
            ),
            Text(
              "概率获得搭币+6",
              style: TextStyles.common(
                11.sp,
                entity.isSign == 1
                    ? AppColors.colorFF999999
                    : AppColors.colorFF5CA766,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSignInBtn() {
    return Obx(
      () => GestureDetector(
        onTap: () {
          if (controller.signedIn.value == false) {
            controller.signIn();
          }
        },
        child: Container(
          width: 143.w,
          height: 44.h,
          alignment: Alignment.center,
          margin: EdgeInsets.only(bottom: 23.h),
          decoration: BoxDecoration(
            image: DecorationImage(
              image:
                  controller.signedIn.value || controller.weekAllSignedIn.value
                      ? const AssetImage(Assets.imagesGradientBtnDisabledBg)
                      : const AssetImage(Assets.imagesCommonGradientBtnBg),
            ),
          ),
          child: Center(
            child: Text(
              controller.weekAllSignedIn.value
                  ? "本周已签满咯~"
                  : controller.signedIn.value
                      ? S.current.signedIn
                      : S.current.sign,
              style: TextStyle(
                color: AppTheme.themeData.colorScheme.onSecondaryContainer,
                fontSize: 16.sp,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
