import 'package:dada/generated/assets.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';

class MultiAvatarWidget extends StatelessWidget {
  final List<UserInfoEntity>? users;
  final List<String>? urls;
  final double size;
  final double space;
  final Color? borderColor;
  final double? borderWidth;

  const MultiAvatarWidget(
      {super.key,
      this.users,
      this.urls,
      required this.size,
      required this.space,
      this.borderColor,
      this.borderWidth});

  @override
  Widget build(BuildContext context) {
    int itemCount = 0;
    List<String> avatarUrls = <String>[];
    if (users != null) {
      itemCount = users!.length;
      avatarUrls = users!.map((e) => e.avatar!).toList();
    } else if (urls != null) {
      itemCount = urls!.length;
      avatarUrls = urls!;
    }
    if (avatarUrls.isEmpty) {
      return Container();
    }
    double width = (size - space) * (itemCount - 1) + size;
    return SizedBox(
      height: size,
      width: width,
      child: Stack(
        children: avatarUrls.map((e) {
          int index = avatarUrls.indexOf(e);
          return Positioned(
            left: index * (size - space),
            child: Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(size / 2),
                border: Border.all(
                  color: borderColor ?? Colors.white,
                  width: borderWidth ?? 1,
                ),
              ),
              child: ImageUtils.getImage(e, size, size, radius: size / 2,
                  placeholder: Assets.imagesAvatarPlaceholder),
            ),
          );
        }).toList(),
      ),
    );
  }
}
