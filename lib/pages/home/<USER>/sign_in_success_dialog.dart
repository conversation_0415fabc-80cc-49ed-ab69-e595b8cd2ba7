import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/reward_item_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SignInSuccessDialog extends StatelessWidget {
  final List<RewardItemEntity> rewards;

  const SignInSuccessDialog({super.key, required this.rewards});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320.w,
      height: rewards.length > 2 ? 520.h : 395.h,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(rewards.length > 2
              ? Assets.imagesSignInSuccessDialogBg2
              : Assets.imagesSignInSuccessDialogBg1),
          fit: BoxFit.fill,
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 171.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                rewards.isNotEmpty
                    ? _buildRewardItemWidget(rewards[0])
                    : Container(),
                rewards.length > 1
                    ? Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(
                                left: 15.w, right: 15.w, top: 21.h),
                            child: ImageUtils.getImage(
                                Assets.imagesSignInSuccessDialogPlus,
                                25.w,
                                25.w),
                          ),
                          _buildRewardItemWidget(rewards[1]),
                        ],
                      )
                    : Container(),
              ],
            ),
          ),
          _buildExtraRewardWidget(),
          SizedBox(
            height: 20.h,
          ),
          Text(
            S.current.signSuccessTaskTip,
            style: TextStyles.common(12.sp, AppColors.colorFF5E8659),
          ),
          const Spacer(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildExtraRewardWidget() {
    if (rewards.length > 2) {
      return Padding(
        padding: EdgeInsets.only(top: 12.h),
        child: Column(
          children: [
            Text(
              S.current.extraRewardTip,
              style: TextStyles.common(12.sp, AppColors.colorFF5E8659),
            ),
            SizedBox(height: 10.h,),
            _buildRewardItemWidget(rewards[2]),
          ],
        ),
      );
    }
    return Container();
  }

  Widget _buildRewardItemWidget(RewardItemEntity entity) {
    return Column(
      children: [
        Container(
          width: 77.w,
          height: 65.h,
          padding: EdgeInsets.symmetric(vertical: 5.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5.r),
            border: Border.all(color: AppColors.colorFF97FFA1, width: 1),
          ),
          child: ImageUtils.getImage(entity.imageUrl ?? "", 77.w, 65.h),
        ),
        SizedBox(
          height: 5.h,
        ),
        Text(
          entity.prizeName ?? "",
          style: TextStyles.common(14.sp, AppColors.colorFF168C1A),
        ),
        SizedBox(
          height: 5.h,
        ),
        entity.number != null
            ? Text(
                "x ${entity.number ?? ""}",
                style: TextStyles.common(12.sp, AppColors.colorFF168C1A),
              )
            : Container(),
      ],
    );
  }

  Widget _buildBottomBtn() {
    return GestureDetector(
      onTap: () {
        Get.back();
      },
      child: Container(
        width: 143.w,
        height: 44.h,
        alignment: Alignment.center,
        margin: EdgeInsets.only(bottom: 25.h),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesCommonGradientBtnBg),
          ),
        ),
        child: Center(
          child: Text(
            S.current.known,
            style: TextStyle(
              color: AppTheme.themeData.colorScheme.onSecondaryContainer,
              fontSize: 16.sp,
            ),
          ),
        ),
      ),
    );
  }
}
