import 'package:dada/model/reward_item_entity.dart';
import 'package:dada/model/sign_in_item_entity.dart';
import 'package:dada/model/sign_in_list_item_entity.dart';
import 'package:dada/pages/home/<USER>/sign_in_success_dialog.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';

const kSignInDialogShownDate = "SignInDialogShownDate";

class SignInController extends GetxController {
  RxList<SignInListItemEntity> list = <SignInListItemEntity>[].obs;
  RxBool signedIn = false.obs;
  RxBool weekAllSignedIn = false.obs;

  @override
  void onReady() async {
    super.onReady();

    await _loadSignInData(true);
  }

  Future<void> _loadSignInData(bool showLoading) async {
    SignInItemEntity? signInItemEntity = await ApiService().getUserSignInData(showLoading);
    if (signInItemEntity != null) {
      list.value = signInItemEntity.list ?? [];
      signedIn.value = signInItemEntity.today ?? false;
      weekAllSignedIn.value = signInItemEntity.isWeekSignOver ?? false;
    }
  }

  Future<void> signIn() async {
    List<RewardItemEntity>? rewards = await ApiService().signIn();
    if (rewards != null) {
      _loadSignInData(false);
      ToastUtils.showDialog(
        dialog: SignInSuccessDialog(rewards: rewards),
      );
    }
  }
}
