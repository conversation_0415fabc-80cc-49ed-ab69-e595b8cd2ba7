import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';

import 'package:dada/pages/login/login_controller.dart';
import 'package:dada/routers/router.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:dada/common/values/themes.dart';

class SecureLoginPage extends StatefulWidget {
  const SecureLoginPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return SecureLoginState();
  }
}

class SecureLoginState extends State<SecureLoginPage> {
  final TextEditingController _textEditingController = TextEditingController();
  final TextEditingController _passwordEditingController =
      TextEditingController();
  final controller = Get.put(LoginController());
  bool _showPassword = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidgets: [
          TextButton(
            onPressed: () {
              Get.toNamed(GetRouter.login);
            },
            child: Text(
              "注册",
              style: TextStyle(
                color: AppTheme.themeData.colorScheme.onTertiaryContainer,
                fontSize: 16,
              ),
            ),
          )
        ],
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: buildBody(),
      ),
    );
  }

  Widget buildBody() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          30.verticalSpace,
          Text(
            "hi~ 欢迎登录",
            style: TextStyles.medium(20.sp),
          ),
          48.h.verticalSpace,
          textFiled("请输入搭吖号/手机号", _textEditingController),
          20.h.verticalSpace,
          Container(
            width: double.infinity,
            height: 45.h,
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              color: AppColors.colorFFF5F5F5,
              borderRadius: BorderRadius.all(Radius.circular(22.5.h)),
            ),
            padding: EdgeInsets.only(left: 20, right: 20.w, top: 2.h),
            child: TextFormField(
              decoration: InputDecoration(
                hintText: '请输入密码', // 添加默认提示文本
                suffixIcon: IconButton(
                  icon: Icon(
                    _showPassword ? Icons.visibility : Icons.visibility_off,
                    color: Colors.grey, // 修改: 将小眼睛图标的颜色改为灰色
                  ),
                  onPressed: () {
                    setState(() {
                      _showPassword = !_showPassword;
                    });
                  },
                ),
                enabledBorder: const UnderlineInputBorder(
                  // 添加: 设置初始状态下的下划线颜色
                  borderSide: BorderSide(
                      color: AppColors.colorFFF5F5F5), // 修改: 下划线颜色改为#E5EEE5
                ),
                focusedBorder: const UnderlineInputBorder(
                  // 添加: 设置初始状态下的下划线颜色
                  borderSide: BorderSide(
                      color: AppColors.colorFFF5F5F5), // 修改: 下划线颜色改为#E5EEE5
                ),
              ),
              obscureText: !_showPassword,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入密码';
                }
                if (!_isValidPassword(value)) {
                  return '密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）';
                }
                return null;
              },
              onChanged: (value) => _passwordEditingController.text = value,
            ),
          ),
          9.h.verticalSpace,
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                Get.toNamed(GetRouter.forgetPassword);
              },
              child: Text(
                "忘记密码",
                style: TextStyle(
                  color: AppTheme.themeData.colorScheme.onTertiaryContainer,
                  fontSize: 16,
                  decoration: TextDecoration.underline,
                  decorationColor:
                      AppTheme.themeData.colorScheme.onTertiaryContainer,
                ),
              ),
            ),
          ),
          33.h.verticalSpace,
          CommonGradientBtn(
            width: double.infinity,
            title: "登录",
            onTap: () {
              controller.inputPhoneNumber.value = _textEditingController.text;
              controller.inputPasswordNumber.value =
                  _passwordEditingController.text;
              controller.passwordLogin();
            },
          )
        ],
      ),
    ).paddingSymmetric(horizontal: 35.w);
  }

  Widget textFiled(String placeholder, TextEditingController controller,
      {bool length = true}) {
    return Container(
      width: double.infinity,
      height: 45.h,
      decoration: BoxDecoration(
        color: AppColors.colorFFF5F5F5,
        borderRadius: BorderRadius.all(Radius.circular(22.5.h)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: placeholder,
        ),
        inputFormatters: length
            ? [
                LengthLimitingTextInputFormatter(11),
                FilteringTextInputFormatter.allow(RegExp("[0-9]"))
              ]
            : [],
      ),
    );
  }

  bool _isValidPassword(String password) {
    // 密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）
    return RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\W_]{8,16}$')
        .hasMatch(password);
  }
}
