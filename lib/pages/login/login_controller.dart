import 'dart:async';
import 'dart:ui';
import 'package:dada/common/values/colors.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/global.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/log.dart';
import 'package:dada/utils/login_utils.dart';
import 'package:dada/utils/regex_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sp_util/sp_util.dart';

class LoginController extends GetxController {
  final RxString inputPhoneNumber = "".obs;
  final RxString inputPasswordNumber = "".obs;
  final RxInt countdownTime = 0.obs;
  String verifyCode = "";
  Timer? _timer;

  final RxString selectedImagePath = "".obs;
  final RxString inputNickname = "".obs;
  final RxInt selectedSexIndex = 0.obs;
  final RxInt selectedHabitIndex = 1.obs;
  final RxInt inputAge = 18.obs;
  final RxString selectedConstellation = "".obs;
  final Rx<DateTime> selectedBirthday = DateTime(1970).obs;

  final RxList<String> selectedLabels = <String>[].obs;

  late List<String> habits;
  late List<Color> habitsColors;
  late List<String> constellation = [
    "白羊座",
    "金牛座",
    "双子座",
    "巨蟹座",
    "狮子座",
    "处女座",
    "天秤座",
    "天蝎座",
    "射手座",
    "摩羯座",
    "水瓶座",
    "双鱼座"
  ];

  @override
  void onInit() {
    super.onInit();

    habits = [
      S.current.cool,
      S.current.slow,
      S.current.moderate,
      S.current.alive,
      S.current.hospitable
    ];

    habitsColors = [
      const Color(0xFF2867FA),
      const Color(0xFF11E7FF),
      const Color(0xFFF0D657),
      const Color(0xFFFF7018),
      const Color(0xFFFF065D),
    ];
  }

  void getSmsCode() async {
    if (inputPhoneNumber.isEmpty) {
      ToastUtils.showToast(S.current.inputPhoneNumberTip);
      return;
    }
    if (!RegexUtils.isMobileSimple(inputPhoneNumber.value)) {
      ToastUtils.showToast(S.current.inputPhoneErrorTip);
      return;
    }
    bool success =
        await ApiService().sendSmsCode(phone: inputPhoneNumber.value);
    if (success) {
      startCountdown();
    }
  }

  void startCountdown() {
    stopCountdown();
    countdownTime.value = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      countdownTime.value--;
    });
  }

  void stopCountdown() {
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
  }

  void login({int? type}) async {
    if (inputPhoneNumber.isEmpty) {
      ToastUtils.showToast(S.current.inputPhoneNumberTip);
      return;
    }
    if (!RegexUtils.isMobileExact(inputPhoneNumber.value)) {
      ToastUtils.showToast(S.current.inputPhoneErrorTip);
      return;
    }
    if (verifyCode.isEmpty) {
      ToastUtils.showToast(S.current.inputCodeTip);
      return;
    }
    bool success = await ApiService().loginWithCode(
      phone: inputPhoneNumber.value,
      code: verifyCode,
      type: type,
      onError: (apiException) {
        if (apiException.code == 10031) {
          ToastUtils.showDialog(
            content: "账号处于注销冷静期，登录即取消注销，确定要登录吗？",
            hideTopImg: true,
            colors: [
              AppColors.colorFFD2F6C0,
              Colors.white,
            ],
            onConfirm: () {
              login(type: 2);
            },
          );
        } else {
          ToastUtils.showToast(apiException.msg!);
        }
      },
    );
    if (success) {
      if (LoginUtils.isLogined()) {
        Global.initServiceAfterLogin();
        String? token = SpUtil.getString("token");
        Log.i(
            "-----------------------------------------------------\n----------------------------token: $token--------------------------\n------------------------------------------------");
        Get.offAllNamed(GetRouter.main);
      } else {
        Get.toNamed(GetRouter.register);
      }
    }
  }

  void passwordLogin({int? type}) async {
    if (inputPhoneNumber.isEmpty) {
      ToastUtils.showToast(S.current.inputPhoneNumberTip);
      return;
    }
    // if (!RegexUtils.isMobileSimple(inputPhoneNumber.value)) {
    //   ToastUtils.showToast(S.current.inputPhoneErrorTip);
    //   return;
    // }
    if (inputPasswordNumber.isEmpty && inputPasswordNumber.value.length < 7) {
      ToastUtils.showToast("请输入密码,密码位数最少8位");
      return;
    }
    bool success = await ApiService().loginWithCode(
      phone: inputPhoneNumber.value,
      password: inputPasswordNumber.value,
      type: type,
      onError: (apiException) {
        if (apiException.code == 10031) {
          ToastUtils.showDialog(
            content: "账号处于注销冷静期，登录即取消注销，确定要登录吗？",
            hideTopImg: true,
            colors: [
              AppColors.colorFFD2F6C0,
              Colors.white,
            ],
            onConfirm: () {
              passwordLogin(type: 2);
            },
          );
        } else {
          ToastUtils.showToast(apiException.msg!);
        }
      },
    );
    if (success) {
      if (LoginUtils.isLogined()) {
        Global.initServiceAfterLogin();
        String? token = SpUtil.getString("token");
        Log.i(
            "-----------------------------------------------------\n----------------------------token: $token--------------------------\n------------------------------------------------");
        Get.offAllNamed(GetRouter.main);
      } else {
        Get.toNamed(GetRouter.register);
      }
    }
  }

  Future<String?> getRandomNickname() async {
    String? nickname = await ApiService().getRandomNickname();
    return nickname;
  }

  void registerNext() async {
    if (inputNickname.isEmpty) {
      ToastUtils.showToast(S.current.nicknamePlaceholder);
      return;
    }

    if (selectedImagePath.isEmpty) {
      ToastUtils.showToast("请选择头像");
      return;
    }
    Get.toNamed(GetRouter.registerAddLabel);
  }

  void register() async {
    if (selectedLabels.isEmpty) {
      ToastUtils.showToast("至少选择一个标签");
      return;
    }
    String? avatarUrl;
    if (selectedImagePath.value.startsWith('https://')) {
      avatarUrl = selectedImagePath.value;
    } else {
      avatarUrl = await ApiService().uploadFile(selectedImagePath.value);
    }
    bool success = await ApiService().register(
      phone: inputPhoneNumber.value,
      nickname: inputNickname.value,
      sex: selectedSexIndex.value,
      socialState: selectedHabitIndex.value + 1,
      avatar: avatarUrl!,
      age: inputAge.value,
      constellation: selectedConstellation.value,
      labels: selectedLabels,
    );
    if (success) {
      SpUtil.putString("isRegister", "1");
      Global.initServiceAfterLogin();
      Get.offAllNamed(GetRouter.main, parameters: {"isCreateNewAccount": "1"});
    }
  }

  Future<bool> changeAccountBindPhone(String phone, String code) async {
    bool success = await ApiService()
        .changeBindPhone(phone: inputPhoneNumber.value, code: code);
    return success;
  }
}
