import 'dart:io';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/fonts_family.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/login/dialog/privacy_policy_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class WelcomePage extends StatefulWidget {
  const WelcomePage({super.key});

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage> {
  final RxBool checkedPrivacy = false.obs;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // bool? privacyDialogShown = SpUtil.getBool("PrivacyDialogShown");
      // if (privacyDialogShown == null || privacyDialogShown == false) {
      if (Get.isDialogOpen == false) {
        ToastUtils.showDialog(
          barrierDismissible: false,
          dialog: PrivacyPolicyDialog(callback: (agree) {
            checkedPrivacy.value = agree;
            if (!agree) {
              exit(0);
            }
          }),
        );
      }
        // SpUtil.putBool("PrivacyDialogShown", true);
      // }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.colorFFADF0B0,
              AppColors.colorFFA4E4B9,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 79.h + ScreenUtil().statusBarHeight),

            ///logo
            Padding(
              padding: EdgeInsets.only(left: 54.w),
              child: Column(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child:
                        ImageUtils.getImage(Assets.imagesAppIcon, 90.w, 90.w),
                  ),
                  SizedBox(
                    height: 13.h,
                  ),
                  Text(
                    "搭吖",
                    style: TextStyle(
                      fontFamily: FontsFamily.youSheBiaoTiHei,
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.colorFF23AF28,
                    ),
                  ),
                ],
              ),
            ),

            ///介绍语
            Padding(
              padding: EdgeInsets.only(top: 35.h, left: 64.w),
              child: Text(
                S.current.welcomeTip,
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.themeData.textTheme.labelMedium?.color,
                    height: 1.7),
              ),
            ),

            const Spacer(),

            ///按钮
            _loginBtn(),
            20.verticalSpace,

            ///按钮
            _passwordBtn(),

            ///协议选择
            _bottomPolicyLabel(),

            SizedBox(height: MediaQuery.of(context).padding.bottom + 36.h),
          ],
        ),
      ),
    );
  }

  Widget _loginBtn() {
    return CommonGradientBtn(
      title: "手机号登录/注册",
      onTap: () {
        if (!checkedPrivacy.value) {
          ToastUtils.showToast(S.current.checkPolicyToast);
          return;
        }
        Get.toNamed(GetRouter.login);
      },
    );
  }

  Widget _passwordBtn() {
    return TextButton(
      style: TextButton.styleFrom(
        minimumSize: Size(double.infinity, 40.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.h),
        ),
        side: const BorderSide(
          color: Color(0xff23AF28),
          width: 1,
        ),
      ),
      onPressed: () {
        if (!checkedPrivacy.value) {
          ToastUtils.showToast(S.current.checkPolicyToast);
          return;
        }
        Get.toNamed(GetRouter.secureLogin);
      },
      child: const Text(
        "密码登录",
        style: TextStyle(
          color: AppColors.colorFF666666,
          fontSize: 16,
        ),
      ),
    ).paddingSymmetric(horizontal: 38.w);
  }

  Widget _bottomPolicyLabel() {
    return Padding(
      padding: EdgeInsets.only(top: 22.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              checkedPrivacy.value = !checkedPrivacy.value;
            },
            child: Obx(
              () => Icon(
                checkedPrivacy.value == true
                    ? Icons.check_circle
                    : Icons.circle_outlined,
                color: AppTheme.themeData.colorScheme.onSecondaryContainer,
                size: 20.w,
              ),
            ),
          ),
          SizedBox(
            width: 6.w,
          ),
          _richText(),
        ],
      ),
    );
  }

  Widget _richText() {
    return Text.rich(
      TextSpan(
        text: S.current.welcomeBottomTipSub1,
        style: TextStyle(
          fontSize: 14.sp,
          color: AppTheme.themeData.colorScheme.onSecondaryContainer,
        ),
        children: [
          TextSpan(
            text: S.current.userAgreement,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                Get.toNamed(GetRouter.webView,
                    parameters: {"url": AppConfig.userAgreementUrl});
              },
          ),
          TextSpan(
            text: S.current.welcomeBottomTipSub2,
            style: TextStyle(
              fontSize: 14.sp,
            ),
          ),
          TextSpan(
            text: S.current.privacyPolicy,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                Get.toNamed(GetRouter.webView,
                    parameters: {"url": AppConfig.privacyUrl});
              },
          ),
        ],
      ),
    );
  }
}
