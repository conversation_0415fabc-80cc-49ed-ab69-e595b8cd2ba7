import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/login/login_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SelectLabelPage extends StatefulWidget {
  const SelectLabelPage({super.key});

  @override
  State<SelectLabelPage> createState() => _SelectLabelPageState();
}

class _SelectLabelPageState extends State<SelectLabelPage> {
  final controller = Get.find<LoginController>();
  Map<String, Color> labelColorCache = {};

  List<String> labels = [
    "音乐",
    "电影/电视剧",
    "阅读/小说",
    "游戏",
    "永劫无间",
    "LOL",
    "金铲铲之战",
    "剑网3",
    "无畏契约",
    "CS:go",
    "绝地求生",
    "旅行",
    "美术/设计",
    "舞蹈/戏剧",
    "文学/诗歌",
    "手工艺/DIY",
    "球类运动",
    "健身/瑜伽",
    "户外运动",
    "游泳",
    "电子产品",
    "外语学习",
    "烹饪/烘焙",
    "时尚/美容",
    "家具/装修",
    "宠物",
    "历史",
    "天文",
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "",
      ),
      body: ListView(
        children: [
          Row(
            children: [
              SizedBox(width: 15.w),
              ImageUtils.getImage(Assets.imagesRegisterTagIcon, 23.w, 21.w),
              SizedBox(width: 4.w),
              Text(
                "请选择1-5个标签",
                style: TextStyles.bold(16.sp),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(
                top: 15.h, left: 15.w, right: 15.w, bottom: 35.h),
            child: Wrap(
              spacing: 15.w,
              runSpacing: 15.h,
              children: labels.map((e) => _buildLabelItem(e)).toList(),
            ),
          ),
          Container(
            alignment: Alignment.center,
            child: Text(
              "进入应用后，依然可以自行添加自定义标签哦~",
              style: TextStyles.common(12.sp, AppColors.colorFF999999),
            ),
          ),
          _bottomBtn(),
        ],
      ),
    );
  }

  Widget _buildLabelItem(String label) {
    return Obx(() {
      bool selected = controller.selectedLabels.contains(label);
      Color textColor = AppColors.colorFF999999;
      Color bgColor = Colors.white;
      Color borderColor = AppColors.colorFFE1E4E0;
      int randomIndex = Random().nextInt(4);
      if (selected) {
        Color? labelColor = labelColorCache[label];
        if (labelColor != null) {
          textColor = labelColor;
        } else {
          textColor = _getRandomTextColor(randomIndex);
          labelColorCache[label] = textColor;
        }
        bgColor = textColor.withOpacity(0.15);
        borderColor = textColor;
      }
      return LabelItemWidget(
        text: label,
        fontSize: 16.sp,
        height: 35.h,
        textColor: textColor,
        editable: false,
        padding: EdgeInsets.only(left: 14.w, right: 14.w),
        minWidth: 70.w,
        bgColor: bgColor,
        borderColor: borderColor,
        alignment: MainAxisAlignment.center,
        borderRadius: 17.5.r,
        onTap: () {
          if (selected) {
            labelColorCache.remove(label);
            controller.selectedLabels.remove(label);
          } else {
            if (controller.selectedLabels.length >= 5) {
              ToastUtils.showToast("最多选择5个标签");
            } else {
              controller.selectedLabels.add(label);
            }
          }
        },
      );
    });
  }

  Widget _bottomBtn() {
    return GestureDetector(
      onTap: () {
        controller.register();
      },
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(bottom: 20.h, top: 16.h),
        padding: EdgeInsets.symmetric(horizontal: 35.w),
        height: 47.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesLoginBtnBg),
          ),
        ),
        child: Center(
          child: Text(
            "开启搭搭",
            style: TextStyle(
              color: AppTheme.themeData.colorScheme.onSecondaryContainer,
              fontSize: 16.sp,
            ),
          ),
        ),
      ),
    );
  }

  Color _getRandomTextColor(int index) {
    List<Color> colors = [
      AppColors.colorFFFF75AB,
      AppColors.colorFFEDB10C,
      AppColors.colorFF26DED7,
      AppColors.colorFFA847E8,
    ];
    return colors[index];
  }
}
