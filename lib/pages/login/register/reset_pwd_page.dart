import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ResetPwdPage extends StatefulWidget {
  final String phoneNumber;
  final String code;

  const ResetPwdPage(
      {super.key,
      required this.phoneNumber,
      required this.code});

  @override
  State<ResetPwdPage> createState() => _ResetPwdPageState();
}

class _ResetPwdPageState extends State<ResetPwdPage> {
  final TextEditingController _pwdTextField1Controller =
      TextEditingController();
  final TextEditingController _pwdTextField2Controller =
      TextEditingController();
  final FocusNode _focusNode1 = FocusNode();
  final FocusNode _focusNode2 = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "重置登录密码",
      ),
      body: Column(
        children: [
          _buildPwdTextField1Widget(),
          _buildPwdTextField2Widget(),
          _buildBottomTipsLabel(),
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildPwdTextField1Widget() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      height: 60.h,
      child: Column(
        children: [
          Container(
            alignment: Alignment.bottomLeft,
            height: 59.h,
            child: Row(
              children: [
                Container(
                  width: 65.w,
                  margin: EdgeInsets.only(right: 30.w),
                  child: Text(
                    "新密码",
                    style: TextStyles.normal(
                      16.sp,
                    ),
                  ),
                ),
                Expanded(
                  child: CustomTextField.build(
                    focusNode: _focusNode1,
                    controller: _pwdTextField1Controller,
                    maxLength: 16,
                    hintText: "填写新密码",
                    textInputAction: TextInputAction.done,
                    style: TextStyles.normal(16.sp),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 1.h,
            color: AppColors.colorFFE5E5E5,
          ),
        ],
      ),
    );
  }

  Widget _buildPwdTextField2Widget() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      height: 60.h,
      child: Column(
        children: [
          Container(
            alignment: Alignment.bottomLeft,
            height: 59.h,
            child: Row(
              children: [
                Container(
                  width: 65.w,
                  margin: EdgeInsets.only(right: 30.w),
                  child: Text(
                    "确认密码",
                    style: TextStyles.normal(
                      16.sp,
                    ),
                  ),
                ),
                Expanded(
                  child: CustomTextField.build(
                    focusNode: _focusNode2,
                    controller: _pwdTextField2Controller,
                    maxLength: 16,
                    hintText: "再次填写密码",
                    textInputAction: TextInputAction.done,
                    style: TextStyles.normal(16.sp),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 1.h,
            color: AppColors.colorFFE5E5E5,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomTipsLabel() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.h),
      child: Text(
        "密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）",
        style: TextStyles.normal(14.sp, h: 1.5),
      ),
    );
  }

  Widget _buildBottomButton() {
    return CommonGradientBtn(
      topMargin: 30.h,
      title: "确定",
      onTap: () async {
        if (_pwdTextField1Controller.text.isEmpty ||
            _pwdTextField2Controller.text.isEmpty) {
          ToastUtils.showToast("请输入密码");
          return;
        }
        if (_pwdTextField1Controller.text != _pwdTextField2Controller.text) {
          ToastUtils.showToast("两次输入密码不一致");
          return;
        }
        if (!_isValidPassword(_pwdTextField1Controller.text) ||
            !_isValidPassword(_pwdTextField2Controller.text)) {
          ToastUtils.showToast("密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）");
          return;
        }
        bool success = await ApiService().setPassword(
            _pwdTextField1Controller.text,
            phone: widget.phoneNumber);
        if (success) {
          ToastUtils.showToast("密码设置成功");
          if (UserService().user != null) {
            Get.until((route) => route.settings.name == GetRouter.myAccount);
          } else {
            Get.until((route) => route.settings.name == GetRouter.secureLogin);
          }
        }
      },
    );
  }

  bool _isValidPassword(String password) {
    // 密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）
    return RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\W_]{8,16}$')
        .hasMatch(password);
  }
}
