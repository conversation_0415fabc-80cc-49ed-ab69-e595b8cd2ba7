import 'dart:async';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/login/register/reset_pwd_page.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/regex_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PwdForgetPage extends StatefulWidget {
  const PwdForgetPage({super.key});

  @override
  State<PwdForgetPage> createState() => _PwdForgetPageState();
}

class _PwdForgetPageState extends State<PwdForgetPage> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();

  final RxInt _countDown = 0.obs;
  Timer? _timer;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "重置登录密码",
      ),
      body: Column(
        children: [
          _buildPhoneInputTextFieldWidget(),
          _buildCodeInputTextFieldWidget(),
          _buildNextButton(),
        ],
      ),
    );
  }

  Widget _buildPhoneInputTextFieldWidget() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      height: 60.h,
      child: Column(
        children: [
          Container(
            alignment: Alignment.bottomLeft,
            height: 59.h,
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 40.w),
                  child: Text(
                    "手机号",
                    style: TextStyles.normal(
                      16.sp,
                    ),
                  ),
                ),
                Expanded(
                  child: CustomTextField.build(
                    controller: _phoneController,
                    maxLength: 11,
                    hintText: "请输入手机号",
                    textInputAction: TextInputAction.done,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    ],
                    style: TextStyles.normal(16.sp),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 1.h,
            color: AppColors.colorFFE5E5E5,
          ),
        ],
      ),
    );
  }

  Widget _buildCodeInputTextFieldWidget() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      height: 60.h,
      child: Column(
        children: [
          SizedBox(
            height: 59.h,
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 40.w),
                  child: Text(
                    "验证码",
                    style: TextStyles.normal(
                      16.sp,
                    ),
                  ),
                ),
                Expanded(
                  child: CustomTextField.build(
                    controller: _codeController,
                    hintText: "请输入验证码",
                    maxLength: 6,
                    textInputAction: TextInputAction.done,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 1.w),
                  child: GestureDetector(
                    onTap: () async {
                      if (_countDown.value > 0) {
                        return;
                      }
                      if (!_phoneController.text.isNotEmpty) {
                        ToastUtils.showToast("请输入手机号");
                        return;
                      }
                      if (!RegexUtils.isMobileSimple(_phoneController.text)) {
                        ToastUtils.showToast(S.current.inputPhoneErrorTip);
                      }
                      bool success = await ApiService()
                          .sendSmsCode(phone: _phoneController.text);
                      if (success) {
                        startTimer();
                      }
                    },
                    child: Obx(
                      () {
                        bool isCountDown = _countDown.value > 0;
                        return Container(
                          width: 90.w,
                          height: 35.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(17.5.r),
                            gradient: LinearGradient(
                              colors: isCountDown
                                  ? [
                                      AppColors.colorFFA0F6A5.withOpacity(0.5),
                                      AppColors.colorFF58C75D.withOpacity(0.5)
                                    ]
                                  : [
                                      AppColors.colorFFA0F6A5,
                                      AppColors.colorFF58C75D
                                    ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                          ),
                          child: Text(
                            isCountDown ? "${_countDown.value}s" : "获取验证码",
                            style: TextStyles.common(
                              14.sp,
                              isCountDown
                                  ? AppColors.colorFF999999
                                  : AppColors.colorFF333333,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 1.h,
            color: AppColors.colorFFE5E5E5,
          ),
        ],
      ),
    );
  }

  Widget _buildNextButton() {
    return CommonGradientBtn(
      topMargin: 32.h,
      title: "下一步",
      onTap: () async {
        if (!_phoneController.text.isNotEmpty) {
          ToastUtils.showToast("请输入手机号");
          return;
        }
        if (!RegexUtils.isMobileSimple(_phoneController.text)) {
          ToastUtils.showToast(S.current.inputPhoneErrorTip);
          return;
        }
        bool success =
            await ApiService().sendSmsCode(phone: _phoneController.text);
        if (success) {
          Get.to(
            () => ResetPwdPage(
              phoneNumber: _phoneController.text,
              code: _codeController.text,
            ),
          );
        }
      },
    );
  }

  void startTimer() {
    if (_timer != null) {
      return;
    }
    _countDown.value = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countDown.value > 0) {
        _countDown.value--;
      } else {
        _timer?.cancel();
        _timer = null;
      }
    });
  }
}
