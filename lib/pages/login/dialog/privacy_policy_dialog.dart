import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/routers/router.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PrivacyPolicyDialog extends StatefulWidget {
  final Function(bool) callback;

  const PrivacyPolicyDialog({
    super.key,
    required this.callback,
  });

  @override
  State<PrivacyPolicyDialog> createState() => _PrivacyPolicyDialogState();
}

class _PrivacyPolicyDialogState extends State<PrivacyPolicyDialog> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 35.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(15.r)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(
              top: 15.h,
            ),
            child: Text(
              "用户协议与隐私政策",
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 30.h, left: 25.w, right: 25.w),
            child: Text(
              "(1)《隐私政策》中关于个人设备用户信息的手机和使用的说明。\n(2)《隐私政策》中与第三方SDK类服务商数据共享、相关信息收集和使用说明。\n用户协议和隐私政策说明：",
              maxLines: 10,
              style: TextStyles.normal(14.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h, left: 25.w, right: 25.w),
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: "阅读完整的",
                    style: TextStyle(
                      color: AppColors.colorFF999999,
                      fontSize: 14.sp,
                    ),
                  ),
                  TextSpan(
                    text: "《用户协议》",
                    style: TextStyle(
                      color: AppColors.colorFFF425BD,
                      fontSize: 14.sp,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(GetRouter.webView,
                            parameters: {"url": AppConfig.userAgreementUrl});
                      },
                  ),
                  TextSpan(
                    text: "和",
                    style: TextStyle(
                      color: AppColors.colorFF999999,
                      fontSize: 14.sp,
                    ),
                  ),
                  TextSpan(
                    text: "《隐私政策》",
                    style: TextStyle(
                      color: AppColors.colorFFF425BD,
                      fontSize: 14.sp,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(GetRouter.webView,
                            parameters: {"url": AppConfig.privacyUrl});
                      },
                  ),
                  TextSpan(
                    text: "了解详情内容。",
                    style: TextStyle(
                      color: AppColors.colorFF999999,
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: Row(
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    widget.callback(false);
                    Get.back();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: (ScreenUtil().screenWidth - 35.w * 2 - 1.w) / 2,
                    height: 50.h,
                    child: Text(
                      "不同意",
                      style: TextStyles.medium(16.sp,
                          c: AppColors.colorFF3D3D3D, w: FontWeight.w500),
                    ),
                  ),
                ),
                Container(
                  width: 1.w,
                  height: 30.h,
                  color: AppColors.colorFFE5E5E5,
                ),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    widget.callback(true);
                    Get.back();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: (ScreenUtil().screenWidth - 35.w * 2 - 1.w) / 2,
                    height: 50.h,
                    child: Text(
                      "同意并继续",
                      style: TextStyles.medium(16.sp,
                          c: AppColors.colorFFF425BD, w: FontWeight.w500),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
