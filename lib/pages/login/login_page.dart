import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/login/login_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final FocusNode _focusNodePhoneNumber = FocusNode();
  final FocusNode _focusNodeVerifyCode = FocusNode();
  final TextEditingController _textEditingController = TextEditingController();
  final TextEditingController _codeEditingController = TextEditingController();
  final controller = Get.put(LoginController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(
                top: 18.h + ScreenUtil().statusBarHeight, left: 15.w),
            child: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: ImageUtils.getImage(Assets.imagesNaviBack, 8.w, 13.w),
            ),
          ),
          SizedBox(
            height: 21.h,
          ),

          ///标题
          Center(
            child: Text(
              S.current.loginTitle,
              style: TextStyles.medium(20.sp),
            ),
          ),
          Center(
            child: Text(
              S.current.loginSubTitle,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.themeData.textTheme.titleSmall?.color,
              ),
            ),
          ),

          SizedBox(height: 46.h),

          ///手机号输入框
          _phoneNumberTextField(),

          SizedBox(height: 20.h),

          ///验证码输入框
          _verifyCodeTextField(),

          SizedBox(height: 10.h),

          ///手机无法使用
          // _phoneUnUsedBtn(),

          SizedBox(height: 40.h),

          ///登录按钮
          _loginBtn(),
        ],
      ),
    );
  }

  ///手机号输入框
  Widget _phoneNumberTextField() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 37.5.w),
      height: 45.h,
      decoration: BoxDecoration(
        color: AppTheme.themeData.inputDecorationTheme.fillColor,
        borderRadius: BorderRadius.circular(45.h / 2),
      ),
      child: Row(
        children: [
          SizedBox(width: 20.w),
          Text(
            S.current.phoneAreaCode,
            style: TextStyles.normal(18.sp),
          ),
          SizedBox(width: 10.w),
          Container(
            width: 1,
            height: 25.h,
            color: AppTheme.themeData.dividerColor,
          ),
          SizedBox(width: 35.w),
          Expanded(
            child: CustomTextField.build(
              focusNode: _focusNodePhoneNumber,
              controller: _textEditingController,
              maxLength: 11,
              autofocus: true,
              hintText: S.current.inputPhoneNumberTip,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              style: TextStyles.normal(18.sp),
              onChanged: (value) {
                controller.inputPhoneNumber.value = _textEditingController.text;
              },
              decoration: _phoneNumberFieldDecoration(),
            ),
          ),
        ],
      ),
    );
  }

  ///手机号输入框外观配置
  InputDecoration _phoneNumberFieldDecoration() {
    return CustomTextField.textFieldDecoration(
      hintText: S.current.inputPhoneNumberTip,
      suffixIcon: Obx(() {
        if (controller.inputPhoneNumber.isNotEmpty) {
          return GestureDetector(
            onTap: () {
              _textEditingController.clear();
              controller.inputPhoneNumber.value = _textEditingController.text;
            },
            child: Padding(
              padding: EdgeInsets.only(right: 15.w),
              child: ImageUtils.getImage(Assets.imagesCloseRounded, 16.w, 16.w),
            ),
          );
        }
        return const SizedBox();
      }),
      suffixIconConstraints: BoxConstraints(
        maxWidth: 16.w + 15.w,
        maxHeight: 16.w,
        minWidth: 16.w,
        minHeight: 16.w,
      ),
    );
  }

  ///验证码输入框
  Widget _verifyCodeTextField() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 37.5.w),
      padding: EdgeInsets.only(left: 20.w),
      height: 45.h,
      decoration: BoxDecoration(
        color: AppTheme.themeData.inputDecorationTheme.fillColor,
        borderRadius: BorderRadius.circular(45.h / 2),
      ),
      child: CustomTextField.build(
        focusNode: _focusNodeVerifyCode,
        controller: _codeEditingController,
        maxLength: 6,
        autofocus: true,
        hintText: S.current.inputCodeTip,
        textInputAction: TextInputAction.done,
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp("[0-9]")),
        ],
        style: TextStyles.normal(18.sp),
        onChanged: (value) {
          controller.verifyCode = _codeEditingController.text;
        },
        decoration: _codeFieldDecoration(),
      ),
    );
  }

  ///验证码输入框外观配置
  InputDecoration _codeFieldDecoration() {
    return CustomTextField.textFieldDecoration(
      hintText: S.current.inputCodeTip,
      suffixIcon: Obx(() {
        if (controller.countdownTime.value > 0) {
          return Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: Text(
              "${controller.countdownTime.value}s",
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.themeData.hintColor,
              ),
            ),
          );
        }
        return GestureDetector(
          onTap: () {
            _codeEditingController.text = "";
            controller.getSmsCode();
            _focusNodeVerifyCode.requestFocus();
          },
          child: Padding(
            padding: EdgeInsets.only(right: 15.w, top: 3.h),
            child: Text(
              S.current.fetchCodeBtnTitle,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.themeData.textTheme.labelMedium?.color,
              ),
            ),
          ),
        );
      }),
      suffixIconConstraints: BoxConstraints(
        maxWidth: 82.w + 15.w,
        maxHeight: 25.w,
        minWidth: 50.w,
        minHeight: 25.w,
      ),
    );
  }

  ///手机不可用点击按钮
  Widget _phoneUnUsedBtn() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap: () {
            ///TODO: 点击手机无法使用
          },
          child: Text(
            S.current.phoneUnUsedTip,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppTheme.themeData.colorScheme.onTertiaryContainer,
              decoration: TextDecoration.underline,
              decorationColor:
                  AppTheme.themeData.colorScheme.onTertiaryContainer,
            ),
          ),
        ),
        Text(
          "?",
          style: TextStyle(
            fontSize: 16.sp,
            color: AppTheme.themeData.colorScheme.onTertiaryContainer,
          ),
        ),
        SizedBox(
          width: 37.w,
        ),
      ],
    );
  }

  ///登录按钮
  Widget _loginBtn() {
    return GestureDetector(
      onTap: () {
        controller.login();
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 35.w),
        height: 47.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesLoginBtnBg),
          ),
        ),
        child: Center(
          child: Text(
            S.current.next,
            style: TextStyle(
              color: AppTheme.themeData.colorScheme.onSecondaryContainer,
              fontSize: 16.sp,
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    controller.stopCountdown();
    super.dispose();
  }
}
