import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class BackpackUserIdCardDialog extends StatelessWidget {
  final String oldIDNumber;
  final String newIDNumber;

  const BackpackUserIdCardDialog(
      {super.key, required this.newIDNumber, required this.oldIDNumber});

  @override
  Widget build(BuildContext context) {
    return GradientWidget(
      width: 320.w,
      height: 218.h,
      cornerRadius: 20.r,
      stops: const [0, 0.3],
      colors: const [
        AppColors.colorFFD2F6C0,
        Colors.white,
      ],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Text(
              '确定更换搭搭号吗？',
              style: TextStyles.medium(18.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 23.h),
            child: Text(
              "旧的搭搭号：$oldIDNumber",
              style: TextStyles.normal(16.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Text(
              "新的搭搭号：$newIDNumber",
              style: TextStyles.common(16.sp, AppColors.colorFF23AF28),
            ),
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(bottom: 20.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                GestureDetector(
                  onTap: () {
                    sendRequest(0);
                  },
                  child: Container(
                    width: 85.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.colorFF999999,
                      ),
                      borderRadius: BorderRadius.circular(17.5.r),
                    ),
                    child: Text(
                      '保持原号',
                      style: TextStyles.common(16.sp, AppColors.colorFF666666),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    sendRequest(1);
                  },
                  child: Container(
                    width: 85.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(left: 9.w),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          AppColors.colorFFA0F6A5,
                          AppColors.colorFF58C75D,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(17.5.r),
                    ),
                    child: Text(
                      '确定更换',
                      style: TextStyles.common(16.sp, AppColors.colorFF344F3D),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void sendRequest(int type) async {
    bool success = await ApiService().updateUserDadaNo(type);
    if (success) {
      if (type == 1) {
        ToastUtils.showToast("更换成功");
        UserService().refresh();
      }
      Get.back();
    }
  }
}
