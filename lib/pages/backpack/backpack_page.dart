import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/input_text_field_dialog.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/prop_entity.dart';
import 'package:dada/model/use_prop_result_entity.dart';
import 'package:dada/pages/backpack/backpack_prop_list_controller.dart';
import 'package:dada/pages/backpack/dialog/backpack_user_id_card_dialog.dart';
import 'package:dada/pages/backpack/role_image/backpack_role_dress_up_list.dart';
import 'package:dada/pages/backpack/select_friend_list/select_friend_list_page.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class BackpackPage extends StatefulWidget {
  const BackpackPage({super.key});

  @override
  State<BackpackPage> createState() => _BackpackPageState();
}

class _BackpackPageState extends State<BackpackPage> {
  RxInt selectedTabIndex = 0.obs;
  RxInt selectedPropListIndex = 0.obs;
  Rx<PropEntity?> selectedPropEntity = Rx(null);
  bool hasChangedCloth = false;

  late BackpackPropListController backpackListController;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        title: "背包",
        backgroundColor: Colors.transparent,
        backAction: () {
          Get.back(result: hasChangedCloth ? 1 : null);
        },
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return GradientWidget(
      width: ScreenUtil().screenWidth,
      height: ScreenUtil().screenHeight,
      colors: const [
        AppColors.colorFFD1EAD2,
        Colors.white,
      ],
      stops: const [0, 0.5],
      child: Padding(
        padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 44.h),
        child: Row(
          children: [
            _buildLeftTabBar(),
            _buildRightListContainer(),
          ],
        ),
      ),
    );
  }

  Widget _buildLeftTabBar() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      width: 48.w,
      child: Column(
        children: [
          _buildTabBarItem(
            index: 0,
            normalImage: Assets.imagesBackpackLeftTabItem1BgNormal,
            selectedImage: Assets.imagesBackpackLeftTabItem1BgSelected,
          ),
          _buildTabBarItem(
            index: 1,
            normalImage: Assets.imagesBackpackLeftTabItem2BgNormal,
            selectedImage: Assets.imagesBackpackLeftTabItem2BgSelected,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBarItem(
      {required int index,
      required String normalImage,
      required String selectedImage}) {
    return Obx(
      () {
        bool selected = selectedTabIndex.value == index;
        return GestureDetector(
          onTap: () {
            selectedTabIndex.value = index;
          },
          child: Container(
            width: 48.w,
            height: 74.h,
            margin: EdgeInsets.only(bottom: 5.h),
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(selected ? selectedImage : normalImage),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRightListContainer() {
    return Expanded(
      child: Obx(() {
        int currentTabIndex = selectedTabIndex.value;
        if (currentTabIndex == 0) {
          return _buildUserPropListWidget();
        } else if (currentTabIndex == 1) {
          return _buildUserRoleListWidget();
        }
        return Container();
      }),
    );
  }

  Widget _buildUserPropListWidget() {
    return Column(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(10.r),
              ),
              gradient: const LinearGradient(
                colors: [AppColors.colorFFE0F9D3, AppColors.colorFFF7FDF3],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              border: Border.all(color: AppColors.colorFF74AF56, width: 1.r),
            ),
            child: GetBuilder(
              init: BackpackPropListController()..listType = 0,
              global: false,
              id: BackpackPropListController().refreshId,
              builder: (listController) {
                backpackListController = listController;
                setInitializeSelectItem(listController.data);
                return RefreshWidget.build(
                  refreshController: listController.refreshController,
                  onRefresh: () => listController.refreshData(),
                  onLoadMore: () => listController.loadMoreData(),
                  child: listController.data.isEmpty
                      ? const ListPageEmptyWidget()
                      : Stack(
                          children: [
                            GridView.extent(
                              padding: EdgeInsets.only(
                                  top: 15.h,
                                  bottom: 35.w,
                                  left: 11.w,
                                  right: 15.w),
                              maxCrossAxisExtent: 95.w,
                              childAspectRatio: 95.w / 135.h,
                              crossAxisSpacing: 8.w,
                              mainAxisSpacing: 8.w,
                              children: listController.data.map((e) {
                                int index = listController.data.indexOf(e);
                                return _buildBackpackPropListItemWidget(
                                    propEntity: e, index: index);
                              }).toList(),
                            ),

                            ///道具介绍
                            _buildPropItemIntroWidget(),
                          ],
                        ),
                );
              },
            ),
          ),
        ),
        _buildBackpackBottomBar(),
      ],
    );
  }

  Widget _buildBackpackBottomBar() {
    return Obx(
      () => Visibility(
        visible: checkShouldShowBottomBar(),
        child: Container(
          margin: EdgeInsets.only(
              top: 10.h, bottom: ScreenUtil().statusBarHeight > 0 ? 20.h : 0),
          height: 55.h,
          alignment: Alignment.centerRight,
          decoration: BoxDecoration(
            color: AppColors.colorFFF7FDF3,
            border: Border.all(color: AppColors.colorFF74AF56),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Visibility(
                visible: selectedPropEntity.value?.propType ==
                        PropType.womenRoleImage.index ||
                    selectedPropEntity.value?.propType ==
                        PropType.manRoleImage.index,
                child: Padding(
                  padding: EdgeInsets.only(right: 10.w, top: 2.h),
                  child: GestureDetector(
                    onTap: () {
                      String result = selectedPropEntity.value?.propType ==
                              PropType.womenRoleImage.index
                          ? "转换为男款"
                          : "转换为女款";
                      ToastUtils.showDialog(
                        content: "确定要将该服装$result吗？",
                        onConfirm: () async {
                          if (selectedPropEntity.value?.packageId != null) {
                            bool success = await ApiService().clothesConvert(
                                packageId:
                                    selectedPropEntity.value!.packageId!);
                            if (success) {
                              ToastUtils.showToast("转换成功");
                              selectedPropEntity.value = null;
                              selectedPropListIndex.value = 0;
                              backpackListController.refreshData();
                            } else {
                              ToastUtils.showToast("转换失败");
                            }
                          }
                        },
                      );
                    },
                    child: Container(
                      width: 90.w,
                      height: 35.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(35.h / 2),
                        border: Border.all(color: AppColors.colorFF23AF28),
                      ),
                      child: Text(
                        "转换服饰",
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF4DC151),
                      ),
                    ),
                  ),
                ),
              ),
              Visibility(
                visible: selectedPropEntity.value?.propType ==
                        PropType.womenRoleImage.index ||
                    selectedPropEntity.value?.propType ==
                        PropType.manRoleImage.index,
                child: Padding(
                  padding: EdgeInsets.only(right: 10.w, top: 2.h),
                  child: GestureDetector(
                    onTap: () {
                      Get.to(
                        () => SelectFriendListPage(
                          callback: (userId, nickname) async {
                            ToastUtils.showDialog(
                              hideTopImg: true,
                              colors: [AppColors.colorFFD2F6C0, Colors.white],
                              content: "确定赠送 $nickname 服装吗？",
                              onConfirm: () {
                                sendPropToUser(
                                    selectedPropEntity.value!.propId!, userId);
                              },
                            );
                          },
                        ),
                      );
                    },
                    child: Container(
                      width: 70.w,
                      height: 35.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(35.h / 2),
                        border: Border.all(color: AppColors.colorFF23AF28),
                      ),
                      child: Text(
                        "赠送",
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF4DC151),
                      ),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: CommonGradientBtn(
                  title: "使用",
                  width: 103.w,
                  height: 40.h,
                  normalImage: Assets.imagesCommonGradientBtnBgShort,
                  onTap: () {
                    ///使用全域喇叭
                    if (selectedPropEntity.value?.propType ==
                        PropType.globalSpeaker.index) {
                      ToastUtils.showDialog(
                        dialog: InputTextFieldDialog(
                          title: "全域喇叭",
                          width: 320.w,
                          leftMargin: 20.w,
                          isMultiLine: true,
                          maxLength: 25,
                          onSubmit: (value) {
                            if (value.isNotEmpty == true) {
                              useProp(
                                selectedPropEntity.value!.propId!,
                                PropType.globalSpeaker,
                                text: value,
                                num: 1,
                              );
                            } else {
                              ToastUtils.showToast("输入内容不能为空");
                            }
                          },
                        ),
                      );
                    } else if (selectedPropEntity.value?.propType ==
                            PropType.womenRoleImage.index ||
                        selectedPropEntity.value?.propType ==
                            PropType.manRoleImage.index) {
                      hasChangedCloth = true;
                      useClothing(
                          selectedPropEntity.value!,
                          PropType.fromInt(
                              selectedPropEntity.value!.propType!));
                    } else if (selectedPropEntity.value?.propType ==
                        PropType.dyePowder.index) {
                      ToastUtils.showDialog(
                        hideTopImg: true,
                        colors: [AppColors.colorFFD2F6C0, Colors.white],
                        content: "确定把染色剂粉末合成染色剂吗",
                        onConfirm: () {
                          useProp(selectedPropEntity.value!.propId!,
                              PropType.dyePowder);
                        },
                      );
                    } else if (selectedPropEntity.value?.propType ==
                        PropType.idCard.index) {
                      ///靓号卡
                      useProp(
                          selectedPropEntity.value!.propId!, PropType.idCard);
                    } else {
                      useProp(selectedPropEntity.value!.propId!,
                          PropType.fromInt(selectedPropEntity.value!.propType!),
                          num: 1);
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackpackPropListItemWidget(
      {required PropEntity propEntity, required int index}) {
    return Obx(() {
      bool selected = selectedPropListIndex.value == index;
      return GestureDetector(
        onTap: () {
          selectedPropListIndex.value = index;
          selectedPropEntity.value = propEntity;
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: Container(
                width: 95.w,
                height: 95.w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                  border: Border.all(
                    color: selected
                        ? AppColors.colorFF88E15C
                        : AppColors.colorFFDDF8CF,
                    width: 3.w,
                  ),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(0.r),
                      child: ImageUtils.getImage(
                        propEntity.url ?? "",
                        72.w,
                        72.w,
                        fit: BoxFit.contain,
                      ),
                    ),
                    Positioned(
                      right: 5.w,
                      bottom: 3.h,
                      child: Text(
                        "${propEntity.num ?? ""}",
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF418044),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: Text(
                propEntity.propName ?? "",
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                maxLines: 2,
                style:
                    TextStyles.common(14.sp, AppColors.colorFF666666, h: 1.2),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildPropItemIntroWidget() {
    return Obx(() {
      return Visibility(
        visible: selectedPropEntity.value?.description?.isNotEmpty == true,
        child: Positioned(
          bottom:
              !checkShouldShowBottomBar() && ScreenUtil().bottomBarHeight > 0
                  ? ScreenUtil().bottomBarHeight
                  : 0.h,
          child: Container(
            width: ScreenUtil().screenWidth - 48.w,
            padding: EdgeInsets.only(left: 10.w, top: 5.h, bottom: 5.h),
            alignment: Alignment.centerLeft,
            color: AppColors.colorFFD4DBCF,
            child: Text(
              selectedPropEntity.value?.description ?? "",
              style: TextStyles.common(12.sp, AppColors.colorFF666666, h: 1.5),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildUserRoleListWidget() {
    return BackpackRoleDressUpList(
      callback: (value) {
        if (value == 1) {
          hasChangedCloth = true;
        }
      },
    );
  }

  ///使用衣服
  Future<bool> useClothing(PropEntity propEntity, PropType propType) async {
    UsePropResultEntity? usePropResultEntity = await ApiService()
        .useProp(propId: propEntity.propId, useType: 1, propType: propType);
    if (usePropResultEntity != null && usePropResultEntity.code == 1) {
      backpackListController.refreshData();
      UserService().refresh();
      ToastUtils.showToast("使用成功");
    }
    return usePropResultEntity != null;
  }

  void setInitializeSelectItem(List<PropEntity> data) {
    if (data.isEmpty == true) {
      return;
    }
    if (selectedPropEntity.value != null) {
      return;
    }
    Future.delayed(const Duration(milliseconds: 300), () {
      selectedPropEntity.value = data.first;
    });
  }

  Future<bool> sendPropToUser(String propId, String userId) async {
    bool success = await ApiService().sendProp(propId: propId, userId: userId);
    if (success == true) {
      ToastUtils.showToast("赠送成功！");
      backpackListController.refreshData();
      Get.back();
    }
    return success;
  }

  ///使用道具
  Future<bool> useProp(String propId, PropType type,
      {String? text, int? num}) async {
    UsePropResultEntity? usePropResultEntity = await ApiService()
        .useProp(propId: propId, text: text, num: num, propType: type);
    if (usePropResultEntity != null && usePropResultEntity.code == 1) {
      if (type == PropType.idCard) {
        if (usePropResultEntity.dadaNo != null &&
            usePropResultEntity.oldDadaNo != null) {
          ToastUtils.showDialog(
            dialog: BackpackUserIdCardDialog(
                oldIDNumber: usePropResultEntity.oldDadaNo!,
                newIDNumber: usePropResultEntity.dadaNo!),
          );
        }
      }
      await backpackListController.refreshData();
      setCurrentSelectedPropIndex(propId);
      UserService().refresh();
    }
    return usePropResultEntity != null;
  }

  void setCurrentSelectedPropIndex(String propId) {
    List<PropEntity?>? result = backpackListController.data.where((element) {
      return element.propId == propId;
    }).toList();
    PropEntity? propEntity;
    if (result.isNotEmpty == true) {
      propEntity = result.first;
    }
    int index = 0;
    if (propEntity != null) {
      index = backpackListController.data.indexOf(propEntity);
    }
    index = max(0, index);
    selectedPropEntity.value = propEntity;
    selectedPropListIndex.value = index;
  }

  bool checkShouldShowBottomBar() {
    return selectedPropEntity.value?.propType == PropType.dyePowder.index ||
        selectedPropEntity.value?.propType == PropType.globalSpeaker.index ||
        selectedPropEntity.value?.propType == PropType.treasureBox.index ||
        selectedPropEntity.value?.propType == PropType.idCard.index ||
        selectedPropEntity.value?.propType == PropType.womenRoleImage.index ||
        selectedPropEntity.value?.propType == PropType.manRoleImage.index;
  }
}
