import 'package:dada/common/values/enums.dart';
import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/prop_entity.dart';
import 'package:dada/model/use_prop_result_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';

class BackpackPropListController
    extends ListPageController<PropEntity, BackpackPropListController> {
  ///列表类型（0：背包；1：角色）
  late int listType;

  ///道具列表（0服装 1头像框 2聊天框 3特效）
  int? propType;

  @override
  Future<List<PropEntity>?> loadData(int page) async {
    List<PropEntity>? list = await ApiService().getBackpackPropList(
        queryType: listType, page: page, propType: propType);
    return list;
  }

  PropEntity? getUserCurrentRoleDressEntity() {
    if (data.isEmpty) {
      return null;
    }
    return data.first;
  }

  Future<bool> dyeClothing(PropEntity propEntity, int colorIndex) async {
    PropDressColorEntity? colorEntity = propEntity.dressColorList?[colorIndex];
    UsePropResultEntity? result = await ApiService().useProp(
      propNo: "STAINS",
      dressNo: colorEntity?.dressNo,
      colorId: colorEntity?.colorId,
      propType: PropType.stains,
    );
    if (result != null && result.code == 1) {
      propEntity.dressColorList?.forEach((e) {
        if (e.colorId == colorEntity?.colorId) {
          e.isExist = 1;
        }
      });
      for (var e in data) {
        if (e.propId == propEntity.propId) {
          e.dressColorList?.forEach((s) {
            if (s.colorId == colorEntity?.colorId) {
              s.isExist = 1;
            }
          });
        }
      }
      update([refreshId]);
    }
    return result != null;
  }

  Future<bool> useClothing(PropEntity propEntity, int colorIndex) async {
    PropDressColorEntity? colorEntity = propEntity.dressColorList?[colorIndex];
    UsePropResultEntity? result = await ApiService().useProp(
        propId: propEntity.propId,
        dressNo: colorEntity?.dressNo,
        useType: 0,
        propType: UserService().user?.sex == 1
            ? PropType.womenRoleImage
            : PropType.manRoleImage);
    if (result != null && result.code == 1) {
      propEntity.dressColorList?.forEach((e) {
        if (e.colorId == colorEntity?.colorId) {
          e.isUse = 1;
        }
      });
      for (var e in data) {
        if (e.propId == propEntity.propId) {
          e.dressColorList?.forEach((s) {
            if (s.colorId == colorEntity?.colorId) {
              s.isUse = 1;
            }
          });
        }
      }
      update([refreshId]);
    }
    return result != null;
  }

  Future<bool> collectClothing(PropEntity propEntity) async {
    bool isFavorites = propEntity.isFavorites == 1;
    bool success = await ApiService()
        .collectProp(propId: propEntity.propId!, favorites: !isFavorites);
    if (success) {
      propEntity.isFavorites = isFavorites == true ? 0 : 1;
    }
    return success;
  }
}
