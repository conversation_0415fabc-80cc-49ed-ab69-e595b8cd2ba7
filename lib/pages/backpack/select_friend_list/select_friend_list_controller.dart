import 'package:dada/common/values/enums.dart';
import 'package:dada/model/contact_group_list_item_entity.dart';
import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/simple_user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class SelectFriendListController extends GetxController {
  List<SimpleUserInfoEntity> allFriends = [];
  Rx<List<SimpleUserInfoEntity>?> searchedResult = Rx(null);

  @override
  void onReady() {
    super.onReady();

    getAllFriendUserList();
  }

  List<SimpleUserInfoEntity> searchUserInList(String keyword) {
    List<SimpleUserInfoEntity> result =
        allFriends.where((e) => e.nickname?.contains(keyword) == true).toList();
    searchedResult.value = result;
    return result;
  }

  void clearSearchResult() {
    searchedResult.value = null;
  }

  Future<List<SimpleUserInfoEntity>> getAllFriendUserList() async {
    List<SimpleUserInfoEntity> allFriendList = [];
    List<ContactGroupListItemEntity>? contactsGroupList;
    if (Get.isRegistered<ContactsController>()) {
      ContactsController contactsController = Get.find<ContactsController>();
      contactsGroupList = contactsController.contactsGroupList;
    }
    contactsGroupList ??= await ApiService().loadContactList(showLoading: true);
    if (contactsGroupList != null) {
      ContactGroupListItemEntity friendGroupEntity = contactsGroupList
          .where((value) => value.groupType == ContactsGroupType.friend.index)
          .toList()
          .first;

      ///通讯录中未分组的好友列表
      if (friendGroupEntity
          .friendGroupInfo?.unGroupedFriendList?.isNotEmpty ==
          true) {
        List<SimpleUserInfoEntity>? list = createSimpleUserList(
            friendGroupEntity.friendGroupInfo?.unGroupedFriendList);
        if (list?.isNotEmpty == true) {
          allFriendList.addAll(list!);
        }
      }

      ///通讯录中分组的好友列表
      if (friendGroupEntity.friendGroupInfo?.subGroupList?.isNotEmpty ==
          true) {
        for (int i = 0;
        i < friendGroupEntity.friendGroupInfo!.subGroupList!.length;
        i++) {
          FriendSubGroupEntity subGroupEntity =
          friendGroupEntity.friendGroupInfo!.subGroupList![i];
          List<SimpleUserInfoEntity>? list =
          createSimpleUserList(subGroupEntity.friendList);
          if (list?.isNotEmpty == true) {
            allFriendList.addAll(list!);
          }
        }
      }

      ///通讯录中的搭子好友
      ContactGroupListItemEntity daziGroupEntity = contactsGroupList
          .where((value) => value.groupType == ContactsGroupType.dazi.index)
          .toList()
          .first;
      if (daziGroupEntity.daziList?.isNotEmpty == true) {
        List<SimpleUserInfoEntity>? list =
        createSimpleUserList(daziGroupEntity.daziList);
        if (list?.isNotEmpty == true) {
          allFriendList.addAll(list!);
        }
      }
    }
    allFriends = allFriendList;
    update();
    return allFriendList;
  }

  List<SimpleUserInfoEntity>? createSimpleUserList(
      List<FriendUserInfoEntity>? sourceList) {
    List<SimpleUserInfoEntity>? list = sourceList?.map((e) {
      SimpleUserInfoEntity userInfo = SimpleUserInfoEntity();
      userInfo.userId = e.userFriendId;
      userInfo.avatar = e.avatar;
      userInfo.nickname = e.friendRemark ?? e.nickname;
      return userInfo;
    }).toList();
    return list;
  }
}
