import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_search_bar.dart';
import 'package:dada/model/simple_user_info_entity.dart';
import 'package:dada/pages/backpack/select_friend_list/select_friend_list_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SelectFriendListPage extends StatefulWidget {
  final Function(String userId, String nickname) callback;

  const SelectFriendListPage({super.key, required this.callback});

  @override
  State<SelectFriendListPage> createState() => _SelectFriendListPageState();
}

class _SelectFriendListPageState extends State<SelectFriendListPage> {
  SelectFriendListController controller = SelectFriendListController();
  TextEditingController editingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "选择好友",
      ),
      body: Column(
        children: [
          _buildSearchbar(),
          _buildListView(),
        ],
      ),
    );
  }

  Widget _buildSearchbar() {
    return Container(
      height: 50.h,
      color: AppColors.colorFFF5F5F5,
      margin: EdgeInsets.only(top: 5.h, bottom: 10.h),
      child: CustomSearchBar(
        controller: editingController,
        bgColor: Colors.white,
        onChanged: (value) {
          if (value.isEmpty) {
            controller.clearSearchResult();
          }
        },
        onSubmit: (value) {
          controller.searchUserInList(editingController.text);
        },
      ),
    );
  }

  Widget _buildListView() {
    return GetBuilder(
      init: controller,
      builder: (controller) {
        List<SimpleUserInfoEntity> data;
        if (controller.searchedResult.value != null) {
          data = controller.searchedResult.value!;
        } else {
          data = controller.allFriends;
        }
        return Expanded(
          child: ListView.separated(
            itemBuilder: (context, index) {
              SimpleUserInfoEntity userInfo = data[index];
              return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    widget.callback(userInfo.userId!, userInfo.nickname!);
                  },
                  child: Container(
                    height: 60.h,
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child: ClipOval(
                            child: ImageUtils.getImage(
                              userInfo.avatar ?? "",
                              40.w,
                              40.w,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 5.w),
                          child: Text(
                            userInfo.nickname ?? "",
                            style: TextStyles.normal(16.sp),
                          ),
                        ),
                      ],
                    ),
                  ));
            },
            separatorBuilder: (context, index) {
              return Container(
                height: 1.h,
                margin: EdgeInsets.symmetric(horizontal: 15.w),
                color: AppColors.colorFFE5EEE5,
              );
            },
            itemCount: data.length,
          ),
        );
      },
    );
  }
}
