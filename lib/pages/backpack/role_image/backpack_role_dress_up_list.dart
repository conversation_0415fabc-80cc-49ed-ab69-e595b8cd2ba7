import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/account_balance_entity.dart';
import 'package:dada/model/prop_entity.dart';
import 'package:dada/model/use_prop_result_entity.dart';
import 'package:dada/pages/backpack/backpack_prop_list_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/dress_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';

class BackpackRoleDressUpList extends StatefulWidget {
  final Function(int)? callback;
  const BackpackRoleDressUpList({super.key, this.callback});

  @override
  State<BackpackRoleDressUpList> createState() =>
      _BackpackRoleDressUpListState();
}

class _BackpackRoleDressUpListState extends State<BackpackRoleDressUpList>
    with TickerProviderStateMixin {
  RxInt selectedTabIndex = 0.obs;
  RxString selectedListItemIndex = "0-0".obs;
  RxString currentShowRoleImage = "".obs;
  late PageController pageController;
  RxInt selectedColorIndex = 0.obs;
  Rx<PropEntity?> selectedPropEntity = Rx(null);
  RxInt stainsCount = 0.obs;

  List<String> tabItemTitles = ["服装", "头像框", "聊天框"];
  BackpackPropListController? dressListController;
  Map<int, BackpackPropListController> propListControllers = {};

  @override
  void initState() {
    super.initState();

    pageController = PageController(initialPage: 0);

    loadUserStainsCount();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildShowRoleImageWidget(),
        _buildRoleImageListWidget(),
        _buildBottomBarWidget(),
      ],
    );
  }

  Widget _buildShowRoleImageWidget() {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(20.r),
      ),
      child: Container(
        width: 327.w,
        height: 240.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesBackpackShowRoleImageBg),
          ),
        ),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            _buildRoleImageWidget(),
            _buildRoleImageResetBtn(),
            _buildRoleImageColorsListWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleImageListWidget() {
    return Expanded(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [AppColors.colorFFE0F9D3, AppColors.colorFFF7FDF3],
          ),
          border: Border(
            left: BorderSide(color: AppColors.colorFF74AF56),
            right: BorderSide(color: AppColors.colorFF74AF56),
            bottom: BorderSide(color: AppColors.colorFF74AF56),
          ),
        ),
        child: Column(
          children: [
            _buildRoleImageCategoryTabBar(),
            _buildListPageView(),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleImageCategoryTabBar() {
    return Container(
      height: 30.h,
      margin: EdgeInsets.only(left: 10.w, top: 10.h, bottom: 10.h),
      alignment: Alignment.centerLeft,
      child: Wrap(
        spacing: 5.w,
        children: tabItemTitles.map((e) {
          return Obx(() {
            int index = tabItemTitles.indexOf(e);
            bool selected = selectedTabIndex.value == index;
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                selectedTabIndex.value = index;
                pageController.animateToPage(index,
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeInCubic);
              },
              child: Container(
                height: 30.h,
                padding: EdgeInsets.symmetric(horizontal: 11.w),
                constraints: BoxConstraints(
                  minWidth: 56.w,
                ),
                decoration: BoxDecoration(
                  color: selected
                      ? AppColors.colorFF89E15C
                      : AppColors.colorFFE1E4E0,
                  borderRadius: BorderRadius.circular(5.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      e,
                      style: TextStyles.common(
                        14.sp,
                        selected
                            ? AppColors.colorFF2D6D0B
                            : AppColors.colorFF999999,
                        w: selected ? FontWeight.w500 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            );
          });
        }).toList(),
      ),
    );
  }

  Widget _buildListPageView() {
    return Expanded(
      child: PageView(
        controller: pageController,
        onPageChanged: (index) {
          selectedTabIndex.value = index;
        },
        children: tabItemTitles.map((e) {
          int index = tabItemTitles.indexOf(e);
          String refreshId = "BackpackPropListController_list_$index";
          BackpackPropListController propListController =
              BackpackPropListController()
                ..listType = 1
                ..propType = index
                ..refreshId = refreshId;
          propListControllers[index] = propListController;
          if (index == 0) {
            dressListController = propListController;
          }
          return GetBuilder(
            init: propListController,
            global: false,
            id: refreshId,
            builder: (listController) {
              if (index == 0) {
                setInitializeShowRoleImageSvga();
              }
              return RefreshWidget.build(
                refreshController: listController.refreshController,
                onRefresh: () => listController.refreshData(),
                onLoadMore: () => listController.loadMoreData(),
                child: listController.data.isEmpty
                    ? EmptyWidget(
                        onTap: () {
                          listController.refreshData();
                        },
                      )
                    : GridView.extent(
                        padding: EdgeInsets.only(
                            left: 10.w, right: 10.w, bottom: 20.h),
                        maxCrossAxisExtent: 95.w,
                        mainAxisSpacing: 10.h,
                        crossAxisSpacing: 10.w,
                        childAspectRatio: 1.0,
                        children: listController.data.map((e) {
                          int index = listController.data.indexOf(e);
                          return _buildListItemView(e, index);
                        }).toList(),
                      ),
              );
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildListItemView(PropEntity itemEntity, int index) {
    return Obx(() {
      bool selected =
          selectedListItemIndex.value == "${selectedTabIndex.value}-$index";
      bool isUsed = false;
      if (selectedTabIndex.value == 0) {
        isUsed = itemEntity.isUse == 1;
        // if (itemEntity.dressColorList != null &&
        //     itemEntity.dressColorList!.length > selectedColorIndex.value) {
        //   PropDressColorEntity? colorEntity =
        //       itemEntity.dressColorList?[selectedColorIndex.value];
        //   isUsed = itemEntity.isUse == 1 && colorEntity?.isUse == 1;
        // }
      } else {
        isUsed = itemEntity.isUse == 1;
      }
      return GestureDetector(
        onTap: () {
          if (selectedListItemIndex.value ==
              "${selectedTabIndex.value}-$index") {
            return;
          }
          selectedListItemIndex.value = "${selectedTabIndex.value}-$index";
          if (selectedTabIndex.value == 0) {
            selectedPropEntity.value = itemEntity;
            String? colorKey = getCurrentDressColorKey(itemEntity);
            setSelectedRoleDressColorImageSvga(itemEntity, colorKey: colorKey);
          } else {
            selectedPropEntity.value = itemEntity;
          }
        },
        child: Stack(
          children: [
            Container(
              width: 95.w,
              height: 95.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                border: Border.all(
                  color: selected
                      ? AppColors.colorFF88E15C
                      : AppColors.colorFFDDF8CF,
                  width: 3.w,
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  ImageUtils.getImage(itemEntity.url ?? "", 90.w, 90.w),
                ],
              ),
            ),

            ///遮罩
            Visibility(
              visible: itemEntity.isExist == 0,
              child: Container(
                width: 95.w,
                height: 95.w,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: ImageUtils.getImage(
                    Assets.imagesBackpackDressListLock, 19.w, 25.w),
              ),
            ),

            ///使用中标签
            Visibility(
              visible: isUsed,
              child: Positioned(
                right: -10.w,
                top: -10.w,
                child: ImageUtils.getImage(
                    Assets.imagesBackpackDressListItemUsedIcon, 58.w, 58.w),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildBottomBarWidget() {
    return Obx(
      () {
        bool visible = checkShouldShowBottomBar();
        bool showLeftBtn = checkShouldShowCollectBtn();
        int leftStainsCount = stainsCount.value;
        int costStainsCount = getDressColorCostStainsCount();
        bool showDyeBtn = checkShouldShowDyeBtn();
        bool showCancelBtn = checkCancelBtn();
        bool dyeEnable = leftStainsCount >= costStainsCount;
        bool showUseBtn = checkShouldShowUseBtn();
        bool isCollect = selectedPropEntity.value?.isFavorites == 1;

        return Visibility(
          visible: true,
          child: Container(
            margin: EdgeInsets.only(
                top: 10.h, bottom: ScreenUtil().statusBarHeight > 0 ? 20.h : 0),
            height: 55.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFF7FDF3,
              border: Border.all(color: AppColors.colorFF74AF56),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(),
                Visibility(
                  visible: showDyeBtn,
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 10.w),
                        constraints: BoxConstraints(
                          maxWidth: showLeftBtn ? 85.w : double.infinity,
                        ),
                        child: Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: "消耗",
                                style: TextStyles.normal(
                                  12.sp,
                                  c: dyeEnable
                                      ? AppColors.colorFF3D3D3D
                                      : AppColors.colorFFF41D1D,
                                ),
                              ),
                              WidgetSpan(
                                child: ImageUtils.getImage(
                                  Assets.imagesBackpackBottomStainsIcon,
                                  13.w,
                                  15.h,
                                ),
                              ),
                              TextSpan(
                                text: "：",
                                style: TextStyles.normal(12.sp,
                                    c: dyeEnable
                                        ? AppColors.colorFF3D3D3D
                                        : AppColors.colorFFF41D1D),
                              ),
                              TextSpan(
                                text: "$leftStainsCount/$costStainsCount",
                                style: TextStyles.normal(
                                  12.sp,
                                  c: dyeEnable
                                      ? AppColors.colorFF3D3D3D
                                      : AppColors.colorFFF41D1D,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Visibility(
                      visible: showLeftBtn,
                      child: GestureDetector(
                        onTap: () async {
                          bool? success = await dressListController
                              ?.collectClothing(selectedPropEntity.value!);
                          if (success == true) {
                            setState(() {});
                          }
                        },
                        child: Container(
                          width: 103.w,
                          height: 44.h,
                          padding: EdgeInsets.only(bottom: 3.h),
                          decoration: const BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage(
                                  Assets.imagesCommonGradientBtnBg103w44h),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ImageUtils.getImage(
                                isCollect
                                    ? Assets.imagesBackpackClothingItemCollected
                                    : Assets.imagesBackpackClothingItemCollect,
                                20.5.w,
                                20.h,
                              ),
                              Padding(
                                padding: EdgeInsets.only(left: 5.w),
                                child: Text(
                                  "收藏",
                                  style: TextStyles.common(
                                      16.sp,
                                      isCollect
                                          ? AppColors.colorFFF3971F
                                          : AppColors.colorFF3D3D3D),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Visibility(
                      visible: true,
                      child: Padding(
                        padding: EdgeInsets.only(right: 10.w),
                        child: Stack(
                          children: [
                            Visibility(
                              visible: showUseBtn,
                              child: CommonGradientBtn(
                                width: 103.w,
                                height: 44.h,
                                title: "使用",
                                normalImage:
                                    Assets.imagesCommonGradientBtnBg103w44h,
                                fit: BoxFit.scaleDown,
                                onTap: () async {
                                  if (selectedTabIndex.value == 0) {
                                    bool? success =
                                        await dressListController?.useClothing(
                                            selectedPropEntity.value!,
                                            selectedColorIndex.value);
                                    if (success == true) {
                                      widget.callback?.call(1);
                                      await dressListController?.refreshData();
                                      dressListController?.update(
                                          [dressListController!.refreshId]);
                                      UserService().refresh();
                                      selectedPropEntity.value =
                                          dressListController!
                                              .getUserCurrentRoleDressEntity();
                                      int index = dressListController!.data
                                          .indexOf(selectedPropEntity.value!);
                                      index = max(0, index);
                                      selectedListItemIndex.value =
                                          "${selectedTabIndex.value}-index";
                                      String? colorKey =
                                          getCurrentDressColorKey(
                                              selectedPropEntity.value);
                                      setSelectedRoleDressColorImageSvga(
                                          selectedPropEntity.value!,
                                          colorKey: colorKey);
                                    }
                                  } else {
                                    UsePropResultEntity? result =
                                        await ApiService().useProp(
                                      propId: selectedPropEntity.value!.propId!,
                                      propType: selectedTabIndex.value == 1
                                          ? PropType.avatarFrame
                                          : PropType.chatBox,
                                    );
                                    if (result != null && result.code == 1) {
                                      BackpackPropListController? controller =
                                          propListControllers[
                                              selectedTabIndex.value];
                                      await controller?.refreshData();
                                      controller
                                          ?.update([controller.refreshId]);
                                      UserService().refresh();
                                      selectedListItemIndex.value =
                                          "${selectedTabIndex.value}-0";
                                      selectedPropEntity.value =
                                          controller?.data.first;
                                    }
                                  }
                                },
                              ),
                            ),
                            Visibility(
                              visible: showDyeBtn,
                              child: CommonGradientBtn(
                                width: 103.w,
                                height: 44.h,
                                title: "染色",
                                normalImage: dyeEnable
                                    ? Assets.imagesCommonGradientBtnBg103w44h
                                    : Assets
                                        .imagesCommonGradientBtnDisabled103w44h,
                                fit: BoxFit.scaleDown,
                                onTap: () async {
                                  if (dyeEnable == true) {
                                    bool? success =
                                        await dressListController?.dyeClothing(
                                            selectedPropEntity.value!,
                                            selectedColorIndex.value);
                                    if (success == true) {
                                      loadUserStainsCount();
                                      setState(() {});
                                    }
                                  }
                                },
                              ),
                            ),
                            Visibility(
                              visible: showCancelBtn,
                              child: GestureDetector(
                                onTap: () async {
                                  UsePropResultEntity? result =
                                  await ApiService().useProp(
                                    propId: selectedPropEntity.value!.propId!,
                                    propType: selectedTabIndex.value == 1
                                        ? PropType.avatarFrame
                                        : PropType.chatBox,
                                  );
                                  if (result != null && result.code == 1) {
                                    BackpackPropListController? controller =
                                    propListControllers[
                                    selectedTabIndex.value];
                                    await controller?.refreshData();
                                    controller
                                        ?.update([controller.refreshId]);
                                    UserService().refresh();
                                    selectedListItemIndex.value =
                                    "${selectedTabIndex.value}-0";
                                    selectedPropEntity.value =
                                        controller?.data.first;
                                  }
                                },
                                child: Container(
                                  width: 103.w,
                                  height: 44.h,
                                  padding: EdgeInsets.only(bottom: 3.h),
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage(
                                          Assets.imagesCommonGradientBtnBg103w44h),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.only(left: 5.w),
                                        child: Text(
                                          "取消使用",
                                          style: TextStyles.common(
                                              16.sp,
                                              isCollect
                                                  ? AppColors.colorFFF3971F
                                                  : AppColors.colorFF3D3D3D),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  ///角色形象显示
  Widget _buildRoleImageWidget() {
    return Obx(() {
      if (currentShowRoleImage.value.isEmpty) {
        return Container();
      }
      return Padding(
        padding: EdgeInsets.only(bottom: 8.h),
        child: SizedBox(
          width: 120.w,
          height: 210.h,
          child: SVGASimpleImage(
            assetsName: currentShowRoleImage.value,
          ),
        ),
      );
    });
  }

  Widget _buildRoleImageResetBtn() {
    return Obx(
      () => Visibility(
        visible: selectedTabIndex.value == 0,
        child: Positioned(
          left: 10.w,
          bottom: 12.h,
          child: GestureDetector(
            onTap: () async {
              if (dressListController != null) {
                selectedPropEntity.value = null;
                currentShowRoleImage.value = "";
                selectedListItemIndex.value = "";
                setInitializeShowRoleImageSvga();
              }
            },
            child: Container(
              width: 24.w,
              height: 50.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.w),
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 8.55.h),
                    child: ImageUtils.getImage(
                        Assets.imagesBackpackRoleImageResetBtn, 16.w, 16.w),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 3.h),
                    child: Text(
                      "重置",
                      style: TextStyles.normal(10.sp),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRoleImageColorsListWidget() {
    return Obx(() {
      if (!(selectedPropEntity.value?.dressColorList?.isNotEmpty == true)) {
        return Container();
      }
      if (selectedTabIndex.value != 0) {
        return Container();
      }
      return Positioned(
        bottom: 13.h,
        right: 10.w,
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(bottom: 5.h),
              child: Text(
                "染色",
                style:
                    TextStyles.common(16.sp, Colors.white, w: FontWeight.w500),
              ),
            ),
            SizedBox(
              width: 24.w * 2 + 8.w,
              child: Wrap(
                alignment: WrapAlignment.center,
                runSpacing: 10.h,
                spacing: 5.w,
                children: selectedPropEntity.value!.dressColorList!.map((e) {
                  return Obx(() {
                    int index =
                        selectedPropEntity.value!.dressColorList!.indexOf(e);
                    bool selected = selectedColorIndex.value == index;
                    bool locked = !(e.isExist == 1);
                    return GestureDetector(
                      onTap: () {
                        selectedColorIndex.value = index;
                        String? colorKey = e.colorNo;
                        setSelectedRoleDressColorImageSvga(
                            selectedPropEntity.value!,
                            colorKey: colorKey);
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 24.w,
                            height: 24.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12.r),
                              border: Border.all(
                                color: selected
                                    ? Colors.white.withOpacity(0.7)
                                    : Colors.transparent,
                              ),
                            ),
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                Container(
                                  width: 20.w,
                                  height: 20.w,
                                  decoration: BoxDecoration(
                                    color: getCurrentColorWithKey(e.colorNo!),
                                    borderRadius: BorderRadius.circular(10.r),
                                    border: Border.all(
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible: locked,
                                  child: Container(
                                    width: 20.w,
                                    height: 20.w,
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(10.r),
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible: locked,
                                  child: ImageUtils.getImage(
                                    Assets.imagesBackpackRoleImageColorLock,
                                    7.5.w,
                                    10.h,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 3.h),
                            child: Text(
                              e.remark ?? "",
                              style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.colorFF764E16),
                            ),
                          ),
                        ],
                      ),
                    );
                  });
                }).toList(),
              ),
            ),
          ],
        ),
      );
    });
  }

  bool checkShouldShowBottomBar() {
    bool show = false;
    if (selectedPropEntity.value != null) {
      if (selectedPropEntity.value!.isExist == 1) {
        if (selectedTabIndex.value == 0) {
          if (selectedPropEntity.value!.dressColorList?.isNotEmpty == true) {
            PropDressColorEntity dressColorEntity = selectedPropEntity
                .value!.dressColorList![selectedColorIndex.value];
            ///1.已拥有已穿戴未染色显示染色
            show = (dressColorEntity.isExist == 1 &&
                    dressColorEntity.isUse != 1) ||
                dressColorEntity.isExist == 0;
          }
        } else if (selectedTabIndex.value == 1 || selectedTabIndex.value == 2) {
          if (!selectedListItemIndex.value.contains("0-")) {
            show = true;
          }
        }
      }
    }
    return show;
  }

  bool checkShouldShowCollectBtn() {
    bool show = false;
    if (selectedPropEntity.value != null) {
      if (selectedTabIndex.value == 0) {
        if (selectedPropEntity.value!.isExist == 1) {
          ///已拥有的衣服才可收藏
          show = true;
        }
      }
    }
    return show;
  }

  bool checkCancelBtn() {
    bool show = false;
    if (selectedPropEntity.value != null) {
      if (selectedTabIndex.value == 1 || selectedTabIndex.value == 2) {
        if (selectedPropEntity.value!.isExist == 1 && selectedPropEntity.value!.isUse == 1) {
          ///正在使用的显示
          show = true;
        }
      }
    }
    return show;
  }

  bool checkShouldShowDyeBtn() {
    bool show = false;
    if (selectedPropEntity.value != null) {
      if (selectedPropEntity.value!.isExist == 1) {
        if (selectedPropEntity.value!.dressColorList?.isNotEmpty == true) {
          PropDressColorEntity dressColorEntity = selectedPropEntity
              .value!.dressColorList![selectedColorIndex.value];
          if (dressColorEntity.isExist == 0) {
            show = true;
          }
        }
      }
    }
    return show;
  }

  bool checkShouldShowUseBtn() {
    bool show = false;
    if (selectedPropEntity.value != null) {
      if (selectedPropEntity.value!.isExist == 1) {
        if (selectedTabIndex.value == 0) {
          if (selectedPropEntity.value!.dressColorList?.isNotEmpty == true) {
            PropDressColorEntity dressColorEntity = selectedPropEntity
                .value!.dressColorList![selectedColorIndex.value];
            show = dressColorEntity.isExist == 1 && dressColorEntity.isUse == 0;
          }
        } else if (selectedTabIndex.value == 1 || selectedTabIndex.value == 2) {
          if (!(selectedPropEntity.value?.isUse == 1)) {
            show = true;
          }
        }
      }
    }
    return show;
  }

  int getDressColorCostStainsCount() {
    int count = 0;
    if (selectedPropEntity.value != null) {
      if (selectedPropEntity.value!.isExist == 1) {
        if (selectedPropEntity.value!.dressColorList?.isNotEmpty == true) {
          PropDressColorEntity dressColorEntity = selectedPropEntity
              .value!.dressColorList![selectedColorIndex.value];
          if (dressColorEntity.isExist == 0) {
            count = dressColorEntity.depletionNo ?? 0;
          }
        }
      }
    }
    return count;
  }

  void setInitializeShowRoleImageSvga() {
    if (dressListController == null) {
      return;
    }
    if (selectedPropEntity.value == null) {
      PropEntity? propEntity =
          dressListController!.getUserCurrentRoleDressEntity();
      String? colorKey = getCurrentDressColorKey(propEntity);
      String roleImageAssetName = "";
      if (propEntity != null) {
        Future.delayed(const Duration(milliseconds: 300), () {
          roleImageAssetName = DressUtils()
              .getSvgaAssetNameWithDressEntity(propEntity, colorKey: colorKey);
          selectedPropEntity.value = propEntity;
          currentShowRoleImage.value = roleImageAssetName;
        });
      }
    }
  }

  String? getCurrentDressColorKey(PropEntity? propEntity) {
    String? colorKey;
    List<PropDressColorEntity>? dressColorList =
        propEntity?.dressColorList?.where((e) => e.isUse == 1).toList();
    PropDressColorEntity? currentUsedColorEntity;
    if (dressColorList?.isNotEmpty == true) {
      currentUsedColorEntity = dressColorList!.first;
    }
    if (currentUsedColorEntity != null) {
      selectedColorIndex.value =
          propEntity!.dressColorList!.indexOf(currentUsedColorEntity);
      colorKey = currentUsedColorEntity.colorNo;
    } else {
      selectedColorIndex.value = 0;
    }
    return colorKey;
  }

  void setSelectedRoleDressColorImageSvga(PropEntity propEntity,
      {String? colorKey}) {
    String svgaName = DressUtils()
        .getSvgaAssetNameWithDressEntity(propEntity, colorKey: colorKey);
    if (svgaName.isNotEmpty) {
      currentShowRoleImage.value = svgaName;
    }
  }

  Color? getCurrentColorWithKey(String key) {
    Map<String, Color> colors = {
      "RED": Colors.red,
      "YELLOW": Colors.yellow,
      "BLUE": Colors.blue,
      "GREEN": Colors.green,
      "ORANGE": Colors.orange,
      "CYAN": Colors.cyan,
      "PURPLE": Colors.purple,
      "GREY": Colors.grey,
      "WHITE": Colors.white,
      "BLACK": Colors.black,
    };
    return colors[key];
  }

  void loadUserStainsCount() async {
    AccountBalanceEntity? entity = await ApiService().getUserAccountBalance();
    stainsCount.value = entity?.stains ?? 0;
  }

  @override
  void dispose() {
    propListControllers.clear();
    super.dispose();
  }
}
