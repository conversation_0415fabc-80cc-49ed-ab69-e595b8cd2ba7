import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/chat_room_history_list_item_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ChatRoomHistoryController extends ListPageController<
    ChatRoomHistoryListItemEntity, ChatRoomHistoryController> {
  ChatRoomController roomController = Get.find<ChatRoomController>();
  late int listType;
  String? lastMsgId1;
  String? lastMsgId2;

  @override
  Future<List<ChatRoomHistoryListItemEntity>?> loadData(int page) async {
    if (page == 1) {
      lastMsgId1 = null;
      lastMsgId2 = null;
    }
    List<ChatRoomHistoryListItemEntity> list = [];
    V2TimValueCallback<List<V2TimMessage>>? res = await ChatIMManager
        .sharedInstance
        .getGroupHistoryMessageListRes(roomController.roomId!,
            lastMsgID: listType == 1 ? lastMsgId1 : lastMsgId2);
    if (res?.data?.isNotEmpty == true) {
      List<V2TimMessage> messageList = res!.data!;
      for (int i = 0; i < messageList.length; i++) {
        V2TimMessage message = messageList[i];
        if (i == messageList.length - 1) {
          lastMsgId1 = message.msgID;
          lastMsgId2 = message.msgID;
        }
        if (message.elemType == MessageElemType.V2TIM_ELEM_TYPE_TEXT &&
            message.sender != null) {
          List<V2TimGroupMemberFullInfo>? membersFullInfoList = await ChatIMManager
              .sharedInstance
              .getGroupMembersInfo(roomController.roomId!, [message.sender!]);
          if (membersFullInfoList?.isNotEmpty == true) {
            ChatRoomHistoryListItemEntity itemEntity =
                ChatRoomHistoryListItemEntity();
            V2TimGroupMemberFullInfo memberFullInfo = membersFullInfoList!.first;
            if ((listType == 1 &&
                    roomController.checkIsOnSeat(userId: message.sender)) ||
                (listType == 2 &&
                    !roomController.checkIsOnSeat(userId: message.sender))) {
              itemEntity.msgId = message.msgID;
              itemEntity.userId = message.sender;
              itemEntity.seatIndex = listType == 1
                  ? roomController.getUserSeatIndex(userId: message.sender)
                  : null;
              itemEntity.content = message.textElem?.text;
              itemEntity.nickname =
                  getMemberChatRoomRoleName(memberFullInfo.customInfo);
              itemEntity.sendDate =
                  TimeUtils.formatConversationDate(message.timestamp ?? 0);
              list.add(itemEntity);
            }
          }
        }
      }
    }
    return list;
  }

  String? getMemberChatRoomRoleName(Map<String, String>? customInfo) {
    if (customInfo != null && customInfo.containsKey("roleInfo")) {
      String? nickname = customInfo["roleInfo"];
      if (nickname != null && nickname.contains("-")) {
        return nickname.replaceAll("-", "");
      }
    }
    return null;
  }
}
