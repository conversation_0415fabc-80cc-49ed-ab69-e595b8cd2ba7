import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_room_history_list_item_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/history/chat_room_history_controller.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tolyui/tolyui.dart';

class ChatRoomHistoryBottomSheet extends StatefulWidget {
  const ChatRoomHistoryBottomSheet({super.key});

  @override
  State<ChatRoomHistoryBottomSheet> createState() =>
      _ChatRoomHistoryBottomSheetState();
}

class _ChatRoomHistoryBottomSheetState extends State<ChatRoomHistoryBottomSheet>
    with TickerProviderStateMixin {
  final ChatRoomController roomController = Get.find<ChatRoomController>();
  late TabController tabController;
  late PageController pageController;
  List<String> tabItemTitles = ["讨论席", "弹幕"];
  final TextEditingController textEditingController = TextEditingController();
  late ChatRoomHistoryController historyController1;
  late ChatRoomHistoryController historyController2;
  final Map<String, PopoverController> popoverControllers = {};

  @override
  void initState() {
    super.initState();

    tabController = TabController(length: 2, vsync: this, initialIndex: 0);
    pageController = PageController(initialPage: 0);
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
      ),
      child: GradientWidget(
        width: ScreenUtil().screenWidth,
        height: 700.h,
        colors: const [
          AppColors.colorFFD2F6C0,
          Colors.white,
        ],
        stops: const [0, 0.25],
        child: Column(
          children: [
            _buildTabBar(),
            _buildPageView(),
            _buildInputTextFieldWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Row(
      children: [
        Container(
          height: 51.h,
          alignment: Alignment.centerLeft,
          child: TabBar(
            padding: EdgeInsets.only(left: 15.w, top: 0.h),
            tabAlignment: TabAlignment.start,
            controller: tabController,
            tabs: tabItemTitles.map((e) => Tab(text: e)).toList(),
            dividerColor: Colors.transparent,
            isScrollable: true,
            labelPadding: EdgeInsets.only(right: 15.w),
            labelStyle: TextStyle(fontSize: 16.sp),
            labelColor: AppColors.colorFF333333,
            unselectedLabelColor: AppColors.colorFF666666,
            unselectedLabelStyle: TextStyle(fontSize: 16.sp),
            indicatorWeight: 4.h,
            indicatorPadding:
                EdgeInsets.only(bottom: 16.h, top: 25.h, left: 5.w, right: 5.w),
            indicator: BoxDecoration(
              color: Theme.of(context)
                  .bottomNavigationBarTheme
                  .selectedLabelStyle
                  ?.color,
              borderRadius: BorderRadius.circular(2.r),
            ),
            onTap: (index) {
              pageController.animateToPage(index,
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInCubic);
            },
          ),
        ),
        const Spacer(),
        Padding(
          padding: EdgeInsets.only(right: 15.w),
          child: GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.close,
              size: 22.w,
              color: AppColors.colorFF666666,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPageView() {
    return Expanded(
      child: PageView(
        controller: pageController,
        onPageChanged: (index) {
          tabController.animateTo(index);
        },
        children: tabItemTitles.map((e) {
          int listType = e == "讨论席" ? 1 : 2;
          if (listType == 1) {
            historyController1 = ChatRoomHistoryController()
              ..listType = 1
              ..refreshId = "ChatRoomHistoryController_1";
          } else {
            historyController2 = ChatRoomHistoryController()
              ..listType = 2
              ..refreshId = "ChatRoomHistoryController_2";
          }
          return GetBuilder(
            init: listType == 1 ? historyController1 : historyController2,
            global: false,
            id: "${listType == 1 ? historyController1.refreshId : historyController2.refreshId}",
            builder: (controller) {
              return Column(
                children: [
                  controller.data.isEmpty
                      ? Expanded(child: EmptyWidget())
                      : Expanded(
                          child: RefreshWidget.build(
                            refreshController: controller.refreshController,
                            onRefresh: () => controller.refreshData(),
                            onLoadMore: () => controller.loadMoreData(),
                            child: ListView.separated(
                              padding: EdgeInsets.only(bottom: 15.h),
                              itemBuilder: (context, index) {
                                ChatRoomHistoryListItemEntity entity =
                                    controller.data[index];
                                return _buildListItemWidget(entity);
                              },
                              separatorBuilder: (context, index) {
                                return Container(
                                  height: 1.h,
                                  margin:
                                      EdgeInsets.symmetric(horizontal: 15.w),
                                  color: AppColors.colorFFF5F6F7,
                                );
                              },
                              itemCount: controller.data.length,
                            ),
                          ),
                        ),
                ],
              );
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildListItemWidget(ChatRoomHistoryListItemEntity itemEntity) {
    String imageAssetsName = Assets.imagesChatRoomBarrageXiguaIcon;
    double imageWidth = 16.w;
    Color textColor = Colors.transparent;
    if (roomController.checkIsOnSeat(userId: itemEntity.userId)) {
      imageAssetsName = Assets.imagesChatRoomSeatMsgBubbleOtherIndexBg;
      imageWidth = 26.w;
      textColor = Colors.white;
      if (itemEntity.userId == UserService().user?.id) {
        imageAssetsName = Assets.imagesChatRoomSeatMsgBubbleMineIndexBg;
        imageWidth = 28.w;
        textColor = AppColors.colorFF2D6D0B;
      }
    }
    return Padding(
      padding: EdgeInsets.only(top: 13.h, bottom: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
              left: 13.w,
              right: 15.w,
            ),
            child: Row(
              children: [
                Row(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: imageWidth,
                      height: imageWidth,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(imageAssetsName),
                        ),
                      ),
                      child: Text(
                        "${itemEntity.seatIndex ?? ""}",
                        style: TextStyles.common(14.sp, textColor),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 4.w),
                      child: Text(
                        itemEntity.nickname ?? "",
                        style:
                            TextStyles.common(14.sp, AppColors.colorFF999999),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Row(
                  children: [
                    Text(
                      itemEntity.sendDate ?? "",
                      style: TextStyles.common(14.sp, AppColors.colorFF999999),
                    ),
                    roomController.checkIsRoomOwner()
                        ? _buildListMoreActionWidget(itemEntity)
                        : _buildListReportActionWidget(itemEntity)
                  ],
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 30.w, right: 15.w, top: 5.h),
            child: Text(
              itemEntity.content ?? "",
              style: TextStyles.normal(16.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputTextFieldWidget() {
    return Container(
      height: 40.h,
      margin: EdgeInsets.only(
        left: 15.w,
        right: 15.w,
        bottom: ScreenUtil().bottomBarHeight + 10.h,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(20.r),
        ),
        color: AppColors.colorFFF5F5F5,
      ),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField.build(
              contentPadding: EdgeInsets.only(left: 13.w),
              controller: textEditingController,
              hintText: "请输入文字",
              textInputAction: TextInputAction.send,
              maxLength: 20,
              onSubmitted: (value) async {
                bool success = await roomController.sendTextMessage(value);
                if (success) {
                  if (tabController.index == 0) {
                    textEditingController.clear();
                    historyController1.refreshData();
                  } else {
                    textEditingController.clear();
                    historyController2.refreshData();
                  }
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListMoreActionWidget(ChatRoomHistoryListItemEntity itemEntity) {
    if (itemEntity.userId == UserService().user!.id) {
      return Container();
    }
    PopoverController popoverController;
    if (popoverControllers.containsKey(itemEntity.msgId)) {
      popoverController = popoverControllers[itemEntity.msgId]!;
    } else {
      popoverController = PopoverController();
      popoverControllers[itemEntity.msgId!] = popoverController;
    }
    return TolyPopover(
      controller: popoverController,
      placement: Placement.bottomStart,
      overlay: Container(
        width: 65.w,
        height: 60.h,
        margin: EdgeInsets.only(right: 10.w),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.r),
          color: AppColors.colorFF727272,
        ),
        child: Column(
          children: [
            GestureDetector(
              onTap: () async {
                popoverController.close();
                await roomController.kickUserFromSeat(userId: itemEntity.userId!);
                Future.delayed(const Duration(milliseconds: 300,), () {
                  if (tabController.index == 0) {
                    historyController1.refreshData();
                  } else {
                    historyController2.refreshData();
                  }
                });
              },
              child: Padding(
                padding: EdgeInsets.only(top: 10.h, left: 10.w),
                child: Row(
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesChatRoomMsgHistoryListMoreKickout,
                        11.w,
                        11.w),
                    Padding(
                      padding: EdgeInsets.only(left: 5.w, right: 10.w),
                      child: Text(
                        "踢人",
                        style: TextStyles.common(14.sp, Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                popoverController.close();
                roomController.reportUser(userId: itemEntity.userId!);
              },
              child: Padding(
                padding: EdgeInsets.only(top: 10.h, left: 10.w),
                child: Row(
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesChatRoomMsgHistoryListMoreReport,
                        11.w,
                        11.w),
                    Padding(
                      padding: EdgeInsets.only(left: 5.w, right: 10.w),
                      child: Text(
                        "举报",
                        style: TextStyles.common(14.sp, Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      overlayDecorationBuilder: (_) => const BoxDecoration(
        color: Colors.transparent,
      ),
      builder: (_, ctrl, __) {
        return GestureDetector(
          onTap: () => ctrl.open(),
          child: Padding(
            padding: EdgeInsets.only(left: 5.w),
            child: ImageUtils.getImage(
              Assets.imagesChatRoomMsgHistoryListMore,
              16.w,
              16.w,
            ),
          ),
        );
      },
    );
  }

  Widget _buildListReportActionWidget(
      ChatRoomHistoryListItemEntity itemEntity) {
    if (itemEntity.userId == UserService().user!.id) {
      return Container();
    }
    return GestureDetector(
      onTap: () {
        if (itemEntity.userId != UserService().user?.id &&
            itemEntity.userId != null) {
          roomController.reportUser(
              userId: itemEntity.userId!);
        }
      },
      child: Padding(
        padding: EdgeInsets.only(left: 8.w),
        child: ImageUtils.getImage(
            Assets.imagesChatRoomMsgHistoryListReport,
            16.w,
            16.w),
      ),
    );
  }
}
