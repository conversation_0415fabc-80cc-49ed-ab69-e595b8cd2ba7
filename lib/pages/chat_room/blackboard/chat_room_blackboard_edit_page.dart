import 'dart:io';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/input_text_field_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/topic_item_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/trtc/chat_room_rtc_event.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class ChatRoomBlackboardEditPage extends StatefulWidget {
  const ChatRoomBlackboardEditPage({super.key});

  @override
  State<ChatRoomBlackboardEditPage> createState() =>
      _ChatRoomBlackboardEditPageState();
}

class _ChatRoomBlackboardEditPageState
    extends State<ChatRoomBlackboardEditPage> {
  final ChatRoomController controller = Get.find<ChatRoomController>();
  RxList<TopicItemEntity> topicList = <TopicItemEntity>[].obs;
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    if (controller.roomInfo?.roomTopicList?.isNotEmpty == true) {
      topicList.value = [...controller.roomInfo!.roomTopicList!];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "编辑黑板",
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(left: 15.w, right: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTopicStrListWidget(),
            _buildTopicImageListWidget(),
            _buildBottomBtnWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicStrListWidget() {
    return Obx(() {
      List<TopicItemEntity> topicStrList = [];
      List<TopicItemEntity>? currentTopicStrList =
          topicList.where((e) => e.topicType == 1).toList();
      if (currentTopicStrList.isNotEmpty == true) {
        topicStrList.addAll(currentTopicStrList);
      }

      if (checkShouldAddTopicItem()) {
        topicStrList.add(TopicItemEntity()..id = "+");
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 15.h, bottom: 10.h),
            child: Text(
              "房间话题:",
              style: TextStyles.normal(16.sp),
            ),
          ),
          ListView.separated(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              TopicItemEntity topicItem = topicStrList[index];
              return _buildTopicTextItemWidget(topicItem, index);
            },
            separatorBuilder: (context, index) {
              return Container(
                height: 10.h,
              );
            },
            itemCount: topicStrList.length,
          ),
        ],
      );
    });
  }

  Widget _buildTopicTextItemWidget(TopicItemEntity topicItem, int index) {
    if (topicItem.id == "+") {
      return GestureDetector(
        onTap: () {
          ToastUtils.showDialog(
            dialog: InputTextFieldDialog(
              title: "添加话题",
              isMultiLine: true,
              leftMargin: 15.w,
              maxLength: 20,
              onSubmit: (value) {
                if (value.isNotEmpty) {
                  TopicItemEntity topic = TopicItemEntity();
                  topic.topicText = value;
                  topic.topicType = 1;
                  topic.roomNo = controller.roomId;
                  topicList.add(topic);
                } else {
                  ToastUtils.showToast("话题不能为空");
                }
              },
            ),
          );
        },
        child: Container(
          height: 45.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.r),
            border: Border.all(color: AppColors.colorFF58C75D),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_circle_outline,
                size: 18.w,
                color: AppColors.colorFF168C1A,
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  "添加话题",
                  style: TextStyles.common(16.sp, AppColors.colorFF168C1A),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return Container(
      padding: EdgeInsets.only(left: 15.w, top: 12.5.h, bottom: 12.5.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: AppColors.colorFFF5F5F5,
        border: Border.all(color: AppColors.colorFFE1E1E1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: 265.w,
            ),
            child: Text(
              "${index + 1}.${topicItem.topicText}",
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              style: TextStyles.common(14.sp, AppColors.colorFF666666, h: 1.5),
            ),
          ),
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  ToastUtils.showDialog(
                    dialog: InputTextFieldDialog(
                      title: "添加话题",
                      text: topicItem.topicText,
                      isMultiLine: true,
                      leftMargin: 15.w,
                      maxLength: 24,
                      onSubmit: (value) {
                        if (value.isNotEmpty) {
                          topicItem.topicText = value;
                          topicList.replaceRange(index, index + 1, [topicItem]);
                        } else {
                          ToastUtils.showToast("话题不能为空");
                        }
                      },
                    ),
                  );
                },
                child: ImageUtils.getImage(
                    Assets.imagesSmallRoomBubbleWordItemEdit, 13.w, 13.w),
              ),
              SizedBox(
                width: 10.w,
              ),
              GestureDetector(
                onTap: () {
                  topicList.remove(topicItem);
                },
                child: ImageUtils.getImage(
                    Assets.imagesSmallRoomBubbleWordItemDelete, 14.w, 14.w),
              ),
              SizedBox(
                width: 10.w,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTopicImageListWidget() {
    return Obx(() {
      List<TopicItemEntity> topicImgsList = [];
      List<TopicItemEntity>? currentTopicImgsList =
          topicList.where((e) => e.topicType == 2).toList();
      if (currentTopicImgsList.isNotEmpty == true) {
        topicImgsList.addAll(currentTopicImgsList);
      }

      if (checkShouldAddTopicItem()) {
        topicImgsList.add(TopicItemEntity()..id = "+");
      }

      return Container(
        margin: EdgeInsets.only(top: 30.h),
        child: Wrap(
          spacing: 15.w,
          runSpacing: 15.w,
          children: topicImgsList.map((e) {
            int index = topicImgsList.indexOf(e);
            return _buildTopicImageItemWidget(e, index);
          }).toList(),
        ),
      );
    });
  }

  Widget _buildTopicImageItemWidget(TopicItemEntity topicItem, int index) {
    if (topicItem.id == "+") {
      return GestureDetector(
        onTap: () {
          ToastUtils.showBottomSheet(
            [S.current.album, S.current.camera],
            onTap: (index) async {
              if (index == 0) {
                List<AssetEntity>? assets = await ImagePickerUtil.selectAsset(
                    maxAssets: 1, isImage: true);
                if (assets != null) {
                  AssetEntity? asset = assets.first;
                  File? file = await asset.originFile;
                  String? assetUrl = file?.path;
                  if (assetUrl?.isNotEmpty == true) {
                    String? imageUrl = await ApiService().uploadFile(assetUrl!);
                    TopicItemEntity topic = TopicItemEntity();
                    topic.topicType = 2;
                    topic.topicText = imageUrl;
                    topic.roomNo = controller.roomId;
                    topicList.add(topic);
                  }
                }
              } else {
                AssetEntity? asset = await ImagePickerUtil.takeAsset();
                if (asset != null) {
                  File? file = await asset.originFile;
                  String? assetUrl = file?.path;
                  if (assetUrl?.isNotEmpty == true) {
                    String? imageUrl = await ApiService().uploadFile(assetUrl!);
                    TopicItemEntity topic = TopicItemEntity();
                    topic.topicType = 2;
                    topic.topicText = imageUrl;
                    topic.roomNo = controller.roomId;
                    topicList.add(topic);
                  }
                }
              }
            },
          );
        },
        child: SizedBox(
          width: 105.w,
          height: 105.w,
          child: ImageUtils.getImage(
              Assets.imagesChatRoomBlackBoardImageAdd, 105.w, 105.w,
              fit: BoxFit.cover),
        ),
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.r),
      child: SizedBox(
        width: 105.w,
        height: 105.w,
        child: Stack(
          children: [
            ImageUtils.getImage(topicItem.topicText ?? "", 105.w, 105.w,
                fit: BoxFit.cover),
            Positioned(
              right: 0,
              child: GestureDetector(
                onTap: () {
                  topicList.remove(topicItem);
                },
                child: ImageUtils.getImage(
                    Assets.imagesDynamicPublishAssetsDelete, 20.w, 20.w),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool checkShouldAddTopicItem() {
    return topicList.length < 10;
  }

  Widget _buildBottomBtnWidget() {
    return CommonGradientBtn(
      title: "创建",
      horizontalMargin: 35.w,
      topMargin: 80.h,
      bottomMargin: 50.h,
      onTap: () async {
        bool success = await ApiService()
            .updateRoomTopic(roomId: controller.roomId!, topicList: topicList);
        if (success) {
          controller.sendTrtcCustomMsgEvent(
              ChatRoomRtcEvent.RoomTopicUpdate, {"roomId": controller.roomId!});
          controller.getRoomInfo(showLoading: false);
          Get.back();
        }
      },
    );
  }
}
