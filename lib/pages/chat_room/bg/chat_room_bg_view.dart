import 'package:dada/common/values/enums.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomBgView extends StatefulWidget {
  const ChatRoomBgView({super.key});

  @override
  State<ChatRoomBgView> createState() => _ChatRoomBgViewState();
}

class _ChatRoomBgViewState extends State<ChatRoomBgView> {
  final ChatRoomController controller = Get.find<ChatRoomController>();

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: controller,
      id: ChatRoomController.KChatRoomBgViewBuildID,
      builder: (controller) {
        String bgImage = "";
        if (controller.roomType == ChatRoomType.teapot.index) {
          bgImage = Assets.imagesChatRoomTeapotBg;
        } else if (controller.roomType == ChatRoomType.fair.index) {
          bgImage = Assets.imagesChatRoomFairBg;
        }
        return Container(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(bgImage),
              fit: BoxFit.fill,
            ),
          ),
        );
      },
    );
  }
}
