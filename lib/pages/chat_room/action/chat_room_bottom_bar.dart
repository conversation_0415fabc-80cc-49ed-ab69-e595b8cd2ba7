import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/bottom_input_textField.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/history/chat_room_history_bottom_sheet.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomBottomBar extends StatefulWidget {
  const ChatRoomBottomBar({super.key});

  @override
  State<ChatRoomBottomBar> createState() => _ChatRoomBottomBarState();
}

class _ChatRoomBottomBarState extends State<ChatRoomBottomBar> {
  final ChatRoomController controller = Get.find<ChatRoomController>();

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: controller,
      id: ChatRoomController.KChatRoomBottomBarBuildID,
      builder: (controller) {
        return Padding(
          padding: EdgeInsets.only(
              bottom: 10.h + ScreenUtil().bottomBarHeight, left: 15.w),
          child: Row(
            children: [
              ///输入文字
              GestureDetector(
                onTap: () {
                  ToastUtils.showBottomDialog(
                    const BottomInputTextField(
                      hintText: "请输入...",
                      maxLength: 200,
                    ),
                  ).then((result) {
                    if (result != null) {
                      controller.sendTextMessage(result);
                    }
                  });
                },
                child: Container(
                  width: 150.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 13.w),
                        child: Text(
                          "请输入文字...",
                          style: TextStyles.common(16.sp, Colors.white),
                        ),
                      ),
                      // const Spacer(),
                      // Padding(
                      //   padding: EdgeInsets.only(right: 10.w),
                      //   child: GestureDetector(
                      //     onTap: () {},
                      //     child: ImageUtils.getImage(
                      //       Assets.imagesChatRoomBottomSendTextEmoj,
                      //       20.w,
                      //       20.w,
                      //     ),
                      //   ),
                      // ),
                    ],
                  ),
                ),
              ),

              const Spacer(),

              ///麦克风
              Visibility(
                visible: controller.checkIsOnSeat(),
                child: GestureDetector(
                  onTap: () {
                    controller.muteLocalAudio(!controller.isMuteMic.value);
                  },
                  child: Padding(
                    padding: EdgeInsets.only(right: 5.w),
                    child: Obx(
                      () => ImageUtils.getImage(
                          controller.isMuteMic.value == true
                              ? Assets.imagesChatRoomBottomMicClose
                              : Assets.imagesChatRoomBottomMicOpen,
                          40.w,
                          40.w),
                    ),
                  ),
                ),
              ),

              ///喇叭
              Visibility(
                visible: true,
                child: GestureDetector(
                  onTap: () {
                    controller.muteAllRemoteAudio(!controller.isMuteSpeaker.value);
                  },
                  child: Padding(
                    padding: EdgeInsets.only(right: 5.w),
                    child: Obx(
                      () => ImageUtils.getImage(
                          controller.isMuteSpeaker.value == true
                              ? Assets.imagesChatRoomBottomSpeakerClose
                              : Assets.imagesChatRoomBottomSpeakerOpen,
                          40.w,
                          40.w),
                    ),
                  ),
                ),
              ),

              ///下麦（下座）
              Visibility(
                visible: controller.checkIsOnSeat(),
                child: GestureDetector(
                  onTap: () {
                    ToastUtils.showDialog(
                      content: "确定要下麦吗？",
                      hideTopImg: true,
                      onConfirm: () {
                        controller.leaveSeat();
                      },
                    );
                  },
                  child: Padding(
                    padding: EdgeInsets.only(right: 5.w),
                    child: ImageUtils.getImage(
                        Assets.imagesChatRoomBottomLeaveSeat, 40.w, 40.w),
                  ),
                ),
              ),

              ///聊天历史
              GestureDetector(
                onTap: () {
                  ToastUtils.showBottomDialog(const ChatRoomHistoryBottomSheet());
                },
                child: Padding(
                  padding: EdgeInsets.only(right: 15.w),
                  child: ImageUtils.getImage(
                      Assets.imagesChatRoomBottomHistoryBtn, 40.w, 40.w),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
