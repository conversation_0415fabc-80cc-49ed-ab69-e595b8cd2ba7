import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat_room/action/chat_room_bottom_bar.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/other/edit/chat_room_edit_name_page.dart';
import 'package:dada/pages/chat_room/other/more_menu/chat_room_more_menu_widget.dart';
import 'package:dada/pages/chat_room/other/more_menu/chat_room_mute_user_bottom_sheet.dart';
import 'package:dada/pages/chat_room/other/online_member/chat_room_online_list_bottom_sheet.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/pages/share/share_bottom_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';

class ChatRoomActionView extends StatefulWidget {
  const ChatRoomActionView({super.key});

  @override
  State<ChatRoomActionView> createState() => _ChatRoomActionViewState();
}

class _ChatRoomActionViewState extends State<ChatRoomActionView> {
  final ChatRoomController controller = Get.put(ChatRoomController());

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: controller,
      id: ChatRoomController.KChatRoomFuncViewBuildID,
      builder: (controller) {
        return Column(
          children: [
            _buildHeaderInfoWidget(),
            const Spacer(),
            _buildBottomBtnWidget(),
          ],
        );
      },
    );
  }

  Widget _buildHeaderInfoWidget() {
    return Container(
      height: 44.h + ScreenUtil().statusBarHeight,
      padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: const [0, 1],
          colors: [
            Colors.black.withOpacity(0.5),
            Colors.black.withOpacity(0),
          ],
        ),
      ),
      child: Row(
        children: [
          ///返回按钮
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              Get.back();
            },
            child: Container(
              width: 24.w,
              height: 24.w,
              alignment: Alignment.center,
              margin: EdgeInsets.only(left: 5.w),
              child: ImageUtils.getImage(Assets.imagesNaviBackWhite, 8.w, 14.w),
            ),
          ),

          ///房主
          GestureDetector(
            onTap: () {
              Get.toNamed(GetRouter.userProfile, parameters: {
                "userId": controller.roomInfo!.currentRoomUserId!
              });
            },
            child: Container(
                height: 24.h,
                alignment: Alignment.center,
                margin: EdgeInsets.only(left: 10.w),
                padding: EdgeInsets.only(right: 10.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.r),
                  gradient: LinearGradient(
                    colors: [Colors.black.withOpacity(0.5), Colors.transparent],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
                child: Row(
                  children: [
                    ImageUtils.getImage(
                        controller.roomInfo?.avatar ?? "", 24.w, 24.w,
                        showBorder: true, radius: 12.r, showPlaceholder: true),
                    Padding(
                      padding: EdgeInsets.only(left: 5.w),
                      child: Text(
                        "房主",
                        style: TextStyles.common(14.sp, Colors.white),
                      ),
                    ),
                  ],
                )),
          ),

          ///房间ID
          Container(
            height: 24.h,
            alignment: Alignment.center,
            margin: EdgeInsets.only(left: 10.w),
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: Colors.black.withOpacity(0.5),
            ),
            child: Text(
              "ID：${controller.roomInfo?.roomNo ?? ""}",
              style: TextStyles.common(14.sp, Colors.white70),
            ),
          ),

          ///分享
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              ToastUtils.showBottomDialog(
                ShareBottomDialog(
                  dialogTitle: "找TA一起玩",
                  btnTitle: "邀请",
                  pageTitle: "邀请好友",
                  callback: (userId) async {
                    List<V2TimUserFullInfo>? list =
                        await ChatIMManager.sharedInstance.getUserInfoList(
                            userIDs: [controller.roomInfo!.currentRoomUserId!]);
                    V2TimUserFullInfo? roomOwner = list?.first;
                    if (roomOwner != null) {
                      ChatIMManager.sharedInstance.sendCustomMessage(
                        receiver: userId,
                        groupID: "",
                        type: ChatImCustomMsgType.ShareRoomToChatMsg,
                        data: {
                          "roomId": controller.roomId!,
                          "roomOwner": roomOwner.nickName,
                          "roomOwnerAvatar": roomOwner.faceUrl,
                        },
                      );
                    }
                  },
                ),
              );
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: 10.w, right: 10.w, bottom: 5.h, top: 5.h),
              child: ImageUtils.getImage(
                  Assets.imagesChatRoomTopShareBtnIcon, 16.w, 16.w),
            ),
          ),

          // ///搜索话题
          // GestureDetector(
          //   onTap: () {},
          //   child: Container(
          //     height: 24.h,
          //     width: 90.w,
          //     alignment: Alignment.center,
          //     margin: EdgeInsets.only(left: 5.w),
          //     decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(12.r),
          //       color: Colors.black.withOpacity(0.5),
          //     ),
          //     child: Row(
          //       children: [
          //         Padding(
          //           padding: EdgeInsets.only(left: 6.w),
          //           child: ImageUtils.getImage(
          //               Assets.imagesChatRoomTopicSearchIcon, 16.w, 16.w),
          //         ),
          //         Padding(
          //           padding: EdgeInsets.only(left: 3.5.w),
          //           child: Text(
          //             "搜索话题",
          //             style: TextStyles.common(14.sp, Colors.white70),
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
          //
          // ///换个房间
          // GestureDetector(
          //   onTap: () {},
          //   child: Container(
          //     height: 24.h,
          //     width: 90.w,
          //     alignment: Alignment.center,
          //     margin: EdgeInsets.only(left: 5.w),
          //     decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(12.r),
          //       color: Colors.black.withOpacity(0.5),
          //     ),
          //     child: Row(
          //       children: [
          //         Padding(
          //           padding: EdgeInsets.only(left: 6.w),
          //           child: ImageUtils.getImage(
          //               Assets.imagesChatRoomChangeRoomIcon, 16.w, 16.w),
          //         ),
          //         Padding(
          //           padding: EdgeInsets.only(left: 3.5.w),
          //           child: Text(
          //             "换个房间",
          //             style: TextStyles.common(14.sp, Colors.white70),
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),

          const Spacer(),

          ///更多
          GestureDetector(
            onTap: () {
              Get.dialog(
                Stack(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        height: 1.sh,
                        color: Colors.transparent,
                      ),
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ChatRoomMoreMenuWidget(
                          callback: (type) {
                            doAction(type);
                          },
                        ),
                      ],
                    )
                  ],
                ),
                barrierColor: Colors.transparent,
                barrierDismissible: true,
                useSafeArea: false,
              );
            },
            child: Padding(
              padding: EdgeInsets.only(
                  right: 15.w, left: 10.w, top: 5.h, bottom: 5.h),
              child: ImageUtils.getImage(
                  Assets.imagesChatRoomTopMoreBtnIcon, 22.w, 22.w),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBtnWidget() {
    return const ChatRoomBottomBar();
  }

  void doAction(ChatRoomMoreMenuActionType type) {
    switch (type) {
      case ChatRoomMoreMenuActionType.editRoomName:
        Get.to(() => const ChatRoomEditNamePage());
        break;
      case ChatRoomMoreMenuActionType.muteUser:
        ToastUtils.showBottomDialog(const ChatRoomMuteUserBottomSheet());
        break;
      case ChatRoomMoreMenuActionType.silenceUser:
        ToastUtils.showBottomDialog(
          const ChatRoomOnlineListBottomSheet(isMute: true),
        );
        break;
      case ChatRoomMoreMenuActionType.smallRoom:
        Get.back();
        break;
      case ChatRoomMoreMenuActionType.dismissRoom:
        controller.closeRoom();
        break;
      case ChatRoomMoreMenuActionType.report:
        Get.to(() => ReportPage(
            reportType: controller.roomType == ChatRoomType.fair.index
                ? ReportType.roomFair
                : ReportType.roomTeapot,
            roomId: controller.roomId!));
        break;
      case ChatRoomMoreMenuActionType.exitRoom:
        controller.exitRoom();
        break;
    }
  }
}
