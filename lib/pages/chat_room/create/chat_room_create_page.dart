import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/search_room_history_result_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomCreatePage extends StatefulWidget {
  const ChatRoomCreatePage({super.key});

  @override
  State<ChatRoomCreatePage> createState() => _ChatRoomCreatePageState();
}

class _ChatRoomCreatePageState extends State<ChatRoomCreatePage> {
  TextEditingController nameEditingController = TextEditingController();
  TextEditingController topicEditingController = TextEditingController();

  late List<String> recommendTopicList;
  RxString selectedRecommendTopic = "".obs;
  RxList<String> hotTopicList = <String>[].obs;
  RxList<SearchRoomHistoryResultTopicBoosterList> nominationTopicList =
      <SearchRoomHistoryResultTopicBoosterList>[].obs;

  late ChatRoomType chatRoomType;

  @override
  void initState() {
    super.initState();

    recommendTopicList = [
      "想吐槽",
      "情感闲聊",
      "趣味辩论",
      "组队开黑",
      "兴趣交流",
      "随便聊聊",
    ];

    chatRoomType = Get.arguments ?? ChatRoomType.fair;
    loadData();

    setNormalRoomName();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "创建房间",
      ),
      body: ListView(
        children: [
          _buildRoomNameInputWidget(),
          _buildRoomTopicInputWidget(),
          _buildRecommendTopicSelectedWidget(),
          _buildHotTopicListWidget(),
          _buildTopicNominationListWidget(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildRoomNameInputWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "房间名称",
            style: TextStyles.normal(16.sp),
          ),
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Container(
              height: 50.h,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                color: AppColors.colorFFF5F5F5,
                borderRadius: BorderRadius.circular(5.r),
                border: Border.all(color: AppColors.colorFFE1E1E1),
              ),
              child: CustomTextField.build(
                contentPadding: EdgeInsets.only(left: 10.w),
                controller: nameEditingController,
                hintText: "请输入房间名称",
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomTopicInputWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "房间话题：",
            style: TextStyles.normal(16.sp),
          ),
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Container(
              height: 90.h,
              decoration: BoxDecoration(
                color: AppColors.colorFFF5F5F5,
                borderRadius: BorderRadius.circular(5.r),
                border: Border.all(color: AppColors.colorFFE1E1E1),
              ),
              child: CustomTextField.build(
                contentPadding: EdgeInsets.only(left: 10.w, top: 10.h),
                controller: topicEditingController,
                hintText: "房间话题将展示在房间列表上",
                maxLines: 5,
                maxLength: 24,
                showLeftLength: true,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendTopicSelectedWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 10.h,
          color: AppColors.colorFFE5E5E5,
        ),
        Padding(
          padding: EdgeInsets.only(left: 15.w, top: 15.h),
          child: Text(
            "可参考这些话题哦：",
            style: TextStyles.normal(16.sp),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
            top: 10.h,
            left: 15.w,
          ),
          child: Wrap(
            spacing: 7.5.w,
            runSpacing: 10.h,
            children: recommendTopicList
                .map((e) => _buildRecommendTopicListItemWidget(e))
                .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendTopicListItemWidget(String text) {
    return Obx(() {
      bool selected = selectedRecommendTopic.value == text;
      return GestureDetector(
        onTap: () {
          selectedRecommendTopic.value = text;
          topicEditingController.text = text;
        },
        child: Container(
          width: 110.w,
          height: 35.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(17.5.r),
            border: Border.all(
              color:
                  selected ? AppColors.colorFF58C75D : AppColors.colorFF999999,
            ),
          ),
          child: Text(
            text,
            style: TextStyles.common(
              16.sp,
              selected ? AppColors.colorFF58C75D : AppColors.colorFF999999,
            ),
          ),
        ),
      );
    });
  }

  Widget _buildHotTopicListWidget() {
    return Obx(() {
      if (hotTopicList.isEmpty == true) {
        return Container();
      }
      List<String> hotList = [];
      if (hotTopicList.length > 3) {
        hotList = [...hotTopicList.sublist(0, 3)];
      } else {
        hotList = [...hotTopicList];
      }
      return Padding(
        padding: EdgeInsets.only(top: 15.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 19.w),
              child: Text(
                "#热门话题",
                style: TextStyles.normal(16.sp),
              ),
            ),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.only(left: 16.w, top: 10.h),
              itemBuilder: (context, index) {
                String text = hotList[index];
                return GestureDetector(
                  onTap: () {
                    topicEditingController.text = text;
                  },
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: ScreenUtil().screenWidth - 16.w * 2,
                    ),
                    child: Text(
                      text,
                      style: TextStyles.common(16.sp, AppColors.colorFF666666),
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 10.h,
                );
              },
              itemCount: hotList.length,
            )
          ],
        ),
      );
    });
  }

  Widget _buildTopicNominationListWidget() {
    return Obx(() {
      if (nominationTopicList.isEmpty) {
        return Container();
      }
      List<SearchRoomHistoryResultTopicBoosterList> topicBoosterList = [];
      if (nominationTopicList.length > 3) {
        topicBoosterList = [...nominationTopicList.sublist(0, 3)];
      } else {
        topicBoosterList = [...nominationTopicList];
      }
      return Padding(
        padding: EdgeInsets.only(top: 20.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 19.w),
              child: Text(
                "#提名话题",
                style: TextStyles.normal(16.sp),
              ),
            ),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.only(left: 16.w, top: 10.h),
              itemBuilder: (context, index) {
                SearchRoomHistoryResultTopicBoosterList topic =
                    topicBoosterList[index];
                return GestureDetector(
                  onTap: () {
                    topicEditingController.text = topic.topic ?? "";
                  },
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: ScreenUtil().screenWidth - 16.w - 13.w,
                    ),
                    child: Row(
                      children: [
                        Container(
                          constraints: BoxConstraints(
                            maxWidth: ScreenUtil().screenWidth -
                                16.w -
                                13.w -
                                50.w -
                                10.w,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${topic.topic}",
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyles.common(
                                    16.sp, AppColors.colorFF666666),
                              ),
                              Padding(
                                padding: EdgeInsets.only(top: 2.h),
                                child: Text(
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  "(${topic.topicCount}人提案，目前${topic.roomCount}房间，快来参与吧！快来参与吧！快来参与吧！）",
                                  style: TextStyles.common(
                                      12.sp, AppColors.colorFF999999,
                                      h: 1.3),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 10.w, right: 13.w),
                          child: GestureDetector(
                            onTap: () {
                              if (topic.topic != null) {
                                topicPower(topic.topic!, topic.topicCount ?? 0);
                              }
                            },
                            child: Container(
                              width: 50.w,
                              height: 25.h,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: AppColors.colorFF89E15C,
                                borderRadius: BorderRadius.circular(25.h / 2),
                              ),
                              child: Text(
                                "助力",
                                style: TextStyles.normal(14.sp),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 10.h,
                );
              },
              itemCount: topicBoosterList.length,
            )
          ],
        ),
      );
    });
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      topMargin: 30.h,
      bottomMargin: 30.h,
      horizontalMargin: 35.w,
      height: 50.h,
      title: "创建",
      onTap: () async {
        // if (nameEditingController.text.isEmpty) {
        //   ToastUtils.showToast('请输入房间名称');
        //   return;
        // }
        if (topicEditingController.text.isEmpty) {
          ToastUtils.showToast('至少添加一个房间话题');
          return;
        }
        if (GlobalFloatingManager().currentIsShowMiniWindow()) {
          return;
        }
        ChatRoomInfoEntity? roomInfo = await ApiService().createChatRoom(
            roomName: nameEditingController.text,
            roomType: chatRoomType.index,
            topicList: [topicEditingController.text]);
        if (roomInfo?.roomNo != null) {
          Get.offNamed(
            GetRouter.chatRoomDetail,
            parameters: {
              "roomId": roomInfo!.roomNo.toString(),
              "roomType": roomInfo.roomType.toString(),
              "isCreate": "1"
            },
          );
        }
      },
    );
  }

  void topicPower(String topic, int count) async {
    if (topic.isNotEmpty) {
      bool success = await ApiService().topicNomination(keyword: topic);
      if (success) {
        ToastUtils.showToast("助力成功");
        loadData();
      }
    }
  }

  void loadData() async {
    SearchRoomHistoryResultEntity? resultEntity =
        await ApiService().getSearchRoomHistory();
    if (resultEntity != null) {
      hotTopicList.value = resultEntity.hotTopicList ?? [];
      nominationTopicList.value = resultEntity.topicBoosterList ?? [];
    }
  }

  void setNormalRoomName() {
    String roomName = "";
    if (chatRoomType == ChatRoomType.teapot) {
      roomName = "茶壶";
    } else {
      roomName = "市集";
    }
    int random = Random().nextInt(100000) + 100000;
    roomName += random.toString();
    nameEditingController.text = roomName;
  }
}
