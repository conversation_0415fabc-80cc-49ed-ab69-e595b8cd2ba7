import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/trtc/chat_room_rtc_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomEditNamePage extends StatefulWidget {
  const ChatRoomEditNamePage({super.key});

  @override
  State<ChatRoomEditNamePage> createState() => _ChatRoomEditNamePageState();
}

class _ChatRoomEditNamePageState extends State<ChatRoomEditNamePage> {
  ChatRoomController controller = Get.find<ChatRoomController>();
  TextEditingController editingController = TextEditingController();

  @override
  void initState() {
    super.initState();

    editingController.text = controller.roomInfo?.roomName ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "房间名称",
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.colorFFF5F5F5,
                border: Border.all(color: AppColors.colorFFE1E1E1),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: CustomTextField.build(
                controller: editingController,
                style: TextStyles.common(14.sp, AppColors.colorFF666666),
                contentPadding: EdgeInsets.only(left: 20.w),
              ),
            ),
          ),
          CommonGradientBtn(
            horizontalMargin: 35.w,
            title: "保存",
            onTap: () async {
              bool success = await ApiService().editRoomName(
                  roomId: controller.roomId!, roomName: editingController.text);
              if (success) {
                controller.sendTrtcCustomMsgEvent(
                    ChatRoomRtcEvent.RoomInfoUpdate,
                    {"roomId": controller.roomId!});
                controller.getRoomInfo(showLoading: false);
                Get.back();
              }
            },
          ),
        ],
      ),
    );
  }
}
