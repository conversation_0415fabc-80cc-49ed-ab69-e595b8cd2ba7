import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/chat_room_online_member_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ChatRoomOnlineController extends ListPageController<
    ChatRoomOnlineMemberEntity, ChatRoomOnlineController> {
  late ChatRoomController roomController;

  late String roomId;
  String nextSeq = "0";

  bool isMute = true;

  ChatRoomOnlineController({required this.roomId});

  @override
  void onInit() {
    super.onInit();

    if (Get.isRegistered<ChatRoomController>()) {
      roomController = Get.find<ChatRoomController>();
    } else {
      roomController = Get.put(ChatRoomController());
      roomController.roomId = roomId;
    }
  }

  @override
  Future<List<ChatRoomOnlineMemberEntity>?> loadData(int page) async {
    if (page == 1) {
      nextSeq = "0";
    }

    if (page > 1 && nextSeq == "0") {
      return null;
    }

    ///获取群成员列表
    V2TimGroupMemberInfoResult? result =
        await ChatIMManager.sharedInstance.getGroupMemberList(roomId, nextSeq);
    if (result != null) {
      if (result.nextSeq == "0") {
        hasMore = false;
      } else {
        nextSeq = result.nextSeq!;
      }
      List<V2TimGroupMemberFullInfo?>? groupMemberList = result.memberInfoList;
      if (groupMemberList?.isNotEmpty == true) {
        List<String> userIDs = [];
        for (int i = 0; i < groupMemberList!.length; i++) {
          V2TimGroupMemberFullInfo? member = groupMemberList[i];
          if (member != null) {
            userIDs.add(member.userID);
          }
        }
        if (userIDs.isNotEmpty) {
          ///获取群成员列表用户详细信息
          List<V2TimUserFullInfo>? userInfoList = await ChatIMManager
              .sharedInstance
              .getUserInfoList(userIDs: userIDs);

          ///将用户详细转换成想要的数据（包括在线状态、禁言时间等...）
          List<ChatRoomOnlineMemberEntity>? memberList = userInfoList?.map((e) {
            List<V2TimGroupMemberFullInfo?>? list = groupMemberList
                .where((element) => element?.userID == e.userID)
                .toList();
            V2TimGroupMemberFullInfo? groupMemberFullInfo = list.first;
            ChatRoomOnlineMemberEntity member = ChatRoomOnlineMemberEntity();
            member.userId = e.userID;
            member.nickname = e.nickName;
            member.avatar = e.faceUrl;
            member.sex = e.gender == 2 ? 1 : 0;
            member.age = e.birthday ?? 0; //IM暂时直接替换用birthday替代age
            if (Get.isRegistered<ChatRoomController>()) {
              member.onSeat =
                  roomController.checkIsOnSeat(userId: member.userId!);
            }
            member.muteUtil = groupMemberFullInfo?.muteUntil;
            return member;
          }).toList();

          ///过滤在线用户
          // memberList?.removeWhere((e) => !(e.onSeat == false));
          return memberList;
        }
      }
    }
    return null;
  }
}
