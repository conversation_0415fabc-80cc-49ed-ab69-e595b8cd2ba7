import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/components/widgets/sex_widget.dart';
import 'package:dada/components/widgets/switch_button.dart';
import 'package:dada/model/chat_room_online_member_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/other/online_member/chat_room_online_controller.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomOnlineListBottomSheet extends StatefulWidget {
  final bool? isMute;

  const ChatRoomOnlineListBottomSheet({super.key, this.isMute});

  @override
  State<ChatRoomOnlineListBottomSheet> createState() =>
      _ChatRoomOnlineListBottomSheetState();
}

class _ChatRoomOnlineListBottomSheetState
    extends State<ChatRoomOnlineListBottomSheet> {
  final roomController = Get.find<ChatRoomController>();
  late ChatRoomOnlineController onlineController;

  @override
  void initState() {
    super.initState();

    onlineController =
        Get.put(ChatRoomOnlineController(roomId: roomController.roomId!));
    onlineController.isMute = widget.isMute ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
      ),
      child: GradientWidget(
        width: ScreenUtil().screenWidth,
        height: 600.h,
        colors: const [
          AppColors.colorFFD2F6C0,
          Colors.white,
        ],
        stops: const [0, 0.25],
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(top: 16.h),
              child: Text(
                "在线列表",
                style: TextStyles.normal(16.sp),
              ),
            ),
            Expanded(
              child: _buildOnlineMemberListWidget(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnlineMemberListWidget() {
    return RefreshWidget.build(
      refreshController: onlineController.refreshController,
      onRefresh: () => onlineController.refreshData(),
      onLoadMore: () => onlineController.loadMoreData(),
      child: GetBuilder(
        init: onlineController,
        id: onlineController.refreshId,
        builder: (onlineController) {
          if (onlineController.data.isEmpty == true) {
            return const ListPageEmptyWidget();
          }
          return ListView.builder(
            padding: EdgeInsets.only(
                top: 10.h, bottom: ScreenUtil().bottomBarHeight + 10.h),
            itemBuilder: (context, index) {
              ChatRoomOnlineMemberEntity memberEntity =
                  onlineController.data[index];
              return _buildMemberListItemWidget(memberEntity);
            },
            itemCount: onlineController.data.length,
          );
        },
      ),
    );
  }

  Widget _buildMemberListItemWidget(ChatRoomOnlineMemberEntity member) {
    return SizedBox(
      height: 60.h,
      child: Row(
        children: [
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(left: 15.w),
                child: SizedBox(
                  width: 40.w,
                  height: 40.w,
                  child: ClipOval(
                    child: ImageUtils.getImage(member.avatar ?? "", 40.w, 40.w,
                        fit: BoxFit.cover),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  member.nickname ?? "",
                  style: TextStyles.normal(16.sp),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: SexAgeWidget(sex: member.sex ?? 0, age: member.age ?? 0),
              ),
            ],
          ),
          const Spacer(),
          widget.isMute == true && member.userId != UserService().user?.id
              ? Padding(
                  padding: EdgeInsets.only(right: 15.w),
                  child: Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(right: 8.w),
                        child: Text(
                          "禁言",
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                      SwitchButton(
                        value: member.muteUtil != null && member.muteUtil! > 0,
                        onChanged: (value) async {
                          ToastUtils.showLoading();
                          bool success = await ChatIMManager.sharedInstance
                              .muteGroupMember(
                                  groupID: onlineController.roomId,
                                  userID: member.userId!,
                                  mute: value);
                          ToastUtils.hideLoading();
                          if (success) {
                            member.muteUtil = value ? (2 ^ 32 - 1) : 0;
                            onlineController.update();
                          }
                        },
                      ),
                    ],
                  ),
                )
              : Visibility(
                  visible: member.onSeat == true,
                  child: Padding(
                    padding: EdgeInsets.only(right: 15.w),
                    child: Text(
                      "已在麦上",
                      style: TextStyles.common(16.sp, AppColors.colorFF666666),
                    ),
                  ),
                ),
        ],
      ),
    );
  }
}
