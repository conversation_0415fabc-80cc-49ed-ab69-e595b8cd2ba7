import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/components/widgets/sex_widget.dart';
import 'package:dada/components/widgets/switch_button.dart';
import 'package:dada/model/chat_room_online_member_entity.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/trtc/chat_room_rtc_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';

class ChatRoomMuteUserBottomSheet extends StatefulWidget {
  const ChatRoomMuteUserBottomSheet({super.key});

  @override
  State<ChatRoomMuteUserBottomSheet> createState() =>
      _ChatRoomMuteUserBottomSheetState();
}

class _ChatRoomMuteUserBottomSheetState
    extends State<ChatRoomMuteUserBottomSheet> {
  final ChatRoomController controller = Get.find<ChatRoomController>();
  List<ChatRoomOnlineMemberEntity> seatUserList =
      <ChatRoomOnlineMemberEntity>[];

  @override
  void initState() {
    super.initState();

    loadSeatUserInfoList();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
      ),
      child: GradientWidget(
        width: ScreenUtil().screenWidth,
        height: 600.h,
        colors: const [
          AppColors.colorFFD2F6C0,
          Colors.white,
        ],
        stops: const [0, 0.25],
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(top: 16.h),
              child: Text(
                "在线列表",
                style: TextStyles.normal(16.sp),
              ),
            ),
            Expanded(
              child: _buildSeatListWidget(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSeatListWidget() {
    if (seatUserList.isEmpty) {
      return EmptyWidget(content: "暂时还没有人上麦");
    }
    return ListView.builder(
      padding: EdgeInsets.only(
          top: 10.h, bottom: ScreenUtil().bottomBarHeight + 10.h),
      itemBuilder: (context, index) {
        ChatRoomOnlineMemberEntity seatUserEntity = seatUserList[index];
        return _buildSeatListItemWidget(seatUserEntity);
      },
      itemCount: seatUserList.length,
    );
  }

  Widget _buildSeatListItemWidget(ChatRoomOnlineMemberEntity seatUserEntity) {
    return SizedBox(
      height: 60.h,
      child: Row(
        children: [
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(left: 15.w),
                child: SizedBox(
                  width: 40.w,
                  height: 40.w,
                  child: ClipOval(
                    child: ImageUtils.getImage(
                        seatUserEntity.avatar ?? "", 40.w, 40.w,
                        fit: BoxFit.cover),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  seatUserEntity.nickname ?? "",
                  style: TextStyles.normal(16.sp),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: SexAgeWidget(
                    sex: seatUserEntity.sex ?? 0, age: seatUserEntity.age ?? 0),
              ),
            ],
          ),
          const Spacer(),
          seatUserEntity.userId != UserService().user?.id
              ? Padding(
                  padding: EdgeInsets.only(right: 15.w),
                  child: Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(right: 8.w),
                        child: Text(
                          "${seatUserEntity.seatIndex}号麦",
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 8.w),
                        child: Text(
                          "屏蔽",
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                      SwitchButton(
                        value: seatUserEntity.isMute == true,
                        onChanged: (value) async {
                          ToastUtils.showLoading();
                          await TRTCManager.sharedInstance.muteRemoteUserAudio(
                              userId: seatUserEntity.userId!, mute: value);
                          ToastUtils.hideLoading();
                          setState(() {
                            seatUserEntity.isMute = value;
                          });
                          TRTCManager.sharedInstance.updateUserMuteListState(
                              seatUserEntity.userId!, value);
                        },
                      ),
                    ],
                  ),
                )
              : Padding(
                  padding: EdgeInsets.only(right: 15.w),
                  child: Text(
                    "我",
                    style: TextStyles.common(16.sp, AppColors.colorFF666666),
                  ),
                ),
        ],
      ),
    );
  }

  void loadSeatUserInfoList() async {
    List<ChatRoomOnlineMemberEntity> list = [];
    List<ChatRoomSeatInfoEntity>? seatList = controller.roomInfo?.seatList;
    if (seatList != null) {
      for (int i = 0; i < seatList.length; i++) {
        ChatRoomSeatInfoEntity seatInfo = seatList[i];
        if (seatInfo.user != null) {
          V2TimUserFullInfo? userFullInfo = await ChatIMManager.sharedInstance
              .getUserInfo(seatInfo.user!.userId!);
          if (userFullInfo != null) {
            ChatRoomOnlineMemberEntity member = ChatRoomOnlineMemberEntity();
            member.userId = userFullInfo.userID;
            member.nickname = userFullInfo.nickName;
            member.avatar = userFullInfo.faceUrl;
            member.age = userFullInfo.birthday ?? 0;
            member.sex = userFullInfo.gender == 2 ? 1 : 0;
            member.seatIndex = seatInfo.index;
            member.isMute =
                TRTCManager.sharedInstance.currentMuteUserMap[member.userId];
            list.add(member);
          }
        }
      }
    }
    setState(() {
      seatUserList = list;
    });
  }
}
