import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomMoreMenuWidget extends StatefulWidget {
  final Function(ChatRoomMoreMenuActionType) callback;
  const ChatRoomMoreMenuWidget({super.key, required this.callback});

  @override
  State<ChatRoomMoreMenuWidget> createState() => _ChatRoomMoreMenuWidgetState();
}

class _ChatRoomMoreMenuWidgetState extends State<ChatRoomMoreMenuWidget> {
  ChatRoomController controller = Get.find<ChatRoomController>();
  List<ChatRoomMoreMenuActionType> actionList = [];

  @override
  void initState() {
    super.initState();

    if (controller.checkIsRoomOwner()) {
      actionList = [
        ChatRoomMoreMenuActionType.editRoomName,
        ChatRoomMoreMenuActionType.muteUser,
        ChatRoomMoreMenuActionType.smallRoom,
        ChatRoomMoreMenuActionType.exitRoom,
        ChatRoomMoreMenuActionType.dismissRoom,
      ];
    } else {
      actionList = [
        ChatRoomMoreMenuActionType.report,
        ChatRoomMoreMenuActionType.muteUser,
        ChatRoomMoreMenuActionType.smallRoom,
        ChatRoomMoreMenuActionType.exitRoom,
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return GradientWidget(
      width: ScreenUtil().screenWidth,
      height: (controller.checkIsRoomOwner() ? 216.h : 112.h) +
          ScreenUtil().statusBarHeight,
      colors: [
        Colors.black.withOpacity(0.8),
        Colors.black.withOpacity(0),
      ],
      child: Padding(
        padding: EdgeInsets.only(
            top: ScreenUtil().statusBarHeight + 35.h, left: 17.w),
        child: Wrap(
          spacing: 27.w,
          runSpacing: 20.h,
          children: actionList.map((e) => _buildActionItemWidget(e)).toList(),
        ),
      ),
    );
  }

  Widget _buildActionItemWidget(ChatRoomMoreMenuActionType type) {
    String assetImage;
    String title;
    switch (type) {
      case ChatRoomMoreMenuActionType.editRoomName:
        assetImage = Assets.imagesChatRoomMoreMenuActionEditName;
        title = "房间名称";
        break;
      case ChatRoomMoreMenuActionType.muteUser:
        assetImage = Assets.imagesChatRoomMoreMenuActionMuteUser;
        title = "屏蔽";
        break;
      case ChatRoomMoreMenuActionType.silenceUser:
        assetImage = Assets.imagesChatRoomMoreMenuActionSilence;
        title = "禁言";
        break;
      case ChatRoomMoreMenuActionType.smallRoom:
        assetImage = Assets.imagesChatRoomMoreMenuActionSmallRoom;
        title = "收起房间";
        break;
      case ChatRoomMoreMenuActionType.dismissRoom:
        assetImage = Assets.imagesChatRoomMoreMenuActionDismissRoom;
        title = "关闭房间";
        break;
      case ChatRoomMoreMenuActionType.report:
        assetImage = Assets.imagesChatRoomMoreMenuActionReport;
        title = "举报";
        break;
      case ChatRoomMoreMenuActionType.exitRoom:
        assetImage = Assets.imagesChatRoomMoreMenuActionExit;
        title = "退出房间";
        break;
    }
    return GestureDetector(
      onTap: () {
        Get.back();
        widget.callback(type);
      },
      child: SizedBox(
        width: 58.w,
        height: 80.h,
        child: Column(
          children: [
            ImageUtils.getImage(assetImage, 50.w, 50.w),
            Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: Text(
                title,
                style: TextStyles.common(14.sp, Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum ChatRoomMoreMenuActionType {
  editRoomName, //修改房间名称
  muteUser, //静音
  silenceUser, //禁言
  smallRoom, //最小化
  dismissRoom, //解散房间
  report, //举报
  exitRoom, //退出房间
}
