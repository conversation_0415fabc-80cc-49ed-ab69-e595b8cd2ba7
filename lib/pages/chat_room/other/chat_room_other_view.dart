import 'package:carousel_slider/carousel_slider.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/fonts_family.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/topic_item_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/other/online_member/chat_room_online_list_bottom_sheet.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart' hide CarouselController;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomOtherView extends StatefulWidget {
  const ChatRoomOtherView({super.key});

  @override
  State<ChatRoomOtherView> createState() => _ChatRoomOtherViewState();
}

class _ChatRoomOtherViewState extends State<ChatRoomOtherView> {
  final ChatRoomController controller = Get.find<ChatRoomController>();

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: controller,
      id: ChatRoomController.KChatRoomOtherViewBuildID,
      builder: (controller) {
        return Stack(
          children: [
            _buildBlackboardWidget(),
            _buildOnlineMemberBtn(),
            _buildViewerSeatInfoWidget(),
          ],
        );
      },
    );
  }

  Widget _buildBlackboardWidget() {
    double top = 347.h;
    double left = 126.w;
    if (controller.roomType == ChatRoomType.teapot.index) {
      top = 595.h;
      left = 250.w;
    }

    return Positioned(
      top: ScreenUtil().statusBarHeight + top,
      left: left,
      child: GestureDetector(
        onTap: () {
          if (controller.checkIsRoomOwner()) {
            Get.toNamed(GetRouter.editBlackboard);
          }
        },
        child: Container(
          width: 128.w,
          height: 128.w,
          padding:
              EdgeInsets.only(top: 15.h, left: 17.w, right: 20.w, bottom: 40.h),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                Assets.imagesChatRoomFairBlackBoard,
              ),
            ),
          ),
          child: CarouselSlider.builder(
            itemCount: controller.roomInfo?.roomTopicList?.length ?? 0,
            itemBuilder: (BuildContext context, int index, int realIndex) {
              TopicItemEntity? topicItem =
                  controller.roomInfo?.roomTopicList?[index];
              return _buildBlackboardTopicListItem(topicItem);
            },
            options: CarouselOptions(
                height: 128.w,
                aspectRatio: 91.w / 60.h,
                autoPlayInterval: const Duration(seconds: 6),
                autoPlay: (controller.roomInfo?.roomTopicList != null &&
                        controller.roomInfo!.roomTopicList!.length > 1)
                    ? true
                    : false,
                viewportFraction: 1.0),
          ),
        ),
      ),
    );
  }

  Widget _buildBlackboardTopicListItem(TopicItemEntity? topicItem) {
    if (topicItem == null) {
      return Container();
    }
    return Container(
      width: 91.w,
      height: 65.h,
      alignment: Alignment.center,
      child: topicItem.topicType == 2
          ? ImageUtils.getImage(topicItem.topicText ?? "", 91.w, 65.h,
              fit: BoxFit.cover)
          : Text(
              topicItem.topicText ?? "",
              textAlign: TextAlign.center,
              style: TextStyles.normal(16.sp,
                  f: FontsFamily.youSheBiaoTiHei, h: 1.0),
            ),
    );

    /*
    *
    * MarqueeText(
      text: topicItem.topicText!,
      textStyle:
      TextStyles.normal(16.sp, f: FontsFamily.youSheBiaoTiHei),
      containerWidth: 91.w,
      containerHeight: 60.h,
      scrollDirection: Axis.vertical,
      textBuilder: (context, text, textStyle) => Text(
        text,
        style: textStyle,
      ),
    ),
    * */
  }

  Widget _buildOnlineMemberBtn() {
    return Positioned(
      right: 0,
      bottom: 130.h + ScreenUtil().bottomBarHeight,
      child: GestureDetector(
        onTap: () {
          if (controller.roomInfo!.roomType == ChatRoomType.fair.index) {
            return;
          }
          ToastUtils.showBottomDialog(
            const ChatRoomOnlineListBottomSheet(),
          );
        },
        child: Container(
          height: 25.h,
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: 5.w, right: 10.w),
          decoration: BoxDecoration(
            color: AppColors.colorFF434343.withOpacity(0.5),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(5.r),
              bottomLeft: Radius.circular(5.r),
            ),
          ),
          child: Obx(
            () => Text(
              "${controller.onlineCount.value}人在线",
              style: TextStyles.common(12.sp, Colors.white),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildViewerSeatInfoWidget() {
    double left = 47.w;
    double bottom = 90.h;
    if (controller.roomType == ChatRoomType.teapot.index) {
      left = 30.w;
      bottom = 63.h;
    }
    return Positioned(
      left: left,
      bottom: bottom + ScreenUtil().bottomBarHeight,
      child: Container(
        width: 54.w,
        height: 25.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesChatRoomViewerIcon),
          ),
        ),
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            Text(
              "观众席",
              style: TextStyles.common(12.sp, Colors.white),
            ),
            Visibility(
              visible: !controller.checkIsOnSeat(
                  userId: controller.roomInfo?.currentRoomUserId),
              child: Positioned(
                left: -12.w,
                top: -15.h,
                child: ImageUtils.getImage(
                  Assets.imagesChatRoomOwnerTagIcon,
                  24.w,
                  22.h,
                ),
              ),
            ),
            Visibility(
              visible: !controller.checkIsOnSeat(),
              child: Positioned(
                top: -20.h,
                child: ImageUtils.getImage(
                  Assets.imagesChatRoomMineTagArrow,
                  14.w,
                  15.h,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
