import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/chat_room_online_member_entity.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/model/use_prop_result_entity.dart';
import 'package:dada/pages/chat_room/other/online_member/chat_room_online_controller.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_callback.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/trtc/chat_room_rtc_event.dart';
import 'package:dada/services/trtc/chat_room_rtc_manager.dart';
import 'package:dada/services/trtc/chat_room_service_subscribe_delegate.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/extensions/list_extension.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/log.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_barrage_craft/flutter_barrage_craft.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_custom_elem.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_trtc_cloud/trtc_cloud_def.dart';

class ChatRoomController extends GetxController {
  static const String KChatRoomBgViewBuildID = "chat_room_bg_view";
  static const String KChatRoomFuncViewBuildID = "chat_room_func_view";
  static const String KChatRoomSeatViewBuildID = "chat_room_seat_view";
  static const String KChatRoomOtherViewBuildID = "chat_room_other_view";
  static const String KChatRoomBottomBarBuildID = "chat_room_bottom_bar";

  static const int flowSeatIndex = 6;
  static const int flowSeatCountDown = 60 * 5;

  DateTime? _lastUpdateSeatTime;

  String? roomId = Get.parameters["roomId"];
  bool? isCreate = Get.parameters["isCreate"] == "1";
  int roomType = int.parse(Get.parameters["roomType"] ?? "1");
  bool isComeFromMini = Get.parameters["comeFromMini"] == "1";
  ChatRoomInfoEntity? roomInfo;
  ChatRoomSeatInfoUser? userRoleInfo;

  BarrageController barrageController = BarrageController();

  TRTCManager trtcManager = TRTCManager.sharedInstance;
  ChatIMManager chatIMManager = ChatIMManager.sharedInstance;

  RxBool isMuteMic = false.obs;
  RxBool isMuteSpeaker = false.obs;

  RxInt onlineCount = 0.obs; //在线人数

  bool openExchangeDialog = false;
  bool isClosing = false;

  final _likeOrDaBangEventSubject = BehaviorSubject<Map<String, int>>();

  Stream<Map<String, int>> get likeOrDaBangEventStream =>
      _likeOrDaBangEventSubject.stream;

  ChatImListener? _chatImListener;
  ChatRoomRTCListener? _rtcListener;

  ///席位音量监听
  RxMap<String, int> userVolume = <String, int>{}.obs;

  RxMap<int, V2TimMessage?> seatShowMessageMap = <int, V2TimMessage?>{
    1: null,
    2: null,
    3: null,
    4: null,
    5: null,
    6: null,
    7: null,
    8: null,
  }.obs;

  @override
  void onInit() async {
    super.onInit();

    TRTCManager.sharedInstance;
    await addRoomService();
    await addChatIMService();
    await getRoomInfo();
    await getOnlineCount();
    await enterRoom();
  }

  @override
  void onReady() {
    super.onReady();

    GlobalFloatingManager().hideMiniWindow();
  }

  @override
  void update([List<Object>? ids, bool condition = true]) {
    if (ids != null) {
      super.update(ids);
    } else {
      super.update([
        KChatRoomBgViewBuildID,
        KChatRoomFuncViewBuildID,
        KChatRoomSeatViewBuildID,
        KChatRoomOtherViewBuildID,
        KChatRoomBottomBarBuildID
      ]);
    }
  }

  Future<void> enterRoom() async {
    if (roomInfo?.roomNo != null) {
      if (!isComeFromMini) {
        await trtcManager.enterRoom(
          roomId: int.parse(roomInfo!.roomNo!),
          userId: UserService().user!.id!,
          role: TRTCCloudDef.TRTCRoleAudience,
          scene: TRTCCloudDef.TRTC_APP_SCENE_VOICE_CHATROOM,
        );
        await joinImGroup();
        trtcManager.enableAudioVolumeEvaluation();
      }
      await getSeatListInfo();
      await getUserRoleInfo();
      await autoTakeSeat();
      ApiService().doTask(taskId: "4");
    }
  }

  Future<void> startLocalAudio() async {
    PermissionStatus status = await Permission.microphone.status;
    if (status.isGranted || status.isDenied) {
      if (Platform.isAndroid) {
        ToastUtils.showTopDialog(
          barrierDismissible: false,
          child: const PermissionUseDescDialog(
              title: "麦克风权限说明", desc: "用于在语音房内能够发布音频流，供语音房间成员进行语音交流"),
        );
      }
      status = await Permission.microphone.request();
      if (Get.isDialogOpen == true) {
        Get.back();
      }
      if (status != PermissionStatus.granted) {
        return;
      }
    }
    await Permission.microphone.request();
    await trtcManager.switchRole(TRTCCloudDef.TRTCRoleAnchor);
    await trtcManager.startLocalAudio();
    trtcManager.localMicState = 1;
    update([KChatRoomBottomBarBuildID]);
  }

  Future<void> stopLocalAudio() async {
    await trtcManager.stopLocalAudio();
    update([KChatRoomBottomBarBuildID]);
  }

  Future<void> muteLocalAudio(bool mute) async {
    await trtcManager.muteLocalAudio(mute);
    isMuteMic.value = mute;
    trtcManager.localMicState = mute ? 0 : 1;
  }

  Future<void> muteAllRemoteAudio(bool mute) async {
    await trtcManager.muteAllRemoteAudio(mute);
    trtcManager.localSpeakerState = mute ? 0 : 1;
    isMuteSpeaker.value = mute;
  }

  Future<void> getRoomInfo({bool? showLoading}) async {
    if (roomId != null) {
      List<ChatRoomSeatInfoEntity>? seatList = roomInfo?.seatList;
      roomInfo = await ApiService()
          .getChatRoomInfo(roomId: roomId!, showLoading: showLoading);
      if (roomInfo == null) {
        Future.delayed(const Duration(seconds: 1), () {
          resetMicAndSpeakerState();
          Get.back(result: -1);
        });
      } else {
        roomType = roomInfo?.roomType ?? 1;
        if (seatList != null) {
          roomInfo!.seatList = seatList;
        }
        setInitSeatInfo();
        update();
      }
    }
  }

  ///获取麦位列表
  Future<void> getSeatListInfo() async {
    List<ChatRoomSeatInfoEntity>? seatList =
        await ApiService().getChatRoomSeatList(roomId: roomId!);
    if (seatList?.isNotEmpty == true) {
      roomInfo?.seatList = seatList;
    }
  }

  ///获取随机角色信息
  Future<void> getUserRoleInfo() async {
    if (roomId != null && !checkIsOnSeat()) {
      userRoleInfo = await ApiService().getMyRoleInfoInRoom();
      if (userRoleInfo != null) {
        userRoleInfo!.dressNo = UserService().user?.currentDressNo;
        if (roomType == ChatRoomType.teapot.index) {
          userRoleInfo!.nickname = UserService().user?.nickname;
        }
        chatIMManager.setGroupMemberInfo(
            groupID: roomId!,
            userID: userRoleInfo!.userId!,
            customInfo: {"roleInfo": userRoleInfo?.nickname ?? ""});
      }
    } else {
      if (checkIsOnSeat()) {
        ChatRoomSeatInfoEntity? seatInfo = roomInfo?.seatList
            ?.where((e) => e.user?.userId == UserService().user?.id)
            .first;
        if (seatInfo?.user != null) {
          userRoleInfo = seatInfo!.user!;
        }
      }
    }
  }

  ///设置初始化麦位信息
  void setInitSeatInfo() {
    if (roomInfo?.seatList != null) {
      return;
    }
    List<ChatRoomSeatInfoEntity> seatList = [];
    for (int i = 1; i <= 8; i++) {
      ChatRoomSeatInfoEntity seatInfo = ChatRoomSeatInfoEntity();
      seatInfo.index = i;
      seatInfo.status = 0;
      seatInfo.type = i == flowSeatIndex ? 2 : 1;
      seatList.add(seatInfo);
    }
    roomInfo?.seatList = seatList;
  }

  ///房主自动上麦(否则，获取后端接口返回麦位列表信息)
  Future<void> autoTakeSeat() async {
    ///如果是房主
    if (roomInfo?.createRoomUserId == UserService().user?.id &&
        userRoleInfo != null &&
        roomInfo?.seatList != null) {
      ///如果是创建房间进入的场景
      if (!checkIsOnSeat() && isCreate == true) {
        bool success = await changeSeat(seatIndex: 1, type: 0);
        if (success) {
          startLocalAudio();
        }
      } else {
        ///其它场景进入房间
        bool onSeat = checkIsOnSeat();
        if (onSeat && trtcManager.localMicState == 1) {
          startLocalAudio();
        }
        updateMicAndSpeakerState();
        update([
          KChatRoomSeatViewBuildID,
          KChatRoomBottomBarBuildID,
          KChatRoomOtherViewBuildID
        ]);
      }
    } else {
      await getSeatListInfo();
      bool onSeat = checkIsOnSeat();
      if (onSeat && trtcManager.localMicState == 1) {
        startLocalAudio();
      }
      updateMicAndSpeakerState();
      update([
        KChatRoomSeatViewBuildID,
        KChatRoomBottomBarBuildID,
        KChatRoomOtherViewBuildID
      ]);
    }
  }

  ///上麦
  Future<void> takeSeat({int? index}) async {
    int? seatIndex;
    if (index != null) {
      seatIndex = index;
    } else {
      ///随机上麦
      seatIndex = _getRandomSeatIndex();
    }
    if (seatIndex != null) {
      if (seatIndex == flowSeatIndex) {
        String content = "该席位为流动席位，每次仅有5分钟发言时间，5分钟后自动下麦，确定要坐上该席位吗？";
        if (checkIsOnSeat()) {
          content = "该席位为流动席位，每次仅有5分钟发言时间，5分钟后自动下麦，确定要更换到该席位吗？";
        }
        ToastUtils.showDialog(
          content: content,
          onConfirm: () async {
            _takeSeatAtIndex(seatIndex!);
          },
        );
      } else {
        _takeSeatAtIndex(seatIndex);
      }
    } else {
      ToastUtils.showToast("席位已满!");
    }
  }

  ///下麦
  Future<void> leaveSeat({int? index}) async {
    if (roomInfo?.seatList?.isNullOrEmpty == true) {
      return;
    }
    index ??= getUserSeatIndex();
    bool success = await changeSeat(seatIndex: index!, type: 1);
    if (success) {
      updateAfterLeaveSeat();
    }
  }

  ///下麦后更新
  void updateAfterLeaveSeat() async {
    await trtcManager.switchRole(TRTCCloudDef.TRTCRoleAudience);
    await stopLocalAudio();
    update([KChatRoomOtherViewBuildID]);
  }

  ///麦位操作
  ///
  ///
  /// fromSeatIndex: 同意别人交换麦位申请时，申请人的麦位序号
  ///
  Future<bool> changeSeat(
      {required int seatIndex,
      required int type,
      int? faction,
      int? fromSeatIndex,
      String? fromUserId}) async {
    ChatRoomSeatInfoEntity? targetSeatInfo =
        roomInfo?.seatList?.where((e) => e.index == seatIndex).toList().first;
    if (targetSeatInfo == null) {
      return false;
    }

    ChatRoomSeatInfoEntity seatInfo =
        ChatRoomSeatInfoEntity.fromJson(targetSeatInfo.toJson());
    int? applyIndex;

    ///上麦或换麦
    if (type == 0 || type == 2) {
      ///如果已在麦位上，换到另一个空的麦位上
      ChatRoomSeatInfoEntity? lastSeatInfo;
      List<ChatRoomSeatInfoEntity?>? result = roomInfo?.seatList
          ?.where((e) => e.user?.userId == UserService().user?.id)
          .toList();
      if (result?.isNotEmpty == true) {
        lastSeatInfo = result!.first;
      }
      seatInfo.type = 1;
      seatInfo.user = userRoleInfo;
      seatInfo.status = lastSeatInfo?.status ?? 1;
      seatInfo.faction = lastSeatInfo?.faction;
      if (seatIndex == flowSeatIndex) {
        ///如果是流动席位
        seatInfo.type = 2;
        seatInfo.takeSeatTime = DateTime.now().toString();
      }
      if (type == 2) {
        applyIndex = fromSeatIndex;
      }
    } else if (type == 1) {
      ///下麦
      seatInfo.user = null;
      seatInfo.status = 0;
      seatInfo.takeSeatTime = null;
      seatInfo.faction = null;
    } else if (type == 3) {
      ///选择红蓝阵营
      seatInfo.faction = faction;
    }

    int timestamp = 0;
    if (_lastUpdateSeatTime != null) {
      timestamp =
          _lastUpdateSeatTime!.difference(DateTime.now()).inMilliseconds.abs();
      if (timestamp < 1200) {
        return false;
      }
    }
    _lastUpdateSeatTime = DateTime.now();
    bool success = await ApiService().changeRoomSeat(
        roomId: roomId!,
        seatInfo: seatInfo,
        type: type,
        applyIndex: applyIndex,
        applyUserId: fromUserId);
    if (success) {
      updateSeatList(seatIndex: seatIndex, seatInfo: seatInfo, type: type);
      update([KChatRoomSeatViewBuildID]);
    }
    return success;
  }

  ///更新麦位列表
  void updateSeatList(
      {required int seatIndex,
      required ChatRoomSeatInfoEntity seatInfo,
      required type}) {
    if (!(roomInfo?.seatList?.isNotEmpty == true)) {
      return;
    }
    if (type == 0) {
      int? currentSeatIndex;
      if (checkIsOnSeat()) {
        currentSeatIndex = getUserSeatIndex();
      }
      ChatRoomSeatInfoEntity? lastSeatInfo;
      for (int i = 0; i < roomInfo!.seatList!.length; i++) {
        ChatRoomSeatInfoEntity seatInfo = roomInfo!.seatList![i];

        ///从旧麦位剔除
        if (currentSeatIndex != null &&
            seatInfo.index == currentSeatIndex &&
            seatInfo.status == 1 &&
            seatInfo.user != null) {
          lastSeatInfo = seatInfo;
          seatInfo.user = null;
        }

        ///设置在新麦位上
        if (seatInfo.index == seatIndex && seatInfo.user == null) {
          seatInfo.user = userRoleInfo;
          seatInfo.status = 1;
          seatInfo.faction = lastSeatInfo?.faction;
          if (seatIndex == flowSeatIndex) {
            seatInfo.type = 2;
            seatInfo.takeSeatTime = DateTime.now().toString();
          }
        }

        if (seatInfo.user == null) {
          seatInfo.status = 0;
          seatInfo.faction = null;
          seatInfo.takeSeatTime = null;
        }
      }
    } else if (type == 1) {
      roomInfo?.seatList?.map((e) {
        if (e.index == seatIndex) {
          e = seatInfo;
        }
      });
    }
  }

  ///退出房间
  Future<void> exitRoom({bool? force}) async {
    if (checkIsOnSeat(userId: UserService().user!.id!)) {
      await leaveSeat();
    }
    await trtcManager.exitRoom();
    trtcManager.disableAudioVolumeEvaluation();

    if (roomId != null || roomInfo?.roomNo != null) {
      ///如果是群主，就转让群主权限给下一个人
      bool isRoomOwner = checkIsRoomOwner();
      if (isRoomOwner) {
        if (force == true && await checkGroupIsOnlyOwner()) {
          await closeRoom(force: true);
        } else {
          ///转让群主
          await transferRoomOwner();
          await quitGroup();
        }
      } else {
        await quitGroup();
      }
    }
    if (!(force == true)) {
      resetMicAndSpeakerState();
      Get.back(result: 1);
    }
  }

  ///关闭房间
  Future<void> closeRoom({bool? force}) async {
    if (!checkIsRoomOwner()) {
      return;
    }
    isClosing = true;
    await trtcManager.exitRoom();
    trtcManager.clearMuteList();
    await dismissRoom();
    await dismissRoomGroup();
    if (!(force == true)) {
      isClosing = false;
      resetMicAndSpeakerState();
      removeChatIMService();
      removeTRCListener();
      clearRoom();
      Get.back(result: 1);
    }
  }

  ///解散房间
  Future<void> dismissRoom() async {
    bool success = await ApiService().dismissRoom(roomId!);
    if (!success) {
      Log.i("市集房间解散失败！");
    }
  }

  ///解散群组
  Future<void> dismissRoomGroup() async {
    bool success = await chatIMManager.dismissGroup(groupID: roomId!);
    if (!success) {
      Log.i("市集房间对应群组解散失败！");
    }
  }

  ///退出群组
  Future<void> quitGroup() async {
    bool success =
        await ChatIMManager.sharedInstance.quitGroup(groupID: roomId!);
    if (!success) {
      Log.i("退出群组失败！");
    }
  }

  ///转让群主
  Future<void> transferRoomOwner() async {
    ChatRoomOnlineController onlineController =
        ChatRoomOnlineController(roomId: roomId!);
    onlineController.roomController = this;
    List<ChatRoomOnlineMemberEntity>? list = await onlineController.loadData(0);
    if (list.isNullOrEmpty == true) {
      return;
    }
    ChatRoomOnlineMemberEntity? nextOwner = _findNextRoomOwner(list!);
    if (nextOwner != null) {
      await ApiService().transformRoomOwner(roomId!, nextOwner.userId!);
      // await ChatIMManager.sharedInstance.transferGroupOwner(
      //     groupID: roomId!, userID: nextOwner.userId!);
    }
  }

  ///查看群里是否仅剩群主
  Future<bool> checkGroupIsOnlyOwner() async {
    if (roomId == null) {
      return false;
    }
    ChatRoomOnlineController onlineController =
        ChatRoomOnlineController(roomId: roomId!);
    List<ChatRoomOnlineMemberEntity>? list = await onlineController.loadData(0);
    if (list.isNullOrEmpty == true) {
      return false;
    }

    bool result = list!
            .where((e) => e.userId == roomInfo?.currentRoomUserId)
            .isNotEmpty ==
        true;
    return result == true && list.length == 1;
  }

  bool checkIsOnSeat({String? userId}) {
    userId ??= UserService().user!.id;
    bool isOnSeat =
        roomInfo?.seatList?.where((e) => e.user?.userId == userId).isNotEmpty ==
            true;
    return isOnSeat;
  }

  bool checkIsRoomOwner({String? userId}) {
    userId ??= UserService().user?.id;
    return roomInfo?.currentRoomUserId == userId;
  }

  ///寻找房间内在线人员，把房主转让给其中一个
  ChatRoomOnlineMemberEntity? _findNextRoomOwner(
      List<ChatRoomOnlineMemberEntity> list) {
    ChatRoomOnlineMemberEntity? roomOwner;
    for (int i = 0; i < list.length; i++) {
      ChatRoomOnlineMemberEntity? member = list[i];
      if (member.userId != UserService().user!.id) {
        roomOwner = member;
        return roomOwner;
      }
    }
    return roomOwner;
  }

  Future<void> getOnlineCount() async {
    onlineCount.value = roomInfo?.onlineNumber ?? 0;
  }

  ///处理新消息
  void handleNewMessage(V2TimMessage msg) {
    if (msg.userID != null ||
        msg.elemType != MessageElemType.V2TIM_ELEM_TYPE_TEXT) {
      if (msg.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM) {
        handleIMCustomEventMsg(msg);
      }
      return;
    }

    ///根据用户ID区分是在讨论席还是观众席
    if (checkIsOnSeat(userId: msg.sender!)) {
      int? seatIndex = roomInfo?.seatList
          ?.where((e) => e.user?.userId! == msg.sender!)
          .first
          .index;
      if (seatIndex != null) {
        seatShowMessageMap[seatIndex] = msg;
      }
    } else {
      sendBarrage(msg);
    }
  }

  ///发送消息
  Future<bool> sendTextMessage(String text) async {
    V2TimMessage? message =
        await chatIMManager.sendTextMessage(text: text, toGroupID: roomId!);
    if (message != null) {
      handleNewMessage(message);
    }
    return message != null;
  }

  ///发送换席位申请
  Future<bool?> sendChangeSeatApply(String toUserId) async {
    int? fromSeatIndex = getUserSeatIndex();
    if (fromSeatIndex == null) {
      return false;
    }
    bool success =
        await sendTrtcCustomMsgEvent(ChatRoomRtcEvent.ChangeSeatApply, {
      "roomId": roomId,
      "toUserId": toUserId,
      "fromUserId": UserService().user?.id,
    });
    return success;
  }

  ///发送弹幕
  void sendBarrage(V2TimMessage msg) {
    if (!(msg.elemType == MessageElemType.V2TIM_ELEM_TYPE_TEXT ||
        msg.elemType == MessageElemType.V2TIM_ELEM_TYPE_FACE)) {
      return;
    }
    if (Get.context == null) {
      return;
    }
    bool isEmoji = msg.elemType == MessageElemType.V2TIM_ELEM_TYPE_FACE;
    String text = msg.elemType == MessageElemType.V2TIM_ELEM_TYPE_TEXT
        ? msg.textElem?.text ?? ""
        : "";
    if (text.length > 20) {
      text = "${text.substring(0, 20)}...";
    }
    barrageController.addBarrage(
      context: Get.context!,
      barrageWidget: Container(
        margin: EdgeInsets.only(bottom: 10.h),
        padding: EdgeInsets.only(
            left: roomType == ChatRoomType.teapot.index ? 0.w : 16.w,
            right: 10.w),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.5),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            roomType == ChatRoomType.fair.index
                ? ImageUtils.getImage(
                    Assets.imagesChatRoomBarrageXiguaIcon, 22.w, 22.w)
                : ClipOval(
                    child: ImageUtils.getImage(msg.faceUrl ?? "", 20.w, 20.w)),
            Visibility(
              visible: roomType == ChatRoomType.teapot.index,
              child: Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  msg.nickName ?? "",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white70,
                  ),
                ),
              ),
            ),
            Text(
              "：",
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white70,
              ),
            ),
            isEmoji
                ? ImageUtils.getImage(msg.faceElem?.data ?? "", 22.w, 22.w)
                : Text(
                    text,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white70,
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  ///获取随机麦位
  int? _getRandomSeatIndex() {
    int? randomSeatIndex;
    List<int>? unTakeSeatIndexes = roomInfo?.seatList?.map((e) {
      if (e.user == null && e.status == 0) {
        return e.index!;
      }
    }).toList() as List<int>?;
    if (unTakeSeatIndexes != null && unTakeSeatIndexes.isNotEmpty) {
      int randomIndex = math.Random().nextInt(unTakeSeatIndexes.length - 1);
      randomSeatIndex = unTakeSeatIndexes[randomIndex];
    }
    return randomSeatIndex;
  }

  ///更新指定麦位
  Future<void> _takeSeatAtIndex(int seatIndex) async {
    bool success = await changeSeat(seatIndex: seatIndex, type: 0);
    if (success) {
      await trtcManager.switchRole(TRTCCloudDef.TRTCRoleAnchor);
      await startLocalAudio();
      update([KChatRoomSeatViewBuildID, KChatRoomOtherViewBuildID]);
    }
  }

  ///选择阵营
  void selectedRedOrBlueFaction(
      {required int seatIndex, required int faction}) {
    changeSeat(seatIndex: seatIndex, type: 3, faction: faction);
  }

  ///获取用户当前麦位
  int? getUserSeatIndex({String? userId}) {
    userId ??= UserService().user!.id;
    return roomInfo?.seatList
        ?.where((e) => e.user?.userId == userId)
        .first
        .index;
  }

  // ///交换麦位
  // void _exchangeSeat(int fromSeatIndex, int toSeatIndex,
  //     {ChatRoomSeatInfoUser? fromUser, ChatRoomSeatInfoUser? toUser}) {
  //   if (!(roomInfo?.seatList?.isNotEmpty == true)) {
  //     return;
  //   }
  //   List<ChatRoomSeatInfoEntity> willChangeSeatList = roomInfo!.seatList!;
  //   fromUser ??= roomInfo?.seatList?.map((e) {
  //     if (e.index == fromSeatIndex) {
  //       return e.user;
  //     }
  //   }).first;
  //   ChatRoomSeatInfoEntity? fromUserSeatInfo = roomInfo?.seatList
  //       ?.where((e) => e.index == fromSeatIndex)
  //       .toList()
  //       .first;
  //
  //   toUser ??= roomInfo?.seatList?.map((e) {
  //     if (e.index == toSeatIndex) {
  //       return e.user;
  //     }
  //   }).first;
  //   ChatRoomSeatInfoEntity? toUserSeatInfo =
  //       roomInfo?.seatList?.where((e) => e.index == toSeatIndex).toList().first;
  //
  //   for (int i = 0; i < willChangeSeatList.length; i++) {
  //     ChatRoomSeatInfoEntity seatInfo = willChangeSeatList[i];
  //     if (seatInfo.index == fromSeatIndex) {
  //       seatInfo.user = toUser;
  //       seatInfo.status = fromUserSeatInfo?.status;
  //       seatInfo.faction = fromUserSeatInfo?.faction;
  //     }
  //     if (seatInfo.index == toSeatIndex) {
  //       seatInfo.user = fromUser;
  //       seatInfo.status = toUserSeatInfo?.status;
  //       seatInfo.faction = toUserSeatInfo?.faction;
  //     }
  //     if (seatInfo.index == flowSeatIndex && seatInfo.user != null) {
  //       seatInfo.takeSeatTime = DateTime.now().toString();
  //     }
  //   }
  //   update([KChatRoomSeatViewBuildID]);
  // }

  ///加入语聊房IM群组
  Future<void> joinImGroup() async {
    if (roomInfo?.currentRoomUserId != UserService().user?.id) {
      bool success = await chatIMManager.joinGroup(
          groupType: GroupType.Meeting, groupID: roomId!, message: '');
      if (!success) {
        ToastUtils.showToast("加入房间失败！");
        resetMicAndSpeakerState();
        Get.back(result: -1);
      }
    }
  }

  ///给席位上的人点赞
  Future<bool> sendLikeToSeatIndex(int seatIndex) async {
    _likeOrDaBangEventSubject.add({"index": seatIndex, "eventType": 1});
    bool success = await sendIMCustomMsgEvent(
        ChatImCustomMsgType.ChatRoomBeLikeMsg,
        {"roomId": roomId, "seatIndex": seatIndex.toString()});
    return success;
  }

  ///赠送搭棒给席位上的人
  Future<bool> sendDaBangToSeatIndex(int seatIndex, String userId) async {
    UsePropResultEntity? result = await ApiService().useProp(
        propNo: "DA_STICK",
        num: 1,
        toUserId: userId,
        propType: PropType.daBang);
    if (result != null && result.code == 1) {
      _likeOrDaBangEventSubject.add({"index": seatIndex, "eventType": 2});
      bool success = await sendIMCustomMsgEvent(
          ChatImCustomMsgType.ChatRoomBeSendDaBangMsg,
          {"roomId": roomId, "seatIndex": seatIndex.toString()});
      return success;
    }
    return false;
  }

  ///踢人
  Future<void> kickUserFromSeat(
      {required String userId, int? seatIndex}) async {
    bool success = await chatIMManager
        .kickGroupMember(groupID: roomId!, memberList: [userId]);
    if (success) {
      if (seatIndex != null) {
        success = await changeSeat(seatIndex: seatIndex, type: 1);
        updateAfterLeaveSeat();
      }
    }
  }

  ///举报
  Future<void> reportUser({required String userId, int? seatIndex}) async {
    Get.to(() => ReportPage(reportType: ReportType.chatMsg, userId: userId));
  }

  void increaseOnlineCount() {
    onlineCount.value++;
  }

  void reduceOnlineCount() {
    onlineCount.value--;
  }

  ///更新Mic和Speaker状态
  void updateMicAndSpeakerState() {
    if (trtcManager.localMicState == 0) {
      isMuteMic.value = true;
    } else {
      isMuteMic.value = false;
    }
    if (trtcManager.localSpeakerState == 0) {
      isMuteSpeaker.value = true;
    } else {
      isMuteSpeaker.value = false;
    }
  }

  ///恢复麦克风、扬声器状态
  void resetMicAndSpeakerState() {
    trtcManager.localMicState = 1;
    trtcManager.localSpeakerState = 1;
  }

  ///发送房间TRTC自定义消息
  Future<bool> sendTrtcCustomMsgEvent(String event, dynamic data) async {
    bool? success =
        await trtcManager.sendCustomCmdMsg(event: event, data: data);
    return success == true;
  }

  ///发送房间IM自定义消息
  Future<bool> sendIMCustomMsgEvent(String type, dynamic data) async {
    bool? success = await chatIMManager.sendCustomMessage(
        type: type, data: data, groupID: roomId!, receiver: "");
    return success == true;
  }

  //////////////////////////// IM & RTC Callback///////////////////////////////

  ///RTC Manager 响应回调
  Future<void> addRoomService() async {
    _rtcListener = ChatRoomRTCListener(
      updateRoomInfo: (roomId) {
        if (roomId != this.roomId) {
          return;
        }
        getRoomInfo(showLoading: false);
      },
      kickOutRoom: (roomId, userId) {
        if (roomId != this.roomId) {
          return;
        }
        reduceOnlineCount();
      },
      onChangeSeatApply: (roomId, fromUserId, toUserId) {
        if (roomId != this.roomId) {
          return;
        }
        if (Get.isDialogOpen == true && openExchangeDialog == true) {
          return;
        }
        ChatRoomSeatInfoEntity? fromSeatInfo = roomInfo?.seatList
            ?.where((e) => e.user?.userId == fromUserId)
            .first;
        if (fromSeatInfo != null && toUserId == UserService().user?.id) {
          String userNickname =
              fromSeatInfo.user?.nickname?.split("-").last ?? "";
          ToastUtils.showDialog(
              title: "要交换位置吗？",
              hideTopImg: true,
              content: "${fromSeatInfo.index}号$userNickname想要和你交换位置",
              cancelBtnTitle: "忽略",
              confirmBtnTitle: "同意",
              onConfirm: () async {
                if (checkIsOnSeat()) {
                  changeSeat(
                      seatIndex: getUserSeatIndex()!,
                      type: 2,
                      fromSeatIndex: fromSeatInfo.index,
                      fromUserId: fromSeatInfo.user?.userId);
                }
              },
              onCancel: () async {
                openExchangeDialog = false;
              });
          openExchangeDialog = true;
        }
      },
      onUserVoiceVolume: (userVolumeInfo) {
        if (roomId == null) {
          return;
        }
        userVolume.value = userVolumeInfo;
      },
    );
    trtcManager.addListener(_rtcListener!);
  }

  ///ChatIM Manager 响应回调
  Future<void> addChatIMService() async {
    _chatImListener = ChatImListener(
      onGroupInfoChanged: (roomId, changeInfo) {
        if (roomId != this.roomId) {
          return;
        }
        getRoomInfo(showLoading: false);
      },
      onRoomSeatListUpdated: (roomId, seatList) async {
        if (roomId != roomInfo!.roomNo!) {
          return;
        }
        roomInfo?.seatList = seatList;
        update([
          KChatRoomSeatViewBuildID,
          KChatRoomBottomBarBuildID,
          KChatRoomOtherViewBuildID
        ]);
        if (!checkIsOnSeat()) {
          await stopLocalAudio();
        }
      },
      onRoomTopicListUpdated: (roomId, topicList) async {
        if (roomId != roomInfo!.roomNo!) {
          return;
        }
        roomInfo?.roomTopicList = topicList;
        update([KChatRoomOtherViewBuildID]);
      },
      onReceiveNewMessage: (message) {
        if (message.groupID != roomId) {
          return;
        }
        handleNewMessage(message);
      },
      onMemberEnter: (roomId, memberList) {
        if (roomId != this.roomId) {
          return;
        }
        increaseOnlineCount();
      },
      onMemberLeave: (roomId, member) {
        if (roomId != this.roomId) {
          return;
        }
        reduceOnlineCount();
        if (checkIsRoomOwner(userId: member.userID)) {
          getRoomInfo(showLoading: false);
        }
      },
      onMemberKicked: (roomId, opUser, memberList) {
        if (checkIsRoomOwner(userId: opUser.userID) ||
            UserService().isOfficialAccount(userId: opUser.userID)) {
          bool containsMe =
              memberList.any((e) => e.userID == UserService().user?.id);
          if (containsMe) {
            ToastUtils.showToast("你已经被踢出房间");
            Future.delayed(const Duration(milliseconds: 1500), () async {
              isClosing = true;
              await stopLocalAudio();
              await trtcManager.exitRoom();
              resetMicAndSpeakerState();
              if (GlobalFloatingManager().isShowMiniWindow.value == true) {
                GlobalFloatingManager().hideMiniWindow();
              } else {
                if (Get.isDialogOpen == true || Get.isBottomSheetOpen == true) {
                  Get.back();
                }
                Get.back(result: -1);
              }
            });
          }
        }
      },
      onGroupDismissed: (roomId, opUser) async {
        if (checkIsRoomOwner() && isClosing) {
          return;
        }
        if (roomInfo?.roomNo == roomId && !checkIsRoomOwner()) {
          await stopLocalAudio();
          await trtcManager.exitRoom();
          ToastUtils.showToast("该房间已解散");
          if (Get.isDialogOpen == true || Get.isBottomSheetOpen == true) {
            Get.back();
          }
          resetMicAndSpeakerState();
          Get.back(result: -1);
        }
      },
    );
    chatIMManager.addListener(_chatImListener!);
  }

  void removeChatIMService() {
    chatIMManager.removeListener(_chatImListener!);
  }

  ///群组自定义消息
  void handleIMCustomEventMsg(V2TimMessage msg) {
    V2TimCustomElem? customElem = msg.customElem;
    if (customElem?.data != null) {
      ChatImCustomMsgEntity customMsgEntity =
          ChatImCustomMsgEntity.fromJson(jsonDecode(customElem!.data!));
      switch (customMsgEntity.type) {
        case ChatImCustomMsgType.ChatRoomBeLikeMsg:
          if (customMsgEntity.data != null) {
            Map<String, dynamic>? dataInfo = customMsgEntity.data;
            if (dataInfo?["seatIndex"] != null) {
              int seatIndex = int.parse(dataInfo!["seatIndex"]!);
              _likeOrDaBangEventSubject
                  .add({"index": seatIndex, "eventType": 1});
            }
          }
          break;
        case ChatImCustomMsgType.ChatRoomBeSendDaBangMsg:
          if (customMsgEntity.data != null) {
            Map<String, dynamic>? dataInfo = customMsgEntity.data;
            if (dataInfo?["seatIndex"] != null) {
              int seatIndex = int.parse(dataInfo!["seatIndex"]!);
              _likeOrDaBangEventSubject
                  .add({"index": seatIndex, "eventType": 2});
            }
          }
          break;
      }
    }
  }

  void removeTRCListener() {
    trtcManager.removeListener(_rtcListener!);
  }

  void clearRoom() {
    roomId = null;
    roomInfo = null;
  }

  @override
  void dispose() {
    debugPrint("ChatRoomController dispose");
    super.dispose();
  }
}
