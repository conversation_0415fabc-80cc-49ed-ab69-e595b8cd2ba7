import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_barrage_craft/flutter_barrage_craft.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomBarrageView extends StatefulWidget {
  const ChatRoomBarrageView({super.key});

  @override
  State<ChatRoomBarrageView> createState() => _ChatRoomBarrageViewState();
}

class _ChatRoomBarrageViewState extends State<ChatRoomBarrageView> {
  final ChatRoomController controller = Get.put(ChatRoomController());

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_){
      controller.barrageController.init(
        Size(ScreenUtil().screenWidth, (24.h + 10.h) * 3),);
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: controller,
      id: ChatRoomController.KChatRoomFuncViewBuildID,
      builder: (controller) {
        return Padding(
          padding:
              EdgeInsets.only(top: ScreenUtil().statusBarHeight + 44.h),
          child: Container(
            width: ScreenUtil().screenWidth,
            height: (24.h + 10.h) * 3,
            margin: EdgeInsets.only(top: 10.h),
            child: BarrageView(controller: controller.barrageController),
          ),
        );
      },
    );
  }
}
