import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/pages/chat_room/list/chat_room_list_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomListPage extends StatefulWidget {
  const ChatRoomListPage({super.key});

  @override
  State<ChatRoomListPage> createState() => _ChatRoomListPageState();
}

class _ChatRoomListPageState extends State<ChatRoomListPage> {
  ChatRoomListController controller = Get.put(ChatRoomListController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "房间列表",
      ),
      body: GetBuilder(
        init: controller,
        id: controller.refreshId,
        builder: (controller) {
          return RefreshWidget.build(
            refreshController: controller.refreshController,
            onRefresh: () => controller.refreshData(),
            onLoadMore: () => controller.loadMoreData(),
            child: _buildRoomListWidget(),
          );
        },
      ),
    );
  }

  Widget _buildRoomListWidget() {
    if (controller.data.isEmpty) {
      return const ListPageEmptyWidget();
    }
    return GridView.extent(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      maxCrossAxisExtent: 165.w,
      childAspectRatio: 165.w / 150.h,
      crossAxisSpacing: 15.w,
      mainAxisSpacing: 15.w,
      children: controller.data.map((e) => _buildListItem(e)).toList(),
    );
  }

  Widget _buildListItem(ChatRoomInfoEntity itemEntity) {
    String bgImage = Assets.imagesAvatarPlaceholder;
    String roomTypeStr = "去市集";
    String catImageAssetName = Assets.imagesChatRoomListCat1;
    double catImageWidth = 51.w;
    double catImageHeight = 32.h;
    if (itemEntity.roomType == ChatRoomType.fair.index) {
      bgImage = Assets.imagesHomeListBg1;
      roomTypeStr = "去市集";
    } else if (itemEntity.roomType == ChatRoomType.teapot.index) {
      bgImage = Assets.imagesHomeListBg2;
      roomTypeStr = "去茶壶";
    } else if (itemEntity.roomType == 3) {
      bgImage = Assets.imagesHomeListBg3;
      roomTypeStr = "去睡前卧谈会";
    }

    if (itemEntity.onlineNumber != null && itemEntity.onlineNumber! > 2) {
      catImageAssetName = Assets.imagesChatRoomListCat2;
      catImageWidth = 76.w;
      catImageHeight = 27.h;
    }

    return GestureDetector(
      onTap: () {
        if (!GlobalFloatingManager()
            .currentIsShowMiniWindow(roomInfo: itemEntity)) {
          bool isInMiniRoom = GlobalFloatingManager().chatRoomController != null &&
              itemEntity.roomNo ==
                  GlobalFloatingManager().chatRoomController!.roomInfo?.roomNo;
          Get.toNamed(GetRouter.chatRoomDetail, parameters: {
            "roomId": itemEntity.roomNo!,
            "roomType": itemEntity.roomType!.toString(),
            "comeFromMini": isInMiniRoom ? "1" : "0",
          })?.then((result) {
            Future.delayed(const Duration(milliseconds: 300), () {
              controller.refreshData();
            });
          });
        }
      },
      child: Container(
        width: 165.w,
        height: 150.h,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(bgImage),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 13.h),
              child: Row(
                children: [
                  SizedBox(width: 16.w),
                  ImageUtils.getImage(Assets.imagesHomeListItemIcon, 8.w, 12.h),
                  SizedBox(
                    width: 5.w,
                  ),
                  Text(
                    roomTypeStr,
                    style: TextStyles.normal(12.sp,
                        c: AppTheme.themeData.textTheme.headlineMedium?.color),
                  ),
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.only(right: 6.w),
                    child: Text(
                      "ID：${itemEntity.roomNo}",
                      style: TextStyles.common(12.sp, AppColors.colorFF666666),
                    ),
                  ),
                ],
              ),
            ),

            ///房间名称
            Visibility(
              visible: itemEntity.roomName?.isNotEmpty == true,
              child: Padding(
                padding: EdgeInsets.only(left: 14.w, top: 5.h),
                child: Text(
                  "名称：${itemEntity.roomName}",
                  style: TextStyles.common(12.sp, AppColors.colorFF666666),
                ),
              ),
            ),

            ///话题
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 15.w, right: 10.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      itemEntity.roomTopicList?.first.topicText ??
                          "没有固定话题，\n随便聊吧",
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyles.normal(16.sp),
                    ),
                  ],
                ),
              ),
            ),

            ///底部在线
            Padding(
              padding: EdgeInsets.only(left: 10.w, bottom: 15.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ImageUtils.getImage(
                      catImageAssetName, catImageWidth, catImageHeight),
                  Padding(
                    padding: EdgeInsets.only(right: 10.w),
                    child: Text(
                      "${itemEntity.onlineNumber ?? 0}${S.current.people}在线",
                      style: TextStyles.normal(14.sp,
                          c: AppTheme
                              .themeData.textTheme.headlineMedium?.color),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
