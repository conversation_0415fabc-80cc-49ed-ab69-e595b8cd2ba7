import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/chat_room_list_result_entity.dart';
import 'package:dada/services/network/api_service.dart';

class ChatRoomListController extends ListPageController<
    ChatRoomInfoEntity, ChatRoomListController> {

  @override
  Future<List<ChatRoomInfoEntity>?> loadData(int page) async {
    ChatRoomListResultEntity? result = await ApiService().getChatRoomList(page);
    if (result != null) {
      return result.roomVoLists;
    }
    return null;
  }
}
