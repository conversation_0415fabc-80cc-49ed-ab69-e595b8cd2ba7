import 'package:dada/pages/chat_room/action/chat_room_action_view.dart';
import 'package:dada/pages/chat_room/barrage/chat_room_barrage_view.dart';
import 'package:dada/pages/chat_room/bg/chat_room_bg_view.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/other/chat_room_other_view.dart';
import 'package:dada/pages/chat_room/seat/chat_room_seat_view.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/utils/log.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class ChatRoomPage extends StatefulWidget {
  const ChatRoomPage({super.key});

  @override
  State<ChatRoomPage> createState() => _ChatRoomPageState();
}

class _ChatRoomPageState extends State<ChatRoomPage> {
  final ChatRoomController controller = Get.put(ChatRoomController());

  @override
  void initState() {
    super.initState();

    WakelockPlus.enable();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        Log.i("didPop: $didPop, result: $result");
        if (didPop && result == null) {
          GlobalFloatingManager().showMiniWindow(roomController: controller);
        }
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        resizeToAvoidBottomInset: false,
        body: GetBuilder(
          init: controller,
          builder: (controller) {
            return const Stack(
              children: [
                ChatRoomBgView(),
                ChatRoomBarrageView(),
                ChatRoomSeatView(),
                ChatRoomOtherView(),
                ChatRoomActionView(),
              ],
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    super.dispose();
  }
}
