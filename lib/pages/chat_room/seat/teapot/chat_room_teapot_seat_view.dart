import 'dart:io';
import 'package:dada/common/values/enums.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/seat/teapot/chat_room_teapot_seat_item_view.dart';
import 'package:dada/utils/dress_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomTeapotSeatView extends StatefulWidget {
  const ChatRoomTeapotSeatView({super.key});

  @override
  State<ChatRoomTeapotSeatView> createState() => _ChatRoomTeapotSeatViewState();
}

class _ChatRoomTeapotSeatViewState extends State<ChatRoomTeapotSeatView> {
  ChatRoomController chatRoomController = Get.find<ChatRoomController>();
  late List<Offset> seatPositions;

  @override
  void initState() {
    super.initState();

    double addOffset = 0.0;
    if (Platform.isAndroid) {
      if (ScreenUtil().screenHeight > 830) {
        addOffset = 20.h;
      } else if (ScreenUtil().screenHeight > 820) {
        addOffset = 10.h;
      }
    }

    seatPositions = [
      Offset(85.w, 355.h),
      Offset(5.w, 400.h),
      Offset(190.w, 380.h),
      Offset(280.w, 345.h),
      Offset(80.w, 170.h + addOffset),
      Offset(10.w, 170.h + addOffset),
      Offset(200.w, 170.h + addOffset),
      Offset(275.w, 170.h + addOffset),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Stack(
        children: [
          /// 二层人物显示
          ...seatPositions.map((e) {
            int index = seatPositions.indexOf(e);
            List<ChatRoomSeatInfoEntity>? list = chatRoomController
                .roomInfo?.seatList
                ?.where((e) => e.index == index + 1)
                .toList();
            if (index < 4) {
              return Container();
            }
            if (list?.isNotEmpty == true) {
              ChatRoomSeatInfoEntity? seatInfo = list!.first;
              double scaleX = -1.0;
              if (seatInfo.index != null && seatInfo.index! > 6) {
                scaleX = 1.0;
              }
              String roleImage = getRoleImageAsset(seatInfo);
              return Positioned(
                left: e.dx,
                top: e.dy,
                child: RepaintBoundary(
                  child: ChatRoomTeapotSeatItemView(
                    index: seatInfo.index!,
                    seatInfo: seatInfo,
                    imageScaleX: scaleX,
                    roleImage: roleImage,
                  ),
                ),
              );
            }
            return Container();
          }),

          ///左栏杆
          Positioned(
            top: Platform.isAndroid
                ? ScreenUtil().screenHeight > 830 ? 256.h : 251.h
                : ScreenUtil().screenHeight < 820
                    ? 230.h
                    : 233.h,
            left: ScreenUtil().screenWidth > 384 ? -0.5.w : -0.5.w,
            child: IgnorePointer(
              child: ImageUtils.getImage(
                  Assets.imagesChatRoomTeapotHandrailLeft, 132.w, 94.h),
            ),
          ),

          ///右栏杆
          Positioned(
            top: Platform.isAndroid
                ? ScreenUtil().screenHeight > 830 ? 255.h : 250.h
                : ScreenUtil().screenHeight < 820
                    ? 230.h
                    : 230.h,
            right: Platform.isAndroid ? 13.w : 10.w,
            child: IgnorePointer(
              child: ImageUtils.getImage(
                  Assets.imagesChatRoomTeapotHandrailRight, 121.w, 99.h),
            ),
          ),

          ///底层人物显示
          ...seatPositions.map((e) {
            int index = seatPositions.indexOf(e);
            if (index > 3) {
              return Container();
            }

            List<ChatRoomSeatInfoEntity>? list = chatRoomController
                .roomInfo?.seatList
                ?.where((e) => e.index == index + 1)
                .toList();
            if (list?.isNotEmpty == true) {
              ChatRoomSeatInfoEntity? seatInfo = list!.first;
              double scaleX = 1.0;
              if (seatInfo.index != null && seatInfo.index! < 3) {
                scaleX = -1.0;
              }
              String roleImage = getRoleImageAsset(seatInfo);
              debugPrint(
                  "--userName: ${seatInfo.user?.nickname ?? ""}, roleImage: $roleImage--, seatList: ${chatRoomController.roomInfo?.seatList} ");
              return Positioned(
                left: e.dx,
                top: e.dy,
                child: RepaintBoundary(
                  child: ChatRoomTeapotSeatItemView(
                    index: seatInfo.index!,
                    seatInfo: seatInfo,
                    imageScaleX: scaleX,
                    roleImage: roleImage,
                  ),
                ),
              );
            }
            return Container();
          }),
        ],
      ),
    );
  }

  String getRoleImageAsset(ChatRoomSeatInfoEntity seatInfo) {
    String roleAssetsName = "";
    if (seatInfo.user?.dressNo != null) {
      bool isRandomStatus = true;
      String? status = "";
      if (seatInfo.index == 5 ||
          seatInfo.index == 6 ||
          seatInfo.index == 7 ||
          seatInfo.index == 8) {
        isRandomStatus = false;
        status = UserRoleImageStatus.sitPlayPhone.value;
      }
      roleAssetsName = DressUtils().getSvgaAssetNameWithDressNo(
        dressNo: seatInfo.user!.dressNo!,
        isRandomStatus: isRandomStatus,
        status: status,
      );
    }
    if (roleAssetsName.isNotEmpty) {
      roleAssetsName = "assets/svga/dress/$roleAssetsName";
    }
    return roleAssetsName;
  }
}
