import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/seat/fair/chat_room_fair_seat_view.dart';
import 'package:dada/pages/chat_room/seat/teapot/chat_room_teapot_seat_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatRoomSeatView extends StatefulWidget {
  const ChatRoomSeatView({super.key});

  @override
  State<ChatRoomSeatView> createState() => _ChatRoomSeatViewState();
}

class _ChatRoomSeatViewState extends State<ChatRoomSeatView> {
  ChatRoomController chatRoomController = Get.find<ChatRoomController>();

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: chatRoomController,
      id: ChatRoomController.KChatRoomSeatViewBuildID,
      builder: (controller) {
        if (chatRoomController.roomType == 1) {
          return ChatRoomFairSeatView();
        }
        return ChatRoomTeapotSeatView();
      },
    );
  }
}
