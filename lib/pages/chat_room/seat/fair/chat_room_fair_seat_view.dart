import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/seat/fair/chat_room_fair_seat_item_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatRoomFairSeatView extends StatefulWidget {
  const ChatRoomFairSeatView({super.key});

  @override
  State<ChatRoomFairSeatView> createState() => _ChatRoomFairSeatViewState();
}

class _ChatRoomFairSeatViewState extends State<ChatRoomFairSeatView> {
  ChatRoomController chatRoomController = Get.find<ChatRoomController>();
  late List<Offset> seatPositions;

  @override
  void initState() {
    super.initState();

    seatPositions = [
      Offset(125.w, 204.h),
      Offset(40.w, 291.h),
      Offset(35.w, 428.h),
      Offset(110.w, 500.h),
      Offset(190.w, 500.h),
      Offset(285.w, 446.h),
      Offset(283.w, 317.h),
      Offset(220.w, 211.h),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Stack(
        children: [
          ...seatPositions.map((e) {
            int index = seatPositions.indexOf(e);
            List<ChatRoomSeatInfoEntity>? list = chatRoomController
                .roomInfo?.seatList
                ?.where((e) => e.index == index + 1)
                .toList();
            if (list?.isNotEmpty == true) {
              ChatRoomSeatInfoEntity? seatInfo = list!.first;
              return Positioned(
                left: e.dx,
                top: e.dy,
                child: ChatRoomFairSeatItemView(
                  index: seatInfo.index!,
                  seatInfo: seatInfo,
                ),
              );
            }
            return Container();
          }),
        ],
      ),
    );
  }
}
