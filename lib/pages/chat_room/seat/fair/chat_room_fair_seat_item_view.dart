import 'dart:async';
import 'dart:math' as math;
import 'dart:ui' as ui show PlaceholderAlignment;
import 'package:animate_do/animate_do.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/svgaplayer_flutter.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tolyui/tolyui.dart';

class ChatRoomFairSeatItemView extends StatefulWidget {
  final int index;
  final ChatRoomSeatInfoEntity seatInfo;

  const ChatRoomFairSeatItemView(
      {super.key, required this.seatInfo, required this.index});

  @override
  State<ChatRoomFairSeatItemView> createState() =>
      _ChatRoomFairSeatItemViewState();
}

class _ChatRoomFairSeatItemViewState extends State<ChatRoomFairSeatItemView>
    with TickerProviderStateMixin {
  final ChatRoomController controller = Get.find<ChatRoomController>();
  late SVGAAnimationController svgaAnimationController;
  Timer? _timer;
  RxInt flowSeatCountDown = 0.obs;
  final double roleWidth = 50.w;
  final double roleHeight = 100.w;
  V2TimMessage? currentShowMsg;

  late GlobalKey<TolyTooltipState> toolTipKey;

  PopoverController popoverController = PopoverController();

  RxList<Widget> likeOrDaBangAnimationWidgets = <Widget>[].obs;

  @override
  void initState() {
    super.initState();

    svgaAnimationController = SVGAAnimationController(vsync: this);
    toolTipKey =
        GlobalKey<TolyTooltipState>(debugLabel: widget.index.toString());
    controller.likeOrDaBangEventStream.listen((event) {
      handleReceiveLikeOrDaBangEvent(event);
    });
  }

  @override
  Widget build(BuildContext context) {
    _startFlowSeatCountDownTimer();
    return _buildSeatUserActionPopover(
      child: Container(
        alignment: Alignment.topCenter,
        color: Colors.transparent,
        width: roleWidth + 10.w,
        height: roleHeight,
        child: Stack(
          alignment: Alignment.center,
          clipBehavior: Clip.none,
          children: [
            _buildEmptySeatWidget(),
            _buildRoleImageWidget(),
            _buildFlowSeatCountDownWidget(),
            _buildReceiveLikeOrDaBangWidget(),
            _buildRoleNicknameWidget(),
            _buildSelectedFactionWidget(),
            _buildMineTagIconWidget(),
          ],
        ),
      ),
    );
  }

  ///空席位展示
  Widget _buildEmptySeatWidget() {
    if (widget.seatInfo.user == null) {
      return GestureDetector(
        onTap: () {
          controller.takeSeat(index: widget.index);
        },
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            ImageUtils.getImage(
                widget.index == ChatRoomController.flowSeatIndex
                    ? Assets.imagesChatRoomFairSeatFlowEmptyIcon
                    : Assets.imagesChatRoomFairSeatEmptyIcon,
                60.w,
                76.h,
                fit: BoxFit.cover),
            Positioned(
              bottom: 1.h,
              left: 23.w,
              child: Text(
                "${widget.seatInfo.index}",
                style:
                    TextStyles.common(12.sp, Colors.white, w: FontWeight.w500),
              ),
            ),
          ],
        ),
      );
    }
    return Container();
  }

  ///席位上用户角色形象及消息气泡显示
  Widget _buildRoleImageWidget() {
    if (widget.seatInfo.user == null) {
      svgaAnimationController.clear();
      svgaAnimationController.videoItem = null;
      return SizedBox(width: roleWidth, height: roleHeight);
    }
    _setRoleAssetImage();
    return Obx(() {
      V2TimMessage? msg = controller.seatShowMessageMap[widget.seatInfo.index];
      if (msg != null && currentShowMsg?.msgID != msg.msgID) {
        _showMessageBubble(msg);
      }
      currentShowMsg = msg;
      return TolyTooltip(
        key: toolTipKey,
        maxWidth: 200.w,
        placement: Placement.top,
        gap: 10.h,
        enableTapToDismiss: false,
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
        triggerMode: TooltipTriggerMode.manual,
        showDuration: const Duration(seconds: 3),
        richMessage: _buildRoleMessage(msg),
        decorationConfig: DecorationConfig(
          backgroundColor: Colors.black.withOpacity(0.3),
          isBubble: true,
          radius: Radius.circular(10.r),
          textColor: Colors.white,
        ),
        child: SizedBox(
          width: roleWidth,
          height: roleHeight,
          child: SVGAImage(svgaAnimationController),
        ),
      );
    });
  }

  ///席位编号和昵称
  Widget _buildRoleNicknameWidget() {
    if (widget.seatInfo.user != null) {
      bool isMine = widget.seatInfo.user?.userId == UserService().user?.id;
      double textWidth = StringUtils.calculateTextWidth(
          widget.seatInfo.user!.nickname?.split('-').first ?? "");
      double width = math.max(90.w, textWidth);
      return Positioned(
        left: -(width - roleWidth) / 2.0,
        top: roleHeight,
        child: Container(
          alignment: Alignment.center,
          width: width,
          height: 47.h,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Column(
                children: [
                  Text(
                    widget.seatInfo.user!.nickname?.split('-').first ?? "",
                    style: TextStyles.common(
                      12.sp,
                      isMine
                          ? AppColors.colorFF2D6D0B
                          : AppColors.colorFF794722,
                    ),
                  ),
                  Container(
                    width: 82.w,
                    height: 30.h,
                    margin: EdgeInsets.only(right: 5.w),
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(isMine
                            ? Assets.imagesChatRoomMineRoleNicknameBg
                            : Assets.imagesChatRoomOtherRoleNicknameBg),
                      ),
                    ),
                    child: Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 10.w),
                          child: Text(
                            "${widget.index}",
                            style: TextStyles.common(
                              14.sp,
                              isMine
                                  ? AppColors.colorFF2D6D0B
                                  : AppColors.colorFFDDDDDD,
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(left: 9.w),
                          width: 82.w - 28.w - 5.w,
                          alignment: Alignment.center,
                          child: Text(
                            widget.seatInfo.user!.nickname?.split('-').last ??
                                "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyles.common(
                              12.sp,
                              isMine
                                  ? AppColors.colorFF2D6D0B
                                  : AppColors.colorFFDDDDDD,
                              w: FontWeight.w700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Visibility(
                visible: controller.checkIsRoomOwner(
                    userId: widget.seatInfo.user?.userId),
                child: Positioned(
                  top: 2.h,
                  left: -6.w,
                  child: ImageUtils.getImage(
                      Assets.imagesChatRoomOwnerTagIcon, 24.w, 22.h),
                ),
              ),
              Obx(
                () {
                  int? volume =
                      controller.userVolume[widget.seatInfo.user?.userId];
                  return Visibility(
                    visible: volume != null && volume > 0,
                    child: Positioned(
                      bottom: 8.h,
                      right: 0.w,
                      child: ImageUtils.getImage(
                          Assets.imagesChatRoomSeatSpeakingStateIcon,
                          15.w,
                          15.w),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      );
    }
    return Container();
  }

  ///流水器倒计时
  Widget _buildFlowSeatCountDownWidget() {
    if (widget.seatInfo.type == 2 &&
        widget.seatInfo.user != null &&
        widget.seatInfo.takeSeatTime != null) {
      return Positioned(
        top: -23.h,
        left: -(60.w - roleWidth) / 2.0,
        child: Container(
          width: 60.w,
          height: 29.h,
          alignment: Alignment.center,
          padding: EdgeInsets.only(bottom: 2.5.h),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(Assets.imagesChatRoomFairFlowSeatCountDownBg),
            ),
          ),
          child: Obx(
            () {
              int minutes = flowSeatCountDown.value ~/ 60;
              int seconds = flowSeatCountDown.value % 60;
              int leftTime = minutes > 0 ? minutes : seconds;
              String unit = minutes > 0 ? "min" : "s";
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Stack(
                    children: [
                      Text(
                        "$leftTime",
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w700,
                          foreground: Paint()
                            ..style = PaintingStyle.stroke
                            ..strokeWidth = 0.2
                            ..color = AppColors.colorFF70311C,
                        ),
                      ),
                      Text(
                        "$leftTime",
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w700,
                          color: AppColors.colorFFFDE484,
                          shadows: [
                            Shadow(
                              color: AppColors.colorFF42CE48,
                              offset: const Offset(0.5, 0.5),
                              blurRadius: 1.r,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 3.6.w, bottom: 5.h),
                    child: Text(
                      unit,
                      style: TextStyles.common(12.sp, AppColors.colorFF70311C),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      );
    }
    return Container();
  }

  ///已经选择的红蓝阵容旗子显示
  Widget _buildSelectedFactionWidget() {
    if (widget.seatInfo.user == null ||
        widget.seatInfo.faction == null ||
        widget.seatInfo.faction == 0) {
      return Container();
    }
    String factionImage = widget.seatInfo.faction == 1
        ? Assets.imagesChatRoomSeatUserFactionRedSelected
        : Assets.imagesChatRoomSeatUserFactionBlueSelected;
    return Positioned(
      top: roleHeight / 2 - 10.h,
      right: 0,
      child: Padding(
        padding: EdgeInsets.only(left: 10.w),
        child: ImageUtils.getImage(factionImage, 16.w, 20.h),
      ),
    );
  }

  ///点赞或收到搭棒展示
  Widget _buildReceiveLikeOrDaBangWidget() {
    if (widget.seatInfo.user == null) {
      likeOrDaBangAnimationWidgets.clear();
      return Container();
    }
    return Obx(
      () => Positioned(
        top: 0.h,
        child: Stack(
          children: [
            ...likeOrDaBangAnimationWidgets,
          ],
        ),
      ),
    );
  }

  void handleReceiveLikeOrDaBangEvent(Map<String, int> data) {
    int? index = data["index"];
    int? eventType = data["eventType"];
    if (index != widget.index || !(eventType == 1 || eventType == 2)) {
      return;
    }
    Widget animationWidget = FadeOutUp(
      animate: true,
      onFinish: (AnimateDoDirection direction) {
        if (likeOrDaBangAnimationWidgets.length > 10) {
          likeOrDaBangAnimationWidgets.removeRange(0, 8);
        }
      },
      duration: const Duration(seconds: 2),
      child: Container(
        width: 50.w,
        height: 20.h,
        alignment: Alignment.center,
        child: ImageUtils.getImage(
            eventType == 1
                ? Assets.imagesChatRoomSeatLikeAnimationIcon
                : Assets.imagesChatRoomSeatDabangAnimationIcon,
            30.w,
            20.h),
      ),
    );
    likeOrDaBangAnimationWidgets.add(animationWidget);
  }

  ///点击席位交互浮层
  Widget _buildSeatUserActionPopover({required Widget child}) {
    if (widget.seatInfo.user == null) {
      return child;
    }
    bool isMine = widget.seatInfo.user?.userId == UserService().user?.id;
    return TolyPopover(
      gap: 0.h,
      controller: popoverController,
      closeShiftPlacement: true,
      offsetCalculator: (calculator) {
        if (isMine) {
          return Offset(
              (100 + 20 + roleWidth) / 2, roleHeight - 56.h / 2 + 30.h);
        }
        return Offset(0.w, 15.h);
      },
      placement: Placement.top,
      overlay: _buildSeatPopoverOverlay(isMine, popoverController),
      overlayDecorationBuilder: (_) => const BoxDecoration(
        color: Colors.transparent,
      ),
      builder: (_, ctrl, __) {
        return GestureDetector(
          onTap: () {
            popoverController.open();
          },
          child: child,
        );
      },
    );
  }

  ///点击席位交互浮层overlay(点赞、送搭棒、举报等)
  Widget _buildSeatPopoverOverlay(bool isMine, PopoverController ctrl) {
    if (isMine) {
      return Row(
        children: [
          GestureDetector(
            onTap: () {
              controller.selectedRedOrBlueFaction(
                  seatIndex: widget.index, faction: 1);
              ctrl.close();
            },
            child: ImageUtils.getImage(
                Assets.imagesChatRoomSeatUserFactionRed, 50.w, 56.h),
          ),
          GestureDetector(
            onTap: () {
              if (widget.seatInfo.faction == 0 ||
                  widget.seatInfo.faction == null) {
                ctrl.close();
                return;
              }
              controller.selectedRedOrBlueFaction(
                  seatIndex: widget.index, faction: 0);
              ctrl.close();
            },
            child: Container(
                width: roleWidth - 15.w,
                margin: EdgeInsets.only(top: 76.h),
                child: ImageUtils.getImage(
                    Assets.imagesCloseRoundBtn20px, 20.w, 20.w)),
          ),
          GestureDetector(
            onTap: () {
              controller.selectedRedOrBlueFaction(
                  seatIndex: widget.index, faction: 2);
              ctrl.close();
            },
            child: ImageUtils.getImage(
                Assets.imagesChatRoomSeatUserFactionBlue, 50.w, 56.h),
          ),
        ],
      );
    }
    bool isRoomOwner = controller.checkIsRoomOwner();
    return SizedBox(
      width: 50.w * 3 + 5.w * 2 + (isRoomOwner ? 25.w * 2 : 0),
      height: 56.h + 28.h + 34.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(left: isRoomOwner ? 25.w : 0),
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 26.h),
                  child: GestureDetector(
                    onTap: () {
                      controller.sendLikeToSeatIndex(widget.index);
                      ctrl.close();
                    },
                    child: ImageUtils.getImage(
                        Assets.imagesChatRoomSeatPopoverActionLike, 50.w, 56.h),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 5.w),
                  child: GestureDetector(
                    onTap: () {
                      controller.sendDaBangToSeatIndex(
                          widget.index, widget.seatInfo.user!.userId!);
                      ctrl.close();
                    },
                    child: ImageUtils.getImage(
                        Assets.imagesChatRoomSeatPopoverActionSend, 50.w, 56.h,
                        fit: BoxFit.cover),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 26.h, left: 5.w),
                  child: GestureDetector(
                    onTap: () {
                      if (isRoomOwner) {
                        ToastUtils.showDialog(
                            content: "确定要将他踢出房间吗？",
                            onConfirm: () {
                              controller.kickUserFromSeat(
                                  userId: widget.seatInfo.user!.userId!,
                                  seatIndex: widget.index);
                            });
                      } else {
                        controller.reportUser(
                            userId: widget.seatInfo.user!.userId!);
                      }
                      ctrl.close();
                    },
                    child: ImageUtils.getImage(
                        isRoomOwner
                            ? Assets.imagesChatRoomSeatPopoverActionKickout
                            : Assets.imagesChatRoomSeatPopoverActionReport,
                        50.w,
                        56.h),
                  ),
                ),
                Visibility(
                  visible: controller.checkIsRoomOwner(),
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: 5.w,
                      top: 8.h,
                    ),
                    child: GestureDetector(
                      onTap: () {
                        controller.reportUser(
                            userId: widget.seatInfo.user!.userId!);
                      },
                      child: ImageUtils.getImage(
                          Assets.imagesChatRoomSeatPopoverActionReportSmall,
                          17.w,
                          16.h),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Visibility(
            visible: controller.checkIsOnSeat(),
            child: CommonGradientBtn(
              width: 85.w,
              height: 34.h,
              title: "交换位置",
              textStyle: TextStyles.normal(14.sp),
              normalImage: Assets.imagesCommonGradientBtnBgShort,
              onTap: () async {
                bool? success = await controller
                    .sendChangeSeatApply(widget.seatInfo.user!.userId!);
                ctrl.close();
                if (success == true) {
                  ToastUtils.showToast("交换位置请求已发送");
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  ///当前人物形象标识
  Widget _buildMineTagIconWidget() {
    return Positioned(
      top: -15.h,
      child: Visibility(
        visible: widget.seatInfo.user?.userId == UserService().user?.id,
        child: ImageUtils.getImage(
          Assets.imagesChatRoomMineTagArrow,
          14.w,
          15.h,
        ),
      ),
    );
  }

  ///获取角色形象素材（性别_衣服主题_衣服颜色）
  void _setRoleAssetImage() async {
    String roleAssetsName = "";
    if (widget.seatInfo.user?.imageIndex != null) {
      roleAssetsName =
          "assets/svga/chat_room_user_role_image_${widget.seatInfo.user!.imageIndex!}.svga";
    }
    if (roleAssetsName.isEmpty == true) {
      svgaAnimationController.reset();
      svgaAnimationController.clear();
      svgaAnimationController.videoItem = null;
      return;
    }
    final videoItem = await SVGAParser.shared.decodeFromAssets(roleAssetsName);
    svgaAnimationController.videoItem = videoItem;
    var random = math.Random();
    int delay = random.nextInt(3);
    Future.delayed(Duration(milliseconds: delay * 100), () {
      if (mounted) {
        svgaAnimationController.reset();
        svgaAnimationController.repeat();
      }
    });
  }

  ///消息气泡内消息
  InlineSpan? _buildRoleMessage(V2TimMessage? message) {
    if (message != null &&
        message.elemType == MessageElemType.V2TIM_ELEM_TYPE_TEXT &&
        message.textElem != null) {
      String imageAssetsName = Assets.imagesChatRoomSeatMsgBubbleOtherIndexBg;
      double imageWidth = 26.w;
      Color textColor = Colors.white;
      if (message.sender == UserService().user?.id) {
        imageAssetsName = Assets.imagesChatRoomSeatMsgBubbleMineIndexBg;
        imageWidth = 28.w;
        textColor = AppColors.colorFF2D6D0B;
      }
      String messageText = message.textElem!.text ?? "";
      if (messageText.length > 100) {
        messageText = "${messageText.substring(0, 100)}...";
      }
      return TextSpan(
        children: [
          WidgetSpan(
            alignment: ui.PlaceholderAlignment.middle,
            child: Container(
              width: imageWidth,
              height: imageWidth,
              margin: EdgeInsets.only(right: 3.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(imageAssetsName),
                ),
              ),
              child: Text(
                "${widget.index}",
                style: TextStyles.common(14.sp, textColor),
              ),
            ),
          ),
          TextSpan(
            text: "：$messageText",
            style: TextStyles.common(16.sp, Colors.white),
          ),
        ],
      );
    }
    return const TextSpan(children: []);
  }

  ///展示消息气泡
  void _showMessageBubble(V2TimMessage message) {
    _hideMessageBubble();
    int delayDuration = currentShowMsg == null ? 300 : 0;
    Future.delayed(Duration(milliseconds: delayDuration), () {
      toolTipKey.currentState?.ensureTooltipVisible();
      Future.delayed(const Duration(seconds: 3), () {
        if (message.msgID == currentShowMsg?.msgID) {
          _hideMessageBubble();
        }
      });
    });
  }

  ///隐藏当前气泡
  void _hideMessageBubble() {
    toolTipKey.currentState?.dismissTooltip();
  }

  ///流水席倒计时
  void _startFlowSeatCountDownTimer() {
    if (widget.seatInfo.user == null) {
      if (_timer != null) {
        _timer?.cancel();
        _timer = null;
      }
      return;
    }

    if (widget.seatInfo.takeSeatTime != null &&
        widget.seatInfo.type == 2 &&
        _timer == null) {
      DateTime beginTime = DateTime.parse(widget.seatInfo.takeSeatTime!);
      DateTime endTime = beginTime
          .add(const Duration(seconds: ChatRoomController.flowSeatCountDown));
      DateTime nowTime = DateTime.now();
      if (nowTime.isBefore(endTime)) {
        int countDown = endTime.difference(DateTime.now()).inSeconds;
        if (countDown > 0) {
          flowSeatCountDown.value = countDown;
          _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
            flowSeatCountDown.value--;
            if (flowSeatCountDown.value == 0) {
              _timer?.cancel();
              _timer = null;
              controller.leaveSeat(index: widget.index);
            }
          });
        }
      }
    }
  }

  @override
  void dispose() {
    svgaAnimationController.stop();
    super.dispose();
  }
}
