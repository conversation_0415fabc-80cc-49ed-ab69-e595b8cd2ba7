import 'dart:io';

import 'package:dada/common/values/constants.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dada/model/tab_bar_item_entity.dart';
import 'package:dada/utils/image_utils.dart';

class CustomBottomNavigationBar extends StatefulWidget {
  final int currentIndex;
  final Function(int index) onTap;
  final List<TabBarItemEntity> items;

  const CustomBottomNavigationBar(
      {super.key,
      required this.items,
      required this.currentIndex,
      required this.onTap});

  @override
  State<CustomBottomNavigationBar> createState() =>
      _CustomBottomNavigationBarState();
}

class _CustomBottomNavigationBarState extends State<CustomBottomNavigationBar> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: Constants.kBottomBarHeight + ScreenUtil().bottomBarHeight,
          padding: EdgeInsets.only(
            bottom: ScreenUtil().bottomBarHeight,
          ),
          decoration: BoxDecoration(
            color: AppTheme.themeData.bottomNavigationBarTheme.backgroundColor,
            boxShadow: [
              BoxShadow(
                color: AppTheme.themeData.colorScheme.shadow,
                offset: const Offset(0, -0.5),
                blurRadius: 0,
              ),
            ],
          ),
        ),
        Positioned(
          left: 5.w,
          right: 5.w,
          bottom: ScreenUtil().bottomBarHeight,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: List.generate(widget.items.length, (index) {
              TabBarItemEntity entity = widget.items[index];
              return CustomBottomNavigationBarItem(
                title: entity.title!,
                icon: entity.icon!,
                selectedIcon: entity.selectedIcon!,
                selected: widget.currentIndex == index,
                size: Size(entity.width!, entity.width!),
                selectedSize: entity.selectedWidth != null
                    ? Size(entity.selectedWidth!, entity.selectedWidth!)
                    : null,
                badgeCount: entity.badgeCount,
                onTap: () {
                  widget.onTap(index);
                },
              );
            }),
          ),
        ),
      ],
    );
  }
}

class CustomBottomNavigationBarItem extends StatelessWidget {
  final String title;
  final String icon;
  final String selectedIcon;
  final bool selected;
  final Size size;
  final Function() onTap;
  final Function()? onDoubleTap;
  final String? badgeCount;
  final Size? selectedSize;

  const CustomBottomNavigationBarItem({
    super.key,
    required this.title,
    required this.icon,
    required this.selectedIcon,
    required this.selected,
    required this.size,
    required this.onTap,
    this.onDoubleTap,
    this.badgeCount,
    this.selectedSize,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: (selectedSize != null && selected
                  ? selectedSize!.height
                  : size.height) +
              26.h,
          alignment: Alignment.center,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: onTap,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildItemIcon(context, icon, selectedIcon, size, selected,
                    badgeCount, selectedSize),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: selected
                        ? AppTheme.themeData.bottomNavigationBarTheme
                            .selectedLabelStyle?.color
                        : AppTheme.themeData.bottomNavigationBarTheme
                            .unselectedLabelStyle?.color,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildItemIcon(BuildContext context, String icon, String selectedIcon,
      Size size, bool selected, String? badgeCount, Size? selectedSize) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Visibility(
          child: BadgeWidget(
            visible: badgeCount != null,
            text: badgeCount?.isEmpty == true ? null : badgeCount,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: SizedBox(
                width:
                    selected ? (selectedSize?.width ?? size.width) : size.width,
                height: selected
                    ? (selectedSize?.height ?? size.height)
                    : size.height,
                child: ImageUtils.getImage(
                    selected ? selectedIcon : icon,
                    selected ? (selectedSize?.width ?? size.width) : size.width,
                    selected
                        ? (selectedSize?.height ?? size.height)
                        : size.height),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
