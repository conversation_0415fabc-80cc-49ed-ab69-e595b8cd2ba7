import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class NewAccountWelfareDialog extends StatelessWidget {
  const NewAccountWelfareDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          margin: EdgeInsets.only(top: 30.h),
          width: 270.w,
          height: 213.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            gradient: const LinearGradient(
              colors: [
                AppColors.colorFFD2F6C0,
                Colors.white,
              ],
              stops: [
                0.0,
                0.25,
              ],
            ),
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 74.h, left: 36.w, right: 30.w),
                child: Text(
                  "恭喜您获得新用户福利，15天月卡权益，搭搭棒+88",
                  style: TextStyles.normal(16.sp, h: 1.5),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: 33.h, left: 36.w, right: 30.w),
                  width: 90.w,
                  height: 35.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(17.5.r),
                    gradient: const LinearGradient(
                      colors: [
                        AppColors.colorFFA0F6A5,
                        AppColors.colorFF58C75D,
                      ],
                      stops: [
                        0.0,
                        1,
                      ],
                    ),
                  ),
                  child: Text(
                    "我知道了",
                    style: TextStyles.common(16.sp, AppColors.colorFF344F3D),
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: 0.h,
          child: ImageUtils.getImage(
              Assets.imagesNewAccountWelfareDialogTopImage, 163.w, 103.h),
        ),
      ],
    );
  }
}
