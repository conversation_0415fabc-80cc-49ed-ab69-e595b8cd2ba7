import 'dart:io';

import 'package:dada/global.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/login_utils.dart';
import 'package:get/get.dart';

class SplashController extends GetxController {

  void checkIsLogined() {
    if (LoginUtils.isLogined()) {
      Global.initServiceAfterLogin();
      if (Platform.isAndroid) {
        Future.delayed(const Duration(milliseconds: 200), (){
          Get.offAllNamed(GetRouter.main);
        });
      } else {
        Get.offAllNamed(GetRouter.main);
      }
    } else {
      Get.offAllNamed(GetRouter.welcome);
    }
  }
}
