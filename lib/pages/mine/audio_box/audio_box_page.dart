import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/audio_box_list_item_entity.dart';
import 'package:dada/pages/mine/audio_box/audio_box_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/parser.dart';
import 'package:svgaplayer_flutter/player.dart';

class AudioBoxPage extends StatefulWidget {
  const AudioBoxPage({super.key});

  @override
  State<AudioBoxPage> createState() => _AudioBoxPageState();
}

class _AudioBoxPageState extends State<AudioBoxPage>
    with TickerProviderStateMixin {
  final controller = Get.put(AudioBoxController());
  final TextEditingController _textEditingController = TextEditingController();

  bool isSelect = false;

  @override
  void initState() {
    super.initState();

    isSelect = Get.parameters["isSelect"] == "1";

    if (Get.arguments != null && Get.arguments.containsKey("userID")) {
      String? userID = Get.arguments["userID"];
      controller.toUserID = userID;
    }

    if (Get.arguments != null && Get.arguments.containsKey("groupID")) {
      String? groupID = Get.arguments["groupID"];
      controller.toGroupID = groupID;
    }

    if (Get.arguments != null && Get.arguments.containsKey("chatDetailController")) {
      controller.chatDetailController = Get.arguments["chatDetailController"];
    }

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: S.current.audioBox,
      ),
      body: Container(
        color: AppTheme.themeData.colorScheme.surfaceTint,
        child: Column(
          children: [
            Container(
              color: AppTheme.themeData.colorScheme.primary,
              height: 57.h,
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 11.h),
              child: _buildSearchbar(),
            ),
            Expanded(child: _buildListView()),
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchbar() {
    return Container(
      padding: EdgeInsets.only(
        left: 17.w,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        color: AppTheme.themeData.colorScheme.surfaceTint,
      ),
      child: Row(
        children: [
          ImageUtils.getImage(Assets.imagesHomeSearchIcon, 14.w, 14.w),
          SizedBox(
            width: 7.w,
          ),
          Expanded(
            child: CustomTextField.build(
              controller: _textEditingController,
              hintText: S.current.audioBoxSearchPlaceholder,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.text,
              style: TextStyles.normal(14.sp),
              onChanged: (value) {
                controller.inputSearchText.value = _textEditingController.text;
                controller.searchResult();
              },
              suffixIconOnTap: () {
                controller.inputSearchText.value = _textEditingController.text;
                controller.searchResult();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return RefreshWidget.build(
      refreshController: controller.refreshController,
      onRefresh: () => controller.refreshData(),
      onLoadMore: () => controller.loadMoreData(),
      child: GetBuilder<AudioBoxController>(
        init: controller,
        builder: (controller) {
          if (controller.data.isEmpty && controller.searchedResult.isEmpty) {
            return EmptyWidget();
          }
          return ListView.separated(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
            itemBuilder: _listItemBuilder,
            separatorBuilder: (context, index) => Container(
              height: 10.h,
            ),
            itemCount: controller.searchedResult.isNotEmpty
                ? controller.searchedResult.length
                : controller.data.length,
          );
        },
        id: controller.refreshId,
      ),
    );
  }

  Widget _listItemBuilder(BuildContext context, int index) {
    SVGAAnimationController svgaAnimationController;
    if (controller.svgaControllerList.length <= index) {
      svgaAnimationController = SVGAAnimationController(vsync: this);
      setVideoItem(svgaAnimationController);
      controller.svgaControllerList.add(svgaAnimationController);
    } else {
      svgaAnimationController = controller.svgaControllerList[index];
    }

    AudioBoxListItemEntity itemEntity;
    if (controller.searchedResult.isNotEmpty) {
      itemEntity = controller.searchedResult[index];
    } else {
      itemEntity = controller.data[index];
    }

    return Container(
      height: 130.h,
      padding: EdgeInsets.only(
          left: isSelect ? 0 : 15.w, right: 15.w, top: 10.h, bottom: 10.h),
      decoration: BoxDecoration(
        color: AppTheme.themeData.colorScheme.primary,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: InkWell(
        onTap: () {
          if (isSelect) {
            bool selected = controller.selectedAudioItems.contains(itemEntity);
            if (selected) {
              controller.selectedAudioItems.remove(itemEntity);
            } else {
              controller.selectedAudioItems.add(itemEntity);
            }
          }
        },
        child: Row(
          children: [
            Visibility(
              visible: isSelect,
              child: Obx(
                () {
                  bool selected =
                      controller.selectedAudioItems.contains(itemEntity);
                  return Padding(
                    padding: EdgeInsets.only(left: 10.w, right: 10.w),
                    child: ImageUtils.getImage(
                        selected
                            ? Assets.imagesAudioBoxListItemSelected
                            : Assets.imagesAudioBoxListUnselected,
                        20.w,
                        20.w),
                  );
                },
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        itemEntity.title ?? "",
                        style: TextStyles.normal(16.sp),
                      ),
                      GestureDetector(
                        onTap: () {
                          controller.deleteAudioItem(itemEntity.id!);
                        },
                        child: ImageUtils.getImage(
                            Assets.imagesSmallRoomMailMessageDelete,
                            16.w,
                            15.h),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  GestureDetector(
                    onTap: () {
                      controller.stopCurrentAndPlayNewAudio(index: index);
                      controller.updateViews();
                    },
                    child: Container(
                      height: 40.h,
                      width: controller.getAudioContainerWidth(itemEntity),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        color: const Color(0xffACE49E),
                      ),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 12.w,
                          ),
                          SizedBox(
                            width: 12.w,
                            height: 16.h,
                            child: Stack(
                              children: [
                                Visibility(
                                  visible: !svgaAnimationController.isAnimating,
                                  child: ImageUtils.getImage(
                                      Assets.imagesAudioBoxVolume, 12.w, 16.h),
                                ),
                                Visibility(
                                  visible: svgaAnimationController.isAnimating,
                                  child: SVGAImage(svgaAnimationController),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            "${itemEntity.duration}\"",
                            style: TextStyles.normal(16.sp),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        itemEntity.username ?? "",
                        style: TextStyles.normal(12.sp,
                            c: AppTheme.themeData.textTheme.labelSmall?.color),
                      ),
                      Text(
                        itemEntity.createDate ?? "",
                        style: TextStyles.normal(12.sp,
                            c: AppTheme.themeData.textTheme.labelSmall?.color),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    if (!isSelect) {
      return Container();
    }
    return Container(
      padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
      color: Colors.white,
      child: SizedBox(
        height: 40.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 25.w),
              child: Obx(
                () => Text(
                  "已选中：${controller.selectedAudioItems.length}个",
                  style: TextStyles.normal(16.sp),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 15.w),
              child: GestureDetector(
                onTap: () async {
                  bool success = await controller.sendSelectedAudio();
                  if (success) {
                    Get.back(result: 1);
                  }
                },
                child: Container(
                  width: 56.w,
                  height: 30.h,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: 5.h),
                  decoration: BoxDecoration(
                    color: AppColors.colorFF89E15C,
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  child: Text(
                    "发送",
                    style: TextStyles.normal(14.sp),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> setVideoItem(SVGAAnimationController controller) async {
    final videoItem =
        await SVGAParser.shared.decodeFromAssets(Assets.svgaAudioPlaying);
    controller.videoItem = videoItem;
    controller.reset();
  }

  @override
  void dispose() {
    controller.stopCurrentAndPlayNewAudio();
    super.dispose();
  }
}
