import 'dart:async';
import 'dart:math';
import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/audio_box_list_item_entity.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/pages/match/assemble/team/match_team_chat_controller.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';

class AudioBoxController
    extends ListPageController<AudioBoxListItemEntity, AudioBoxController> {
  final RxString inputSearchText = "".obs;
  List<AudioBoxListItemEntity> searchedResult = <AudioBoxListItemEntity>[];
  final AudioPlayerUtils audioPlayer = AudioPlayerUtils();
  final List<SVGAAnimationController> svgaControllerList = [];
  final RxList<AudioBoxListItemEntity> selectedAudioItems =
      <AudioBoxListItemEntity>[].obs;

  ChatDetailController? chatDetailController;

  String? toUserID;
  String? toGroupID;

  @override
  void onInit() {
    super.onInit();

    audioPlayer.onInit();
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.complete) {
        stopOtherAnimation();
        updateViews();
      }
    });
  }

  @override
  Future<List<AudioBoxListItemEntity>?> loadData(int page) async {
    List<AudioBoxListItemEntity>? result =
        await ApiService().getAudioBoxList(page: page);
    return result;
  }

  double getAudioContainerWidth(AudioBoxListItemEntity itemEntity) {
    double maxWidth = 160.w;
    double minWidth = 60.w;
    double width = maxWidth;
    if (itemEntity.duration != null) {
      if (itemEntity.duration! <= 10 && itemEntity.duration! > 1) {
        width = maxWidth + 100.w * (itemEntity.duration! / 10);
      } else if (itemEntity.duration! > 10 && itemEntity.duration! <= 20) {
        width = 100.w + 30.w * (itemEntity.duration! - 10) / 10;
      } else if (itemEntity.duration! > 20 && itemEntity.duration! <= 30) {
        width = 130.w + 30.w * (itemEntity.duration! - 20) / 10;
      } else {
        width = maxWidth;
      }
    }
    return min(maxWidth, max(width, minWidth));
  }

  void searchResult() {
    searchedResult.clear();
    for (int i = 0; i < data.length; i++) {
      AudioBoxListItemEntity itemEntity = data[i];
      if (itemEntity.title?.contains(inputSearchText.value) == true) {
        searchedResult.add(itemEntity);
      }
    }
    svgaControllerList.clear();
    update([refreshId]);
  }

  void stopCurrentAndPlayNewAudio({int? index}) {
    if (audioPlayer.state == AudioPlayerState.playing) {
      audioPlayer.stop();
    }

    if (index != null) {
      ///跟当前播放的是同一条语音
      SVGAAnimationController currentSvgaController = svgaControllerList[index];
      if (currentSvgaController.isAnimating) {
        currentSvgaController.stop();
        currentSvgaController.reset();
        return;
      }
      AudioBoxListItemEntity itemEntity = data[index];
      if (searchedResult.isNotEmpty) {
        itemEntity = searchedResult[index];
      }
      audioPlayer.setUrl(itemEntity.url!);
      audioPlayer.play();
    }

    playNewAndStopOtherAnimation(index: index);
  }

  void playNewAndStopOtherAnimation({int? index}) {
    if (index != null) {
      SVGAAnimationController currentSvgaController = svgaControllerList[index];
      currentSvgaController.repeat();
    }

    stopOtherAnimation(exclude: index);
  }

  void stopOtherAnimation({int? exclude}) {
    for (int i = 0; i < svgaControllerList.length; i++) {
      final svgaController = svgaControllerList[i];
      if (svgaController.isAnimating &&
          ((i != exclude && exclude != null) || exclude == null)) {
        svgaController.stop();
        svgaController.reset();
      }
    }
  }

  Future<bool> sendSelectedAudio() async {
    int times = 0;
    for (int i = 0; i < selectedAudioItems.length; i++) {
      AudioBoxListItemEntity audioItem = selectedAudioItems[i];
      bool success = false;
      if (chatDetailController != null) {
        success = await chatDetailController!.sendCustomMsg(
            audioItem.toString(), ChatImCustomMsgType.UserAudioBoxAudioMsg);
      } else if (Get.isRegistered<MatchTeamChatController>()) {
        MatchTeamChatController matchTeamChatController =
            Get.find<MatchTeamChatController>();
        success = await Get.find<MatchTeamChatController>().sendCustomMsg(
            audioItem.toString(), ChatImCustomMsgType.UserAudioBoxAudioMsg,
            groupID: matchTeamChatController.teamEntity.value?.teamNo);
      }
      if (success) {
        times++;
      }
    }
    if (times == selectedAudioItems.length) {
      return true;
    }
    ToastUtils.showToast("发送失败，请重新尝试！");
    return false;
  }

  void updateViews() {
    update([refreshId]);
  }

  void deleteAudioItem(String audioId) async {
    bool success = await ApiService().deleteAudioBox(audioId: audioId);
    if (success) {
      refreshData();
    }
  }

  @override
  void dispose() {
    audioPlayer.stop();
    audioPlayer.cancelPlayerSubscriptions();
    stopOtherAnimation();
    super.dispose();
  }
}
