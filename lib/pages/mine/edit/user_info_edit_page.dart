import 'dart:math';

import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/common_setting_list_item_widget.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/mine/edit/audio_edit_dialog.dart';
import 'package:dada/components/widgets/input_text_page.dart';
import 'package:dada/pages/mine/edit/user_info_edit_controller.dart';
import 'package:dada/pages/mine/mine_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/log.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class UserInfoEditPage extends StatefulWidget {
  const UserInfoEditPage({super.key});

  @override
  State<UserInfoEditPage> createState() => _UserInfoEditPageState();
}

class _UserInfoEditPageState extends State<UserInfoEditPage>
    with SingleTickerProviderStateMixin {
  final controller = Get.put(UserInfoEditController());

  AnimationController? _animationController;
  Animation<double>? _progressAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.colorScheme.inverseSurface,
      appBar: CustomAppBar(
        title: S.current.editUserInfo,
        backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                ///修改头像
                _buildAvatarLine(),
                SizedBox(height: 5.h),

                ///语音签名
                _buildAudioSignature(),
                SizedBox(height: 5.h),

                ///文字签名
                _buildTextSignature(),
                SizedBox(height: 5.h),

                ///个人信息
                _buildOtherInfo(),

                SizedBox(
                  height: 25.h,
                ),

                // ///保存按钮
                // _buildSaveBtn(),
              ],
            ),
          ),
          _buildSaveBtn(),
        ],
      ),
    );
  }

  Widget _buildAvatarLine() {
    return InkWell(
      onTap: () {
        ToastUtils.showBottomSheet(
          [S.current.album, S.current.camera, '系统头像(全部)'],
          onTap: (index) async {
            if (index == 0 || index == 1) {
              bool res = await ImagePickerUtil.checkPermission(
                  index == 0 ? 1 : 2,
                  index == 0 ? "相册权限说明" : "相机权限说明",
                  index == 0 ? "获取图片用于更换头像" : "拍摄图片用于更换头像, 拍摄后的图片将存放在系统照片中");

              if (!res) return;
              /* XFile? imageFile = await ImagePicker().pickImage(
                  source:
                      index == 0 ? ImageSource.gallery : ImageSource.camera); */
              AssetEntity? asset;
              if (index == 0) {
                List<AssetEntity>? assets = await ImagePickerUtil.selectAsset(
                    maxAssets: 1, isImage: true);
                if (assets != null) {
                  asset = assets.first;
                }
              } else {
                asset = await ImagePickerUtil.takeAsset();
              }
              String? imagePath = await ImagePickerUtil.getEntityPath(asset);
              if (imagePath != null && imagePath.isNotEmpty) {
                String? croppedPath = await ImageUtils.cropImage(imagePath);
                if (croppedPath != null) {
                  controller.selectedImagePath.value = croppedPath;
                  controller.hasChanges.value = true;
                }
              }
            } else {
              Get.toNamed(GetRouter.selectAvatar)?.then((value) {
                if (value != null) {
                  controller.selectedImagePath.value = value;
                  controller.hasChanges.value = true;
                }
              });
            }
          },
        );
      },
      child: Container(
        height: 70.h,
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        color: AppTheme.themeData.colorScheme.surface,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.current.avatar,
              style: TextStyles.normal(16.sp,
                  c: AppTheme.themeData.textTheme.bodyMedium?.color),
            ),
            Row(
              children: [
                Obx(
                  () => ImageUtils.getImage(
                    controller.selectedImagePath.value.isNotEmpty
                        ? controller.selectedImagePath.value
                        : controller.userInfoEntity.avatar ?? "",
                    40.w,
                    40.w,
                    radius: 20.w,
                    fit: BoxFit.cover,
                    placeholder: Assets.imagesAvatarPlaceholder,
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                ImageUtils.getImage(Assets.imagesCommonListMore, 8.w, 14.h),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioSignature() {
    return InkWell(
      onTap: () {
        bool hasAudio = controller.inputAudioSignaturePath.value.isNotEmpty ||
            (controller.userInfoEntity.voiceSignature != null &&
                controller.userInfoEntity.voiceSignature != "");
        if (hasAudio) {
          ToastUtils.showDialog(
            dialog: AudioEditDialog(
              onRepeat: () {
                pushAudioRecordPage();
              },
              onDelete: () {
                controller.deleteAudio();
              },
            ),
          );
        } else {
          pushAudioRecordPage();
        }
      },
      child: Container(
        height: 105.h,
        color: AppTheme.themeData.colorScheme.surface,
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 15.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(S.current.audioSignature, style: TextStyles.medium(16.sp)),
                ImageUtils.getImage(Assets.imagesCommonListMore, 8.w, 14.h),
              ],
            ),
            SizedBox(
              height: 20.h,
            ),
            _buildAudioPlayWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioPlayWidget() {
    return Obx(
      () {
        bool hasAudio = (controller.inputAudioSignaturePath.value.isNotEmpty &&
                controller.inputAudioSignatureDuration.value > 0) ||
            (controller.userInfoEntity.voiceSignature != null &&
                controller.userInfoEntity.voiceLength != null &&
                controller.userInfoEntity.voiceLength! > 0);
        String btnImage = Assets.imagesUserInfoEditAudioPlayDisable;
        double progressWidth = 250.w;
        int audioDuration = 0;
        if (hasAudio) {
          btnImage = controller.audioPlaying.value
              ? Assets.imagesUserInfoEditAudioPause
              : Assets.imagesUserInfoEditAudioPlay;
          progressWidth = controller.audioPlaying.value
              ? (250.w * controller.audioProgress.value)
              : 250.w;
          audioDuration = controller.inputAudioSignatureDuration.value != 0
              ? controller.inputAudioSignatureDuration.value
              : controller.duration.value;
        } else {
          btnImage = Assets.imagesUserInfoEditAudioPlayDisable;
        }

        _progressAnimation =
            Tween<double>(begin: 0.0, end: controller.audioProgress.value)
                .animate(
          CurvedAnimation(
            parent: _animationController!,
            curve: Curves.linear,
          ),
        );

        return Row(
          children: [
            GestureDetector(
              onTap: () {
                if (!hasAudio) {
                  return;
                }
                controller.playAudio();
                _animationController?.forward();
              },
              child: ImageUtils.getImage(btnImage, 30.w, 30.w),
            ),
            SizedBox(
              width: 15.w,
            ),
            Stack(
              children: [
                Container(
                  width: 250.w,
                  height: 3.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFFBCBCBC),
                    borderRadius: BorderRadius.circular(1.5.h),
                  ),
                ),
                Visibility(
                  visible: hasAudio,
                  child: AnimatedBuilder(
                    animation: _progressAnimation!,
                    builder: (context, child) {
                      return Container(
                        width: controller.audioPlaying.value == true
                            ? _progressAnimation!.value * progressWidth
                            : progressWidth,
                        height: 3.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFF65D06A),
                          borderRadius: BorderRadius.circular(1.5.h),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            SizedBox(
              width: 15.w,
            ),
            Text(
              "${audioDuration}s",
              style: TextStyles.normal(16.sp,
                  c: hasAudio
                      ? const Color(0xFF65D06A)
                      : AppTheme.themeData.textTheme.bodyMedium?.color),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTextSignature() {
    return Obx(() {
      String? textSignature = controller.inputTextSignature.value.isNotEmpty
          ? controller.inputTextSignature.value
          : controller.userInfoEntity.txtSignature;
      return InkWell(
        onTap: () {
          pushInputTextPage(
            "介绍自己",
            textSignature ?? "",
            (changedText) {
              controller.inputTextSignature.value = changedText;
              controller.hasChanges.value = true;
            },
            placeholder: S.current.signaturePlaceholder,
            isMultiLine: true,
            maxLength: 120,
          );
        },
        child: Container(
          height: 105.h,
          color: AppTheme.themeData.colorScheme.surface,
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 15.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text("介绍自己", style: TextStyles.medium(16.sp)),
                  ImageUtils.getImage(Assets.imagesCommonListMore, 8.w, 14.h),
                ],
              ),
              SizedBox(
                height: 20.h,
              ),
              Text(
                textSignature ?? S.current.unSetting,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: textSignature != null
                    ? TextStyles.normal(16.sp)
                    : TextStyles.normal(16.sp,
                        c: AppTheme.themeData.textTheme.bodyMedium?.color),
              )
            ],
          ),
        ),
      );
    });
  }

  Widget _buildOtherInfo() {
    return Container(
      color: AppTheme.themeData.colorScheme.surface,
      padding: EdgeInsets.only(
        top: 15.w,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w),
            child: Text(
              S.current.userInfo,
              style: TextStyles.medium(16.sp),
            ),
          ),
          SizedBox(
            height: 15.h,
          ),
          Column(
            children: [
              ///昵称
              _buildNickName(),

              ///生日
              _buildBirthday(),

              ///年龄
              _buildAge(),

              ///星座
              _buildConstellation(),

              ///家乡
              _buildHometown(),

              ///性别
              _buildSex(),

              ///职业
              _buildVocation(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNickName() {
    return Obx(
      () {
        String? subTitle = controller.inputNickname.value.isNotEmpty
            ? controller.inputNickname.value
            : controller.userInfoEntity.nickname;
        return CommonSettingListItemWidget(
          title: S.current.nickname,
          subTitle: subTitle,
          onTap: () {
            pushInputTextPage(S.current.nickname, subTitle ?? "",
                (changedText) {
              controller.inputNickname.value = changedText;
              controller.hasChanges.value = true;
            }, placeholder: S.current.nicknamePlaceholder);
          },
        );
      },
    );
  }

  Widget _buildBirthday() {
    return Obx(
      () {
        String? subTitle = controller.selectedBirthday.value.year != 1970
            ? TimeUtils.dateFormatString(
                dateTime: controller.selectedBirthday.value,
                formatList: ["yyyy", "-", "mm", "-", "dd"],
              )
            : controller.userInfoEntity.birthday;
        return CommonSettingListItemWidget(
          title: S.current.birthday,
          subTitle: subTitle,
          onTap: () async {
            DateTime initialDate =
                controller.selectedBirthday.value.year != 1970
                    ? controller.selectedBirthday.value
                    : DateTime.now();
            DateTime? result = await showDatePicker(
                context: context,
                initialDate: initialDate,
                firstDate: DateTime(1950),
                lastDate: DateTime.now(),
                cancelText: S.current.cancel,
                confirmText: S.current.sure,
                locale: Get.deviceLocale);
            if (result != null) {
              controller.selectedBirthday.value = result;
              controller.hasChanges.value = true;
            }
            Log.v(result);
          },
        );
      },
    );
  }

  Widget _buildAge() {
    return Obx(
      () {
        // 确保 age 在 minValue 和 maxValue 之间
        int age = (controller.inputAge.value > 0
                ? controller.inputAge.value
                : controller.userInfoEntity.age ?? 18)
            .clamp(14, 100);
        return CommonSettingListItemWidget(
          title: "年龄",
          subTitle: null,
          rightWidget: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 60.w, // 调整宽度以适应垂直选择框
                height: 50.h, // 调整高度以适应垂直选择框
                child: DropdownButton<int>(
                  value: age,
                  underline: Container(height: 0),
                  items: List.generate(87, (index) {
                    int ageValue = index + 14;
                    return DropdownMenuItem<int>(
                      value: ageValue,
                      child: Text(ageValue.toString()),
                    );
                  }),
                  onChanged: (value) {
                    if (value != null) {
                      controller.inputAge.value = value;
                      controller.hasChanges.value = true;
                    }
                  },
                  style: TextStyles.normal(16.sp),
                  // 调整文本大小以适应选择框
                  dropdownColor: Colors.white,
                  icon: const Icon(Icons.arrow_drop_down),
                  iconSize: 24,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConstellation() {
    return Obx(
      () {
        String? subTitle = controller.inputConstellation.value.isNotEmpty
            ? controller.inputConstellation.value
            : controller.userInfoEntity.constellation;
        return CommonSettingListItemWidget(
          title: "星座",
          subTitle: subTitle,
          hideMore: false,
          rightWidget: _buildConstellationDetail(),
        );
      },
    );
  }

  Widget _buildConstellationDetail() {
    return Obx(
      () {
        String? subTitle = controller.inputConstellation.value.isNotEmpty
            ? controller.inputConstellation.value
            : controller.userInfoEntity.constellation;
        return GestureDetector(
          onTap: () {
            Get.bottomSheet(
              Container(
                height: 300.h,
                decoration: BoxDecoration(
                  color: AppTheme.themeData.scaffoldBackgroundColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                child: Column(
                  children: [
                    SizedBox(height: 10.h),
                    Text(
                      "请选择星座",
                      style: TextStyles.bold(16.sp),
                    ),
                    SizedBox(height: 10.h),
                    Expanded(
                      child: ListView.builder(
                        itemCount: controller.constellation.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            title: Text(
                              controller.constellation[index],
                              style: TextStyles.normal(16.sp),
                            ),
                            onTap: () {
                              controller.inputConstellation.value =
                                  controller.constellation[index];
                              controller.hasChanges.value = true;
                              Get.back();
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
          child: Container(
            height: 45.h,
            padding: EdgeInsets.only(left: 21.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  subTitle ?? "请选择星座",
                  style: TextStyles.common(
                      16.sp, AppTheme.themeData.textTheme.bodyMedium?.color),
                ),
                SizedBox(width: 10.w),
                ImageUtils.getImage(Assets.imagesCommonListMore, 8.w, 14.h),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHometown() {
    return Obx(
      () {
        String? subTitle = controller.inputHometown.value.isNotEmpty
            ? controller.inputHometown.value
            : controller.userInfoEntity.hometown;
        return CommonSettingListItemWidget(
          title: "地区",
          subTitle: subTitle,
          onTap: () {
            pushInputTextPage("地区", subTitle ?? "", (changedText) {
              controller.inputHometown.value = changedText;
              controller.hasChanges.value = true;
            }, placeholder: S.current.input);
          },
        );
      },
    );
  }

  Widget _buildSex() {
    return Obx(
      () {
        int sex = controller.selectedSexIndex.value != -1
            ? controller.selectedSexIndex.value
            : controller.userInfoEntity.sex ?? 0;
        bool isFirstChange = UserService().user?.genderChangeCount == 1;
        String alertContent = "修改性别需要消耗一次性别转换卡，确定要修改吗？";
        if (isFirstChange) {
          alertContent = "本次修改免费，后期修改，需要性别转换卡才可再次修改哦！";
        }
        return CommonSettingListItemWidget(
          title: S.current.sex,
          subTitle: null,
          rightWidget: Row(
            children: [
              _sexRadioWidget(S.current.man, sex == 0, () {
                if (sex == 0) {
                  return;
                }
                ToastUtils.showDialog(
                  content: alertContent,
                  onConfirm: () async {
                    bool success = await ApiService().editUserInfo(
                        sex: 0,
                        audioSignature: UserService().user?.voiceSignature,
                        voiceLength: UserService().user?.voiceLength);
                    if (success) {
                      controller.selectedSexIndex.value = 0;
                      Get.find<MineController>()
                          .loadUserInfo(showLoading: false);
                      //提示
                      ToastUtils.showToast("修改成功");
                    }
                  },
                );
              }),
              SizedBox(width: 15.w),
              _sexRadioWidget(S.current.woman, sex == 1, () {
                if (sex == 1) {
                  return;
                }
                ToastUtils.showDialog(
                  content: alertContent,
                  onConfirm: () async {
                    bool success = await ApiService().editUserInfo(
                        sex: 1,
                        audioSignature: UserService().user?.voiceSignature,
                        voiceLength: UserService().user?.voiceLength);
                    if (success) {
                      controller.selectedSexIndex.value = 1;
                      Get.find<MineController>()
                          .loadUserInfo(showLoading: false);
                      //提示
                      ToastUtils.showToast("修改成功");
                    }
                  },
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVocation() {
    return Obx(
      () {
        String? subTitle = controller.inputVocation.value.isNotEmpty
            ? controller.inputVocation.value
            : controller.userInfoEntity.work;
        return CommonSettingListItemWidget(
          title: S.current.vacation,
          subTitle: subTitle,
          onTap: () {
            pushInputTextPage(S.current.vacation, subTitle ?? "",
                (changedText) {
              controller.inputVocation.value = changedText;
              controller.hasChanges.value = true;
            }, placeholder: S.current.input);
          },
        );
      },
    );
  }

  Widget _sexRadioWidget(String title, bool selected, Function() onTap) {
    Color? normalColor = AppTheme.themeData.textTheme.labelSmall?.color;
    Color? selectedColor = AppTheme.themeData.colorScheme.onTertiary;
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(
            selected ? Icons.radio_button_checked : Icons.radio_button_off,
            size: 16.w,
            color: selected ? selectedColor : normalColor,
          ),
          SizedBox(
            width: 5.w,
          ),
          Text(
            title,
            style: TextStyles.normal(16.sp,
                c: selected ? selectedColor : normalColor),
          ),
        ],
      ),
    );
  }

  ///保存按钮
  Widget _buildSaveBtn() {
    return Obx(() {
      // 假设有一个变量 hasChanges 来跟踪是否有修改
      bool hasChanges = controller.hasChanges.value; // 这里需要根据你的实际情况来获取是否有修改的状态
      return GestureDetector(
        onTap: hasChanges
            ? () {
                controller.save();
              }
            : null, // 如果没有修改，禁用按钮点击事件
        child: Container(
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              bottom: ScreenUtil().bottomBarHeight > 0 ? 25.h : 10.h,
              top: 10.h),
          padding: EdgeInsets.symmetric(horizontal: 35.w),
          height: 50.h,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: hasChanges
                  ? const AssetImage(Assets.imagesLoginBtnBg)
                  : const AssetImage(Assets.imagesGradientBtnDisabledBg1),
            ),
          ),
          child: Center(
            child: Text(
              S.current.save,
              style: TextStyle(
                color: AppTheme.themeData.colorScheme.onSecondaryContainer,
                fontSize: 16.sp,
              ),
            ),
          ),
        ),
      );
    });
  }

  void pushInputTextPage(
      String title, String text, Function(String changedText) callback,
      {String? placeholder, bool? isMultiLine, int? maxLength}) {
    if (title == "介绍自己") {
      List<String> placeholders = [
        "偷偷告诉你可以这样介绍自己哦~\n\n06年大学生\n爱笑、爱瞎想\n容易感动容易满足\n认定的东西就会守候\n喜欢简简单单的关系\n\n我能成为你单身路上的一个不一样的点吗？",
        "偷偷告诉你可以这样介绍自己哦~\n\n找队友一起开黑咯~\n我真是搞不懂你们\n那么喜欢玩游戏\n打个游戏还废寝忘食\n眼睛不疲劳吗\n都不知道注意身体吗\n\n当然\n如果你愿意带我玩\n这些话当我没说",
        "偷偷告诉你可以这样介绍自己哦~\n\n这里是叶神~\n声音算是少年/少女音吧~\n平时喜欢暖酷风，\n不会粘着人，又希望别人粘着自己。\n喜欢羽毛球和看动漫~\n性别偏内向，遇到喜欢的人又不知道如何开口\n如果你看到这里对我感兴趣的话，\n可以来找我聊天哦。",
      ];
      placeholder = placeholders[Random().nextInt(placeholders.length)];
    }
    Get.to(
      () => InputTextPage(
        title: title,
        text: text,
        placeholder: placeholder,
        callback: callback,
        isMultiLine: isMultiLine,
        maxLength: maxLength,
        height: title == "介绍自己" ? 350.h : null,
        maxLines: title == "介绍自己" ? 20 : null,
      ),
    );
  }

  void pushAudioRecordPage() {
    Get.toNamed(GetRouter.audioRecord)?.then((value) {
      if (value != null) {
        int duration = value["duration"];
        String filepath = value["filePath"];
        controller.updateAudio(filepath, duration);
        controller.hasChanges.value = true;
      }
    });
  }
}
