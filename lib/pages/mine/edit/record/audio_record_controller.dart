import 'package:dada/utils/audio_recorder.dart';
import 'package:get/get.dart';

class AudioRecordController extends GetxController {
  int _currentIndex = 0;
  RxString currentParagraph = "".obs;
  List<String> paragraphs = [];

  RxInt currentRecordSecs = 0.obs;

  @override
  void onInit() {
    super.onInit();
    paragraphs = [
      "听到一个人的声音等于认识了他一半，来说说关于你的生活、情感和喜好吧",
    ];

    currentParagraph.value = paragraphs.first;
  }

  void changeParagraph() {
    if (_currentIndex >= paragraphs.length - 1) {
      _currentIndex = 0;
    } else {
      _currentIndex++;
    }
    currentParagraph.value = paragraphs[_currentIndex];
  }

  @override
  void onClose() {
    AudioRecorder().stopRecording();
    super.onClose();
  }
}
