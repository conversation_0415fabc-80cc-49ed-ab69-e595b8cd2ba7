import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class AudioEditDialog extends StatelessWidget {
  final Function()? onRepeat;
  final Function()? onDelete;
  final Function()? onTry;
  final bool? canTry;

  const AudioEditDialog(
      {super.key,
      this.onRepeat,
      this.onDelete,
      this.canTry = false,
      this.onTry});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 270.w,
      height: 220.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        gradient: const LinearGradient(
            colors: [Color(0xFFD2F6C0), Color(0xFFFFFFFF)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0, 0.32]),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Text(
              S.current.editAudioSignature,
              style: TextStyles.medium(16.sp),
            ),
          ),
          const Spacer(),

          ///重新录制
          GestureDetector(
            onTap: () {
              Get.back();
              onRepeat?.call();
            },
            child: Container(
              width: 140.w,
              height: 35.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(35.h / 2),
                gradient: const LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Color(0xFFA0F6A5),
                      Color(0xFF58C75D),
                    ]),
              ),
              child: Text(
                S.current.repeatRecord,
                style: TextStyles.normal(16.sp,
                    c: AppTheme.themeData.colorScheme.onSecondaryContainer),
              ),
            ),
          ),
          SizedBox(height: 10.h),

          ///删除录音
          GestureDetector(
            onTap: () {
              Get.back();
              onDelete?.call();
            },
            child: Container(
              width: 140.w,
              height: 35.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(35.h / 2),
                border: Border.all(
                    color: AppTheme.themeData.textTheme.labelSmall?.color ??
                        const Color(0xFF999999),
                    width: 1),
              ),
              child: Text(
                S.current.deleteRecord,
                style: TextStyles.normal(16.sp,
                    c: AppTheme.themeData.textTheme.bodyMedium?.color),
              ),
            ),
          ),
          SizedBox(height: 10.h),

          ///取消
          GestureDetector(
            onTap: () {
              if (canTry ?? false) {
                onTry?.call();
              } else {
                Get.back();
              }
            },
            child: Container(
              width: 140.w,
              height: 35.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(35.h / 2),
                border: Border.all(
                    color: AppTheme.themeData.textTheme.labelSmall?.color ??
                        const Color(0xFF999999),
                    width: 1),
              ),
              child: Text(
                canTry ?? false ? "试听" : S.current.cancel,
                style: TextStyles.normal(16.sp,
                    c: AppTheme.themeData.textTheme.bodyMedium?.color),
              ),
            ),
          ),
          SizedBox(
            height: 35.h,
          ),
        ],
      ),
    );
  }
}
