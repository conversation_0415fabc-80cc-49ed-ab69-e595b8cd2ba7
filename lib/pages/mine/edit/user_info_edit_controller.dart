import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';

class UserInfoEditController extends GetxController {
  final RxString selectedImagePath = "".obs;
  final RxString inputNickname = "".obs;
  final RxString inputAudioSignaturePath = "".obs;
  final RxString inputTextSignature = "".obs;
  final RxInt selectedSexIndex = (-1).obs;
  final Rx<DateTime> selectedBirthday = DateTime(1970).obs;
  final RxString inputHometown = "".obs;
  final RxString inputVocation = "".obs;
  final RxBool audioPlaying = false.obs;
  final RxBool hasChanges = false.obs;
  final RxInt inputAge = 0.obs;
  final RxString inputConstellation = "".obs;
  late List<String> constellation = [
    "白羊座",
    "金牛座",
    "双子座",
    "巨蟹座",
    "狮子座",
    "处女座",
    "天秤座",
    "天蝎座",
    "射手座",
    "摩羯座",
    "水瓶座",
    "双鱼座"
  ];

  final RxInt inputAudioSignatureDuration = 0.obs;

  final RxDouble audioProgress = 0.0.obs;

  final UserInfoEntity userInfoEntity = Get.arguments;

  final audioPlayer = AudioPlayerUtils();

  late RxInt duration = 0.obs;

  @override
  void onInit() async {
    super.onInit();

    audioPlayer.onInit();

    if (userInfoEntity.voiceSignature != null) {
      duration.value = userInfoEntity.voiceLength ?? 0;
      if (duration.value == 0) {
        Duration? audioDuration =
            await audioPlayer.setUrl(userInfoEntity.voiceSignature ?? "");
        duration.value = audioDuration?.inSeconds ?? 0;
      }
    }
    audioPlayer.durationStream?.listen((event) {
      if (duration.value > 0) {
        audioProgress.value = (event ?? 0) / (duration.value);
      }
    });
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.playing) {
        audioPlaying.value = true;
      } else {
        audioPlaying.value = false;
      }
    });
  }

  void save() async {
    // if (selectedImagePath.value.isEmpty &&
    //     inputNickname.value.isEmpty &&
    //     inputAudioSignaturePath.value.isEmpty &&
    //     inputTextSignature.value.isEmpty &&
    //     selectedBirthday.value.year == 1970 &&
    //     inputHometown.value.isEmpty &&
    //     inputVocation.value.isEmpty &&
    //     inputConstellation.value.isEmpty &&
    //     inputAge.value == 0) {
    // ToastUtils.showToast(S.current.userInfoNoEditTip);
    // return;
    //}
    if (inputNickname.value.isNotEmpty && inputNickname.value.length > 8) {
      ToastUtils.showToast("昵称最长8个字");
      return;
    }
    if (inputHometown.value.isNotEmpty && inputHometown.value.length > 8) {
      ToastUtils.showToast("地区最长8个字");
      return;
    }
    if (inputVocation.value.isNotEmpty && inputVocation.value.length > 8) {
      ToastUtils.showToast("职业最长8个字");
      return;
    }
    String? birthday;
    if (selectedBirthday.value.year != 1970) {
      birthday = TimeUtils.dateFormatString(
        dateTime: selectedBirthday.value,
        formatList: ["yyyy", "-", "mm", "-", "dd"],
      );
    }

    String? avatarUrl;
    if (selectedImagePath.value.isNotEmpty) {
      if (selectedImagePath.startsWith('https://')) {
        avatarUrl = selectedImagePath.value;
      } else {
        avatarUrl = await ApiService().uploadFile(selectedImagePath.value);
      }
    }

    String? audioUrl;
    if (inputAudioSignaturePath.value.isNotEmpty) {
      audioUrl = await ApiService().uploadFile(inputAudioSignaturePath.value);
    } else {
      audioUrl = userInfoEntity.voiceSignature;
    }

    bool success = await ApiService().editUserInfo(
      avatar: avatarUrl,
      nickName: inputNickname.value.isNotEmpty ? inputNickname.value : null,
      audioSignature: audioUrl,
      textSignature:
          inputTextSignature.value.isNotEmpty ? inputTextSignature.value : null,
      birthday: birthday,
      hometown: inputHometown.value.isNotEmpty ? inputHometown.value : null,
      // sex: selectedSexIndex.value != -1 ? selectedSexIndex.value : null,
      work: inputVocation.value.isNotEmpty ? inputVocation.value : null,
      voiceLength: inputAudioSignatureDuration.value != 0
          ? inputAudioSignatureDuration.value
          : userInfoEntity.voiceLength,
      age: inputAge.value == 0 ? null : inputAge.value,
      constellation:
          inputConstellation.value.isNotEmpty ? inputConstellation.value : null,
    );
    if (inputNickname.value.isNotEmpty ||
        avatarUrl?.isNotEmpty == true ||
        selectedSexIndex.value != -1) {
      UserService().refresh();
      if (Get.isRegistered<ContactsController>()) {
        Get.find<ContactsController>().loadData();
      }
    }
    if (success) {
      ToastUtils.showToast(S.current.saveSuccess);
      Get.back(result: "1");
    }
  }

  void playAudio() async {
    audioPlayer.play();
  }

  void deleteAudio() async {
    if (audioPlaying.value == true) {
      await audioPlayer.stop();
    }
    inputAudioSignaturePath.value = "";
    inputAudioSignatureDuration.value = 0;
    userInfoEntity.voiceSignature = null;
    userInfoEntity.voiceLength = null;
    hasChanges.value = true;
  }

  void updateAudio(String filePath, int audioLength) async {
    // duration.value = await audioPlayer.setUrl(filePath);
    // userInfoEntity.voiceLength = duration.value?.inSeconds;
    inputAudioSignaturePath.value = filePath;
    inputAudioSignatureDuration.value = audioLength;
  }

  @override
  void onClose() {
    audioPlayer.stop();
    audioPlayer.cancelPlayerSubscriptions();
    super.onClose();
  }
}
