import 'package:dada/model/user_avatar_entity.dart';
import 'package:get/get.dart';

import 'package:dada/services/network/api_service.dart';

class SelectAvatarController extends GetxController {
  final RxInt currentTab = 0.obs;
  final RxList<UserAvatarEntity> avatarList = <UserAvatarEntity>[].obs;
  final RxInt price = 1.obs;


  // @override
  // void onInit() {
  //   super.onInit();
  //   fetchAvatarList();
  // }
  @override
  void onReady() {

    super.onReady();
    fetchAvatarList();
  }
  void initWithParams({ r}) {
    price.value=r;
  }

  void switchTab(int index) {
    if (currentTab.value == index) return;
    currentTab.value = index;
    fetchAvatarList();
  }

  Future<void> fetchAvatarList() async {
    try {
      List<UserAvatarEntity>? response = await ApiService().getAvatarList(type: currentTab.value, price: price.value);
      avatarList.value = response!;
    } catch (e) {
      print('Error fetching avatar list: $e');
    }
  }

}
