import 'package:dada/pages/mine/edit/avatar/select_avatar_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';

import 'package:dada/model/user_avatar_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';

import 'avatar_detail_dialog.dart';

class SelectAvatarPage extends StatefulWidget {
  const SelectAvatarPage({super.key});

  @override
  State<SelectAvatarPage> createState() => _SelectAvatarPageState();
}

class _SelectAvatarPageState extends State<SelectAvatarPage> {
  final SelectAvatarController controller = Get.put(SelectAvatarController());
  int? _selectedAvatarIndex; // 添加选中图片的索引变量
  @override
  void initState() {
    super.initState();
    final arguments = int.parse(Get.parameters["price"] ?? "1");

    controller.initWithParams(r: arguments);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: "选择头像",
        rightWidgets: [
          IconButton(
            icon: const Icon(Icons.check, color: Colors.green),
            onPressed: () {
              if (_selectedAvatarIndex != null) {
                final selectedAvatar =
                    controller.avatarList[_selectedAvatarIndex!];
                Get.back(result: selectedAvatar.url); // 返回选中的图片URL
              }
            },
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标签栏
          Container(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
            child: Row(
              children: [
                _buildTabItem("男生", 0),
                SizedBox(width: 10.w),
                _buildTabItem("女生", 1),
                SizedBox(width: 10.w),
                _buildTabItem("个性", 2),
              ],
            ),
          ),

          // 提示
          Container(
            padding: EdgeInsets.only(left: 20.w),
            child: Text(
              "进入应用后，会有更多的头像供选择哦~",
              style: TextStyles.common(12.sp, AppColors.colorFF999999),
            ),
          ),

          // 头像网格
          Expanded(
            child: Obx(() => GridView.builder(
                  padding: EdgeInsets.all(15.w),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    mainAxisSpacing: 15.w,
                    crossAxisSpacing: 15.w,
                    childAspectRatio: 1,
                  ),
                  itemCount: controller.avatarList.length,
                  itemBuilder: (context, index) {
                    final item = controller.avatarList[index];
                    return _buildAvatarItem(item, index);
                  },
                )),
          ),
        ],
      ),
    );
  }

  Widget _buildTabItem(String title, int index) {
    return Obx(() => GestureDetector(
          onTap: () => controller.switchTab(index),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: controller.currentTab.value == index
                  ? AppColors.colorFF89E15C
                  : Colors.grey[200],
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Text(
              title,
              style: TextStyles.common(
                  14.sp,
                  controller.currentTab.value == index
                      ? Colors.white
                      : AppColors.colorFF666666),
            ),
          ),
        ));
  }

  Widget _buildAvatarItem(UserAvatarEntity item, int index) {
    bool isSelected = _selectedAvatarIndex == index; // 判断是否选中
    return Stack(
      children: [
        // 头像图片
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(
              // color: AppColors.colorFFE1E1E1,
              // width: 0.5,
              color: isSelected ? Colors.green : AppColors.colorFFE1E1E1,
              // 添加绿色边框
              width: isSelected ? 2.0 : 0.5,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10.r),
            child: ImageUtils.getImage(
                item.url!, double.infinity, double.infinity),
          ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedAvatarIndex = index; // 更新选中的图片索引
            });
          },
        ),

        // 剩余天数
        if (item.expireDay! > 0)
          Positioned(
            left: 5.w,
            top: 5.h,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Text(
                "${item.expireDay}天",
                style: TextStyles.common(10.sp, Colors.white),
              ),
            ),
          ),

        // 锁定图标和蒙层
        if (item.expireDay! < 1 && item.price! > 0)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: Colors.black.withOpacity(0.7),
              ),
              child: Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.only(right: 5.w, top: 5.h),
                  child: ImageUtils.getImage(
                    Assets.imagesBackpackDressListLock,
                    15.w,
                    15.w,
                  ),
                ),
              ),
            ),
          ),

        // 价格
        if (item.price! > 0)
          Positioned(
            bottom: 5.h,
            left: 0,
            right: 0,
            child: Center(
              child: InkWell(
                  onTap: () {
                    ToastUtils.showBottomDialog(
                      AvatarDetailDialog(userAvatarEntity: item),
                    ).then((value) {
                      if (value != null) {
                        item.expireDay = value.expireDay;
                        setState(() {
                          controller.avatarList[index] = item;
                        });
                      }
                    });
                  },
                  child: Container(
                    height: 16.h,
                    width: 46.w,
                    //padding: EdgeInsets.symmetric(horizontal: 1.w),
                    decoration: BoxDecoration(
                      color: AppColors.colorFF23AF28,
                      borderRadius: BorderRadius.circular(15.h / 2),
                      gradient: const LinearGradient(
                        colors: [
                          AppColors.colorFFA0F6A5,
                          AppColors.colorFF58C75D
                        ],
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ImageUtils.getImage(
                            Assets.imagesRechargeDaBang, 13.w, 14.h),
                        Padding(
                            padding: EdgeInsets.only(left: 1.w),
                            child: Center(
                              child: Text(
                                "${item.price}",
                                style: TextStyles.normal(12.sp).copyWith(
                                  height: 1,
                                ),
                              ),
                            )),
                      ],
                    ),
                  )),
            ),
          )
      ],
    );
  }
}
