import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/user_avatar_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:dada/services/network/api_service.dart';

class AvatarDetailDialog extends StatefulWidget {
  final UserAvatarEntity userAvatarEntity;

  const AvatarDetailDialog({super.key, required this.userAvatarEntity});

  @override
  State<AvatarDetailDialog> createState() => _AvatarDetailDialogState();
}

class _AvatarDetailDialogState extends State<AvatarDetailDialog> {
  @override
  Widget build(BuildContext context) {
    String coinAssetName = Assets.imagesRechargeDaCoin;
    double coinImageWidth = 16.w;
    double coinImageHeight = 16.w;
    if (widget.userAvatarEntity.priceType == 1) {
      coinAssetName = Assets.imagesRechargeDaCoin;
      coinImageWidth = 16.w;
      coinImageHeight = 16.w;
    } else if (widget.userAvatarEntity.priceType == 2) {
      coinAssetName = Assets.imagesRechargeDaBang;
      coinImageWidth = 13.w;
      coinImageHeight = 20.h;
    } else if (widget.userAvatarEntity.priceType == 3) {
      coinAssetName = Assets.imagesRechargeDaCoin;
      coinImageWidth = 16.w;
      coinImageHeight = 16.w;
    }
    return GradientWidget(
      width: ScreenUtil().screenWidth,
      height: 420.h,
      colors: const [
        AppColors.colorFFD2F6C0,
        Colors.white,
      ],
      stops: const [0, 0.45],
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(top: 20.h),
            constraints: BoxConstraints(
              maxWidth: 288.w,
            ),
            child: Text(
              "有效期: ${widget.userAvatarEntity.useTime ?? ""}天",
              textAlign: TextAlign.center,
              maxLines: 2,
              style: TextStyles.normal(16.sp),
            ),
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(top: 20.h, bottom: 15.h),
                width: 290.w,
                height: 217.5.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color: AppColors.colorFFE5EEE5,
                  ),
                ),
                child: ImageUtils.getImage(
                    widget.userAvatarEntity.url ?? "", 290.w, 217.5.h,
                    fit: BoxFit.contain),
              ),
              Positioned(
                bottom: 0,
                child: Container(
                  height: 30.h,
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  decoration: BoxDecoration(
                    color: AppColors.colorFF23AF28,
                    borderRadius: BorderRadius.circular(30.h / 2),
                    gradient: const LinearGradient(
                      colors: [
                        AppColors.colorFFA0F6A5,
                        AppColors.colorFF58C75D
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ImageUtils.getImage(
                          coinAssetName, coinImageWidth, coinImageHeight),
                      Padding(
                        padding: EdgeInsets.only(left: 5.w),
                        child: Text(
                          "${widget.userAvatarEntity.price ?? 0}",
                          textAlign: TextAlign.center,
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          CommonGradientBtn(
            title: widget.userAvatarEntity.expireDay! > 0 ? "续费" : "兑换",
            topMargin: 30.h,
            onTap: () async {
              try {
                UserAvatarEntity? response = await ApiService().buyAvatar(avatarId: widget.userAvatarEntity.avatarId!);
                Get.back(result: response);
              } catch (e) {
                print('Error fetching avatar list: $e');
                Get.back(result: null);
              }

            },
          ),
        ],
      ),
    );
  }
}
