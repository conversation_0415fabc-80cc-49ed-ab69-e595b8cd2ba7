import 'dart:async';

import 'package:dada/components/widgets/input_text_field_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/system_config_entity.dart';
import 'package:dada/model/teen_mode_status_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/mine/invitation/mine_invitation_user_info_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MineController extends GetxController {
  final Rx<UserInfoEntity?> userInfo = Rx(null);

  List<Map<String, dynamic>> list = [];

  StreamSubscription? _streamSubscription;

  @override
  void onInit() async {
    super.onInit();

    userInfo.value = UserService().user;

    ///初始化列表数据
    initList();

    loadUserInfo();
    loadSystemConfig();

    _streamSubscription = EventBusEngine.eventBus.on().listen((event) {
      EventEntity entity = event;
      if (entity.event == BusEvent.userInfoUpdate) {
        userInfo.value = UserService().user;
      }
    });
  }

  @override
  void onClose() {
    _streamSubscription?.cancel();
    super.onClose();
  }

  void refreshUserInfo() {
    userInfo.value = UserService().user;
  }

  Future<void> loadUserInfo({bool? showLoading}) async {
    UserInfoEntity? userInfoEntity = await ApiService()
        .getUserInfo(userInfo.value?.id ?? "", showLoading: showLoading);
    if (userInfoEntity != null) {
      userInfo.value = userInfoEntity;
      update();
    }
  }

  Future<void> loadSystemConfig() async {
    SystemConfigEntity? config = UserService().systemConfig;
    config ??= await ApiService().getSystemConfig();
    if (config != null) {
      UserService().systemConfig = config;
      bool isShowExchangeCode = config.isShowCdk == "1";
      if (isShowExchangeCode && list.length > 6) {
        bool containsExchangeCode =
            list.where((e) => e["title"] == "领取福利").toList().isNotEmpty;
        if (!containsExchangeCode) {
          list.insert(
            6,
            {
              "title": "领取福利",
              "icon": Assets.imagesMineListInputExchangeCode,
              "onTap": () {
                ToastUtils.showDialog(
                  dialog: InputTextFieldDialog(
                    title: "填写兑换码",
                    confirmBtnTitle: "确定",
                    hintText: "请输入兑换码",
                    textFieldHeight: 40.h,
                    textFieldCornerRadius: 20.r,
                    onSubmit: (value) async {
                      bool success = await ApiService().receiptCdk(code: value);
                      if (success) {
                        ToastUtils.showDialog(title: "兑换成功", content: "兑换成功");
                      }
                    },
                  ),
                );
                //}
              }
            },
          );
          update();
        }
      }
    }
  }

  void initList() {
    list = [
      {
        "title": S.current.myAccount,
        "icon": Assets.imagesMineListMyAccount,
        "onTap": () {
          Get.toNamed(GetRouter.myAccount);
        }
      },
      {
        "title": S.current.audioBox,
        "icon": Assets.imagesMineListAudioBox,
        "onTap": () {
          Get.toNamed(GetRouter.audioBox);
        }
      },
      // {
      //   "title": S.current.exchangeMall,
      //   "icon": Assets.imagesMineListExchangeMall,
      //   "onTap": () {
      //     Get.toNamed(GetRouter.store);
      //   }
      // },
      {
        "title": S.current.realNameAuth,
        "icon": Assets.imagesMineListRealNameAuth,
        "onTap": () {
          if (UserService().user?.isRealName != true) {
            Get.toNamed(GetRouter.realAuth)?.then((value) {
              if (value != null) {
                loadUserInfo(showLoading: false);
              }
            });
          } else {
            ToastUtils.showToast("实名认证已完成");
          }
        }
      },
      {
        "title": "青少年模式",
        "icon": Assets.imagesMineListTeenMode,
        "onTap": () async {
          TeenModeStatusEntity? entity = await ApiService().getTeenModeStatus();
          if (entity?.toggle == "1") {
            Get.toNamed(GetRouter.teenModeSetting,
                parameters: {"isOpen": "1", "pwd": entity?.password ?? ""});
          } else {
            Get.toNamed(GetRouter.teenMode);
          }
        }
      },
      {
        "title": "隐私/权限设置",
        "icon": Assets.imagesMineListPrivacy,
        "onTap": () {
          Get.toNamed(GetRouter.myPrivacy);
        }
      },
      {
        "title": "填写邀请码",
        "icon": Assets.imagesMineListInputInvitationCode,
        "onTap": () {
          if (userInfo.value?.inviteUserInfo != null) {
            ToastUtils.showDialog(
              dialog: MineInvitationUserInfoDialog(
                userInfo: userInfo.value!.inviteUserInfo!,
              ),
            );
          } else {
            ToastUtils.showDialog(
              dialog: InputTextFieldDialog(
                title: "请填写邀请码",
                confirmBtnTitle: "确定",
                hintText: "请输入邀请码",
                textFieldHeight: 40.h,
                textFieldCornerRadius: 20.r,
                onSubmit: (value) async {
                  bool success =
                      await ApiService().submitInvitationCode(code: value);
                  if (success) {
                    await loadUserInfo();
                    initList();
                    if (userInfo.value?.inviteUserInfo != null) {
                      ToastUtils.showDialog(
                        dialog: MineInvitationUserInfoDialog(
                          userInfo: userInfo.value!.inviteUserInfo!,
                        ),
                      );
                    }
                  }
                },
              ),
            );
          }
        }
      },
      {
        "title": "邀请好友",
        "icon": Assets.imagesMineListInvite,
        "onTap": () {
          Get.toNamed(GetRouter.inviteFriends);
        }
      },
      {
        "title": S.current.settings,
        "icon": Assets.imagesMineListSettings,
        "onTap": () {
          Get.toNamed(GetRouter.settings);
        }
      },
    ];
  }
}
