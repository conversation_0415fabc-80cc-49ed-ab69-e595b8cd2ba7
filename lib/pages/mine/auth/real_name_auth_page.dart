import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class RealNameAuthPage extends StatefulWidget {
  const RealNameAuthPage({super.key});

  @override
  State<RealNameAuthPage> createState() => _RealNameAuthPageState();
}

class _RealNameAuthPageState extends State<RealNameAuthPage> {
  TextEditingController nameEditingController = TextEditingController();
  TextEditingController idCardEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "实名认证",
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 32.5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 10.h, left: 5.w),
              child: Text(
                "真实姓名",
                style: TextStyles.medium(16.sp, w: FontWeight.w500),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 10.h),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.colorFFF5F6F7,
                  borderRadius: BorderRadius.circular(45.h / 2),
                ),
                child: CustomTextField.build(
                  contentPadding: EdgeInsets.only(left: 15.w),
                  controller: nameEditingController,
                  hintText: "请输入你的真实姓名",
                  style: TextStyles.normal(16.sp),
                  hintStyle: TextStyles.common(16.sp, AppColors.colorFF666666),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 20.h, left: 5.w),
              child: Text(
                "身份证号",
                style: TextStyles.medium(16.sp, w: FontWeight.w500),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 10.h),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.colorFFF5F6F7,
                  borderRadius: BorderRadius.circular(45.h / 2),
                ),
                child: CustomTextField.build(
                  contentPadding: EdgeInsets.only(left: 15.w),
                  controller: idCardEditingController,
                  hintText: "请输入你的身份证号",
                  style: TextStyles.normal(16.sp),
                  hintStyle: TextStyles.common(16.sp, AppColors.colorFF666666),
                ),
              ),
            ),
            CommonGradientBtn(
              topMargin: 20.h,
              horizontalMargin: 0.w,
              title: "确定",
              onTap: () async {
                bool success = await ApiService().realNameAuth(
                    name: nameEditingController.text,
                    idCard: idCardEditingController.text);
                if (success) {
                  ToastUtils.showToast("认证成功");
                  Get.back(result: 1);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
