import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:get/get.dart';

class UpdatePassWordPage extends StatefulWidget {
  const UpdatePassWordPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return UpdatePassWordState();
  }
}

class UpdatePassWordState extends State<UpdatePassWordPage> {
  final _formKey = GlobalKey<FormState>();
  String? _oldPassword, _newPassword, _confirmPassword;
  bool _showOldPassword = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;

  bool _isValidPassword(String password) {
    // 密码必须是8-16位的英文字母、数字组合（不能是纯数字）
    return RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\W_]{8,16}$')
        .hasMatch(password);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '修改密码',
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              TextFormField(
                enabled: false, // 设置为不可编辑
                decoration: InputDecoration(
                  hintText: UserService().user?.dadaNo ?? "", // 设置hintText为搭搭号
                  prefixIcon: const Padding(
                    padding:
                        EdgeInsets.only(top: 8.0, bottom: 8.0, right: 30.0),
                    child: Text(
                      '搭搭号',
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontSize: 16.0),
                    ),
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    // 添加下划线边框
                    borderSide: BorderSide(color: Color(0xFFE5EEE5)),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              TextFormField(
                decoration: InputDecoration(
                  hintText: '填写原密码', // 修改: 设置hintText
                  prefixIcon: const Padding(
                    padding:
                        EdgeInsets.only(top: 8.0, bottom: 8.0, right: 30.0),
                    child: Text(
                      '原密码',
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontSize: 16.0),
                    ),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showOldPassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      setState(() {
                        _showOldPassword = !_showOldPassword;
                      });
                    },
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xFFE5EEE5)),
                  ),
                ),
                obscureText: !_showOldPassword,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入原密码';
                  }
                  return null;
                },
                onChanged: (value) => _oldPassword = value,
              ),
              SizedBox(height: 20),
              TextFormField(
                decoration: InputDecoration(
                  hintText: '填写新密码', // 修改: 设置hintText
                  prefixIcon: const Padding(
                    padding:
                        EdgeInsets.only(top: 8.0, bottom: 8.0, right: 30.0),
                    child: Text(
                      '新密码',
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontSize: 16.0),
                    ),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showNewPassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      setState(() {
                        _showNewPassword = !_showNewPassword;
                      });
                    },
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xFFE5EEE5)),
                  ),
                ),
                obscureText: !_showNewPassword,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入新密码';
                  }
                  if (!_isValidPassword(value)) {
                    return '密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）';
                  }
                  return null;
                },
                onChanged: (value) => _newPassword = value,
              ),
              SizedBox(height: 20),
              TextFormField(
                decoration: InputDecoration(
                  hintText: '请确认密码', // 修改: 设置hintText
                  prefixIcon: const Padding(
                    padding:
                        EdgeInsets.only(top: 8.0, bottom: 8.0, right: 16.0),
                    child: Text(
                      '确认密码',
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontSize: 16.0),
                    ),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showConfirmPassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      setState(() {
                        _showConfirmPassword = !_showConfirmPassword;
                      });
                    },
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xFFE5EEE5)),
                  ),
                ),
                obscureText: !_showConfirmPassword,
                validator: (value) {
                  if (value != _newPassword) {
                    return '两次输入的密码不一致';
                  }
                  return null;
                },
                onSaved: (value) => _confirmPassword = value,
              ),
              const SizedBox(height: 20),
              const Text(
                '密码必须是8-16位的英文字母、数字、字符组合（不能 是纯数字）',
                style: TextStyle(fontSize: 14, color: Color(0xFF3D3D3D)),
              ),
              const SizedBox(height: 10),
              GestureDetector(
                onTap: () {
                  Get.toNamed(GetRouter.forgetPassword);
                },
                child: const Text(
                  '忘记原密码',
                  style: TextStyle(fontSize: 14, color: Colors.green),
                ),
              ),
              SizedBox(height: 30.h),
              CommonGradientBtn(
                title: "确定",
                height: 55.h,
                onTap: () async {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    bool success = await ApiService().changePassword(
                        _oldPassword!, _newPassword!); // 修改: 使用新密码
                    if (success) {
                      ToastUtils.showToast("密码修改成功");
                      Get.back();
                    }
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
