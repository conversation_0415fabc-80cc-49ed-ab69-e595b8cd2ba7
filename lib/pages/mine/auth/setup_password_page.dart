import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class SetupPassWordPage extends StatefulWidget {
  const SetupPassWordPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return SetupPassWordState();
  }
}

class SetupPassWordState extends State<SetupPassWordPage> {
  final _formKey = GlobalKey<FormState>();
  String? _password, _confirmPassword;
  bool _showPassword = false;
  bool _showConfirmPassword = false;

  bool _isValidPassword(String password) {
    // 密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）
    return RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\W_]{8,16}$')
        .hasMatch(password);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '设置密码',
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              TextFormField(
                decoration: InputDecoration(
                  hintText: '请输入密码', // 添加默认提示文本
                  prefixIcon: const Padding(
                    padding:
                        EdgeInsets.only(top: 8.0, bottom: 8.0, right: 16.0),
                    // 修改: 增加右侧的间距，使提示文本右移
                    child: Text(
                      '设置密码',
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontSize: 16.0),
                    ),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showPassword ? Icons.visibility : Icons.visibility_off,
                      color: Colors.grey, // 修改: 将小眼睛图标的颜色改为灰色
                    ),
                    onPressed: () {
                      setState(() {
                        _showPassword = !_showPassword;
                      });
                    },
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    // 添加: 设置初始状态下的下划线颜色
                    borderSide: BorderSide(
                        color: Color(0xFFE5EEE5)), // 修改: 下划线颜色改为#E5EEE5
                  ),
                ),
                obscureText: !_showPassword,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入密码';
                  }
                  if (!_isValidPassword(value)) {
                    return '密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）';
                  }
                  return null;
                },
                onChanged: (value) => _password = value,
              ),
              const SizedBox(height: 20),
              TextFormField(
                decoration: InputDecoration(
                  hintText: '请确认密码', // 添加默认提示文本
                  prefixIcon: const Padding(
                    padding: EdgeInsets.only(
                        top: 8.0, bottom: 8.0, right: 16.0), // 修改: 增加顶部和底部的间距
                    child: Text(
                      '确认密码',
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontSize: 16.0),
                    ),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showConfirmPassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: Colors.grey, // 修改: 将小眼睛图标的颜色改为灰色
                    ),
                    onPressed: () {
                      setState(() {
                        _showConfirmPassword = !_showConfirmPassword;
                      });
                    },
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    // 添加: 设置初始状态下的下划线颜色
                    borderSide: BorderSide(
                        color: Color(0xFFE5EEE5)), // 修改: 下划线颜色改为#E5EEE5
                  ),
                ),
                obscureText: !_showConfirmPassword,
                validator: (value) {
                  if (value != _password) {
                    return '两次输入的密码不一致';
                  }
                  return null;
                },
                onSaved: (value) => _confirmPassword = value,
              ),
              const SizedBox(height: 20),
              const Text(
                '密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）',
                style: TextStyle(fontSize: 14, color: Color(0xFF3D3D3D)),
              ),
              SizedBox(height: 30.h),
              CommonGradientBtn(
                title: "确定",
                onTap: () async {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    // 调用 passwordSet 接口设置密码
                    bool success = await ApiService().passwordSet(_password!);
                    if (success) {
                      // 检查返回结果是否异常
                      ToastUtils.showToast("密码设置成功");
                      Get.back(result: true);
                    }
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
