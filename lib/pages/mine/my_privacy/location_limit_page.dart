import 'package:city_pickers/city_pickers.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/region_limit_entity.dart';
import 'package:dada/pages/mine/my_privacy/location_limit_controller.dart';
import 'package:dada/utils/extensions/border_extension.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class LocationLimitPage extends StatelessWidget {
  LocationLimitPage({super.key});

  final LocationLimitController controller = Get.put(LocationLimitController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: "区域限制",
      ),
      body: Padding(
        padding: EdgeInsets.all(15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("不对以下区域展示我的搭圈",
                    style: TextStyle(
                        fontSize: 16.sp, color: AppColors.colorFF3D3D3D)),
                _buildAddButton(context)
              ],
            ),
            _buildLocations()
          ],
        ),
      ),
    );
  }

  Widget _buildAddButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CityPickers.showCityPicker(
                context: context,
                showType: ShowType.pc,
                height: 225.w,
                confirmWidget: Text("保存",
                    style: TextStyle(
                        color: AppColors.colorFF3D3D3D, fontSize: 14.sp)),
                cancelWidget: Text("取消",
                    style: TextStyle(
                        color: AppColors.colorFF3D3D3D, fontSize: 14.sp)))
            .then((result) {
          String? province = result?.provinceName;
          String? city = result?.cityName;
          String? provinceId = result?.provinceId;
          if (province?.isNotEmpty == true && city?.isNotEmpty == true) {
            bool isCity = provinceId == "110000" ||
                provinceId == "120000" ||
                provinceId == "310000" ||
                provinceId == "500000";
            controller.addRegionLimit(isCity ? province! : "$province$city");
          }
        });
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            ImageUtils.getAssetImage(Assets.imagesSmallRoomBubbleWordAdd,
                width: 20.w, height: 20.w),
            SizedBox(
              width: 10.w,
            ),
            Text(
              "添加地区",
              style: TextStyle(color: AppColors.colorFF168C1A, fontSize: 16.sp),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildLocations() {
    return GetBuilder(
        init: controller,
        builder: (controller) {
          return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: controller.limitRegions
                  .map<Widget>((RegionLimitEntity region) {
                return Container(
                  margin: EdgeInsets.only(top: 10.w),
                  padding: EdgeInsets.only(left: 10.w),
                  decoration: BoxDecoration(
                      color: AppColors.colorFFEAECF1,
                      borderRadius: 360.0.borderRadius),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(region.region ?? ""),
                      SizedBox(width: 20.w),
                      GestureDetector(
                        onTap: () {
                          controller.deleteRegionLimit(region.id ?? "");
                        },
                        child: Padding(
                          padding: EdgeInsets.all(10.w),
                          child: Icon(
                            Icons.close,
                            size: 12.w,
                          ),
                        ),
                      )
                    ],
                  ),
                );
              }).toList(growable: false));
        });
  }
}
