import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_setting_list_item_widget.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/switch_button.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MyPrivacyPage extends StatefulWidget {
  const MyPrivacyPage({super.key});

  @override
  State<MyPrivacyPage> createState() => _MyPrivacyPageState();
}

class _MyPrivacyPageState extends State<MyPrivacyPage> {
  RxBool isReceiveStrangerMsg = true.obs;

  @override
  void initState() {
    super.initState();

    loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "个人隐私",
      ),
      body: Column(
        children: [
          // CommonSettingListItemWidget(
          //   title: "修改密码",
          //   subTitle: "",
          //   onTap: () {
          //     Get.toNamed(GetRouter.passwordChange);
          //   },
          // ),
          // Obx(
          //   () => CommonSettingListItemWidget(
          //     title: "接受陌生人消息",
          //     rightWidget: SwitchButton(
          //       value: isReceiveStrangerMsg.value,
          //       onChanged: (value) async {
          //         bool success =
          //             await ApiService().updateStrangerMsgState(state: value);
          //         if (success) {
          //           isReceiveStrangerMsg.value = !(isReceiveStrangerMsg.value);
          //         }
          //       },
          //     ),
          //   ),
          // ),
          CommonSettingListItemWidget(
            title: "搭圈权限",
            subTitle: "",
            onTap: () {
              Get.toNamed(GetRouter.locationLimit);
            },
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(bottom: 40.h),
            child: Column(
              children: [
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: "《隐私政策》",
                        style: TextStyle(
                          color: AppColors.colorFF6265FF,
                          fontSize: 14.sp,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Get.toNamed(GetRouter.webView,
                                parameters: {"url": AppConfig.privacyUrl});
                          },
                      ),
                      TextSpan(
                        text: "《用户协议》",
                        style: TextStyle(
                          color: AppColors.colorFF6265FF,
                          fontSize: 14.sp,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Get.toNamed(GetRouter.webView, parameters: {
                              "url": AppConfig.userAgreementUrl
                            });
                          },
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 5.h),
                  child: Text(
                    "客服联系方式：<EMAIL>",
                    style: TextStyles.common(14.sp, AppColors.colorFF999999),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  void loadData() async {
    bool status =
        await ApiService().getStrangerMsgState(userId: UserService().user!.id!);
    isReceiveStrangerMsg.value = status;
  }
}
