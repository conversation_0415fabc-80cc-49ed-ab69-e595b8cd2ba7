import 'package:dada/model/region_limit_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';

class LocationLimitController extends GetxController {
  List<RegionLimitEntity> limitRegions = [];

  @override
  void onReady() {
    _loadLimits();
    super.onReady();
  }

  void _loadLimits() async {
    List<RegionLimitEntity>? list = await ApiService().listPostRegionLimit();
    if (list?.isNotEmpty == true) {
      limitRegions = list!;
      update();
    }
  }

  void addRegionLimit(String region) async {
    var firstWhere = limitRegions.firstWhereOrNull((element) => element.region == region);
    if (firstWhere != null) {
      ToastUtils.showToast("区域已限制");
      return;
    }
    bool added = await ApiService().addPostRegionLimit(region: region);
    if (added) {
      _loadLimits();
    }
  }

  void deleteRegionLimit(String id) async{
    bool result = await ApiService().deletePostRegionLimit(id: id);
    if (result) {
      limitRegions.removeWhere((element) => element.id == id);
      update();
    }
  }
}
