import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PasswordEditPage extends StatefulWidget {
  const PasswordEditPage({super.key});

  @override
  State<PasswordEditPage> createState() => _PasswordEditPageState();
}

class _PasswordEditPageState extends State<PasswordEditPage> {
  RxBool completeEnable = false.obs;
  TextEditingController editingController1 = TextEditingController();
  TextEditingController editingController2 = TextEditingController();
  TextEditingController editingController3 = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "修改密码",
        rightWidgets: [
          Obx(
            () => GestureDetector(
              onTap: () {
                if (completeEnable.value == false) {
                  return;
                }
                Get.back();
              },
              child: Container(
                width: 56.w,
                height: 30.h,
                margin: EdgeInsets.only(right: 10.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15.r),
                  color: completeEnable.value == false
                      ? AppColors.colorFFF5F5F5
                      : AppColors.colorFF89E15C,
                ),
                child: Text(
                  "完成",
                  style: TextStyles.normal(14.sp),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildListItem(
            title: "搭搭账号",
            textEditingController: TextEditingController(),
            text: UserService().user?.dadaNo,
            readOnly: true,
          ),
          _buildListItem(
            title: "原密码",
            textEditingController: editingController1,
            placeholder: "填写原密码",
            onChanged: (value) {
              updateRightBarBtnEnabled();
            },
          ),
          _buildListItem(
            title: "新密码",
            textEditingController: editingController2,
            placeholder: "填写新密码",
            onChanged: (value) {
              updateRightBarBtnEnabled();
            },
          ),
          _buildListItem(
            title: "确认密码",
            textEditingController: editingController3,
            placeholder: "再次填写新密码",
            onChanged: (value) {
              updateRightBarBtnEnabled();
            },
          ),
          _buildBottomTipLabel(),
          _buildForgetPwdLabel(),
        ],
      ),
    );
  }

  Widget _buildListItem({
    required String title,
    required TextEditingController textEditingController,
    String? text,
    String? placeholder,
    bool? readOnly,
    ValueChanged<String>? onChanged,
  }) {
    if (text?.isNotEmpty == true) {
      textEditingController.text = text!;
    }
    return Container(
      height: 50.h,
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.colorFFE5EEE5,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            width: 95.w,
            padding: EdgeInsets.only(top: 8.h),
            child: Text(
              title,
              style: TextStyles.normal(16.sp),
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: CustomTextField.build(
                readOnly: readOnly,
                controller: textEditingController,
                hintText: placeholder,
                hintStyle: TextStyles.common(16.sp, AppColors.colorFF999999),
                style: TextStyles.normal(16.sp),
                onChanged: onChanged,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildBottomTipLabel() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.h),
      child: Text(
        "密码必须是8-16位的英文字母、数字、字符组合（不能是纯数字）",
        maxLines: 2,
        style: TextStyles.normal(14.sp),
      ),
    );
  }

  Widget _buildForgetPwdLabel() {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, top: 15.h),
      child: RichText(
        text: TextSpan(
          text: "忘记原密码",
          style: TextStyle(
            color: AppColors.colorFF23AF28,
            fontSize: 14.sp,
            decoration: TextDecoration.underline,
            decorationColor: AppColors.colorFF23AF28,
          ),
        ),
      ),
    );
  }

  void updateRightBarBtnEnabled() {
    RegExp pwdRegExp = RegExp(r"^(?=.*[A-Za-z])(?=.*\W)(?!.*\d+$).{8,16}$");
    if (editingController1.text.isNotEmpty &&
        editingController2.text.isNotEmpty &&
        editingController3.text.isNotEmpty &&
        pwdRegExp.hasMatch(editingController2.text) &&
        editingController2.text == editingController3.text) {
      completeEnable.value = true;
    } else {
      completeEnable.value = false;
    }
  }
}
