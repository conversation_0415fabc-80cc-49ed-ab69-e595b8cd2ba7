import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MineInvitationUserInfoDialog extends StatefulWidget {
  final UserInfoEntity userInfo;

  const MineInvitationUserInfoDialog({super.key, required this.userInfo});

  @override
  State<MineInvitationUserInfoDialog> createState() =>
      _MineInvitationUserInfoDialogState();
}

class _MineInvitationUserInfoDialogState
    extends State<MineInvitationUserInfoDialog> {
  @override
  Widget build(BuildContext context) {
    return GradientWidget(
      width: 270.w,
      height: 295.h,
      colors: const [
        AppColors.colorFFD2F6C0,
        Colors.white,
      ],
      stops: const [0, 0.45],
      cornerRadius: 20.r,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 20.h),
                child: Text(
                  "邀请人绑定",
                  style: TextStyles.medium(16.sp, w: FontWeight.w500),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 20.h),
                child: ImageUtils.getImage(
                    widget.userInfo.avatar ?? "", 100.w, 100.w,
                    fit: BoxFit.cover, radius: 50.w),
              ),
              Padding(
                padding: EdgeInsets.only(top: 10.h),
                child: Text(
                  widget.userInfo.nickname ?? "",
                  style: TextStyles.normal(16.sp),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 10.h),
                child: Text(
                  "id：${widget.userInfo.dadaNo ?? ""}",
                  style: TextStyles.normal(16.sp),
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Container(
                  width: 80.w,
                  height: 35.h,
                  margin: EdgeInsets.only(bottom: 20.h),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        AppColors.colorFFA0F6A5,
                        AppColors.colorFF58C75D
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(17.5.r),
                  ),
                  child: Text(
                    "确定",
                    style: TextStyles.common(16.sp, AppColors.colorFF344F3D),
                  ),
                ),
              ),
            ],
          ),
          Positioned(
            right: 10.w,
            top: 10.h,
            child: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Icon(
                Icons.close,
                size: 20.w,
                color: AppColors.colorFF666666,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
