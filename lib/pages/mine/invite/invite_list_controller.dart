import 'package:dada/model/invite_list_entity.dart';
import 'package:get/get.dart';
import 'package:dada/services/network/api_service.dart';

class InviteListController extends GetxController {
  final RxInt inviteCount = 0.obs;
  final RxList<InviteListInviteRecordDetailsVos> inviteList = <InviteListInviteRecordDetailsVos>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchInviteList();
  }

  Future<void> fetchInviteList() async {
    try {
      final response = await ApiService().getInviteList();
      if (response == null) {
        return;
      }
      inviteCount.value = response.inviteCount!;
      inviteList.value = response.inviteRecordDetailsVos!;
    } catch (e) {
      print('Error fetching invite list: $e');
    }
  }
}
