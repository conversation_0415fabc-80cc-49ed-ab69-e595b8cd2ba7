import 'package:dada/pages/mine/invite/invite_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';

import 'package:dada/utils/image_utils.dart';

class InviteListPage extends StatefulWidget {
  const InviteListPage({super.key});

  @override
  _InviteListPage createState() => _InviteListPage();
}

class _InviteListPage extends State<InviteListPage> {
  final InviteListController controller = Get.put(InviteListController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "邀请列表",
        backgroundColor: Colors.transparent,
      ),
      body: Column(
        children: [
          // 已邀请人数
          Container(
            padding: EdgeInsets.only(left: 15.w, top: 15.h),
            alignment: Alignment.centerLeft,
            child: Obx(() => Text(
                  "已邀请: ${controller.inviteCount}人",
                  style: TextStyles.common(14.sp, AppColors.colorFF23AF28),
                )),
          ),

          // 列表标题栏
          Container(
            margin: EdgeInsets.only(top: 15.h),
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
            decoration: const BoxDecoration(
              color: AppColors.colorFFE4F6E4,
              border: Border(
                bottom: BorderSide(
                  color: AppColors.colorFFE1E1E1,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    "时间",
                    style: TextStyles.common(14.sp, AppColors.colorFF666666),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    "用户信息",
                    style: TextStyles.common(14.sp, AppColors.colorFF666666),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    "已奖励",
                    style: TextStyles.common(14.sp, AppColors.colorFF666666),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),

          // 列表内容
          Expanded(
            child: Obx(() => ListView.builder(
                  itemCount: controller.inviteList.length,
                  itemBuilder: (context, index) {
                    final item = controller.inviteList[index];
                    return Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 15.w, vertical: 12.h),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: AppColors.colorFFE1E1E1,
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              item.inviteDate!,
                              style: TextStyles.common(
                                  14.sp, AppColors.colorFF666666),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                ImageUtils.getImage(item.avatar!, 24.w, 24.w,
                                    color: Colors.grey[300], radius: 12.w),
                                const SizedBox(width: 6.0), // 添加间距
                                Container(
                                  constraints: BoxConstraints(
                                    maxWidth: 85.w,
                                  ),
                                  child: Text(
                                    item.nickname!,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyles.common(
                                        14.sp, AppColors.colorFF666666),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: item.inviteReward
                                      ?.map(
                                        (e) => Padding(
                                          padding: EdgeInsets.only(
                                            right: 2.w,
                                          ),
                                          child: Row(
                                            children: [
                                              ImageUtils.getImage(
                                                  e.rewardUrl ?? "", 20.w, 20.w,
                                                  fit: BoxFit.cover),
                                              Text(
                                                "x${e.rewardNum}",
                                                style: TextStyles.common(14.sp,
                                                    AppColors.colorFF666666),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                      .toList() ??
                                  [],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                )),
          ),
        ],
      ),
    );
  }
}
