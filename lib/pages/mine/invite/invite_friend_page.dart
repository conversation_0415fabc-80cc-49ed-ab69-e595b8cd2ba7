import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:dada/services/user/user_service.dart';

class InviteFriendPage extends StatelessWidget {
  const InviteFriendPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      extendBodyBehindAppBar: true,
      body: ListView(
        padding: EdgeInsets.zero,
        children: [
          // 顶部背景图和标题
          Stack(
            clipBehavior: Clip.none,
            children: [
              // 背景图
              Container(
                width: double.infinity,
                height: 520.h,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Assets.imagesInviteFriendBg),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              // 返回按钮和标题
              Positioned(
                top: ScreenUtil().statusBarHeight,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back_ios,
                            color: Colors.black, size: 18.0),
                        onPressed: () => Get.back(),
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.toNamed(GetRouter.inviteList);
                        },
                        child: Text(
                          '邀请列表',
                          style: TextStyle(
                            color: AppColors.colorFF3D3D3D,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                bottom: -50.h,
                child: // 邀请码输入框
                    Container(
                  height: 40.h,
                  width: 345.w,
                  margin: EdgeInsets.only(left: 15.w, bottom: 30.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6.r),
                    border: Border.all(color: AppColors.colorFF2E3E6B),
                  ),
                  child: Row(
                    children: [
                      Container(
                        height: 50.h,
                        width: 100.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: AppColors.colorFFD5F1D6, // 设置背景颜色为绿色
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(6.r),
                              bottomLeft: Radius.circular(6.r)),
                          border: const Border(
                            right: BorderSide(color: AppColors.colorFF2E3E6B),
                          ),
                        ),
                        child: Text(
                          '我的邀请码',
                          style: TextStyle(
                            color: AppColors.colorFF3D3D3D, // 文字颜色调整为白色
                            fontSize: 14.sp,
                            height: 1.6.h,
                          ),
                        ),
                      ),

                      SizedBox(width: 31.6.w), // 添加一些间距
                      Text(
                        UserService().user?.inviteCode ?? "",
                        style: TextStyle(
                          color: AppColors.colorFF3D3D3D,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        height: 50.h,
                        padding: EdgeInsets.only(right: 25.w),
                        child: InkWell(
                          onTap: () {
                            Clipboard.setData(ClipboardData(
                                text: UserService().user?.inviteCode ?? ""));
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('邀请码已复制到剪贴板')),
                            );
                          },
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              '复制',
                              style: TextStyle(
                                color: AppColors.colorFF23AF28,
                                fontSize: 14.sp,
                                height: 1.8.h,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Container(
            margin: EdgeInsets.only(top: 35.h),
            alignment: Alignment.center,
            child: Text(
              '对方输入你的邀请码后，也将获得10搭币奖励',
              style: TextStyle(
                color: AppColors.colorFF666666,
                fontSize: 12.sp,
              ),
            ),
          ),

          Container(
            margin: EdgeInsets.only(
                top: 15.h, left: 15.w, right: 15.w, bottom: 50.h),
            decoration: BoxDecoration(
              color: AppColors.colorFFF1FEF1,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 12.h, bottom: 12.h),
                  child: Text(
                    "邀请好友得奖励",
                    style: TextStyles.medium(16.sp,
                        w: FontWeight.w600, c: AppColors.colorFF23AF28),
                  ),
                ),
                ListView.builder(
                  padding: EdgeInsets.only(bottom: 15.h),
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: 7,
                  itemBuilder: (context, index) {
                    Color bgColor = index % 2 == 0
                        ? AppColors.colorFFD5F1D6
                        : AppColors.colorFFF1FEF1;
                    String leftTitle = _getLeftTitle(index);
                    String rightTitle = _getRightTitle(index);
                    return Container(
                      width: 310.w,
                      constraints: BoxConstraints(
                        minHeight: 30.h,
                      ),
                      color: bgColor,
                      margin: EdgeInsets.symmetric(horizontal: 17.5.w),
                      child: Row(
                        children: [
                          Container(
                            width: 310.w / 2,
                            padding: EdgeInsets.only(left: 20.w),
                            child: Text(
                              leftTitle,
                              style: index == 0
                                  ? TextStyles.medium(12.sp,
                                      c: AppColors.colorFF2D6D0B)
                                  : TextStyles.common(
                                      12.sp, AppColors.colorFF666666),
                            ),
                          ),
                          SizedBox(
                            width: 310.w / 2,
                            child: Container(
                              constraints: BoxConstraints(maxWidth: 80.w),
                              child: Text(
                                rightTitle,
                                style: index == 0
                                    ? TextStyles.medium(12.sp,
                                        c: AppColors.colorFF2D6D0B, h: 1.3)
                                    : TextStyles.common(
                                        12.sp, AppColors.colorFF666666,
                                        h: 1.3),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getLeftTitle(int index) {
    List<String> leftTitles = [
      "邀请人数",
      "1人",
      "2~3人",
      "4~6人",
      "7~11人",
      "12人",
      "12人以上",
    ];
    return leftTitles[index];
  }

  String _getRightTitle(int index) {
    List<String> rightTitles = [
      "奖励",
      "10搭币",
      "每邀一人得12搭币",
      "每邀一人得15搭币",
      "每邀一人得17搭币",
      "金岚世家服装一套、传火者标识、20搭币",
      "每邀一人得20搭币",
    ];
    return rightTitles[index];
  }
}
