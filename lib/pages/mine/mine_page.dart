import 'dart:ui';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/pages/mine/mine_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MinePage extends StatefulWidget {
  const MinePage({super.key});

  @override
  State<MinePage> createState() => _MinePageState();
}

class _MinePageState extends State<MinePage> {
  final controller = Get.put(MineController());
  final RxInt selectedIndex = 0.obs;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFADF0B0), Color(0x00E4FFBA)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                ///用户信息
                _buildHeader(),

                ///功能快捷入口
                _buildFunctionWidget(),

                ///列表功能
                _buildListWidget(),
              ],
            ),
          ),
          Container(
            height: 20.h,
            margin: EdgeInsets.only(bottom: 5.h, top: 5.h),
            child: Text(
              "Copyright©郑州搭吖科技有限公司版权所有",
              style: TextStyles.common(12.sp, AppColors.colorFF999999),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.only(top: 30.h),
      child: Stack(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(width: 15.w),

              ///头像
              Stack(
                children: [
                  Obx(
                    () => AvatarWidget(
                      userInfo: controller.userInfo.value,
                      dressUp: controller.userInfo.value?.avatarFrame,
                      size: 70.w,
                      heroTag: HeroTagName.profileAvatar.name,
                      onTap: () {
                        ImageUtils.showImageBrowser(
                          ImageBrowserArgs(
                            [HeroTagName.profileAvatar.name],
                            [
                              controller.userInfo.value?.avatar ??
                                  Assets.imagesAvatarPlaceholder
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    right: 6.w,
                    bottom: 0.w,
                    child: Container(
                      margin: EdgeInsets.only(right: 6.w),
                      child: Obx(
                        () => ImageUtils.getImage(
                            controller.userInfo.value?.sex == 1
                                ? Assets.imagesUserProfileSexFemale
                                : Assets.imagesUserProfileSexMale,
                            15.w,
                            15.w),
                      ),
                    ),
                  )
                ],
              ),

              SizedBox(width: 10.w),

              ///名字、徽章等级、ID、IP
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 3.h),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      ///跳转修改资料
                      Get.toNamed(GetRouter.editUserInfo,
                              arguments: controller.userInfo.value)
                          ?.then((value) {
                        if (value == "1") {
                          controller.loadUserInfo();
                        }
                      });
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Obx(
                          () => Container(
                            constraints: BoxConstraints(
                              maxWidth: 180.w,
                            ),
                            child: Text(
                              controller.userInfo.value?.nickname ?? "",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyles.medium(16.sp),
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              bottom: 5.h, left: 13.w, right: 10.w, top: 5.h),
                          child: ImageUtils.getImage(
                              Assets.imagesMineInfoEdit, 14.35.w, 14.35.w),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 5.h),

                  ///等级
                  Row(
                    children: [
                      Container(
                        height: 16.h,
                        constraints:
                            BoxConstraints(minWidth: 42.w, maxWidth: 100.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.h),
                          gradient: const LinearGradient(colors: [
                            Color(0xFFFFF3D1),
                            Color(0xFFFFD18D),
                          ]),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ImageUtils.getImage(
                                Assets.imagesMineInfoLevel, 16.w, 16.w),
                            Padding(
                              padding: EdgeInsets.only(left: 5.w, right: 5.w),
                              child: Obx(
                                () => Text(
                                  "${controller.userInfo.value?.monthlyPassDay ?? 0}天",
                                  textAlign: TextAlign.center,
                                  style: TextStyles.medium(
                                    13.sp,
                                    c: const Color(0xFF612103),
                                    h: 1.25,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 5.w),
                      Container(
                        height: 16.h,
                        constraints:
                            BoxConstraints(minWidth: 42.w, maxWidth: 100.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.h),
                          gradient: const LinearGradient(colors: [
                            Color(0xFFFFC8BF),
                            Color(0xFFFA7761),
                          ]),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ImageUtils.getImage(
                                Assets.imagesMineInfoDadaLevel, 16.w, 16.w),
                            Padding(
                              padding: EdgeInsets.only(left: 5.w, right: 5.w),
                              child: Obx(
                                () => Text(
                                  "${controller.userInfo.value?.daStickNum ?? 0}",
                                  textAlign: TextAlign.center,
                                  style: TextStyles.medium(
                                    13.sp,
                                    c: const Color(0xFF612103),
                                    h: 1.25,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 5.h),

                  ///ID、地址
                  Row(
                    children: [
                      Obx(
                        () => Text(
                          "${S.current.dadaID}：${controller.userInfo.value?.dadaNo ?? 0}",
                          style: TextStyles.normal(12.sp,
                              c: AppTheme
                                  .themeData.textTheme.bodyMedium?.color),
                        ),
                      ),
                      SizedBox(
                        width: 15.w,
                      ),
                      Obx(
                        () => Container(
                          constraints: BoxConstraints(
                            maxWidth: 150.w,
                          ),
                          child: Text(
                            "${S.current.ipAddress}：${controller.userInfo.value?.ipRegion ?? ""}",
                            overflow: TextOverflow.ellipsis,
                            style: TextStyles.normal(12.sp,
                                c: AppTheme
                                    .themeData.textTheme.bodyMedium?.color),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const Spacer(),
            ],
          ),

          ///用户Profile页
          Positioned(
            right: 0,
            child: Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: GestureDetector(
                onTap: () {
                  Get.toNamed(GetRouter.userProfile)?.then((value) {
                    controller.update();
                  });
                },
                child: Container(
                  width: 60.w,
                  height: 30.h,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color:
                        AppTheme.themeData.colorScheme.surface.withOpacity(0.5),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(15.h),
                      bottomLeft: Radius.circular(15.h),
                    ),
                  ),
                  child: Text(
                    S.current.myPage,
                    style: TextStyles.normal(14.sp,
                        c: AppTheme.themeData.textTheme.bodyMedium?.color),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionWidget() {
    return Container(
      height: 90.h,
      margin: EdgeInsets.only(top: 20.h, left: 15.w, right: 15.w),
      padding: EdgeInsets.only(left: 33.w, right: 33.w, top: 11.h),
      decoration: BoxDecoration(
        color: AppTheme.themeData.colorScheme.surface,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildFunctionItemWidget(
            Assets.imagesMineMonthCard,
            S.current.monthCard,
            () async {
              bool isOpenTeenMode =
                  await Get.find<MainController>().checkCurrentTeenModeStatus();
              if (isOpenTeenMode) {
                return;
              }
              Get.toNamed(GetRouter.rechargeMonthCard);
            },
          ),
          _buildFunctionItemWidget(
            Assets.imagesMineDadaCoin,
            S.current.dadaCoin,
            () async {
              bool isOpenTeenMode =
                  await Get.find<MainController>().checkCurrentTeenModeStatus();
              if (isOpenTeenMode) {
                return;
              }
              Get.toNamed(GetRouter.recharge);
            },
          ),
          _buildFunctionItemWidget(
            Assets.imagesMineExchangeShop,
            "商城",
            () {
              Get.toNamed(GetRouter.store);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionItemWidget(String icon, String title, Function() onTap,
      {double? blur}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          ClipOval(
            child: ImageFiltered(
              imageFilter:
                  ImageFilter.blur(sigmaX: blur ?? 0, sigmaY: blur ?? 0),
              child: ImageUtils.getImage(icon, 40.w, 40.w,
                  placeholder:
                      blur != null ? Assets.imagesAvatarPlaceholder : null),
            ),
          ),
          SizedBox(height: 5.h),
          Text(
            title,
            style: TextStyles.normal(
              14.sp,
              c: AppTheme.themeData.textTheme.bodyMedium?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListWidget() {
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: AppTheme.themeData.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            blurRadius: 4.w,
            offset: Offset(0, 3.h),
            color: AppTheme.themeData.colorScheme.shadow,
          )
        ],
      ),
      child: Column(
        children: controller.list.map((e) {
          int index = controller.list.indexOf(e);
          bool isLast = index == controller.list.length - 1;
          return InkWell(
            onTap: e["onTap"],
            child: Container(
              height: 56.h,
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              decoration: !isLast
                  ? BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1,
                          color: AppTheme.themeData.dividerTheme.color ??
                              AppTheme.themeData.dividerColor,
                        ),
                      ),
                    )
                  : null,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      ImageUtils.getImage(e["icon"], 18.w, 18.w),
                      SizedBox(width: 9.w),
                      Text(e["title"], style: TextStyles.normal(16)),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 5.w),
                    child: ImageUtils.getImage(
                        Assets.imagesCommonListMore, 8.w, 14.w),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
