import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class AccountLogOffDialog extends StatefulWidget {
  final Function() onConfirm;

  const AccountLogOffDialog({super.key, required this.onConfirm});

  @override
  State<AccountLogOffDialog> createState() => _AccountLogOffDialogState();
}

class _AccountLogOffDialogState extends State<AccountLogOffDialog> {
  @override
  Widget build(BuildContext context) {
    DateTime endTime = DateTime.now().add(const Duration(days: 15));
    String endTimeStr = TimeUtils.dateFormatString(
        dateTime: endTime, formatList: [yyyy, "年", mm, "月", dd, "日"]);

    return GradientWidget(
      width: 270.w,
      // height: 250.h,
      colors: const [
        AppColors.colorFFF3BF69,
        Colors.white,
      ],
      stops: const [0, 0.4],
      borderRadius: BorderRadius.circular(20.r),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Text(
              "确定注销账号？",
              style: TextStyles.medium(16.sp, w: FontWeight.w600),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h, left: 29.w, right: 29.w),
            child: Text(
              "15天后账号将注销成功。再次之前若重新登录该账号，注销申请将自动撤销。",
              style: TextStyles.common(14.sp, AppColors.colorFF666666, h: 1.3),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 6.h, left: 29.w, right: 29.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.error,
                  size: 16.w,
                  color: AppColors.colorFFF38043,
                ),
                Padding(
                  padding: EdgeInsets.only(left: 5.w),
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: 190.w,
                    ),
                    child: Text(
                      "账号将在$endTimeStr 彻底删除",
                      maxLines: 2,
                      style: TextStyles.common(14.sp, AppColors.colorFFF38043,
                          h: 1.3),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h, bottom: 30.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    width: 100.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(35.h / 2),
                      border:
                          Border.all(color: AppColors.colorFF999999, width: 1),
                    ),
                    child: Text(
                      "取消",
                      style:
                          TextStyles.normal(16.sp, c: AppColors.colorFF666666),
                    ),
                  ),
                ),
                SizedBox(
                  width: 11.5.w,
                ),
                GestureDetector(
                  onTap: () {
                    widget.onConfirm.call();
                    Get.back();
                  },
                  child: Container(
                    width: 100.w,
                    height: 35.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(35.h / 2),
                      gradient: const LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            Color(0xFFA0F6A5),
                            Color(0xFF58C75D),
                          ]),
                    ),
                    child: Text(
                      "同意",
                      style:
                          TextStyles.normal(16.sp, c: AppColors.colorFF344F3D),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
