import 'dart:ffi';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_setting_list_item_widget.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/mine/my_account/account_log_off_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/login_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MyAccountPage extends StatefulWidget {
  const MyAccountPage({super.key});

  @override
  State<MyAccountPage> createState() => _MyAccountPageState();
}

class _MyAccountPageState extends State<MyAccountPage> {
  RxBool isSetPassword = false.obs; // 新增状态变量

  @override
  void initState() {
    super.initState();

    _fetchPasswordStatus();
  }

  @override
  Widget build(BuildContext context) {
    String phoneNumber = "";
    if (UserService().user?.phone?.isNotEmpty == true &&
        UserService().user!.phone!.length >= 11) {
      phoneNumber = UserService().user!.phone!.replaceRange(8, 11, "***");
    }
    return Scaffold(
      appBar: CustomAppBar(
        title: "我的账号",
      ),
      body: ListView(
        children: [
          CommonSettingListItemWidget(
            title: "手机换绑",
            rightWidget: Row(
              children: [
                Text(
                  phoneNumber,
                  style: TextStyles.common(16.sp, AppColors.colorFF666666),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 5.w),
                  child: ImageUtils.getImage(
                      Assets.imagesCommonListMore, 8.w, 14.h),
                ),
              ],
            ),
            onTap: () {
              Get.toNamed(GetRouter.phoneChange)?.then(
                (value) {
                  if (value != null) {
                    setState(() {
                      UserService().user?.phone = value;
                    });
                  }
                },
              );
            },
          ),
          CommonSettingListItemWidget(
            title: "设置密码",
            rightWidget: Row(
              children: [
                Text(
                  "******",
                  style: TextStyles.common(16.sp, AppColors.colorFF666666),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 5.w),
                  child: ImageUtils.getImage(
                      Assets.imagesCommonListMore, 8.w, 14.h),
                ),
              ],
            ),
            onTap: () {
              if (isSetPassword.value) {
                Get.toNamed(GetRouter.updatePassword);
              } else {
                Get.toNamed(GetRouter.setupPassword)?.then((refresh) {
                  if (refresh == true) {
                    isSetPassword.value = true;
                  }
                });
              }
            },
          ),
          CommonSettingListItemWidget(
            title: "注销账号",
            subTitle: "",
            onTap: () {
              ToastUtils.showDialog(
                dialog: AccountLogOffDialog(
                  onConfirm: () async {
                    bool success = await ApiService().logoffAccount();
                    if (success) {
                      LoginUtils.logOut(force: true);
                    }
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  //获取密码状态
  Future<void> _fetchPasswordStatus() async {
    bool result = await ApiService().isSetPassword();
    isSetPassword.value = result;
  }
}
