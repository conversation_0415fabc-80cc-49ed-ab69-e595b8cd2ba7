import 'dart:math';
import 'dart:ui';

import 'package:date_format/date_format.dart';
import 'package:get/get.dart';

/// 日期格式化类型
enum DateFormatType {
  /// 日/月/年 时间 样式
  ddmmyyyyhhnnss,
  ddmmyyyy,
  yyyymmdd, // 年/月/日
  hhnnss,
}

class TimeUtils {
  ///两个日期是否为一天
  static bool isSameDay(DateTime date1, DateTime date2) {
    final String dateStr1 =
        dateFormatString(dateTime: date1, formatType: DateFormatType.yyyymmdd);
    final String dateStr2 =
        dateFormatString(dateTime: date2, formatType: DateFormatType.yyyymmdd);
    return dateStr1 == dateStr2;
  }

  ///是否为今天
  static bool isToday(DateTime date) {
    return TimeUtils.isSameDay(date, DateTime.now());
  }

  ///格式化显示动态时间
  static String formatPostDate(String dateStr, {List<String>? formats}) {
    DateTime nowDate = DateTime.now();
    if (dateStr.isNotEmpty) {
      DateTime dateTime = DateTime.parse(dateStr);

      ///与当前时间间隔超出一年
      if (dateTime.year != DateTime.now().year) {
        if (formats != null) {
          return formatDate(dateTime, formats);
        }
        return formatDate(
            dateTime, [yyyy, "年", m, "月", d, "日", " ", HH, ":", nn]);
      } else {
        if (nowDate.month - dateTime.month >= 1) {
          if (formats != null) {
            return formatDate(dateTime, formats);
          }
          return formatDate(dateTime, [m, "月", d, "日", " ", HH, ":", nn]);
        } else if (nowDate.day - dateTime.day > 1) {
          return "${nowDate.day - dateTime.day}天前";
        } else if (nowDate.day - dateTime.day == 1) {
          return "昨天";
        } else if (nowDate.hour - dateTime.hour > 1) {
          return "${nowDate.hour - dateTime.hour}小时前";
        } else {
          int inMinutes = nowDate.difference(dateTime).inMinutes.abs();
          if (inMinutes >= 60) {
            return "1小时前";
          } else if (inMinutes <= 60 && inMinutes > 1) {
            return "$inMinutes分钟前";
          } else if (inMinutes == 1) {
            return "1分钟前";
          } else {
            return "刚刚";
          }
        }
      }
    }
    return dateStr;
  }

  ///会话时间格式化
  static String formatConversationDate(int timestamp) {
    DateTime nowDate = DateTime.now();
    if (timestamp > 0) {
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);

      ///与当前时间间隔超出一年
      if (dateTime.year != DateTime.now().year) {
        return formatDate(dateTime, [yyyy, "年", m, "月", d, "日"]);
      } else {
        if (nowDate.month - dateTime.month >= 1) {
          return formatDate(dateTime, [m, "月", d, "日"]);
        } else if (nowDate.day - dateTime.day == 1) {
          return "昨天";
        } else {
          return formatDate(dateTime, [HH, ":", nn]);
        }
      }
    }
    return "";
  }

  ///通过生日日期计算年龄
  static int calculateAgeWithBirthday(String? birthday) {
    if (birthday == null) {
      return 0;
    }
    DateTime birthdayDate = DateTime.parse(birthday);
    DateTime nowDate = DateTime.now();
    if (birthdayDate.isBefore(nowDate)) {
      int years = nowDate.year - birthdayDate.year;
      if (nowDate.month < birthdayDate.month ||
          (nowDate.month == birthdayDate.month &&
              nowDate.day < birthdayDate.day)) {
        years--;
      }
      return max(0, years);
    }
    return 0;
  }

  ///按指定格式显示时间
  static String dateFormatString({
    required DateTime dateTime,
    List<String>? formatList,
    DateFormatType? formatType,
    DateLocale? locale,
  }) {
    List<String> formats = [];
    if (formatList != null && formatList.isNotEmpty) {
      formats = formatList;
    } else if (formatType != null) {
      switch (formatType) {
        case DateFormatType.ddmmyyyyhhnnss:
          formats = [dd, "/", mm, "/", yyyy, " ", HH, ':', nn, ':', ss];
          break;
        case DateFormatType.ddmmyyyy:
          formats = [dd, "/", mm, "/", yyyy];
          break;
        case DateFormatType.yyyymmdd:
          formats = [yyyy, "/", mm, "/", dd];
          break;
        case DateFormatType.hhnnss:
          formats = [HH, ':', nn, ':', ss];
          break;
        default:
          formats = [dd, "/", mm, "/", yyyy, " ", HH, ':', nn, ':', ss];
          break;
      }
    }
    Locale? deviceLocale = Get.deviceLocale;
    DateLocale locale = const SimplifiedChineseDateLocale();
    if (deviceLocale?.languageCode == "en") {
      locale = const EnglishDateLocale();
    }
    return formatDate(dateTime, formats, locale: locale);
  }

  ///将时间字符串格式转换成时间戳
  static int timestampWithDateString(String? dateString) {
    if (dateString != null) {
      DateTime dateTime = DateTime.parse(dateString);
      return dateTime.millisecondsSinceEpoch;
    }
    return 0;
  }

  ///将birthday字符串转换成数字(例如："2024-10-05" => 20241005)
  static int transformBirthdayDateStrToInt(String? birthdayStr) {
    if (birthdayStr != null) {
      String? birthdayTime = birthdayStr.split(" ").first;
      if (birthdayTime.isNotEmpty == true && birthdayTime.contains("-")) {
        String birthday = birthdayTime.replaceAll("-", "");
        int result = int.parse(birthday);
        return result;
      }
    }
    return 0;
  }

  ///计算倒计时时间（timestamp是以秒为单位)
  static String countdownTime(int timestamp,
      {bool? showDay = true,
      bool? showHour = true,
      bool? showMinute = true,
      bool? showSecond = true}) {
    int day = (timestamp / 3600) ~/ 24;
    int hour = (timestamp ~/ 3600) % 24;
    int minute = timestamp % 3600 ~/ 60;
    int second = timestamp % 60;

    var str = '';
    if (day > 0 && showDay == true) {
      hour += 24 * day;
    }

    if (hour > 0 && showHour == true) {
      str = '$str$hour:';
    }

    if (showMinute == true) {
      if (minute / 10 < 1 && showMinute == true) {
        str = '${str}0$minute:';
      } else {
        str = '$str$minute:';
      }
    }

    if (showSecond == true) {
      if (second / 10 < 1) {
        str = '${str}0$second';
      } else {
        str = str + second.toString();
      }
    }

    return str;
  }
}
