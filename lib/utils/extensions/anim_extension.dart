import 'package:flutter/animation.dart';

extension AnimExtension<T> on Animation<T> {
  ///动画completed状态时执行
  void doOnAnimComplete(void Function() function) {
    if (status == AnimationStatus.completed) {
      function.call();
    } else if (status != AnimationStatus.reverse) {
      void completeListener(status) {
        if (status == AnimationStatus.completed) {
          function.call();
          removeStatusListener(completeListener);
        }
      }

      addStatusListener(completeListener);
    }
  }
}
