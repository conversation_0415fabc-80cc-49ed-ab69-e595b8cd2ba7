import 'package:flutter/material.dart';

/// @fileName string_extension.dart
/// @description String 类的扩展类， 为标准库扩展一些自定义方法
extension StringExtension on String {
  /// 判断类型
  bool get isNumeric => int.tryParse(this) != null ? true : false;

  /// methodName fixAutoLines
  /// description 处理一些英文换行
  /// date 2022/6/9 09:41
  /// author LiuChuanan
  String fixAutoLines() {
    return Characters(this).join('\u{200B}');
  }

}