import 'package:dada/model/user_info_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:get/get.dart';
import 'package:sp_util/sp_util.dart';

class LoginUtils {
  static bool isLogined() {
    String? token = SpUtil.getString("token");
    UserInfoEntity? infoEntity = UserService().user;
    String? isRegister = SpUtil.getString("isRegister");
    if (token != null && infoEntity?.id != null && isRegister == "1") {
      return true;
    }
    return false;
  }

  static logOut({bool? force}) async {
    if (force == true) {
      UserService().removeUser();
      SpUtil.clear();
      GlobalFloatingManager().closeMiniWindow();
      ChatIMManager.sharedInstance.logout();
      ChatIMManager.sharedInstance.onClose();
      UserService().onClose();
      if (Get.isDialogOpen == true) {
        Get.back();
      }
      Get.offAllNamed(GetRouter.welcome);
    } else {
      bool success = await ApiService().logout();
      if (success) {
        GlobalFloatingManager().closeMiniWindow();
        await ChatIMManager.sharedInstance.logout();
        ChatIMManager.sharedInstance.onClose();
        UserService().onClose();
        if (Get.isDialogOpen == true) {
          Get.back();
        }
        Get.offAllNamed(GetRouter.welcome);
      }
    }
  }
}
