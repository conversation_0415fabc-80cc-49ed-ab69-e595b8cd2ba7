import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';

class ToastUtils {
  static void showToast(String msg) {
    EasyLoading.instance
      ..userInteractions = true
      ..contentPadding = const EdgeInsets.symmetric(
        vertical: 15.0,
        horizontal: 20.0,
      );
    if (msg.trim().isNotEmpty) EasyLoading.showToast(msg);
  }

  static void showError(String msg) {
    EasyLoading.instance
      ..userInteractions = true
      ..contentPadding = const EdgeInsets.symmetric(
        vertical: 15.0,
        horizontal: 20.0,
      );
    if (msg.trim().isNotEmpty) EasyLoading.showError(msg);
  }

  static void showSuccess(String msg) {
    EasyLoading.instance
      ..userInteractions = true
      ..contentPadding = const EdgeInsets.symmetric(
        vertical: 15.0,
        horizontal: 20.0,
      );
    if (msg.trim().isNotEmpty) EasyLoading.showSuccess(msg);
  }

  static void showLoading({String? message}) {
    EasyLoading.instance
      // ..displayDuration = const Duration(milliseconds: 2000)
      // ..indicatorType = EasyLoadingIndicatorType.fadingCircle
      ..loadingStyle = EasyLoadingStyle.dark
      // ..indicatorSize = 45.0
      // ..radius = 10.0
      // ..progressColor = Colors.yellow
      ..contentPadding = EdgeInsets.zero
      ..backgroundColor = Colors.transparent
      // ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..boxShadow = <BoxShadow>[]
      // ..maskColor = Colors.blue.withOpacity(0.5)
      ..userInteractions = false
      ..dismissOnTap = false;
    EasyLoading.show(
      indicator: Container(
        width: 80.w,
        height: 70.w,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 60.w,
              height: 50.w,
              child: const SVGASimpleImage(
                assetsName: Assets.svgaLoading,
              ),
            ),
            message != null
                ? Text(
                    message,
                    style: TextStyle(color: Colors.white, fontSize: 14.sp),
                  )
                : const SizedBox()
          ],
        ),
      ),
    );
  }

  static void hideLoading() {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
  }

  ///底部选择视图
  static Future showBottomSheet(List<String> data,
      {String? title,
      int? destructiveIndex,
      Function(int index)? onTap,
      Color? titleColor,
      double? titleFontSize}) {
    return Get.bottomSheet(
      Container(
        padding: EdgeInsets.only(bottom: Get.context!.mediaQueryPadding.bottom),
        decoration: BoxDecoration(
          color: AppTheme.themeData.colorScheme.surface,
          borderRadius: BorderRadius.circular(15.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            title != null && title.isNotEmpty
                ? Container(
                    height: 50.h,
                    alignment: Alignment.center,
                    child: Text(
                      title,
                      style: TextStyle(
                        color: titleColor ??
                            AppTheme.themeData.textTheme.labelMedium?.color,
                        fontSize: titleFontSize ?? 16.sp,
                      ),
                    ),
                  )
                : Container(),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: data.map((e) {
                int index = data.indexOf(e);
                return GestureDetector(
                  onTap: () {
                    Get.back();
                    onTap?.call(index);
                  },
                  child: Container(
                    height: 50.h,
                    width: double.infinity,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: AppTheme.themeData.dividerColor,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Text(
                      e,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: index == destructiveIndex
                            ? AppColors.colorFFEA4A4A
                            : AppTheme.themeData.textTheme.labelMedium?.color,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Container(
                height: 50.w,
                alignment: Alignment.center,
                child: Text(
                  "取消",
                  style: TextStyle(
                    color: AppTheme.themeData.textTheme.labelMedium?.color
                        ?.withOpacity(0.66),
                    fontSize: 16.sp,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///显示顶部弹窗
  static Future showTopDialog(
      {Widget? child,
      double? marginTop,
      bool barrierDismissible = true,
      Color? color,
      double? radius}) {
    return Get.dialog(
        Stack(
          children: [
            GestureDetector(
              onTap: () {
                if (barrierDismissible == true) {
                  Get.back();
                }
              },
              child: Container(
                height: 1.sh,
                color: Colors.transparent,
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: ScreenUtil().statusBarHeight + (marginTop ?? 0)),
              padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
              decoration: BoxDecoration(
                color: color ?? Colors.white,
                borderRadius: BorderRadius.circular(radius ?? 20),
              ),
              width: double.infinity,
              child: child,
            ),
          ],
        ),
        barrierColor: Colors.transparent,
        barrierDismissible: barrierDismissible,
        useSafeArea: false);
  }

  static Future showBottomDialog(
    Widget widget, {
    double? cornerRadius,
    Color? backgroundColor,
    Color? barrierColor,
    bool? enableDrag = true,
    bool? isScrollControlled = true,
    bool? showClose,
    Function? dismissAction,
  }) {
    return Get.bottomSheet(
      Stack(
        children: [
          widget,
          Visibility(
            visible: showClose == true,
            child: Positioned(
              right: 15.w,
              top: 15.w,
              child: GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Icon(
                  Icons.close,
                  size: 20.w,
                  color: AppColors.colorFF666666,
                ),
              ),
            ),
          ),
        ],
      ),
      shape: RoundedRectangleBorder(
        borderRadius:
            BorderRadius.vertical(top: Radius.circular(cornerRadius ?? 0)),
      ),
      isScrollControlled: true,
      enableDrag: enableDrag ?? true,
      backgroundColor: backgroundColor,
      barrierColor: Colors.black45,
    );
  }

  static Future showDialog(
      {Widget? dialog,
      String? title,
      String? topImg,
      String? content,
      bool? hideTopImg,
      bool? hideCloseBtn,
      bool? hideCancelBtn,
      bool? barrierDismissible,
      List<Color>? colors,
      String? cancelBtnTitle,
      String? confirmBtnTitle,
      TextStyle? contentStyle,
      Function()? onConfirm,
      Function()? onCancel}) {
    return Get.dialog(
        Dialog(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          insetPadding: EdgeInsets.zero,
          child: dialog ??
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 270.w,
                    // constraints: BoxConstraints(
                    //   minHeight: hideTopImg == true ? 160.h : 190.h,
                    // ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.r),
                      gradient: LinearGradient(
                          colors: colors ??
                              [
                                const Color(0xFFFFD198),
                                const Color(0xFFFFFFFF)
                              ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: const [0, 0.32]),
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Visibility(
                              visible: hideTopImg != true,
                              child: Padding(
                                padding: EdgeInsets.only(top: 17.w),
                                child: ImageUtils.getImage(
                                    Assets.imagesCommonAlertTopIcon,
                                    43.w,
                                    37.h),
                              ),
                            ),
                            Visibility(
                              visible: hideTopImg == true &&
                                  title?.isNotEmpty == true,
                              child: Padding(
                                padding: EdgeInsets.only(top: 17.w),
                                child: Text(
                                  title ?? "",
                                  style: TextStyles.medium(16.sp),
                                ),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(
                                top: hideTopImg == true &&
                                        !(title?.isNotEmpty == true)
                                    ? 20.h
                                    : 0.h,
                              ),
                              padding: EdgeInsets.only(
                                  top: 25.h, left: 15.w, right: 15.w),
                              child: Text(
                                content ?? "",
                                textAlign: TextAlign.center,
                                maxLines: 5,
                                overflow: TextOverflow.ellipsis,
                                style: contentStyle ??
                                    (title?.isNotEmpty == true
                                        ? TextStyles.common(
                                            14.sp, AppColors.colorFF666666,
                                            h: 1.5)
                                        : TextStyles.normal(16.sp)),
                              ),
                            ),
                            SizedBox(height: 25.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Visibility(
                                  visible: hideCancelBtn != true,
                                  child: GestureDetector(
                                    onTap: () {
                                      Get.back();
                                      onCancel?.call();
                                    },
                                    child: Container(
                                      width: 100.w,
                                      height: 35.h,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(35.h / 2),
                                        border: Border.all(
                                            color: AppTheme.themeData.textTheme
                                                    .labelSmall?.color ??
                                                const Color(0xFF999999),
                                            width: 1),
                                      ),
                                      child: Text(
                                        cancelBtnTitle ?? S.current.cancel,
                                        style: TextStyles.normal(16.sp,
                                            c: AppTheme.themeData.textTheme
                                                .bodyMedium?.color),
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  width: hideCancelBtn != true ? 11.5.w : 0,
                                ),
                                GestureDetector(
                                  onTap: () {
                                    Get.back();
                                    onConfirm?.call();
                                  },
                                  child: Container(
                                    width: 100.w,
                                    height: 35.h,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      borderRadius:
                                          BorderRadius.circular(35.h / 2),
                                      gradient: const LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: [
                                            Color(0xFFA0F6A5),
                                            Color(0xFF58C75D),
                                          ]),
                                    ),
                                    child: Text(
                                      confirmBtnTitle ?? S.current.sure,
                                      style: TextStyles.normal(16.sp,
                                          c: AppTheme.themeData.colorScheme
                                              .onSecondaryContainer),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 30.h),
                          ],
                        ),
                        Visibility(
                          visible: hideCloseBtn != true,
                          child: Positioned(
                            top: 10.h,
                            right: 10.w,
                            child: GestureDetector(
                              onTap: () {
                                onCancel?.call();
                                Get.back();
                              },
                              child: Icon(
                                Icons.close,
                                color: AppTheme
                                    .themeData.textTheme.titleSmall?.color,
                                size: 20.w,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
        ),
        barrierDismissible: barrierDismissible ?? true);
  }
}
