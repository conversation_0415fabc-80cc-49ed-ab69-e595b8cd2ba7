import 'dart:io';

import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

class ImagePickerUtil {
  ///图片或视频
  static Future<List<AssetEntity>?> selectAsset(
      {List<AssetEntity>? selectedAssets,
      int maxAssets = 1,
      bool isImage = true}) async {
    final entityList = await AssetPicker.pickAssets(
      Get.context!,
      pickerConfig: AssetPickerConfig(
        maxAssets: maxAssets,
        selectedAssets: selectedAssets,
        requestType: isImage ? RequestType.image : RequestType.video,
      ),
    );

    return entityList;
  }

  ///图片和视频
  static Future<List<AssetEntity>?> selectAssets(
      {List<AssetEntity>? selectedAssets, int maxAssets = 1}) async {
    final entityList = await AssetPicker.pickAssets(
      Get.context!,
      pickerConfig: AssetPickerConfig(
        pathNameBuilder: (path) => switch (path) {
          final p when p.isAll => "最近",
          _ => path.name,
        },
        maxAssets: maxAssets,
        selectedAssets: selectedAssets,
        specialPickerType: SpecialPickerType.wechatMoment,
        requestType: RequestType.common,
      ),
    );
    return entityList;
  }

  ///从相机拍照或录视频
  static Future<AssetEntity?> takeAsset(
      {bool? enableRecording, bool? enableAudio}) async {
    final entity = await CameraPicker.pickFromCamera(
      Get.context!,
      pickerConfig: CameraPickerConfig(
        enableRecording: enableRecording ?? false,
        enableAudio: enableAudio ?? false,
        maximumRecordingDuration: const Duration(seconds: 30),
      ),
    );
    return entity;
  }

  ///头像图片选择
  static Future<String?> selectAvatarImageAndUpload(ImageSource source) async {
    XFile? imageFile = await ImagePicker().pickImage(source: source);
    if (imageFile != null) {
      final imgUrl =
          await ApiService().uploadFile(imageFile.path, showLoading: true);
      if (imgUrl != null) {
        return imgUrl;
      }
    }
    return null;
  }

  ///相册或相机权限检查
  ///type 1-相册 2-相机
  static Future<bool> checkPermission(
      int type, String title, String desc) async {
    if (type == 1) {
      PermissionState status = await PhotoManager.getPermissionState(
          requestOption: PermissionRequestOption());
      if (status != PermissionState.authorized &&
          status != PermissionState.limited) {
        if (Platform.isAndroid) {
          ToastUtils.showTopDialog(
              barrierDismissible: false,
              child: PermissionUseDescDialog(title: title, desc: desc));
        }
        status = await PhotoManager.requestPermissionExtend();
        if (Get.isDialogOpen == true) {
          Get.back();
        }
        if (status != PermissionState.authorized &&
            status != PermissionState.limited) {
          return false;
        }
      }
    } else {
      PermissionStatus status = await Permission.camera.status;
      if (status.isGranted || status.isDenied) {
        if (Platform.isAndroid) {
          ToastUtils.showTopDialog(
              barrierDismissible: false,
              child: PermissionUseDescDialog(title: title, desc: desc));
        }
        status = await Permission.camera.request();
        if (Get.isDialogOpen == true) {
          Get.back();
        }
        if (status != PermissionStatus.granted) {
          return false;
        }
      }
    }
    return true;
  }

  static Future<String?> getEntityPath(AssetEntity? asset) async {
    File? file = await asset?.loadFile(isOrigin: false);
    return file?.path;
  }
}
