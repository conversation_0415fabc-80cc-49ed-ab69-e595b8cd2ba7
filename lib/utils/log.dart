import 'package:logger/logger.dart';
import 'package:dada/utils/inline_pritty_printer.dart';

class Log {
  static final Logger _logger = Logger(
    printer: InlinePrettyPrinter(stackTraceBeginIndex: 2, lineLength: 120),
  );

  static void v(dynamic message) {
    _logger.v(message);
  }

  static void d(dynamic message) {
    _logger.d(message);
  }

  static void i(dynamic message) {
    _logger.i(message);
  }

  static void w(dynamic message) {
    _logger.w(message);
  }

  static void e(dynamic message) {
    _logger.e(message);
  }

  static void wtf(dynamic message) {
    _logger.wtf(message);
  }
}
