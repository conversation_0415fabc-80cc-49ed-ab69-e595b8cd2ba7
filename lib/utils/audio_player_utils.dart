import 'dart:async';
import 'dart:io';
import 'package:audio_session/audio_session.dart';
import 'package:dada/utils/log.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

enum AudioPlayerState {
  idle,
  playing,
  pause,
  complete,
}

class AudioPlayerUtils extends GetxController {
  AudioPlayer? _audioPlayer;
  AudioSession? session;
  Duration? duration;
  int? current; //seconds
  AudioPlayerState state = AudioPlayerState.idle;

  bool get playing => state == AudioPlayerState.playing;

  final StreamController<AudioPlayerState> _stateStreamController =
      StreamController<AudioPlayerState>.broadcast();
  final StreamController<int?> _durationStreamController =
      StreamController<int?>.broadcast();

  ///监听播放器状态
  Stream<AudioPlayerState>? get stateStream => _stateStreamController.stream;

  ///监听播放器当前播放进度
  Stream<int?>? get durationStream => _durationStreamController.stream;

  @override
  void onInit() {
    super.onInit();

    _initAudioPlayer();
  }

  Future<void> _initAudioPlayer() async {
    _audioPlayer = AudioPlayer();
    _audioPlayer?.playerStateStream.listen((event) async {
      state = (event.playing && event.processingState == ProcessingState.ready)
          ? AudioPlayerState.playing
          : AudioPlayerState.idle;
      if (event.processingState == ProcessingState.completed) {
        ///播放完成后充值播放器状态
        state = AudioPlayerState.complete;
        _stateStreamController.add(state);
        update();
        await stop();
      }
      _stateStreamController.add(state);
      Log.i('playerStateStream $event');
    });

    _audioPlayer?.positionStream.listen((event) {
      current = event.inSeconds;
      _durationStreamController.add(current);
      update();
      Log.i('positionStream $event');
    });

    if (Platform.isIOS) {
      await session?.configure(AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions:
            AVAudioSessionCategoryOptions.allowBluetooth |
                AVAudioSessionCategoryOptions.defaultToSpeaker,
        avAudioSessionMode: AVAudioSessionMode.spokenAudio,
        avAudioSessionRouteSharingPolicy:
            AVAudioSessionRouteSharingPolicy.defaultPolicy,
        avAudioSessionSetActiveOptions:
            AVAudioSessionSetActiveOptions.notifyOthersOnDeactivation,
        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.voiceCommunication,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: true,
      ));
    } else {
      await session?.configure(const AudioSessionConfiguration());
    }
  }

  Future<Duration?> setUrl(String url) async {
    if (_audioPlayer == null) {
      await _initAudioPlayer();
    }
    if (url.startsWith("http://") || url.startsWith("https://")) {
      duration = await _audioPlayer?.setUrl(url);
      return duration;
    } else if (url.startsWith("/")) {
      duration = await _audioPlayer?.setFilePath(url);
      return duration;
    } else {
      duration = await _audioPlayer?.setAsset(url);
      return duration;
    }
  }

  Future<void> play() async {
    await _audioPlayer?.play();
    update();
  }

  Future<void> pause() async {
    await _audioPlayer?.pause();
    state = AudioPlayerState.pause;
    update();
  }

  Future<void> stop() async {
    await _audioPlayer?.stop();
    await _audioPlayer?.seek(Duration.zero, index: 0);
    state = AudioPlayerState.idle;
    update();
  }

  void cancelPlayerSubscriptions() {
    if (stateStream != null) {
      _stateStreamController.close();
    }

    if (durationStream != null) {
      _durationStreamController.close();
    }
  }

  @override
  void dispose() async {
    await _audioPlayer?.dispose();
    cancelPlayerSubscriptions();
    super.dispose();
  }
}
