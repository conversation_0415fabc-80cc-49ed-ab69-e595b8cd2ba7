import 'dart:async';
import 'dart:io';
import 'package:alipay_kit/alipay_kit.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/model/pay_info.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:wechat_kit/wechat_kit.dart';
import 'package:in_app_purchase_storekit/src/types/app_store_product_details.dart';

class PayUtils {
  static PayUtils? _sharedInstance;

  static PayUtils get sharedInstance => _sharedInstance ??= PayUtils._();

  PayUtils._() {
    init();
  }

  factory PayUtils() => sharedInstance;

  StreamSubscription<AlipayResp>? _alipayRespSubs;
  StreamSubscription<WechatResp>? _wechatRespSubs;
  StreamSubscription<List<PurchaseDetails>>? _appleRespSubs;

  ///苹果支付
  final InAppPurchase _applePay = InAppPurchase.instance;

  ///支付回调
  void Function(bool)? _callback;

  ///支付订单信息
  PayInfo? _payInfo;

  void init() {
    ///微信需要先注册
    WechatKitPlatform.instance.registerApp(
        appId: AppConfig.wxAppId, universalLink: AppConfig.iOSUniversalLink);

    ///注册监听回调
    registerListener();
  }

  void registerListener() {
    ///注册Alipay回调监听
    _alipayRespSubs = AlipayKitPlatform.instance.payResp().listen((resp) {
      _listenAlipayResp(resp);
    }, onDone: () {
      _alipayRespSubs?.cancel();
      _callback?.call(false);
      debugPrint("Alipay支付失败");
    }, onError: (error) {
      _callback?.call(false);
      debugPrint("Alipay支付失败");
    });

    ///注册微信支付回调监听
    _wechatRespSubs = WechatKitPlatform.instance.respStream().listen((resp) {
      _listenWechatResp(resp);
    }, onDone: () {
      _wechatRespSubs?.cancel();
      _callback?.call(false);
      debugPrint("Wechat支付失败");
    }, onError: (error) {
      _callback?.call(false);
      debugPrint("Wechat支付失败");
    });

    ///注册苹果支付回调监听
    _appleRespSubs =
        InAppPurchase.instance.purchaseStream.listen((purchaseDetailsList) {
      _listenAppleResp(purchaseDetailsList);
    }, onDone: () {
      _appleRespSubs?.cancel();
      _callback?.call(false);
      debugPrint("Apple支付失败");
    }, onError: (error) {
      _callback?.call(false);
      debugPrint("Apple支付失败");
    });
  }

  ///支付宝支付回调监听
  void _listenAlipayResp(AlipayResp resp) {
    final content = 'pay: ${resp.resultStatus} - ${resp.result}';
    debugPrint('_listenAlipayResp====${resp.toJson()}, \n content: $content');
    if (resp.isSuccessful) {
      _callback?.call(resp.isSuccessful);
    } else if (resp.isCancelled) {
      _callback?.call(false);
      ToastUtils.showToast('支付取消');
    } else {
      _callback?.call(false);
      ToastUtils.showToast('支付失败');
    }
  }

  ///微信支付回调监听
  void _listenWechatResp(WechatResp resp) {
    debugPrint('_listenWechatResp====${resp.toJson()}');
    if (resp is WechatPayResp) {
      if (resp.isSuccessful) {
        _callback?.call(resp.isSuccessful);
      } else if (resp.isCancelled) {
        _callback?.call(false);
        ToastUtils.showToast('支付取消');
      } else {
        _callback?.call(false);
        ToastUtils.showToast('支付失败');
      }
    }
  }

  ///苹果支付回调
  void _listenAppleResp(List<PurchaseDetails> purchaseDetailsList) async {
    for (PurchaseDetails purchase in purchaseDetailsList) {
      if (purchase.status == PurchaseStatus.pending) {
        ///等待支付完成
        debugPrint("Apple pay pending!");
      } else if (purchase.status == PurchaseStatus.canceled) {
        ///用户取消支付
        debugPrint("Apple pay canceled!");
        _callback?.call(false);
        _applePay.completePurchase(purchase);
      } else if (purchase.status == PurchaseStatus.error) {
        ///支付出错
        debugPrint("Apple pay failed， error is ${purchase.error}!");
        ToastUtils.hideLoading();
        _callback?.call(false);
      } else if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        checkApplePayInfo(purchase);
      }
    }
  }

  ///支付
  Future<void> pay(
      {required PayType payType,
      required PayInfo payInfo,
      void Function(bool)? callback}) async {
    _callback = callback;
    _payInfo = payInfo;
    if (payType == PayType.alipay) {
      AlipayKitPlatform.instance.pay(orderInfo: payInfo.alipayOrderString!);
    } else if (payType == PayType.wechat) {
      if (!await WechatKitPlatform.instance.isInstalled()) {
        ToastUtils.showToast('请先安装微信');
        return;
      }
      WechatKitPlatform.instance.pay(
        appId: AppConfig.wxAppId,
        partnerId: payInfo.wxPayPartnerId!,
        prepayId: payInfo.wxPayPrepayId!,
        package: 'Sign=WXPay',
        nonceStr: payInfo.wxPayNonceStr!,
        timeStamp: payInfo.wxPayTimeStamp!,
        sign: payInfo.wxPaySign!,
      );
    } else if (payType == PayType.apple) {
      // _startApplePay(productID: "300000001");
      _startApplePay(productID: payInfo.applePayProductID!);
    }
  }

  ///检测是否安装对应支付软件
  Future<bool> checkInstalled(PayType type) async {
    if (type == PayType.alipay) {
      return await AlipayKitPlatform.instance.isInstalled();
    } else if (type == PayType.wechat) {
      return await WechatKitPlatform.instance.isInstalled();
    } else if (type == PayType.apple) {
      return Platform.isIOS;
    }
    return false;
  }

  ///苹果支付处理
  Future<void> _startApplePay({required String productID}) async {
    bool available = await _applePay.isAvailable();
    if (!available) {
      ToastUtils.showToast("无法连接到商店，请检查网络");
      _callback?.call(false);
      return;
    }

    List<String> productIDs = [productID];
    ProductDetailsResponse response =
        await _applePay.queryProductDetails(productIDs.toSet());
    debugPrint("Apple Pay 获取商品结果：${response.toString()}");
    if (response.notFoundIDs.isNotEmpty) {
      debugPrint("Apple Pay 未找到指定商品：$productID");
      ToastUtils.showToast("支付失败");
      _callback?.call(false);
      return;
    }

    List<AppStoreProductDetails> products =
        response.productDetails as List<AppStoreProductDetails>;
    if (products.isNotEmpty) {
      resumePurchase();
      debugPrint("Apple Pay products: $products");

      try {
        AppStoreProductDetails productDetails =
            products.firstWhere((product) => product.id == productID);
        _applePay.buyConsumable(
          purchaseParam: PurchaseParam(
              productDetails: productDetails,
              applicationUserName: UserService().user?.id),
        );
      } catch (e) {
        debugPrint("Apple pay failed，error info is $e");
        ToastUtils.showToast("支付失败");
        _callback?.call(false);
      }
    }
  }

  ///支付成功校验
  void checkApplePayInfo(PurchaseDetails purchaseDetails) async {
    _applePay.completePurchase(purchaseDetails);

    // AppStoreProductDetails appStoreProductDetails =
    //     purchaseDetails as AppStoreProductDetails;
    bool success = await ApiService().verifyApplePayResult(
        orderNo: _payInfo!.orderCode!,
        productId: purchaseDetails.productID,
        receiptData: purchaseDetails.verificationData.serverVerificationData);
    if (success) {
      _callback?.call(true);
      debugPrint("Apple支付交易ID为: ${purchaseDetails.purchaseID}");
      debugPrint(
          "Apple支付验证收据为: ${purchaseDetails.verificationData.serverVerificationData}");
    } else {
      _callback?.call(false);
    }
  }

  ///获取恢复购买列表
  void getAvailablePurchases() async {
    // List<PurchaseDetails> purchaseDetailsList =
    //     await _applePay.restorePurchases();
    // debugPrint("Apple支付恢复购买列表: $purchaseDetailsList");
  }

  ///恢复购买
  void resumePurchase() {
    _applePay.restorePurchases();
  }

  ///移除监听
  void removeListener() {
    _alipayRespSubs?.cancel();
    _wechatRespSubs?.cancel();
    _appleRespSubs?.cancel();
    _alipayRespSubs = null;
    _wechatRespSubs = null;
    _appleRespSubs = null;
  }
}
