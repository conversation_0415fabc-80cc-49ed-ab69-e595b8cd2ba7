import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StringUtils {
  ///计算文本高度
  static double calculateTextHeight(String text,
      {double? maxWidth, TextStyle? textStyle}) {
    Size size =
        calculateTextSize(text, maxWidth: maxWidth, textStyle: textStyle);
    return size.height;
  }

  ///计算文本宽度
  static double calculateTextWidth(String text,
      {double? maxWidth, TextStyle? textStyle}) {
    Size size =
        calculateTextSize(text, maxWidth: maxWidth, textStyle: textStyle);
    return size.width;
  }

  ///计算文本Size
  static Size calculateTextSize(String text,
      {double? maxWidth, TextStyle? textStyle}) {
    if (text.isEmpty) {
      return Size.zero;
    }
    double textHeight = 0;
    double textWidth = 0;
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: textStyle ??
            TextStyle(
              fontSize: 16.sp, // 根据需要设置字体大小
            ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(minWidth: 0, maxWidth: maxWidth ?? double.infinity);
    textHeight = textPainter.size.height;

    // 获取文本的高度
    final boxes = textPainter.getBoxesForSelection(
      TextSelection(baseOffset: 0, extentOffset: text.length),
    );

    final top = boxes.first.top;
    final left = boxes.first.left;
    final right = boxes.first.right;
    final bottom = boxes.last.bottom;
    textHeight = bottom - top;
    textWidth = right - left;

    return Size(textWidth, textHeight);
  }

  ///检查文本是否为纯数字
  static bool isPureNumber(String text) {
    return RegExp(r'^\d+$').hasMatch(text);
  }

  static bool isJsonString(String str) {
    try {
      jsonDecode(str);
      return true;
    } on FormatException {
      return false;
    }
  }

  static Rect? stringToRect(String rectString) {
    // 分割字符串
    List<String> parts = rectString.split(',');

    // 检查是否包含四个部分
    if (parts.length != 4) {
      return null;
    }

    // 将每个部分转换为 double
    double left = double.parse(parts[0]);
    double top = double.parse(parts[1]);
    double right = double.parse(parts[2]);
    double bottom = double.parse(parts[3]);

    // 创建 Rect 对象
    return Rect.fromLTRB(left, top, right, bottom);
  }

}
