import 'dart:async';
import 'dart:io';

import 'package:audio_session/audio_session.dart';
import 'package:dada/pages/mine/edit/record/audio_record_controller.dart';
import 'package:dada/utils/log.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

enum AudioRecorderStatus { idle, recording, pause, stop }

class AudioRecorder extends GetxController {
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  String? _filePath;
  AudioSession? _session;

  AudioRecorderStatus status = AudioRecorderStatus.idle;
  int? duration;

  StreamSubscription? _recorderSubscription;

  String? get filePath => _filePath;

  int maxLength = 30;
  int minLength = 3;

  @override
  void onInit() async {
    super.onInit();
    await _initRecorder();
  }

  Future<void> _initRecorder() async {
    try {
      var status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        throw RecordingPermissionException('Microphone permission not granted');
      }
      _recorder = FlutterSoundRecorder();
      _player = FlutterSoundPlayer();
      await _recorder?.openRecorder();
      await _player?.openPlayer();
      await _initAudioSession();
      await _addRecorderListener();
    } catch (e) {
      Log.d('Failed to initialize recorder/player: $e');
    }
  }

  Future<void> _initAudioSession() async {
    _session = await AudioSession.instance;
    if (Platform.isIOS) {
      await _session?.configure(AudioSessionConfiguration(
        avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
        avAudioSessionCategoryOptions:
            AVAudioSessionCategoryOptions.allowBluetooth |
                AVAudioSessionCategoryOptions.defaultToSpeaker,
        avAudioSessionMode: AVAudioSessionMode.spokenAudio,
        avAudioSessionRouteSharingPolicy:
            AVAudioSessionRouteSharingPolicy.defaultPolicy,
        avAudioSessionSetActiveOptions:
            AVAudioSessionSetActiveOptions.notifyOthersOnDeactivation,
        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          flags: AndroidAudioFlags.none,
          usage: AndroidAudioUsage.voiceCommunication,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: true,
      ));
    } else {
      await _session?.configure(const AudioSessionConfiguration());
    }
  }

  Future<String> _getFilePath() async {
    Directory tempDir = await getTemporaryDirectory(); //获取获取临时目录
    var time = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    String path = '${tempDir.path}/$time${ext[Codec.aacADTS.index]}';
    return path;
  }

  Future<void> startRecording() async {
    try {
      var permissionStatus = await Permission.microphone.request(); //获取权限
      if (permissionStatus != PermissionStatus.granted) {
        //如果没有权限则不进行录音
        throw RecordingPermissionException('Microphone permission not granted');
      }
      if (_recorder == null) {
        _initRecorder();
      }
      _filePath = await _getFilePath(); //获取路径
      await _addRecorderListener();
      await _recorder?.startRecorder(
        //开始录音
        toFile: _filePath,
        codec: Codec.aacMP4,
      );
      status = AudioRecorderStatus.recording; //设置状态-正在录音
      update();
    } catch (e) {
      Log.d('Failed to start recording: $e');
    }
  }

  Future<void> pauseRecording() async {
    try {
      await _recorder?.pauseRecorder();
      status = AudioRecorderStatus.pause;
      update();
    } catch (e) {
      Log.d('Failed to pause recording: $e');
    }
  }

  Future<void> resumeRecording() async {
    try {
      await _recorder?.resumeRecorder();
      status = AudioRecorderStatus.pause;
      update();
    } catch (e) {
      Log.d('Failed to pause recording: $e');
    }
  }

  Future<void> stopRecording() async {
    try {
      await _recorder?.stopRecorder();
      status = AudioRecorderStatus.stop;
      _cancelRecorderSubscriptions();
      update();
      Log.d('Recording saved to: $_filePath');
    } catch (e) {
      Log.d('Failed to stop recording: $e');
    }
  }

  Future<bool?> deleteRecord({required String fileName}) async {
    return await _recorder?.deleteRecord(fileName: fileName);
  }

  Future<String?> getRecordURL({required String path}) async {
    var url = await _recorder?.getRecordURL(path: path);
    return url;
  }

  void reset() async {
    if (status == AudioRecorderStatus.idle) {
      return;
    }
    if (status != AudioRecorderStatus.stop) {
      await stopRecording();
    }
    duration = 0;
    deleteRecord(fileName: _filePath ?? "");
    status = AudioRecorderStatus.idle;
    update();
  }

  Future<void> _closeAudioRecorder() async {
    try {
      await _recorder?.closeRecorder();
    } catch (e) {
      Log.d('Failed to close recorder: $e');
    }
    await _clearTemporaryDirectory();
  }

  Future<void> _clearTemporaryDirectory() async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      if (tempDir.existsSync()) {
        tempDir.deleteSync(recursive: true);
      }
      update();
      Log.d('Temporary directory cleared');
    } catch (e) {
      Log.d('Failed to clear temporary directory: $e');
    }
  }

  Future<void> _addRecorderListener() async {
    if (_recorderSubscription != null) {
      return;
    }
    await _recorder?.setSubscriptionDuration(const Duration(milliseconds: 10));
    _recorderSubscription = _recorder?.onProgress?.listen((event) {
      Log.i('onProgress ${event.decibels}  ${event.duration.inSeconds}');
      var date = DateTime.fromMillisecondsSinceEpoch(
          event.duration.inMilliseconds,
          isUtc: true);
      var txt = DateFormat('mm:ss:SS', 'en_GB').format(date);
      Log.i('当前录音时长：$txt');
      duration = event.duration.inSeconds;
      if (date.second >= maxLength) {
        stopRecording();
        return;
      }
      update();
    });
  }

  void _cancelRecorderSubscriptions() {
    if (_recorderSubscription != null) {
      _recorderSubscription!.cancel();
      _recorderSubscription = null;
    }
  }

  @override
  void dispose() async {
    _closeAudioRecorder();
    _cancelRecorderSubscriptions();
    _clearTemporaryDirectory();
    super.dispose();
  }
}
