import 'dart:math' as math;
import 'dart:convert';
import 'package:dada/common/values/enums.dart';
import 'package:dada/model/prop_entity.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

class DressUtils {
  String getSvgaAssetNameWithDressEntity(PropEntity propEntity,
      {String? colorKey}) {
    String assetsName = "";
    if (propEntity.dressColorList?.isNotEmpty == true) {
      if (colorKey != null) {
        String? dressNo = propEntity.dressColorList!
            .where((e) => e.colorNo == colorKey)
            .first
            .dressNo;
        if (dressNo != null) {
          assetsName += getSvgaAssetNameWithDressNo(dressNo: dressNo);
        }
      } else {
        String? dressNo = propEntity.dressColorList!.first.dressNo;
        if (dressNo != null) {
          assetsName += getSvgaAssetNameWithDressNo(dressNo: dressNo);
        }
      }
    }
    if (assetsName.isNotEmpty) {
      return "assets/svga/dress/$assetsName";
    }
    return "";
  }

  ///通过衣服编码获取衣服的svga文件名
  /// status : FRONT, LISTEN_MUSIC, LOOK_BOOK, SIT_PLAY_PHONE, BACK
  String getSvgaAssetNameWithDressNo(
      {required String dressNo, String? status, bool? isRandomStatus}) {
    String assetsName = "";
    if (dressNo.isNotEmpty) {
      assetsName += dressNo;
      if (status?.isNotEmpty == true) {
        assetsName += "_$status";
      } else if (isRandomStatus != null) {
        var random = math.Random();
        int direction = random.nextInt(3);
        List randomStatusNos = ["_FRONT", "_LISTEN_MUSIC", "_LOOK_BOOK"];
        assetsName += randomStatusNos[direction];
      } else {
        assetsName += "_FRONT";
      }
    }
    if (assetsName.isNotEmpty) {
      assetsName += ".svga";
    }
    return assetsName;
  }

  ///获取消息的发送者 dressUrl
  /// propertyType: 1：道具颜色String；2：道具Offset
  String? getMessageUserDressUrl(V2TimMessage message, DressUpType dressUpType,
      {int? propertyType}) {
    String? dressUrl;
    String? cloudCustomData = message.cloudCustomData;
    if (cloudCustomData?.isNotEmpty == true) {
      Map<String, dynamic> map = jsonDecode(cloudCustomData!);
      if (map.isNotEmpty == true) {
        if (map.containsKey("chatBubble") == true &&
            dressUpType == DressUpType.chatBox) {
          String? chatBubbleJsonStr = map["chatBubble"];
          if (chatBubbleJsonStr != null) {
            Map<String, dynamic> chatBubbleMap = jsonDecode(chatBubbleJsonStr);
            if (chatBubbleMap.isNotEmpty == true) {
              if (chatBubbleMap.containsKey("effectImage")) {
                dressUrl = chatBubbleMap["effectImage"];
                if (dressUrl?.isNotEmpty == true) {
                  if (chatBubbleMap.containsKey("fontColor") &&
                      propertyType == 1) {
                    dressUrl = chatBubbleMap["fontColor"];
                  } else if (chatBubbleMap.containsKey("offset") &&
                      propertyType == 2) {
                    dressUrl = chatBubbleMap["offset"];
                  }
                }
              }
            }
          }
        } else if (map.containsKey("avatarFrame") == true &&
            dressUpType == DressUpType.avatar) {
          dressUrl = map["avatarFrame"];
        }
      }
    }
    return dressUrl;
  }
}
