import 'dart:convert';
import 'dart:io';
import 'package:dada/configs/app_config.dart';
import 'package:dada/model/trtc_custom_msg_entity.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api.dart';
import 'package:dada/services/trtc/chat_room_rtc_event.dart';
import 'package:dada/services/trtc/chat_room_service_subscribe_delegate.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/log.dart';
import 'package:get/get.dart';
import 'package:tencent_trtc_cloud/trtc_cloud.dart';
import 'package:tencent_trtc_cloud/trtc_cloud_def.dart';
import 'package:tencent_trtc_cloud/trtc_cloud_listener.dart';
import 'package:tencent_trtc_cloud/tx_device_manager.dart';

class TRTCManager extends GetxService {
  static TRTCManager? _sharedInstance;

  TRTCManager._internal() {
    _initSDK();
  }

  static TRTCManager get sharedInstance =>
      _sharedInstance ??= TRTCManager._internal();

  late TRTCCloud _trtcCloud;
  late TXDeviceManager _txDeviceManager;
  bool _initializedSdk = false;

  int localMicState = 1;

  ///当前麦克风状态：0 是关闭，1是开启
  int localSpeakerState = 1;

  ///当前喇叭状态：0 是关闭，1是开启
  ///当前被静音的麦上用户
  Map<String, bool> currentMuteUserMap = {};

  List<ChatRoomRTCListener> _listeners = [];

  Future<void> _initSDK() async {
    _trtcCloud = (await TRTCCloud.sharedInstance())!;
    _txDeviceManager = _trtcCloud.getDeviceManager();
    _registerListenerCallback();
    _initializedSdk = true;
  }

  void _registerListenerCallback() {
    _trtcCloud.registerListener(_onRtcListener);
  }

  void _unRegisterListenerCallback() {
    _trtcCloud.unRegisterListener(_onRtcListener);
  }

  ///进入聊天室
  Future<void> enterRoom({
    required int roomId,
    required String userId,
    required int role,
    int? scene,
  }) async {
    ///未初始化
    if (!_initializedSdk) {
      await _initSDK();
    }

    ///IM 未登录要先登录
    if (!ChatIMManager.sharedInstance.imLogined) {
      await ChatIMManager.sharedInstance.login();
    }
    TRTCParams params = TRTCParams(
      sdkAppId:
          ApiConfig.env == Env.dev ? AppConfig.imAppId_Dev : AppConfig.imAppId,
      userId: userId,
      userSig: ChatIMManager.sharedInstance.userSig,
      role: role,
      roomId: roomId,
    );

    ///进入房间, 若不存在则创建房间
    _trtcCloud.enterRoom(
      params,
      scene ?? TRTCCloudDef.TRTC_APP_SCENE_VOICE_CHATROOM,
    );
  }

  ///退出房间
  Future<void> exitRoom() async {
    return _trtcCloud.exitRoom();
  }

  ///转换角色
  Future<void> switchRole(int role) async {
    await _trtcCloud.switchRole(role);
  }

  ///打开麦克风权限
  Future<void> enableFollowingDefaultAudioDevice() async {
    _txDeviceManager.enableFollowingDefaultAudioDevice(
        TRTCCloudDef.TXMediaDeviceTypeMic, true);
  }

  ///开启音频输入
  Future<void> startLocalAudio() async {
    await _trtcCloud.startLocalAudio(TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT);
  }

  ///关闭音频输入
  Future<void> stopLocalAudio() async {
    await _trtcCloud.stopLocalAudio();
  }

  ///暂停麦克风输入
  Future<void> muteLocalAudio(bool mute) async {
    await _trtcCloud.muteLocalAudio(mute);
  }

  ///关闭扬声器
  Future<void> muteAllRemoteAudio(bool mute) async {
    await _trtcCloud.muteAllRemoteAudio(mute);
  }

  ///静音某个人
  Future<void> muteRemoteUserAudio(
      {required String userId, required bool mute}) async {
    await _trtcCloud.muteRemoteAudio(userId, mute);
  }

  ///停止音频录制
  Future<void> stopLocalRecording() async {
    await _trtcCloud.stopLocalRecording();
  }

  ///更新用户静音状态
  void updateUserMuteListState(String userId, bool mute) {
    currentMuteUserMap[userId] = mute;
  }

  ///开启音量监听
  void enableAudioVolumeEvaluation() async {
    _trtcCloud.enableAudioVolumeEvaluation(300);
  }

  ///关闭音量监听
  void disableAudioVolumeEvaluation() async {
    _trtcCloud.enableAudioVolumeEvaluation(-1);
  }

  ///清空静音
  void clearMuteList() {
    currentMuteUserMap.clear();
  }

  ///恢复麦克风、扬声器状态
  void resetMicAndSpeakerState() {
    localMicState = 1;
    localSpeakerState = 1;
  }

  ///发送TRTC自定义消息给房间内所有人
  Future<bool?> sendCustomCmdMsg(
      {required String event, required dynamic data}) async {
    TRTCCustomMsgEntity msg = TRTCCustomMsgEntity();
    msg.event = event;
    msg.data = data;
    String jsonStr = msg.toString();
    bool? success = await _trtcCloud.sendCustomCmdMsg(1, jsonStr, false, false);
    if (success == true) {
      Log.i("TRTC 自定义消息发送成功！\n消息内容：$jsonStr");
    } else {
      Log.i("TRTC 自定义消息发送失败！\n消息内容：$jsonStr");
    }
    return success;
  }

  ///RTC Event callback
  _onRtcListener(type, param) async {
    switch (type) {
      case TRTCCloudListener.onEnterRoom:
        Log.i("TRTC onEnterRoom, param: $param");
        break;
      case TRTCCloudListener.onExitRoom:
        Log.i("TRTC onExitRoom, param: $param");
        break;
      case TRTCCloudListener.onSendFirstLocalAudioFrame:
        Log.i(param);
        break;
      case TRTCCloudListener.onRemoteUserEnterRoom:
        {
          String userId = param;
          for (var element in _listeners) {
            element.onRemoteUserEnterRoom?.call(userId);
          }
        }
        break;
      case TRTCCloudListener.onRemoteUserLeaveRoom:
        {
          String userId = param["userId"];
          for (var element in _listeners) {
            element.onRemoteUserLeaveRoom?.call(userId);
          }
        }
        break;
      case TRTCCloudListener.onUserAudioAvailable:
        {}
        break;
      case TRTCCloudListener.onSwitchRole:
        {}
        break;
      case TRTCCloudListener.onRecvCustomCmdMsg:
        {
          TRTCCustomMsgEntity msg =
              TRTCCustomMsgEntity.fromJson(jsonDecode(param["message"]));
          String? event = msg.event;
          switch (event) {
            case ChatRoomRtcEvent.RoomInfoUpdate:
              String roomId = msg.data;
              for (var element in _listeners) {
                element.updateRoomInfo?.call(roomId);
              }
              break;

            case ChatRoomRtcEvent.KickOutOfRoom:
              Map data = msg.data;
              String roomId = data["roomId"];
              String userId = data["userId"];
              for (var element in _listeners) {
                element.kickOutRoom?.call(roomId, userId);
              }
              break;

            case ChatRoomRtcEvent.ChangeSeatApply:
              Map data = msg.data;
              String roomId = data["roomId"];
              String fromUserId = data["fromUserId"];
              String toUserId = data["toUserId"];
              for (var element in _listeners) {
                element.onChangeSeatApply?.call(roomId, fromUserId, toUserId);
              }
              break;
          }
        }
        break;

      case TRTCCloudListener.onUserVoiceVolume:
        {
          Map<String, int> userVolume = {};
          List<dynamic> userVolumes = param["userVolumes"];
          if (userVolumes.isNotEmpty) {
            for (Map userVolumeMap in userVolumes) {
              if (userVolumeMap.containsKey("userId")) {
                String? userId = userVolumeMap["userId"];
                if (!(userId?.isNotEmpty == true) && Platform.isIOS) {
                  userVolume[UserService().user!.id!] = userVolumeMap["volume"];
                } else {
                  userVolume[userVolumeMap["userId"]] = userVolumeMap["volume"];
                }
              }
            }
            for (var element in _listeners) {
              element.onUserVoiceVolume?.call(userVolume);
            }
          }
        }
        break;

      case TRTCCloudListener.onError:
        Log.i(param);
        break;
    }
  }

  void addListener(ChatRoomRTCListener listener) {
    _listeners.add(listener);
  }

  void removeListener(ChatRoomRTCListener listener) {
    _listeners.remove(listener);
  }

  @override
  void onClose() {
    _listeners.clear();
    _unRegisterListenerCallback();
    super.onClose();
  }
}
