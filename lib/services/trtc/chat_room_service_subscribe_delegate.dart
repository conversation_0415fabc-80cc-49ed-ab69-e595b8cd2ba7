class ChatRoomRTCListener {
  /// 进入房间
  Function(String userId)? onRemoteUserEnterRoom;

  /// 退出房间
  Function(String userId)? onRemoteUserLeaveRoom;

  /// 更新房间信息
  Function(String roomId)? updateRoomInfo;

  /// 被踢出房间
  Function(String roomId, String userId)? kickOutRoom;

  /// 转换角色（上麦下麦）
  Function(String roomId, String userId)? onSwitchRole;

  /// 收到换席位请求消息
  Function(String roomId, String fromUserId, String toUserId)? onChangeSeatApply;

  /// 席位声音监听
  Function(Map<String, int>)? onUserVoiceVolume;
  

  ChatRoomRTCListener({
    this.onRemoteUserEnterRoom,
    this.onRemoteUserLeaveRoom,
    this.updateRoomInfo,
    this.kickOutRoom,
    this.onSwitchRole,
    this.onChangeSeatApply,
    this.onUserVoiceVolume,
  });
}