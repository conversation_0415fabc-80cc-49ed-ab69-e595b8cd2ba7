import 'dart:async';
import 'package:dada/configs/app_config.dart';
import 'package:dada/model/system_config_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';
import 'package:sp_util/sp_util.dart';

const String KEY_SP_UTIL_USER_INFO = "UserInfo";
const String KEY_SP_UTIL_USER_SIG = "Tencent_Cloud_UserSig";

class UserService extends GetxService {
  UserService._();

  static init() {
    Get.put<UserService>(UserService._(), permanent: true);
  }

  factory UserService() => Get.find<UserService>();

  final StreamController<UserInfoEntity?> _updateStreamController =
      StreamController<UserInfoEntity?>.broadcast();

  Stream<UserInfoEntity?> get updateStream => _updateStreamController.stream;

  UserInfoEntity? _user;

  SystemConfigEntity? systemConfig;

  UserInfoEntity? get user {
    if (_user?.id != null) {
      return _user;
    }
    Map<String, dynamic>? userMap =
        SpUtil.getObject(KEY_SP_UTIL_USER_INFO) as Map<String, dynamic>?;
    if (userMap != null) {
      UserInfoEntity userInfoEntity = UserInfoEntity.fromJson(userMap);
      if (userInfoEntity.id != null) {
        _user = userInfoEntity;
        return userInfoEntity;
      }
    }
    return null;
  }

  set user(UserInfoEntity? userInfoEntity) {
    _user = userInfoEntity;
    if (userInfoEntity != null) {
      SpUtil.putObject(KEY_SP_UTIL_USER_INFO, userInfoEntity);
    } else {
      SpUtil.remove(KEY_SP_UTIL_USER_INFO);
    }
  }

  ///用户签名（用于腾讯云IM、TRTC等产品SDK所必要的用户鉴权）
  String? _userSig;

  String? get userSig {
    if (_userSig != null) {
      return _userSig;
    }
    String? sig = SpUtil.getString(KEY_SP_UTIL_USER_SIG);
    return sig;
  }

  set userSig(String? sig) {
    _userSig = sig;
    if (sig != null) {
      SpUtil.putString(KEY_SP_UTIL_USER_SIG, sig);
    } else {
      SpUtil.remove(KEY_SP_UTIL_USER_SIG);
    }
  }

  Future<void> refresh() async {
    if (user?.id == null) {
      return;
    }
    UserInfoEntity? result = await ApiService().getUserInfo(user!.id!);
    if (result != null) {
      user = result;
      ChatIMManager.sharedInstance.setImUserInfo();
      _updateStreamController.add(result);
      EventBusEngine.fire(event: BusEvent.userInfoUpdate);
    }
  }

  void removeUser() async {
    _user = null;
    _userSig = null;
    await SpUtil.clear();
  }

  /// 是否月卡用户
  bool checkIsMonthCardUser() {
    return _user?.monthlyPassUser ?? false;
  }

  ///是否是官方账号
  bool isOfficialAccount({String? userId}) {
    if (userId != null) {
      return userId == AppConfig.officialUser1;
    }
    return user?.id == AppConfig.officialUser1;
  }

  @override
  void onClose() {
    removeUser();
    super.onClose();
  }
}
