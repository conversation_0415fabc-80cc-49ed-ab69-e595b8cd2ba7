import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/use_prop_result_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PropManager extends GetxService {
  PropManager._();

  static init() {
    Get.put<PropManager>(PropManager._(), permanent: true);
  }

  factory PropManager() => Get.find<PropManager>();

  /// 检查是否需要展示使用道具情况的弹窗
  void checkShouldShowUsePropDialog(
      UsePropResultEntity? result, PropType propType) {
    if (result != null) {
      switch (propType) {
        case PropType.daBang:
          {
            if (result.code == -1) {
              if (result.msg != null) {
                ToastUtils.showToast(result.msg!);
              }
            }
          }
          break;
        case PropType.dyePowder:
        case PropType.stains:
        case PropType.skinChip:
        case PropType.globalSpeaker:
        case PropType.avatarFrame:
        case PropType.chatBox:
        case PropType.womenRoleImage:
        case PropType.manRoleImage:
        case PropType.treasureBox:
          {
            if (result.code == 1) {
              ToastUtils.showDialog(dialog: usePropSuccessDialog(propType));
            } else {
              ToastUtils.showDialog(
                  content: result.msg ?? "",
                  confirmBtnTitle: "知道了",
                  hideCancelBtn: true);
              Future.delayed(const Duration(seconds: 3), () {
                if (Get.isDialogOpen == true) {
                  Get.back();
                }
              });
            }
          }
          break;
        default:
          break;
      }
    }
  }

  Widget usePropSuccessDialog(PropType propType) {
    String textImage = Assets.imagesUsePropSuccessDialogText1;
    return GradientWidget(
      width: 220.w,
      height: 60.h,
      cornerRadius: 10.r,
      colors: const [
        AppColors.colorFFD2F6C0,
        Colors.white,
      ],
      stops: const [
        0,
        0.35,
      ],
      child: Center(
        child: ImageUtils.getImage(textImage, 75.w, 25.h),
      ),
    );
  }
}
