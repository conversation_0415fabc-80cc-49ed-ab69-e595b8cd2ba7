import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseUtils {

  static const KEY_CHAT_LIST_DB = "chat_list";
  static const KEY_CHAT_DETAIL_DB = "chat_detail";

  static final DatabaseUtils _instance = DatabaseUtils._internal();
  factory DatabaseUtils() => _instance;

  DatabaseUtils._internal() {
    _initDatabases();
  }

  Future<void> _initDatabases() async {
    bool existChatListDb = await existDatabase(KEY_CHAT_LIST_DB);
    if (!existChatListDb) {
      await createDatabase(KEY_CHAT_DETAIL_DB);
    }

    bool existChatDetailDb = await existDatabase(KEY_CHAT_DETAIL_DB);
    if (!existChatDetailDb) {
      await createDatabase(KEY_CHAT_DETAIL_DB);
    }
  }

  Future<bool> existDatabase(String key) async {
    String path = await getDatabasePath(key: key);
    bool isExist = await databaseExists(path);
    return isExist;
  }

  /*获取数据库路径*/
  Future<String> getDatabasePath({String? key}) async {
    final dbPath = await getDatabasesPath();
    String name = key ?? "my";
    String path = join(dbPath, '${name}_database.db');
    return path;
  }

  /*获取指定数据库*/
  Future<Database> getDatabase(String key) async {
    String path = await getDatabasePath(key: key);
    bool isExist = await existDatabase(key);
    if (isExist) {
      return await openDatabase(path, version: 1);
    }
    return createDatabase(key);
  }

  /*创建数据库*/
  Future<Database> createDatabase(String key) async {
    if (key == "chat_list") {
      return createChatListDb();
    } else if (key == "chat_detail") {
      return createChatDetailDb();
    } else {
      return createChatListDb();
    }
  }

  Future<Database> createChatListDb() async {
    String path = await getDatabasePath(key: "chat_list");
    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute(
          'CREATE TABLE users(id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT)',
        );
      },
    );
  }

  Future<Database> createChatDetailDb() async {
    String path = await getDatabasePath(key: "chat_detail");
    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute(
          'CREATE TABLE users(id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT)',
        );
      },
    );
  }


}
