import 'package:dada/generated/l10n.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/chat_room/list/chat_room_list_controller.dart';
import 'package:dada/pages/match/assemble/team/match_team_chat_controller.dart';
import 'package:dada/services/floating/global_floating_mini_window.dart';
import 'package:dada/services/im/chat_im_callback.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/trtc/chat_room_rtc_manager.dart';
import 'package:dada/services/trtc/chat_room_service_subscribe_delegate.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter_floating/floating/assist/floating_slide_type.dart';
import 'package:flutter_floating/floating/floating.dart';
import 'package:flutter_floating/floating/manager/floating_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_info.dart';

class GlobalFloatingManager extends GetxService {
  GlobalFloatingManager._();

  static init() {
    Get.put<GlobalFloatingManager>(GlobalFloatingManager._(), permanent: true);
  }

  factory GlobalFloatingManager() => Get.find<GlobalFloatingManager>();

  static const String MiniWindowKey = "MiniWindow";

  RxBool isShowMiniWindow = false.obs;

  MatchTeamChatController? teamChatController;
  ChatRoomController? chatRoomController;

  ChatImListener? _chatImListener;
  ChatRoomRTCListener? _chatRoomRTCListener;

  void checkShowSmallWindow() {
    ever(isShowMiniWindow, (newValue) {
      if (newValue) {
        if (teamChatController != null || chatRoomController != null) {
          floatingManager.createFloating(
            MiniWindowKey,
            Floating(
              GlobalFloatingMiniWindow(
                teamChatController: teamChatController,
                chatRoomController: chatRoomController,
              ),
              slideType: FloatingSlideType.onRightAndBottom,
              isShowLog: false,
              isSnapToEdge: true,
              isPosCache: true,
              moveOpacity: 1,
              bottom: 200.h,
              right: 8.w,
              left: 8.w,
              slideBottomHeight: 0,
            ),
          );
        }
      } else {
        floatingManager.closeFloating(MiniWindowKey);
      }
    });
  }

  void showMiniWindow(
      {MatchTeamChatController? teamController,
      ChatRoomController? roomController}) {
    if (isShowMiniWindow.value == true) {
      hideMiniWindow();
    }
    Floating floating = Floating(
      GlobalFloatingMiniWindow(
        teamChatController: teamController,
        chatRoomController: roomController,
      ),
      slideType: FloatingSlideType.onRightAndBottom,
      isShowLog: false,
      isSnapToEdge: true,
      isPosCache: true,
      moveOpacity: 1,
      bottom: 200.h,
      right: 8.w,
      left: 8.w,
      slideBottomHeight: 0,
    );
    floatingManager.createFloating(MiniWindowKey, floating).open(Get.context!);
    isShowMiniWindow.value = true;
    if (teamController != null) {
      teamChatController = teamController;
    }
    if (roomController != null) {
      chatRoomController = roomController;
    }

    addTRTCServiceListener();
  }

  void hideMiniWindow() {
    if (floatingManager.floatingSize() == 0) {
      return;
    }
    Floating floating = floatingManager.getFloating(MiniWindowKey);
    if (floating.isShowing) {
      floatingManager.closeFloating(MiniWindowKey);
    } else {
      floatingManager.closeAllFloating();
    }
    isShowMiniWindow.value = false;
    chatRoomController = null;
    teamChatController = null;
  }

  void closeMiniWindow() {
    if (teamChatController != null) {
      TRTCManager.sharedInstance.resetMicAndSpeakerState();
      teamChatController!.exitRoom(force: true);
    } else if (chatRoomController != null) {
      TRTCManager.sharedInstance.resetMicAndSpeakerState();
      chatRoomController!.exitRoom(force: true);
    }
    hideMiniWindow();
    removeTRTCServiceListener();
    if (Get.isRegistered<ChatRoomListController>()) {
      Get.find<ChatRoomListController>().refreshData();
    }
  }

  bool currentIsShowMiniWindow(
      {MatchTeamResultEntity? teamEntity, ChatRoomInfoEntity? roomInfo}) {
    ///进入一个集结队时
    if (teamEntity != null) {
      if (teamChatController != null &&
          teamEntity.teamId != teamChatController!.teamEntity.value?.teamId) {
        ToastUtils.showToast(S.current.alreadyInOtherTeam);
        return true;
      } else if (chatRoomController != null) {
        ToastUtils.showToast("您已在其他聊天室中，请先退出其他聊天室");
        return true;
      }
      return false;
    }

    ///进入一个聊天室时
    if (roomInfo != null) {
      if (chatRoomController != null &&
          roomInfo.roomNo != chatRoomController!.roomInfo?.roomNo) {
        ToastUtils.showToast("您已在其他房间中，请先退出其他房间");
        return true;
      } else if (teamChatController != null) {
        ToastUtils.showToast(S.current.alreadyInOtherTeam);
        return true;
      }
      return false;
    }

    ///创建集结队时
    if (teamChatController != null) {
      ToastUtils.showToast(S.current.alreadyInOtherTeam);
      return true;
    }

    ///创建聊天室时
    if (chatRoomController != null) {
      ToastUtils.showToast("您已在其他聊天室中，请先退出其他聊天室");
      return true;
    }
    return false;
  }

  void addTRTCServiceListener() {
    _chatRoomRTCListener = ChatRoomRTCListener(
      kickOutRoom: (roomId, userId) async {
        if (teamChatController != null &&
            teamChatController!.teamEntity.value?.teamNo == roomId) {
          if (userId == UserService().user?.id) {
            await teamChatController!.stopLocalAudio();
            await TRTCManager.sharedInstance.exitRoom();
            hideMiniWindow();
            ToastUtils.showToast("你已被踢出小队");
            TRTCManager.sharedInstance.localMicState = 1;
            TRTCManager.sharedInstance.localSpeakerState = 1;
          }
        } else if (chatRoomController != null &&
            (chatRoomController!.roomInfo?.roomNo == roomId ||
                chatRoomController!.roomId == roomId)) {
          if (userId == UserService().user?.id) {
            await chatRoomController!.stopLocalAudio();
            await TRTCManager.sharedInstance.exitRoom();
            hideMiniWindow();
            ToastUtils.showToast("你已被踢出房间");
            TRTCManager.sharedInstance.localMicState = 1;
            TRTCManager.sharedInstance.localSpeakerState = 1;
          }
        }
      },
    );
    TRTCManager.sharedInstance.addListener(_chatRoomRTCListener!);

    _chatImListener = ChatImListener(
        onGroupDismissed: (String groupID, V2TimGroupMemberInfo opUser) async {
      if (isShowMiniWindow.value == true) {
        if (teamChatController != null &&
            teamChatController!.teamEntity.value?.teamNo == groupID) {
          await teamChatController!.stopLocalAudio();
          await TRTCManager.sharedInstance.exitRoom();
          hideMiniWindow();
          TRTCManager.sharedInstance.localMicState = 1;
          TRTCManager.sharedInstance.localSpeakerState = 1;
        } else if (chatRoomController != null &&
            (chatRoomController!.roomInfo?.roomNo == groupID ||
                chatRoomController!.roomId == groupID)) {
          await chatRoomController!.stopLocalAudio();
          await TRTCManager.sharedInstance.exitRoom();
          hideMiniWindow();
          TRTCManager.sharedInstance.localMicState = 1;
          TRTCManager.sharedInstance.localSpeakerState = 1;
        }
      }
    });
    ChatIMManager.sharedInstance.addListener(_chatImListener!);
  }

  void removeTRTCServiceListener() {
    if (_chatRoomRTCListener != null) {
      TRTCManager.sharedInstance.removeListener(_chatRoomRTCListener!);
    }
    if (_chatImListener != null) {
      ChatIMManager.sharedInstance.removeListener(_chatImListener!);
    }
  }
}
