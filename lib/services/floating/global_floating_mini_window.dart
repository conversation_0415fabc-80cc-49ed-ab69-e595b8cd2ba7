import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/match_team_member_entity.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/pages/chat_room/chat_room_controller.dart';
import 'package:dada/pages/match/assemble/team/match_team_chat_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/trtc/chat_room_rtc_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';

class GlobalFloatingMiniWindow extends StatefulWidget {
  final MatchTeamChatController? teamChatController;
  final ChatRoomController? chatRoomController;

  const GlobalFloatingMiniWindow(
      {super.key, this.teamChatController, this.chatRoomController});

  @override
  State<GlobalFloatingMiniWindow> createState() =>
      _GlobalFloatingMiniWindowState();
}

class _GlobalFloatingMiniWindowState extends State<GlobalFloatingMiniWindow>
    with WidgetsBindingObserver {
  RxString miniWindowImage = "".obs;
  late String miniWindowTitle;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    if (widget.teamChatController != null) {
      miniWindowTitle = S.current.assembleTeam;
      miniWindowImage.value =
          widget.teamChatController!.teamEntity.value!.teamImg ?? "";
    } else if (widget.chatRoomController != null) {
      miniWindowTitle = "聊天室";
      getChatRoomOwnerAvatar();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          GestureDetector(
            onTap: () {
              GlobalFloatingManager().closeMiniWindow();
            },
            child: ImageUtils.getImage(
                Assets.imagesCloseBtnBlackCircle, 15.w, 15.w),
          ),
          SizedBox(
            height: 5.h,
          ),
          GestureDetector(
            onTap: () async {
              await jumpToPage();
              GlobalFloatingManager().hideMiniWindow();
            },
            child: Container(
              width: 50.w,
              height: 60.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5.r),
                color: Colors.black.withOpacity(0.5),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 6.h),
                    child: ClipOval(
                      child: Obx(
                        () => ImageUtils.getImage(
                          miniWindowImage.value,
                          30.w,
                          30.w,
                          fit: BoxFit.cover,
                          showPlaceholder: true,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 6.h,
                  ),
                  Text(
                    miniWindowTitle,
                    style: TextStyles.common(12.sp, Colors.white),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> jumpToPage() async {
    if (widget.teamChatController != null) {
      MatchTeamResultEntity? entity = await ApiService().joinTeam(
          teamId: widget.teamChatController!.teamEntity.value!.teamId!);
      if (entity != null) {
        await ChatIMManager.sharedInstance.joinGroup(
            groupType: GroupType.Meeting,
            groupID:
                widget.teamChatController!.teamEntity.value!.teamNo.toString(),
            message: '');
        V2TimConversation conversation =
            await ChatIMManager.sharedInstance.getConversation(
          type: 2,
          groupID:
              widget.teamChatController!.teamEntity.value!.teamNo.toString(),
        );
        Get.toNamed(
          GetRouter.teamDetail,
          parameters: {
            "teamId":
                widget.teamChatController!.teamEntity.value!.teamId!.toString(),
            "comeFromMini": "1",
          },
          arguments: {"conversation": conversation},
        );
      }
    } else if (widget.chatRoomController != null) {
      Get.toNamed(GetRouter.chatRoomDetail, parameters: {
        "roomId": widget.chatRoomController!.roomId!,
        "roomType": widget.chatRoomController!.roomType.toString(),
        "comeFromMini": "1",
      });
    }
  }

  void getChatRoomOwnerAvatar() async {
    String? chatRoomOwnerId =
        widget.chatRoomController!.roomInfo?.currentRoomUserId;
    if (chatRoomOwnerId != null) {
      V2TimUserFullInfo? userFullInfo =
          await ChatIMManager.sharedInstance.getUserInfo(chatRoomOwnerId);
      if (userFullInfo != null) {
        miniWindowImage.value = userFullInfo.faceUrl ?? "";
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('App is in the foreground');
        // 在这里执行进入前台的操作
        checkRoomExit();
        break;
      case AppLifecycleState.paused:
        debugPrint('App is in the background');
        // 在这里执行进入后台的操作
        break;
      case AppLifecycleState.inactive:
        debugPrint('App is inactive');
        // 在某些平台上，应用可能处于非活动状态
        break;
      case AppLifecycleState.detached:
        debugPrint('App is detached');
        // 在某些平台上，应用可能被分离
        break;
      case AppLifecycleState.hidden:
      // TODO: Handle this case.
    }
  }

  void checkRoomExit() async {
    if (widget.teamChatController != null &&
        widget.teamChatController!.teamEntity.value?.teamId != null) {
      MatchTeamResultEntity? teamEntity = await ApiService()
          .getAssembleTeamInfo(
              teamId: widget.teamChatController!.teamEntity.value!.teamId!,
              showLoading: false);
      if (teamEntity?.teamMembers != null) {
        bool isInTeam = teamEntity!.teamMembers!
            .where((e) => e.userId == UserService().user?.id)
            .toList()
            .isNotEmpty;
        if (!isInTeam) {
          GlobalFloatingManager().hideMiniWindow();
          TRTCManager.sharedInstance.resetMicAndSpeakerState();
        }
      }
    }
  }
}
