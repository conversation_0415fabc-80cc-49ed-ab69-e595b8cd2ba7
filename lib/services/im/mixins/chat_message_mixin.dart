import 'dart:convert';

import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_priority_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/receive_message_opt_enum.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_callback.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message_online_url.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_msg_create_info_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';

mixin ChatMessageMixin {
  /// 获取多媒体消息URL
  Future<V2TimMessageOnlineUrl?> getMessageOnlineAudioUrl({
    required String msgID,
  }) async {
    V2TimValueCallback<V2TimMessageOnlineUrl> valueCallback =
        await TencentImSDKPlugin.v2TIMManager
            .getMessageManager()
            .getMessageOnlineUrl(msgID: msgID);
    if (valueCallback.code == 0) {
      return valueCallback.data;
    }
    return null;
  }

  ///发送文本消息
  Future<V2TimMessage?> sendTextMessage(
      {required String text, String? toUserID, String? toGroupID}) async {
    if (!(toUserID?.isNotEmpty == true) && !(toGroupID?.isNotEmpty == true)) {
      return null;
    }
    // 创建文本消息
    V2TimValueCallback<V2TimMsgCreateInfoResult> createTextMessageRes =
        await TencentImSDKPlugin.v2TIMManager
            .getMessageManager()
            .createTextMessage(
              text: text, // 文本信息
            );
    if (createTextMessageRes.code == 0) {
      // 文本信息创建成功
      String? id = createTextMessageRes.data?.id;
      // 发送文本消息
      // 在sendMessage时，若只填写receiver则发个人用户单聊消息
      //                 若只填写groupID则发群组消息
      //                 若填写了receiver与groupID则发群内的个人用户，消息在群聊中显示，只有指定receiver能看见

      Map<String, dynamic> cloudCustomData = {};

      UserInfoEntity loginUserInfo = UserService().user!;
      String? chatBubble = loginUserInfo.chatBubble;
      if (chatBubble?.isNotEmpty == true) {
        cloudCustomData["chatBubble"] = chatBubble;
      }
      String? avatarFrame = loginUserInfo.avatarFrame;
      if (avatarFrame?.isNotEmpty == true) {
        cloudCustomData["avatarFrame"] = avatarFrame;
      }
      V2TimValueCallback<V2TimMessage> sendMessageRes =
          await TencentImSDKPlugin.v2TIMManager.getMessageManager().sendMessage(
              id: id!,
              receiver: toUserID ?? "",
              groupID: toGroupID ?? "",
              cloudCustomData: jsonEncode(cloudCustomData));
      if (sendMessageRes.code == 0) {
        // 发送成功
        return sendMessageRes.data;
      }
    }
    return null;
  }

  ///发送自定义消息
  Future<bool> sendCustomMessage({
    required String type,
    required dynamic data,
    required String receiver,
    required String groupID,
    String? pushDesc,
    MessagePriorityEnum? priority = MessagePriorityEnum.V2TIM_PRIORITY_NORMAL,
  }) async {
    ChatImCustomMsgEntity msg = ChatImCustomMsgEntity();
    msg.type = type;
    msg.data = data;
    String jsonStr = msg.toString();
    Map<String, dynamic> cloudCustomData = {};

    UserInfoEntity loginUserInfo = UserService().user!;
    String? chatBubble = loginUserInfo.chatBubble;
    if (chatBubble?.isNotEmpty == true) {
      cloudCustomData["chatBubble"] = chatBubble;
    }
    String? avatarFrame = loginUserInfo.avatarFrame;
    if (avatarFrame?.isNotEmpty == true) {
      cloudCustomData["avatarFrame"] = avatarFrame;
    }
    V2TimValueCallback<V2TimMsgCreateInfoResult> valueCallback =
        await TencentImSDKPlugin.v2TIMManager
            .getMessageManager()
            .createCustomMessage(data: jsonStr, desc: pushDesc ?? "");
    if (valueCallback.code == 0) {
      bool success = await sendMessage(
          id: valueCallback.data!.id!,
          receiver: receiver,
          groupID: groupID,
          cloudCustomData: jsonEncode(cloudCustomData));
      return success;
    }
    return false;
  }

  ///发送消息
  Future<bool> sendMessage(
      {required String id, // 自己创建的ID
      required String receiver,
      required String groupID,
      String? cloudCustomData}) async {
    V2TimValueCallback<V2TimMessage> sendMsgRes =
        await TencentImSDKPlugin.v2TIMManager.getMessageManager().sendMessage(
              id: id,
              receiver: receiver,
              groupID: groupID,
              cloudCustomData: cloudCustomData,
            );
    return sendMsgRes.code == 0;
  }

  ///设置好友消息接收选项
  /// ReceiveMsgOptEnum类型
  /// V2TIM_RECEIVE_MESSAGE: 在线正常接收消息，离线时会进行离线推送（正常）
  /// V2TIM_NOT_RECEIVE_MESSAGE: 不会接收到消息	（拉黑）
  /// V2TIM_RECEIVE_NOT_NOTIFY_MESSAGE: 在线正常接收消息，离线不会有推送通知（消息免打扰）
  Future<bool> setC2CReceiveMessageOpt({
    required String userId,
    required ReceiveMsgOptEnum opt,
  }) async {
    V2TimCallback setC2CReceiveMessageOptRes = await TencentImSDKPlugin
        .v2TIMManager
        .getMessageManager()
        .setC2CReceiveMessageOpt(
            userIDList: [userId], // 需要设置的用户id列表
            opt: opt);
    if (setC2CReceiveMessageOptRes.code == 0) {
      return true;
    }
    return false;
  }

  ///获取群历史消息
  Future<V2TimValueCallback<List<V2TimMessage>>?> getGroupHistoryMessageListRes(
      String groupID,
      {String? lastMsgID}) async {
    V2TimValueCallback<List<V2TimMessage>> getGroupHistoryMessageListRes =
        await TencentImSDKPlugin.v2TIMManager
            .getMessageManager()
            .getGroupHistoryMessageList(
              groupID: groupID, // 单聊用户id
              count: 20, // 拉取数据数量
              lastMsgID: lastMsgID, // 拉取起始消息id
            );
    if (getGroupHistoryMessageListRes.code == 0) {
      //获取成功
      return getGroupHistoryMessageListRes;
    }
    return null;
  }
}
