import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/log.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_callback.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';

mixin ChatConversationMixin {
  ///获取指定会话（没有的话，就创建）
  ///
  /// type: 1.单聊；2.群聊
  ///
  Future<V2TimConversation> getConversation({
    String? conversationID,
    int? type,
    String? userID,
    String? groupID,
    String? groupName,
    String? groupType,
    String? groupFaceUrl,
  }) async {
    if (!ChatIMManager.sharedInstance.imLogined) {
      ChatIMManager.sharedInstance.login();
    }
    String conversationIDStr =
        conversationID ?? (type == 2 ? "group_${groupID!}" : "c2c_${userID!}");
    V2TimConversation conversation;
    V2TimValueCallback? valueCallback = await TencentImSDKPlugin
        .v2TIMManager.v2ConversationManager
        .getConversation(conversationID: conversationIDStr);
    if (valueCallback.code == 0 && valueCallback.data != null) {
      conversation = valueCallback.data;
    } else {
      conversation = V2TimConversation(
        conversationID: conversationIDStr,
        userID: userID,
        groupID: groupID,
        type: type,
        showName: groupName,
        groupType: groupType.toString(),
        faceUrl: groupFaceUrl,
      );
    }
    return conversation;
  }

  ///获取会话列表，可分页拉取（一次最多100个）
  Future<V2TimValueCallback<V2TimConversationResult>> getConversationList({
    required int count,
    required String nextSeq,
  }) async {
    if (!ChatIMManager.sharedInstance.imLogined) {
      ChatIMManager.sharedInstance.login();
    }
    V2TimValueCallback<V2TimConversationResult> valueCallback =
        await TencentImSDKPlugin.v2TIMManager
            .getConversationManager()
            .getConversationList(nextSeq: nextSeq, count: count);
    return valueCallback;
  }

  ///获取会话未读总数
  Future<int?> getTotalUnreadMessageCount() async {
    V2TimValueCallback<int?> getTotalUnreadMessageCountRes =
        await TencentImSDKPlugin.v2TIMManager
            .getConversationManager()
            .getTotalUnreadMessageCount();
    if (getTotalUnreadMessageCountRes.code == 0) {
      //拉取成功
      int? count = getTotalUnreadMessageCountRes.data; //会话未读总数
      if (count != null && count > 0) {
        if (Get.isRegistered<MainController>()) {
          Get.find<MainController>().updateUnreadMessageCount(count);
        }
      }
      return count;
    }
    return null;
  }

  ///清除某个单聊消息未读数
  Future<void> clearC2CMessageUnReadCount(String userID) async {
    // 设置单聊消息已读
    V2TimCallback markC2CMessageAsReadRes = await TencentImSDKPlugin
        .v2TIMManager
        .getMessageManager()
        .markC2CMessageAsRead(userID: userID); // 需要设置消息已读的用户id
    if (markC2CMessageAsReadRes.code == 0) {
      // 标记成功
      Log.d("成功清除和用户$userID的单聊消息未读数！");
    }
  }

  Future<bool> deleteConversation(String conversationID) async {
    //删除会话
    V2TimCallback deleteConversationRes = await TencentImSDKPlugin.v2TIMManager
        .getConversationManager()
        .deleteConversation(
          conversationID: conversationID, //需要删除的会话id
        );
    if (deleteConversationRes.code == 0) {
      //删除成功
      return true;
    }
    return false;
  }
}
