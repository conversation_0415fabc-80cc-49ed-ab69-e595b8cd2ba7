import 'dart:core';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_add_opt_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_application_type_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_filter_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_role_enum.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_type.dart';
import 'package:tencent_cloud_chat_sdk/enum/receive_message_opt_enum.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_callback.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_application.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_application_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_info_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_operation_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';

mixin ChatGroupMixin {
  ///创建群聊
  Future<String?> createGroup({
    required String groupType,
    required String groupName,
    required String faceUrl,
    String? groupID,
    List<V2TimGroupMember>? memberList,
    GroupAddOptTypeEnum? addOptTypeEnum,
  }) async {
    if (!ChatIMManager.sharedInstance.imLogined) {
      ChatIMManager.sharedInstance.login();
    }
    V2TimValueCallback<String> callback = await TencentImSDKPlugin.v2TIMManager
        .getGroupManager()
        .createGroup(
            groupType: groupType.toString(),
            groupName: groupName,
            groupID: groupID,
            faceUrl: faceUrl,
            memberList: memberList,
            addOpt: addOptTypeEnum ?? GroupAddOptTypeEnum.V2TIM_GROUP_ADD_AUTH);
    if (callback.code == 0) {
      return callback.data;
    }
    return null;
  }

  ///获取群信息
  Future<V2TimGroupInfo?> getGroupInfo(String groupID) async {
    V2TimValueCallback<List<V2TimGroupInfoResult>> getGroupsInfoRes =
        await TencentImSDKPlugin.v2TIMManager
            .getGroupManager()
            .getGroupsInfo(groupIDList: [groupID]);
    if (getGroupsInfoRes.code == 0) {
      if (getGroupsInfoRes.data?.isNotEmpty == true) {
        List<V2TimGroupInfoResult> list = getGroupsInfoRes.data!;
        return list.first.groupInfo;
      }
      return null;
    }
    return null;
  }

  ///设置群信息
  Future<bool> setGroupInfo(
    String groupID, {
    String? groupType,
    String? groupName,
    String? faceUrl,
    String? notice,
    String? owner,
    Map<String, String>? customInfo,
  }) async {
    // 修改指定的群资料
    V2TimCallback setGroupInfoRes =
        await TencentImSDKPlugin.v2TIMManager.getGroupManager().setGroupInfo(
              info: V2TimGroupInfo(
                groupID: groupID,
                // 群组id
                groupType: groupType ?? GroupType.Public,
                // 群组类型
                groupName: groupName,
                faceUrl: faceUrl,
                notification: notice,
                customInfo: customInfo,
              ),
            );
    if (setGroupInfoRes.code == 0) {
      // 修改成功
      return true;
    }
    return false;
  }

  ///加入群聊
  Future<bool> joinGroup({
    required String groupType,
    required String groupID,
    required String message,
  }) async {
    if (!ChatIMManager.sharedInstance.imLogined) {
      ChatIMManager.sharedInstance.login();
    }
    V2TimCallback callback = await TencentImSDKPlugin.v2TIMManager
        .joinGroup(groupID: groupID, message: message, groupType: groupType);
    if (callback.code == 0 || callback.code == 10013) {
      return true;
    }
    return false;
  }

  ///退出群聊
  Future<bool> quitGroup({
    required String groupID,
  }) async {
    V2TimCallback callback =
        await TencentImSDKPlugin.v2TIMManager.quitGroup(groupID: groupID);
    if (callback.code != 0) {
      return false;
    }
    return true;
  }

  ///解散群聊
  Future<bool> dismissGroup({
    required String groupID,
  }) async {
    V2TimCallback callback =
        await TencentImSDKPlugin.v2TIMManager.dismissGroup(groupID: groupID);
    if (callback.code != 0) {
      return false;
    }
    return true;
  }

  ///转让群主
  Future<bool> transferGroupOwner({
    required String groupID,
    required String userID,
  }) async {
    V2TimCallback callback = await TencentImSDKPlugin.v2TIMManager
        .getGroupManager()
        .transferGroupOwner(groupID: groupID, userID: userID);
    if (callback.code != 0) {
      return false;
    }
    return true;
  }

  ///踢人
  Future<bool> kickGroupMember({
    required String groupID,
    required List<String> memberList,
  }) async {
    V2TimCallback callback = await TencentImSDKPlugin.v2TIMManager
        .getGroupManager()
        .kickGroupMember(groupID: groupID, memberList: memberList);
    if (callback.code != 0) {
      return false;
    }
    return true;
  }

  ///获取群申请列表
  Future<List<V2TimGroupApplication?>?> getGroupApplicationList(
      {String? groupID}) async {
    if (!ChatIMManager.sharedInstance.imLogined) {
      ChatIMManager.sharedInstance.login();
    }
    V2TimValueCallback<V2TimGroupApplicationResult> getGroupApplicationListRes =
        await TencentImSDKPlugin.v2TIMManager
            .getGroupManager()
            .getGroupApplicationList();
    if (getGroupApplicationListRes.code == 0) {
      if (getGroupApplicationListRes.data?.groupApplicationList?.isNotEmpty ==
          true) {
        List<V2TimGroupApplication?>? list =
            getGroupApplicationListRes.data!.groupApplicationList;
        return list;
      }
      return null;
    }
    return null;
  }

  ///获取群聊成员列表(不分页)
  Future<List<V2TimGroupMemberFullInfo?>?> getAllGroupMemberList(
      String groupID, String nextSeq) async {
    List<V2TimGroupMemberFullInfo?>? list = [];
    // 获取群成员列表
    V2TimValueCallback<V2TimGroupMemberInfoResult> getGroupMemberListRes =
        await TencentImSDKPlugin.v2TIMManager
            .getGroupManager()
            .getGroupMemberList(
              groupID: groupID,
              //需要查询的群组 ID
              filter: GroupMemberFilterTypeEnum.V2TIM_GROUP_MEMBER_FILTER_ALL,
              //查询群成员类型
              nextSeq: nextSeq,
              // 分页拉取标志，第一次拉取填0，回调成功如果 nextSeq 不为零，需要分页，传入返回值再次拉取，直至为0。
              count: 100,
              // 需要拉取的数量。最大值：100，避免回包过大导致请求失败。若传入超过100，则只拉取前100个。
              offset: 0, // 偏移量，默认从0开始拉取。
            );
    if (getGroupMemberListRes.code == 0) {
      if (getGroupMemberListRes.data?.nextSeq == "0") {
        return getGroupMemberListRes.data?.memberInfoList;
      } else {
        List<V2TimGroupMemberFullInfo?>? subList = await getAllGroupMemberList(
            groupID, getGroupMemberListRes.data!.nextSeq!);
        if (subList != null) {
          list.addAll(subList);
        }
      }
    }
    return list;
  }

  ///获取群聊成员列表(分页)
  Future<V2TimGroupMemberInfoResult?> getGroupMemberList(
      String groupID, String nextSeq) async {
    // 获取群成员列表
    V2TimValueCallback<V2TimGroupMemberInfoResult> getGroupMemberListRes =
        await TencentImSDKPlugin.v2TIMManager
            .getGroupManager()
            .getGroupMemberList(
              groupID: groupID,
              //需要查询的群组 ID
              filter: GroupMemberFilterTypeEnum.V2TIM_GROUP_MEMBER_FILTER_ALL,
              //查询群成员类型
              nextSeq: nextSeq,
              // 分页拉取标志，第一次拉取填0，回调成功如果 nextSeq 不为零，需要分页，传入返回值再次拉取，直至为0。
              count: 20,
              // 需要拉取的数量。最大值：100，避免回包过大导致请求失败。若传入超过100，则只拉取前100个。
              offset: 0, // 偏移量，默认从0开始拉取。
            );
    if (getGroupMemberListRes.code == 0) {
      return getGroupMemberListRes.data;
    }
    return null;
  }

  ///获取当前用户已经加入的群列
  Future<List<V2TimGroupInfo>?> getJoinedGroupList() async {
    V2TimValueCallback<List<V2TimGroupInfo>> getJoinedGroupListRes =
        await TencentImSDKPlugin.v2TIMManager
            .getGroupManager()
            .getJoinedGroupList();
    if (getJoinedGroupListRes.code == 0) {
      return getJoinedGroupListRes.data;
    }
    return null;
  }

  ///设置群成员信息
  Future<bool> setGroupMemberInfo(
      {required String groupID,
      required String userID,
      String? nameCard,
      Map<String, String>? customInfo}) async {
    // 修改指定的群成员资料
    V2TimCallback setGroupMemberInfoRes = await TencentImSDKPlugin.v2TIMManager
        .getGroupManager()
        .setGroupMemberInfo(
            groupID: groupID, // 需要修改的群组id
            userID: userID, // 需要修改的用户id
            nameCard: nameCard, // 修改名片属性
            customInfo: customInfo // 修改自定义属性 需在控制台开通此功能
            );
    if (setGroupMemberInfoRes.code == 0) {
      return true;
    }
    return false;
  }

  ///设置群成员角色
  Future<bool> setGroupMemberRole(
      {required String groupID,
      required String userID,
      required GroupMemberRoleTypeEnum role}) async {
    // 设置群成员的角色
    V2TimCallback setGroupMemberRoleRes = await TencentImSDKPlugin.v2TIMManager
        .getGroupManager()
        .setGroupMemberRole(
            groupID: groupID, // 设置群组的id
            userID: userID, // 被设置角色的用户id
            role: role // 用户被设置的角色属性
            );
    if (setGroupMemberRoleRes.code == 0) {
      // 设置成功
      return true;
    }
    return false;
  }

  ///批量获取群成员详细信息
  Future<List<V2TimGroupMemberFullInfo>?> getGroupMembersInfo(
      String groupID, List<String> userIDs) async {
    // 获取指定的群成员资料
    V2TimValueCallback<List<V2TimGroupMemberFullInfo>> getGroupMembersInfoRes =
        await TencentImSDKPlugin.v2TIMManager
            .getGroupManager()
            .getGroupMembersInfo(
              groupID: groupID, // 需要获取的群组id
              memberList: userIDs, // 需要获取的用户id列表
            );
    if (getGroupMembersInfoRes.code == 0) {
      // 获取成功
      return getGroupMembersInfoRes.data;
    }
    return null;
  }

  ///清除历史聊天记录
  Future<bool> clearGroupHistoryMessageList(String groupID) async {
    // 清空群聊本地及云端的消息（不删除会话）
    V2TimCallback clearGroupHistoryMessageRes = await TencentImSDKPlugin
        .v2TIMManager
        .getMessageManager()
        .clearGroupHistoryMessage(groupID: groupID); // 需要清空记录的群组id
    if (clearGroupHistoryMessageRes.code == 0) {
      //清除成功
      return true;
    }
    return false;
  }

  ///设置接收消息选项
  Future<bool> setGroupReceiveMessageOpt(
      String groupID, ReceiveMsgOptEnum optEnum) async {
    //设置群组消息接收选项
    V2TimCallback setGroupReceiveMessageOptRes = await TencentImSDKPlugin
        .v2TIMManager
        .getMessageManager()
        .setGroupReceiveMessageOpt(
            groupID: groupID, // 需要设置的群组id
            opt: optEnum); // 用户消息接收选项属性
    if (setGroupReceiveMessageOptRes.code == 0) {
      //设置成功
      return true;
    }
    return false;
  }

  ///邀请他人入群
  Future<bool> inviteUserToJoinGroup(
      String groupID, List<String> userList) async {
    V2TimValueCallback<List<V2TimGroupMemberOperationResult>>
        inviteUserToGroupRes = await TencentImSDKPlugin.v2TIMManager
            .getGroupManager()
            .inviteUserToGroup(
              groupID: groupID, // 需要加入的群组id
              userList: userList, // 邀请的用户id列表
            );
    if (inviteUserToGroupRes.code == 0) {
      return true;
    }
    return false;
  }

  ///同意加群申请
  Future<bool> acceptJoinGroupApplication(String groupID, String toUser, {String? reason, int? addTime}) async {
    V2TimCallback acceptGroupApplicationRes = await TencentImSDKPlugin
        .v2TIMManager
        .getGroupManager()
        .acceptGroupApplication(
          groupID: groupID,
          fromUser: toUser,
          toUser: UserService().user!.id!,
          reason: reason,
          type: GroupApplicationTypeEnum.V2TIM_GROUP_APPLICATION_GET_TYPE_JOIN,
          addTime: addTime,
        );
    if (acceptGroupApplicationRes.code == 0) {
      // 同意成功
      return true;
    }
    return false;
  }

  ///拒绝加群申请
  Future<bool> refuseJoinGroupApplication(
      String groupID, String fromUser, int addTime, String? reason) async {
    V2TimCallback acceptGroupApplicationRes = await TencentImSDKPlugin
        .v2TIMManager
        .getGroupManager()
        .refuseGroupApplication(
          groupID: groupID,
          fromUser: fromUser,
          toUser: UserService().user!.id!,
          reason: reason,
          addTime: addTime,
          type: GroupApplicationTypeEnum.V2TIM_GROUP_APPLICATION_GET_TYPE_JOIN,
        );
    if (acceptGroupApplicationRes.code == 0) {
      // 拒绝成功
      return true;
    }
    return false;
  }

  ///标记所有群组申请已读
  Future<bool> clearGroupApplicationRead() async {
    // 标记所有群组申请列表为已读
    V2TimCallback setGroupApplicationReadRes = await TencentImSDKPlugin
        .v2TIMManager
        .getGroupManager()
        .setGroupApplicationRead();
    if (setGroupApplicationReadRes.code == 0) {
      // 设置成功
      return true;
    }
    return false;
  }

  ///禁言群组内成员
  Future<bool> muteGroupMember(
      {required String groupID,
      required String userID,
      required bool mute}) async {
    // 禁言群组内的用户
    V2TimCallback muteGroupMemberRes =
        await TencentImSDKPlugin.v2TIMManager.getGroupManager().muteGroupMember(
              groupID: groupID, // 禁言的群组id
              userID: userID, // 禁言的用户id
              seconds: mute == true ? 2 ^ 32 - 1 : 0, // 禁言时间
            );
    if (muteGroupMemberRes.code == 0) {
      // 禁言成功
      return true;
    }
    return false;
  }
}
