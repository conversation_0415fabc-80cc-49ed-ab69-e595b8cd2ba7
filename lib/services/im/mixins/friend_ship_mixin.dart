import 'package:dada/services/im/chat_im_manager.dart';
import 'package:tencent_cloud_chat_sdk/enum/friend_type_enum.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_callback.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_application_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_operation_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_status.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';

mixin FriendShipMixin {
  ///加好友
  Future<bool> addFriend({required String userID}) async {
    if (!ChatIMManager.sharedInstance.imLogined) {
      ChatIMManager.sharedInstance.login();
    }
    V2TimValueCallback<V2TimFriendOperationResult> addFriendRes =
        await TencentImSDKPlugin.v2TIMManager.getFriendshipManager().addFriend(
              userID: userID,
              //需要添加的用户id
              remark: "",
              //添加的好友的好友备注
              friendGroup: "",
              //添加好友所在分组
              addWording: "",
              //添加好友附带信息
              addSource: "",
              //添加来源描述
              addType: FriendTypeEnum.V2TIM_FRIEND_TYPE_BOTH, //设置加好友类型，默认双向
            );
    if (addFriendRes.code == 0) {
      // 添加请求发送成功
      addFriendRes.data?.resultCode; //添加结果错误码
      addFriendRes.data?.resultInfo; //添加结果描述
      addFriendRes.data?.userID; //被添加的用户id
      return true;
    }
    return false;
  }

  ///获取好友申请未读数
  Future<int> getFriendApplicationUnreadCount() async {
    V2TimValueCallback<V2TimFriendApplicationResult>
        getFriendApplicationListRes = await TencentImSDKPlugin.v2TIMManager
            .getFriendshipManager()
            .getFriendApplicationList();
    if (getFriendApplicationListRes.code == 0) {
      return getFriendApplicationListRes.data?.unreadCount ?? 0;
    }
    return 0;
  }

  ///设置好友申请已读
  Future<bool> clearFriendApplicationUnreadCount() async {
    V2TimCallback setFriendApplicationReadRes = await TencentImSDKPlugin
        .v2TIMManager
        .getFriendshipManager()
        .setFriendApplicationRead();
    if (setFriendApplicationReadRes.code == 0) {
      // 设置成功
      return true;
    }
    return false;
  }

  ///获取用户在线状态
  Future<bool> getUserOnlineStatus({required String userID}) async {
    V2TimValueCallback<List<V2TimUserStatus>> getUserStatusRes =
        await TencentImSDKPlugin.v2TIMManager
            .getUserStatus(userIDList: [userID]);
    if (getUserStatusRes.code == 0) {
      if (getUserStatusRes.data?.isNotEmpty == true) {
        V2TimUserStatus userStatus = getUserStatusRes.data?.first ??
            V2TimUserStatus(statusType: 0, userID: userID);
        return userStatus.statusType == 1;
      }
      return false;
    }
    return false;
  }
}
