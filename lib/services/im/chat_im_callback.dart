import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/model/topic_item_entity.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_change_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

class ChatImListener {
  /// 用户签名 将要失效
  Function()? onUserSigWillExpired;

  /// 用户签名 已失效
  Function()? onUserSigExpired;

  /// 收到新的消息
  Function(V2TimMessage msg)? onReceiveNewMessage;

  /// 收到加入群组申请
  Function(String groupID, V2TimGroupMemberInfo member, String opReason)?
      onReceiveJoinApplication;

  /// 群组信息已变更
  Function(String groupID, List<V2TimGroupChangeInfo> changeInfos)?
      onGroupInfoChanged;

  /// 群组已解散
  Function(String groupID, V2TimGroupMemberInfo opUser)? onGroupDismissed;

  /// 新成员加入群组
  Function(String groupID, List<V2TimGroupMemberInfo> memberList)?
      onMemberEnter;

  /// 成员退出群组
  Function(String groupID, V2TimGroupMemberInfo member)? onMemberLeave;

  /// 被邀请进群
  Function(String groupID, V2TimGroupMemberInfo opUser,
      List<V2TimGroupMemberInfo> memberList)? onMemberInvited;

  /// 被踢出群组
  Function(String groupID, V2TimGroupMemberInfo opUser,
      List<V2TimGroupMemberInfo> memberList)? onMemberKicked;

  ///房间内座位变更
  Function(String groupID, List<ChatRoomSeatInfoEntity> seatList)?
      onRoomSeatListUpdated;

  ///房间内话题变更
  Function(String groupID, List<TopicItemEntity> topicList)?
      onRoomTopicListUpdated;

  ChatImListener(
      {this.onUserSigWillExpired,
      this.onUserSigExpired,
      this.onReceiveNewMessage,
      this.onReceiveJoinApplication,
      this.onGroupInfoChanged,
      this.onGroupDismissed,
      this.onMemberEnter,
      this.onMemberLeave,
      this.onMemberInvited,
      this.onMemberKicked,
      this.onRoomSeatListUpdated,
      this.onRoomTopicListUpdated});
}
