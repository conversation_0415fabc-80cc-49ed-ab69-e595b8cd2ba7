// import 'package:dada/utils/log.dart';
// import 'package:flutter_foreground_task/flutter_foreground_task.dart';
//
// class MyTaskHandler extends TaskHandler {
//   // Called when the task is started.
//   @override
//   Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
//     Log.d('onStart(starter: ${starter.name})');
//   }
//
//   // Called based on the eventAction set in ForegroundTaskOptions.
//   @override
//   void onRepeatEvent(DateTime timestamp) {
//     // Send data to main isolate.
//     final Map<String, dynamic> data = {
//       "timestampMillis": timestamp.millisecondsSinceEpoch,
//     };
//     FlutterForegroundTask.sendDataToMain(data);
//   }
//
//   // Called when the task is destroyed.
//   @override
//   Future<void> onDestroy(DateTime timestamp) async {
//     Log.d('onDestroy');
//   }
//
//   // Called when data is sent using `FlutterForegroundTask.sendDataToTask`.
//   @override
//   void onReceiveData(Object data) {
//     Log.d('onReceiveData: $data');
//   }
//
//   // Called when the notification button is pressed.
//   @override
//   void onNotificationButtonPressed(String id) {
//     Log.d('onNotificationButtonPressed: $id');
//   }
//
//   // Called when the notification itself is pressed.
//   @override
//   void onNotificationPressed() {
//     FlutterForegroundTask.launchApp('/');
//     Log.d('onNotificationPressed');
//   }
//
//   // Called when the notification itself is dismissed.
//   @override
//   void onNotificationDismissed() {
//     Log.d('onNotificationDismissed');
//   }
// }