// import 'dart:io';
// import 'package:dada/utils/log.dart';
// import 'package:flutter_foreground_task/flutter_foreground_task.dart';
// import 'my_task_handle.dart';
//
// mixin ForegroundTaskService {
//   Future<void> requestPermissions() async {
//     // Android 13+, you need to allow notification permission to display foreground service notification.
//     //
//     // iOS: If you need notification, ask for permission.
//     final NotificationPermission notificationPermission =
//         await FlutterForegroundTask.checkNotificationPermission();
//     if (notificationPermission != NotificationPermission.granted) {
//       await FlutterForegroundTask.requestNotificationPermission();
//     }
//
//     if (Platform.isAndroid) {
//       // Android 12+, there are restrictions on starting a foreground service.
//       //
//       // To restart the service on device reboot or unexpected problem, you need to allow below permission.
//       if (!await FlutterForegroundTask.isIgnoringBatteryOptimizations) {
//         // This function requires `android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` permission.
//         await FlutterForegroundTask.requestIgnoreBatteryOptimization();
//       }
//
//       // Use this utility only if you provide services that require long-term survival,
//       // such as exact alarm service, healthcare service, or Bluetooth communication.
//       //
//       // This utility requires the "android.permission.SCHEDULE_EXACT_ALARM" permission.
//       // Using this permission may make app distribution difficult due to Google policy.
//       if (!await FlutterForegroundTask.canScheduleExactAlarms) {
//         // When you call this function, will be gone to the settings page.
//         // So you need to explain to the user why set it.
//         await FlutterForegroundTask.openAlarmsAndRemindersSettings();
//       }
//     }
//   }
//
//   void initService() {
//     FlutterForegroundTask.init(
//       androidNotificationOptions: AndroidNotificationOptions(
//         channelId: 'foreground_service',
//         channelName: 'Foreground Service Notification',
//         channelDescription:
//             'This notification appears when the foreground service is running.',
//         onlyAlertOnce: true,
//         channelImportance: NotificationChannelImportance.LOW,
//         priority: NotificationPriority.LOW,
//       ),
//       iosNotificationOptions: const IOSNotificationOptions(
//         showNotification: false,
//         playSound: false,
//       ),
//       foregroundTaskOptions: ForegroundTaskOptions(
//         eventAction: ForegroundTaskEventAction.repeat(300),
//         autoRunOnBoot: true,
//         autoRunOnMyPackageReplaced: true,
//         allowWakeLock: true,
//         allowWifiLock: true,
//       ),
//     );
//   }
//
//   void onReceiveTaskData(Object data) {
//     if (data is Map<String, dynamic>) {
//       final dynamic timestampMillis = data["timestampMillis"];
//       if (timestampMillis != null) {
//         final DateTime timestamp =
//             DateTime.fromMillisecondsSinceEpoch(timestampMillis, isUtc: true);
//         Log.d('timestamp: ${timestamp.toString()}');
//       }
//     }
//   }
//
//   ///启动前台通知
//   Future<void> startForegroundService() async {
//     if (await FlutterForegroundTask.isRunningService) {
//       FlutterForegroundTask.restartService();
//     } else {
//       var result = await FlutterForegroundTask.startService(
//         notificationTitle: '搭吖',
//         notificationText: '',
//         notificationIcon: null,
//         notificationButtons: null,
//         callback: startCallback,
//       );
//       Log.d("foreground service start result $result");
//     }
//   }
//
//   ///停止前台通知
//   Future<void> stopForegroundService() async {
//     var result = await FlutterForegroundTask.stopService();
//     Log.d("foreground service stop result $result");
//   }
//
// // The callback function should always be a top-level function.
//   @pragma('vm:entry-point')
//   void startCallback() {
//     // The setTaskHandler function must be called to handle the task in the background.
//     FlutterForegroundTask.setTaskHandler(MyTaskHandler());
//   }
// }
