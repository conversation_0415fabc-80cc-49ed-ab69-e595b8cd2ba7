import 'dart:async';
import 'dart:convert';

import 'package:dada/common/values/enums.dart';
import 'package:dada/model/account_balance_entity.dart';
import 'package:dada/model/api_exception.dart';
import 'package:dada/model/audio_box_list_item_entity.dart';
import 'package:dada/model/banner_entity.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/chat_room_list_result_entity.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/model/chat_searched_result_entity.dart';
import 'package:dada/model/chat_group_info_entity.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/model/comment_list_entity.dart';
import 'package:dada/model/comment_reply_item_entity.dart';
import 'package:dada/model/comment_reply_list_entity.dart';
import 'package:dada/model/contact_group_list_item_entity.dart';
import 'package:dada/model/crystal_ball_life_record_entity.dart';
import 'package:dada/model/dada_match_result_entity.dart';
import 'package:dada/model/dynamic_message_item_entity.dart';
import 'package:dada/model/dynamic_message_list_entity.dart';
import 'package:dada/model/dynamic_resonance_entity.dart';
import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/goods_entity.dart';
import 'package:dada/model/invite_play_entity.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/model/pay_info.dart';
import 'package:dada/model/post_entity.dart';
import 'package:dada/model/post_list_entity.dart';
import 'package:dada/model/prop_entity.dart';
import 'package:dada/model/recharge_detail_list_item_entity.dart';
import 'package:dada/model/region_limit_entity.dart';
import 'package:dada/model/reward_item_entity.dart';
import 'package:dada/model/search_room_history_result_entity.dart';
import 'package:dada/model/sign_in_item_entity.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';
import 'package:dada/model/small_room_detail_info_entity.dart';
import 'package:dada/model/small_room_explore_info_entity.dart';
import 'package:dada/model/small_room_mail_list_item_entity.dart';
import 'package:dada/model/small_room_mail_unread_msg_entity.dart';
import 'package:dada/model/small_room_see_me_entity.dart';
import 'package:dada/model/small_room_task_entity.dart';
import 'package:dada/model/store_goods_item_entity.dart';
import 'package:dada/model/sweet_bottle_entity.dart';
import 'package:dada/model/system_config_entity.dart';
import 'package:dada/model/teen_mode_status_entity.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/model/todo_together_list_entity.dart';
import 'package:dada/model/topic_item_entity.dart';
import 'package:dada/model/use_prop_result_entity.dart';
import 'package:dada/model/user_card_box_entity.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/model/invite_list_entity.dart';
import 'package:dada/pages/small_room/function_view/see_me/small_room_see_me_controller.dart';
import 'package:dada/services/network/api.dart';
import 'package:dada/services/network/http_utils.dart';
import 'package:dada/services/prop/prop_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:get/get.dart';
import 'package:sp_util/sp_util.dart';
import 'package:dada/model/user_avatar_entity.dart';

class ApiService extends GetxService {
  ApiService._();

  static init() {
    Get.put<ApiService>(ApiService._(), permanent: true);
  }

  factory ApiService() => Get.find<ApiService>();

  ///获取验证码
  Future<bool> sendSmsCode({String? phone}) async {
    String? success = await HttpUtil.post<String>(Api.getSmsCode,
        data: {
          "phone": phone ?? "",
        },
        showLoading: true);
    return success != null;
  }

  ///验证码登录 type：2 是取消注销登录
  Future<bool> loginWithCode(
      {String? code,
      String? phone,
      int? type,
      String? password,
      Function(ApiException)? onError}) async {
    Map<String, dynamic>? result = await HttpUtil.post<Map<String, dynamic>?>(
      Api.loginWithCode,
      data: {'phone': phone, 'code': code, 'type': type, 'password': password},
      showLoading: true,
      onError: onError,
    );
    if (result != null && result.isNotEmpty) {
      String? token = result["token"];
      String? userSig = result["userSig"];
      UserInfoEntity? userInfo;
      String? isRegister = result["isRegister"];
      if (token != null) {
        await SpUtil.putString("token", token);
      }
      if (userSig != null) {
        UserService().userSig = userSig;
      }
      if (result["user"] != null && isRegister == "1") {
        await SpUtil.putString("isRegister", isRegister!);
        userInfo = UserInfoEntity.fromJson(result["user"]);
        UserService().user = userInfo;
      }
      return true;
    }
    return false;
  }

  ///退出登录
  Future<bool> logout() async {
    bool? result = await HttpUtil.post<bool>(
      Api.logout,
      data: {},
      showLoading: true,
    );
    return result == true;
  }

  ///上传图片
  Future<String?> uploadFile(String filePath, {bool showLoading = true}) async {
    String? result = await HttpUtil.uploadFile<String>(filePath, Api.uploadFile,
        showLoading: showLoading);
    return result;
  }

  ///获取随机昵称
  Future<String?> getRandomNickname() async {
    String? nickName =
        await HttpUtil.post(Api.getRandomNickname, showLoading: true);
    return nickName;
  }

  ///注册
  Future<bool> register(
      {required String phone,
      required String nickname,
      required int sex,
      required int socialState,
      required String avatar,
      required int age,
      required String constellation,
      required List<String> labels}) async {
    UserInfoEntity? result = await HttpUtil.post(
      Api.register,
      data: {
        "phone": phone,
        "nickname": nickname,
        "sex": sex,
        "socialState": socialState,
        "avatar": avatar,
        "age": age,
        "constellation": constellation,
        "label": labels,
      },
      showLoading: true,
    );
    if (result?.id != null) {
      UserService().user = result;
      return true;
    }
    return false;
  }

  ///获取用户信息
  Future<UserInfoEntity?> getUserInfo(String userId,
      {bool? showLoading}) async {
    UserInfoEntity? result = await HttpUtil.post<UserInfoEntity>(
      Api.userInfo,
      data: {
        "id": userId,
      },
      showLoading: showLoading == true,
    );
    if (result?.id != null) {
      if (result?.id == UserService().user?.id) {
        UserService().user = result;
      }
    }
    return result;
  }

  ///修改用户信息
  Future<bool> editUserInfo(
      {String? avatar,
      String? nickName,
      int? sex,
      String? audioSignature,
      String? textSignature,
      String? birthday,
      String? work,
      String? hometown,
      int? voiceLength,
      int? socialState,
      int? age,
      String? constellation}) async {
    String? result = await HttpUtil.post<String>(
      Api.updateUser,
      data: {
        "id": UserService().user?.id,
        "avatar": avatar,
        "nickname": nickName,
        "sex": sex,
        "txtSignature": textSignature,
        "voiceSignature": audioSignature,
        "voiceLength": voiceLength,
        "birthday": birthday,
        "work": work,
        "hometown": hometown,
        "socialState": socialState,
        "age": age,
        "constellation": constellation
      },
      showLoading: true,
    );
    return result == "修改成功";
  }

  ///添加或修改用户标签
  Future<bool> addUserLabel({
    String? labelId,
    String? labelName,
    List<String>? labelText,
    List<String>? labelImgs,
  }) async {
    String? result = await HttpUtil.post<String?>(
      Api.addUserLabel,
      data: {
        "userId": UserService().user?.id,
        "labelId": labelId,
        "labelName": labelName,
        "labelText": labelText,
        "labelImgs": labelImgs,
      },
      showLoading: true,
    );
    return result == "添加成功";
  }

  ///删除用户标签
  Future<bool> deleteUserLabel({required String labelId}) async {
    String? result = await HttpUtil.post<String?>(
      Api.deleteUserLabel,
      data: {
        "userId": UserService().user?.id,
        "labelId": labelId,
      },
      showLoading: true,
    );
    return result == "删除成功";
  }

  ///获取用户卡片列表（cardType: 1. 身份卡；2.晒欧卡；3.穿搭卡；4.高光卡）
  Future<List<UserIdCardEntity>?> getUserCardList(
      {required String cardType,
      required int pageIndex,
      String? userId,
      bool? showLoading}) async {
    List<UserIdCardEntity>? result =
        await HttpUtil.post<List<UserIdCardEntity>>(
      Api.userCardList,
      data: {
        "userId": userId ?? UserService().user?.id,
        "cardType": cardType,
        "page": pageIndex,
      },
      showLoading: showLoading != false ? pageIndex == 1 : false,
    );
    return result;
  }

  /// 新增或修改用户卡片(CardType: 1.身份卡，2.晒欧卡，3.穿搭卡，4.高光卡)
  Future<bool> addUserCard({
    String? cardId,
    String? cardType,
    String? cardName,
    String? cardNickname,
    String? gameId,
    String? serverName,
    String? cardState,
    List<String>? cardText,
    String? cardUrl,
  }) async {
    String? result = await HttpUtil.post<String>(
      Api.addUserCard,
      data: {
        "userId": UserService().user?.id,
        "cardId": cardId,
        "cardType": cardType,
        "cardName": cardName,
        "cardNickname": cardNickname,
        "gameId": gameId,
        "serverName": serverName,
        "cardState": cardState,
        "cardText": cardText ?? [],
        "cardUrl": cardUrl,
      },
      showLoading: true,
    );
    return result == "添加成功";
  }

  ///删除用户卡片
  Future<bool> deleteUserCard({required String cardId}) async {
    String? result = await HttpUtil.post<String?>(
      Api.deleteUserCard,
      data: {
        "userId": UserService().user?.id,
        "cardId": cardId,
      },
      showLoading: true,
    );
    return result == "删除成功";
  }

  ///卡片操作 (operationType: 1.点赞；2.已阅；3.懂)
  Future<bool> operationCard({
    required String cardId,
    required String operationType,
    required bool required,
  }) async {
    String? result = await HttpUtil.post<String?>(
      Api.userCardOperation,
      data: {
        "userId": UserService().user?.id,
        "cardId": cardId,
        "type": operationType,
      },
      showLoading: true,
    );
    return result == "操作成功";
  }

  ///获取签到数据
  Future<SignInItemEntity?> getUserSignInData(bool showLoading) async {
    SignInItemEntity? result = await HttpUtil.get<SignInItemEntity>(
      Api.userSignInData,
      queryParameters: {
        "userId": UserService().user?.id,
      },
      showLoading: showLoading,
    );
    return result;
  }

  ///签到
  Future<List<RewardItemEntity>?> signIn() async {
    List<RewardItemEntity>? result =
        await HttpUtil.post<List<RewardItemEntity>?>(Api.signIn, data: {
      "userId": UserService().user?.id,
    });
    return result;
  }

  ///获取动态列表（type: 1. 推荐；2.好友；3.我的）
  Future<List<PostEntity>?> getDynamicList(
      {required int type, required int pageIndex, String? userId}) async {
    PostListEntity? result = await HttpUtil.post<PostListEntity>(
      Api.dynamicList,
      data: {
        "userId": userId ?? UserService().user?.id,
        "type": type,
        "limit": 10,
        "page": pageIndex,
      },
      showLoading: false,
    );
    return result?.list;
  }

  ///发布动态 (PostType: 1.纯文本；2.含图片)
  Future<bool> sendPost(
      {String? title,
      String? content,
      int? postType,
      List<String>? imageUrls}) async {
    String? result = await HttpUtil.post<String?>(
      Api.sendPost,
      data: {
        "userId": UserService().user?.id,
        "title": title,
        "content": content,
        "postType": postType,
        "postImgUrls": imageUrls,
      },
      showLoading: true,
    );
    return result == "发布成功";
  }

  ///点赞（likeType: 1.动态；2.评论；3.回复）
  Future<bool> postLike({
    required String postId,
    required int likeType,
  }) async {
    String? result = await HttpUtil.post<String?>(
      Api.postLike,
      data: {
        "userId": UserService().user?.id,
        "postId": postId,
        "likeType": likeType,
      },
      showLoading: true,
    );
    return result == "点赞成功";
  }

  ///取消点赞（likeType: 1.动态；2.评论；3.回复）
  Future<bool> postUnLike({
    required String postId,
    required int likeType,
    String? userId,
  }) async {
    String? result = await HttpUtil.post<String?>(
      Api.postUnLike,
      data: {
        "userId": UserService().user?.id,
        "postId": postId,
        "likeType": likeType,
      },
      showLoading: true,
    );
    return result == "取消成功";
  }

  ///删除动态
  Future<bool> postDelete({
    required String postId,
  }) async {
    String? result = await HttpUtil.post<String?>(
      Api.postDelete,
      data: {
        "postId": postId,
      },
      showLoading: true,
    );
    return result == "删除成功";
  }

  ///置顶动态（1.取消置顶；2.置顶）
  Future<bool> postTop({required String postId, required bool top}) async {
    String? result = await HttpUtil.post<String?>(
      Api.postTop,
      data: {
        "postId": postId,
        "isTop": top ? "2" : "1",
      },
      showLoading: true,
    );
    return result == "设置成功";
  }

  ///动态仅自己可见（0.公开；1.隐藏）
  Future<bool> postHide({required String postId, required bool hide}) async {
    String? result = await HttpUtil.post<String?>(
      Api.postHide,
      data: {
        "postId": postId,
        "state": hide ? "1" : "0",
      },
      showLoading: true,
    );
    return result == "设置成功";
  }

  ///获取动态详情
  Future<PostEntity?> getPostDetail({required String postId}) async {
    PostEntity? result = await HttpUtil.post<PostEntity>(
      Api.postDetail,
      data: {
        "userId": UserService().user?.id,
        "postId": postId,
      },
      showLoading: true,
    );
    return result;
  }

  ///获取动态评论列表
  Future<List<CommentItemEntity>?> getPostCommentList(
      {required String postId, required int pageIndex}) async {
    CommentListEntity? result = await HttpUtil.post<CommentListEntity>(
      Api.postCommentList,
      data: {
        "userId": UserService().user?.id,
        "limit": 10,
        "postId": postId,
        "page": pageIndex,
      },
      showLoading: pageIndex == 1,
    );
    return result?.list;
  }

  ///发布动态评论
  Future<bool> sendPostComment(
      {required String postId, required String content}) async {
    String? result = await HttpUtil.post<String>(
      Api.postComment,
      data: {
        "userId": UserService().user?.id,
        "postId": postId,
        "content": content,
        "commentsType": "1",
      },
      showLoading: true,
    );
    return result == "评论成功";
  }

  /// 查询动态限制地区
  Future<List<RegionLimitEntity>?> listPostRegionLimit() async {
    return await HttpUtil.get<List<RegionLimitEntity>>(Api.postRegionLimitList,
        showLoading: true);
  }

  /// 添加动态限制地区
  Future<bool> addPostRegionLimit({required String region}) async {
    bool? result = await HttpUtil.post<bool>(Api.postRegionLimitAdd,
        data: {"userId": UserService().user?.id, "region": region},
        showLoading: true);
    return result == true;
  }

  /// 删除动态限制地区
  Future<bool> deletePostRegionLimit({required String id}) async {
    bool? result = await HttpUtil.delete<bool>(Api.postRegionLimitDelete,
        queryParameters: {"id": id}, showLoading: true);
    return result == true;
  }

  ///发布评论回复 以及 回复的回复
  Future<bool> sendCommentReply(
      {required String commentsId,
      String? replyUserId,
      int? isAuthor,
      required String content}) async {
    String? result = await HttpUtil.post<String?>(
      Api.commentReply,
      data: {
        "userId": UserService().user?.id,
        "commentsId": commentsId,
        "replayUser": replyUserId,
        "isAuthor": isAuthor,
        "content": content,
        "repliesType": "1",
      },
      showLoading: true,
    );
    return result == "回复成功";
  }

  ///获取评论回复列表
  Future<List<CommentReplyItemEntity>?> getCommentReplyList(
      {required String commentId, required int pageIndex}) async {
    CommentReplyListEntity? result =
        await HttpUtil.post<CommentReplyListEntity>(
      Api.commentReplyList,
      data: {
        "userId": UserService().user?.id,
        "limit": 10,
        "commentsId": commentId,
        "page": pageIndex,
      },
      showLoading: pageIndex == 1,
    );
    return result?.list;
  }

  ///获取评论回复列表
  Future<List<DynamicMessageItemEntity>?> getDynamicMessageList(
      {required int pageIndex}) async {
    DynamicMessageListEntity? result =
        await HttpUtil.post<DynamicMessageListEntity>(
      Api.dynamicMessage,
      data: {
        "userId": UserService().user?.id,
        "limit": 10,
        "page": pageIndex,
      },
      showLoading: pageIndex == 1,
    );
    return result?.list;
  }

  ///获取匹配搭子列表
  /// type: -1:是不限，0：是男，1：是女
  Future<DadaMatchResultEntity?> getMatchDadaList(
      {required int type,
      List<String>? labels,
      List<String>? subLabels,
      bool? showLoading}) async {
    DadaMatchResultEntity? result = await HttpUtil.post<DadaMatchResultEntity>(
      Api.dadaMatch,
      data: {"type": type, "mustLabel": labels, "betterLabel": subLabels},
      showLoading: showLoading ?? true,
    );
    return result;
  }

  ///发送开始了解
  Future<bool> startKnown({required String userId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.startKnow,
      data: {
        "daUserId": userId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///发送结为搭子申请(搭子类型1纯搭,2浅搭,3浅搭随缘,4深搭)
  Future<bool> applyDazi(
      {required String userId, required int friendType}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.applyDazi,
      data: {
        "daUserId": userId,
        "friendType": friendType,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///创建小队
  Future<MatchTeamResultEntity?> createTeam({
    required String teamName,
    required int teamNumber,
    required List<String> labels,
  }) async {
    MatchTeamResultEntity? result = await HttpUtil.post<MatchTeamResultEntity>(
      Api.createTeam,
      data: {
        "teamName": teamName,
        "teamLabels": labels,
        "teamTotalNum": teamNumber
      },
      showLoading: false,
    );
    return result;
  }

  ///匹配小队
  Future<MatchTeamResultEntity?> matchTeam({
    required List<String> labels,
  }) async {
    MatchTeamResultEntity? result = await HttpUtil.post<MatchTeamResultEntity>(
      Api.matchTeam,
      data: {
        "teamLabels": labels,
      },
      showLoading: false,
    );
    return result;
  }

  ///集结处大厅
  Future<List<MatchTeamResultEntity>?> getAssemblePlaceHallList({
    required int pageIndex,
    required int showFilled,
    required int showLocked,
  }) async {
    List<MatchTeamResultEntity>? result =
        await HttpUtil.post<List<MatchTeamResultEntity>>(
      Api.assembleHall,
      data: {
        "limit": 10,
        "page": pageIndex,
        "isFull": showFilled,
        "isLock": showLocked,
      },
      showLoading: true,
    );
    return result;
  }

  ///集结处大厅
  Future<List<MatchTeamResultEntity>?> searchTeam(
      {String? teamNo,
      String? teamName,
      required int showFilled,
      required int showLocked}) async {
    List<MatchTeamResultEntity>? result =
        await HttpUtil.post<List<MatchTeamResultEntity>>(
      Api.searchTeam,
      data: {
        "teamNo": teamNo,
        "teamName": teamName,
        "isFull": showFilled,
        "isLock": showLocked,
      },
      showLoading: true,
    );
    return result;
  }

  ///获取小队详情
  Future<MatchTeamResultEntity?> getAssembleTeamInfo({
    required String teamId,
    bool? showLoading,
  }) async {
    MatchTeamResultEntity? result = await HttpUtil.post<MatchTeamResultEntity>(
      Api.teamInfo,
      data: {
        "teamId": teamId,
      },
      showLoading: showLoading ?? true,
    );
    return result;
  }

  ///加入小队
  Future<MatchTeamResultEntity?> joinTeam(
      {required String teamId, String? pwd}) async {
    MatchTeamResultEntity? result = await HttpUtil.post<MatchTeamResultEntity>(
      Api.joinTeam,
      data: {
        "teamId": teamId,
        "teamPassword": pwd,
      },
      showLoading: true,
    );
    return result;
  }

  ///邀请加入小队
  Future<MatchTeamResultEntity?> inviteToJoinTeam(
      {required String teamId, required String userId, String? pwd}) async {
    MatchTeamResultEntity? result = await HttpUtil.post<MatchTeamResultEntity>(
      Api.inviteTeam,
      data: {
        "teamId": teamId,
        "teamPassword": pwd,
        "userId": userId,
      },
      showLoading: true,
    );
    return result;
  }

  ///小队上锁 or 解锁 （type: 1.上锁; 2.解锁）
  Future<bool> lockTeam({
    required String teamId,
    required int type,
    String? pwd,
  }) async {
    String? result = await HttpUtil.post<String>(
      Api.teamLock,
      data: {
        "teamId": teamId,
        "type": type,
        "teamPassword": pwd,
      },
      showLoading: true,
    );
    return result == "设置成功";
  }

  ///小队信息修改
  Future<bool> editTeamInfo({
    required String teamId,
    String? teamName,
    int? teamMatchState,
    List? teamLabels,
  }) async {
    String? result = await HttpUtil.post<String>(
      Api.teamUpdate,
      data: {
        "teamId": teamId,
        "teamName": teamName,
        "teamMatchState": teamMatchState,
        "teamLabels": teamLabels
      },
      showLoading: true,
    );
    return result != null;
  }

  ///退出小队
  Future<bool> exitTeam({required String teamId}) async {
    String? result = await HttpUtil.post<String>(
      Api.exitTeam,
      data: {
        "teamId": teamId,
      },
      showLoading: false,
    );
    return result != null;
  }

  ///小队踢人
  Future<bool> kickOutOfTeam(
      {required String teamId, required String? userId}) async {
    String? result = await HttpUtil.post<String>(
      Api.kickTeamMember,
      data: {
        "teamId": teamId,
        "userId": userId,
      },
      showLoading: true,
    );
    return result != null;
  }

  ///邀请加入小队
  Future<bool> inviteJoinTeam(
      {required String teamId, required String? userId}) async {
    String? result = await HttpUtil.post<String>(
      Api.teamLock,
      data: {
        "teamId": teamId,
        "userId": userId,
      },
      showLoading: true,
    );
    return result != null;
  }

  ///获取通讯录
  Future<List<ContactGroupListItemEntity>?> loadContactList(
      {bool? showLoading}) async {
    List<ContactGroupListItemEntity>? list =
        await HttpUtil.get<List<ContactGroupListItemEntity>>(
      Api.contactsList,
      showLoading: showLoading ?? true,
    );
    return list;
  }

  ///加好友
  Future<bool> sendAddFriendRequest(
      {required String userFriendId,
      String? applyMsg,
      int? friendType,
      String? friendGroupId,
      String? friendRemark,
      String? friendGroupName,
      int? lookMeState}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.addFriend,
      data: {
        "userId": UserService().user?.id,
        "userFriendId": userFriendId,
        "friendType": friendType,
        "applyMsg": applyMsg,
        "friendGroupId": friendGroupId,
        "friendRemark": friendRemark?.isEmpty == true ? null : friendRemark,
        "friendGroupName": friendGroupName,
        "lookMeState": lookMeState,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///同意他人加好友申请
  Future<bool> agreeFriendApply(
      {required String userFriendId,
      String? friendGroupId,
      String? friendRemark,
      String? friendGroupName,
      int? lookMeState,
      int? lookFriendState,
      int? disturbed}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.agreeFriendApply,
      data: {
        "userId": UserService().user?.id,
        "userFriendId": userFriendId,
        "friendGroupId": friendGroupId,
        "friendRemark": friendRemark?.isEmpty == true ? null : friendRemark,
        "friendGroupName": friendGroupName,
        "lookMeState": lookMeState,
        "lookFriendState": lookFriendState,
        "isDisturb": disturbed,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///同意他人加搭子申请(state: 1:同意；2：拒绝；3：维持现状)
  Future<bool> handleDaziApply({required String userId, int? state}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.agreeDaziApply,
      data: {
        "userId": userId,
        "state": state,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取好友信息详情
  Future<FriendUserInfoEntity?> getFriendUserInfo(String userId,
      {bool? showLoading}) async {
    FriendUserInfoEntity? friendUserInfo =
        await HttpUtil.get<FriendUserInfoEntity>(
      Api.friendInfo,
      queryParameters: {
        "id": userId,
      },
      showLoading: showLoading ?? true,
    );
    return friendUserInfo;
  }

  ///修改好友信息
  Future<bool> updateFriendUserInfo(
    String userId, {
    String? friendGroupId,
    String? friendRemark,
    String? friendGroupName,
    int? lookMeState,
    int? lookFriendState,
    int? disturbed,
    int? blacked,
  }) async {
    bool? result = await HttpUtil.put<bool>(
      Api.updateFriendInfo,
      data: {
        "userFriendId": userId,
        "friendGroupId": friendGroupId,
        "friendRemark": friendRemark?.isEmpty == true ? null : friendRemark,
        "friendGroupName": friendGroupName,
        "lookMeState": lookMeState,
        "lookFriendState": lookFriendState,
        "isDisturb": disturbed,
        "isBlack": blacked,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取好友自定义分组
  Future<List<FriendSubGroupEntity>?> getFriendCustomGroupList() async {
    List<FriendSubGroupEntity>? list =
        await HttpUtil.get<List<FriendSubGroupEntity>>(
            Api.friendCustomGroupList,
            queryParameters: {
          "userId": UserService().user?.id,
        });
    return list;
  }

  ///修改自定义好友分组信息
  Future<bool> updateFriendCustomGroupInfo(
      String groupId, String groupName) async {
    bool? result = await HttpUtil.put<bool>(
      Api.updateFriendCustomGroupInfo,
      data: {
        "userId": UserService().user?.id,
        "id": groupId,
        "groupName": groupName,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///自定义好友分组排序
  Future<bool> sortFriendCustomGroupList(
      List<Map<String, dynamic>> list) async {
    bool? result = await HttpUtil.post<bool>(
      Api.sortFriendCustomGroupList,
      data: list,
      showLoading: true,
    );
    return result == true;
  }

  ///添加自定义好友分组
  Future<bool> addFriendCustomGroup(String groupName, int sort) async {
    bool? result = await HttpUtil.post<bool>(
      Api.addFriendCustomGroup,
      data: {
        "userId": UserService().user?.id,
        "groupName": groupName,
        "sort": sort,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///删除自定义好友分组
  Future<bool> deleteFriendCustomGroup(String groupId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.deleteFriendCustomGroup,
      data: {
        "ids": groupId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///搜索 好友/群/陌生人
  ///SearchType: 0: 全部 1：个人的好友和群
  Future<ChatSearchedResultEntity?> searchUsersAndGroupList(
      String keyword, int searchType) async {
    ChatSearchedResultEntity? result = await HttpUtil.post(
      Api.searchAllUsersAndGroups,
      data: {
        "keyword": keyword,
        "searchType": searchType,
      },
      showLoading: true,
    );
    return result;
  }

  ///获取群聊信息详情
  Future<ChatGroupInfoEntity?> getGroupInfo(String groupID) async {
    ChatGroupInfoEntity? result = await HttpUtil.get<ChatGroupInfoEntity>(
      Api.getGroupInfo,
      queryParameters: {
        "id": groupID,
      },
      showLoading: false,
    );
    return result;
  }

  ///删除好友
  Future<bool> deleteFriendUser(String userID) async {
    bool? result = await HttpUtil.delete<bool>(
      Api.deleteFriend,
      queryParameters: {
        "id": int.parse(userID),
      },
      showLoading: true,
    );
    return result == true;
  }

  ///加入黑名单
  Future<bool> addToBlackList(String userID) async {
    bool? result = await HttpUtil.post<bool>(
      Api.addToBlackList,
      data: {
        "userBlackid": userID,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///移出黑名单
  Future<bool> deleteFromBlackList(String userID) async {
    bool? result = await HttpUtil.delete<bool>(
      Api.deleteFromBlackList,
      queryParameters: {
        "userBlackId": userID,
      },
      showLoading: true,
    );
    return result == true;
  }

  Future<ChatGroupInfoEntity?> createGroup({required String groupName}) async {
    ChatGroupInfoEntity? result = await HttpUtil.post<ChatGroupInfoEntity>(
      Api.createChatGroup,
      data: {
        "chatsName": groupName,
      },
      showLoading: true,
    );
    return result;
  }

  ///修改群信息
  Future<bool> updateGroupInfo(
      {required String groupID,
      String? groupName,
      String? faceUrl,
      String? notice,
      int? isDisturb,
      String? creatorId,
      List<Map<String, String>>? tagList}) async {
    bool? result = await HttpUtil.put<bool>(
      Api.updateGroupInfo,
      data: {
        "id": groupID,
        "chatsName": groupName,
        "avatarUrl": faceUrl,
        "notice": notice,
        "isDisturb": isDisturb,
        "tagList": tagList,
        "creatorId": creatorId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///解散群聊
  Future<bool> disbandGroup({required String groupID}) async {
    bool? result = await HttpUtil.delete<bool>(
      Api.disbandGroup,
      queryParameters: {
        "ids": groupID,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取搜索历史
  Future<List<String>?> getSearchHistoryList() async {
    List<String>? result = await HttpUtil.get<List<String>>(
      Api.searchHistoryList,
      showLoading: false,
    );
    return result;
  }

  ///获取存甜罐数量
  Future<int> querySweetTotal(String daUserId) async {
    String? count = await HttpUtil.post<String>(Api.sweetTotal,
        showLoading: true, queryParameters: {"daUserId": daUserId});
    return int.parse(count ?? "0");
  }

  ///获取存甜罐列表
  Future<List<SweetBottleEntity>?> querySweetBottleList(
      String daUserId, int page) async {
    return await HttpUtil.post<List<SweetBottleEntity>>(Api.sweetList,
        data: {"daUserId": daUserId, "page": page, "limit": 10});
  }

  ///添加存甜罐
  Future<bool> addSweetBottle(
      String daUserId, String? url, String? content, String? bottleId) async {
    var data = {"daUserId": daUserId, "url": url, "content": content};
    if (bottleId != null) {
      data["sweetBottleId"] = bottleId;
    }
    bool? result = await HttpUtil.post<bool>(Api.insertSweet, data: data);
    return result == true;
  }

  ///删除存甜罐
  Future<bool> deleteSweetBottle(String sweetBottleId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.deleteSweet,
      data: {"sweetBottleId": sweetBottleId},
    );
    return result == true;
  }

  ///查询邀请去玩
  Future<List<InvitePlayEntity>?> queryInvitePlayList() async {
    Completer<List<InvitePlayEntity>?> completer = Completer();
    HttpUtil.get<List<InvitePlayEntity>>(Api.invitePlayList,
        onResponse: (data) {
      completer.complete(data.data ?? []);
    }, onError: (error) {
      completer.complete(null);
    });
    return completer.future;
  }

  ///添加邀请去玩
  Future<bool> addInvitePlay(String text) async {
    bool? result = await HttpUtil.post<bool>(
      Api.addInvitePlay,
      data: {
        "userId": UserService().user?.id,
        "text": text,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///更新邀请去玩
  Future<bool> updateInvitePlay(String id, String text) async {
    bool? result = await HttpUtil.put<bool>(
      Api.updateInvitePlay,
      data: {
        "id": id,
        "userId": UserService().user?.id,
        "text": text,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///删除邀请去玩
  Future<bool> deleteInvitePlay(String invitePlayId) async {
    bool? result = await HttpUtil.delete<bool>(
      Api.deleteInvitePlay,
      data: invitePlayId,
      showLoading: true,
    );
    return result == true;
  }

  ///删除搜索历史
  Future<bool> deleteSearchHistoryList(List<String> list) async {
    bool? result = await HttpUtil.post<bool>(
      Api.deleteSearchHistoryList,
      data: list,
      showLoading: false,
    );
    return result == true;
  }

  ///保存语音到语音盒子
  Future<bool> saveAudioToAudioBox(
      {required String audioUrl, required int duration, String? remark}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.saveAudioToBox,
      data: {
        "videoUrl": audioUrl,
        "voiceLength": duration,
        "remark": remark,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取邮件信息列表（type: 1.弹幕；2.系统消息；3.搭子申请消息）
  Future<List<SmallRoomMailListItemEntity>?> getSmallRoomMailMessageList(
      {required String dadaRoomId,
      required int type,
      required int page}) async {
    List<SmallRoomMailListItemEntity>? list =
        await HttpUtil.post<List<SmallRoomMailListItemEntity>>(
      Api.getMailMessageList,
      data: {
        "page": page,
        "limit": 10,
        "type": type,
        "dadaRoomId": dadaRoomId,
      },
      showLoading: true,
    );
    return list;
  }

  ///获取小屋信息详情
  Future<SmallRoomDetailInfoEntity?> getSmallRoomDetailRoomInfo(
      {required String userId, bool? showLoading}) async {
    SmallRoomDetailInfoEntity? result =
        await HttpUtil.post<SmallRoomDetailInfoEntity>(
      Api.roomInfo,
      data: {
        "userId": userId,
      },
      showLoading: showLoading ?? true,
    );
    return result;
  }

  ///保存或修改气泡语
  Future<SmallRoomBubbleWordEntity?> saveSmallRoomBubbleWord({
    required String userId,
    required String dadaRoomId,
    required String content,
    required int type,
    int? voiceLength,
    String? bubbleId,
  }) async {
    SmallRoomBubbleWordEntity? result =
        await HttpUtil.post<SmallRoomBubbleWordEntity>(
      Api.addRoomBubbleWord,
      data: {
        "userId": userId,
        "dadaRoomId": dadaRoomId,
        "content": content,
        "bubbleId": bubbleId,
        "type": type,
        "voiceLength": voiceLength,
      },
      showLoading: true,
    );
    return result;
  }

  ///删除气泡语
  Future<bool> deleteSmallRoomBubbleWord({required String bubbleId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.deleteRoomBubbleWord,
      data: {
        "bubbleId": bubbleId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///小屋内添加弹幕
  Future<bool> addSmallRoomBarrage({
    required String roomId,
    required String content,
  }) async {
    bool? result = await HttpUtil.post<bool>(
      Api.addRoomBarrage,
      data: {
        "userId": UserService().user?.id,
        "dadaRoomId": roomId,
        "type": "1",
        "content": content,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///小屋内删除弹幕
  Future<bool> deleteSmallRoomBarrage({required String roomMsgId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.deleteRoomBarrage,
      data: {
        "roomMsgId": roomMsgId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///是否接收搭子申请
  Future<bool> updateRoomDaziApply({required int isAccept}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.updateRoomDaziApplyState,
      data: {
        "id": UserService().user?.id,
        "isAccept": isAccept,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///修改小屋信息
  Future<bool> updateRoomInfo(
      {required String roomId,
      String? musicContent,
      String? computerContent,
      String? bookContent,
      int? roomLifeState}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.updateRoomInfo,
      data: {
        "dadaRoomId": roomId,
        "musicContent": musicContent,
        "computerContent": computerContent,
        "bookContent": bookContent,
        "roomLifeState": roomLifeState,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///修改搭子关系
  Future<bool> updateDaziRelation(
      {required String userFriendId,
      required int friendType,
      String? daName}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.updateDaziRelation,
      data: {
        "id": UserService().user?.id,
        "userFriendId": userFriendId,
        "friendType": friendType,
        "daName": daName,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///把人踢出小屋
  Future<bool> kickOutUserFromSmallRoom({required String userId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.kickOutUserFromSmallRoom,
      data: {
        "daUserId": userId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///任务列表
  Future<List<SmallRoomTaskEntity>?> getTaskList(
      {required String daRoomId, bool? showLoading}) async {
    List<SmallRoomTaskEntity>? taskList =
        await HttpUtil.post<List<SmallRoomTaskEntity>>(
      Api.roomTaskList,
      data: {
        "daRoomId": daRoomId,
      },
      showLoading: showLoading ?? true,
    );
    return taskList;
  }

  ///创建语音房（RoomType: 1.市集；2.茶壶）
  Future<ChatRoomInfoEntity?> createChatRoom(
      {required String roomName,
      required int roomType,
      required List<String> topicList}) async {
    ChatRoomInfoEntity? result = await HttpUtil.post<ChatRoomInfoEntity>(
      Api.createChatRoom,
      data: {
        "roomName": roomName,
        "roomType": roomType,
        "createRoomUserId": UserService().user?.id,
        "currentRoomUserId": UserService().user?.id,
        "roomTopicList": topicList,
      },
      showLoading: true,
    );
    return result;
  }

  ///匹配房间
  Future<ChatRoomInfoEntity?> getRandomMatchingRoom(
      {required int roomType, bool? showLoading}) async {
    ChatRoomInfoEntity? result = await HttpUtil.get<ChatRoomInfoEntity>(
      Api.matchChatRoom,
      queryParameters: {
        "roomType": roomType,
      },
      showLoading: showLoading ?? true,
    );
    return result;
  }

  ///（市集、茶壶）房间详情信息
  Future<ChatRoomInfoEntity?> getChatRoomInfo(
      {required String roomId, bool? showLoading}) async {
    ChatRoomInfoEntity? result = await HttpUtil.get<ChatRoomInfoEntity>(
      Api.chatRoomInfo,
      queryParameters: {
        "roomNo": roomId,
      },
      showLoading: showLoading ?? true,
    );
    return result;
  }

  ///获取房间内麦位列表
  Future<List<ChatRoomSeatInfoEntity>?> getChatRoomSeatList(
      {required String roomId}) async {
    List<ChatRoomSeatInfoEntity>? seatList =
        await HttpUtil.get<List<ChatRoomSeatInfoEntity>>(
      Api.chatRoomSeatList,
      queryParameters: {"roomNo": roomId},
      showLoading: false,
    );
    return seatList;
  }

  ///获取房间内麦位列表(type: 0:上麦、1：下麦、2：换麦, )
  Future<bool> setChatRoomAttributes(
      {required String roomId,
      required String key,
      required int type,
      required int index,
      required dynamic value}) async {
    Map<String, dynamic> json = {
      "GroupId": roomId,
      "GroupAttr": [
        {"key": key, "value": jsonEncode(value)}
      ],
    };
    bool? result = await HttpUtil.post<bool>(
      Api.setChatRoomAttributes,
      data: {
        "index": index,
        "type": type,
        "json": jsonEncode(json),
      },
      showLoading: false,
    );
    return result == true;
  }

  ///房间内麦位变化(type: 0:上麦、1：下麦、2：换麦, 3.红蓝方)
  Future<bool> changeRoomSeat({
    required String roomId,
    required ChatRoomSeatInfoEntity seatInfo,
    required int type,
    int? applyIndex,
    String? applyUserId,
  }) async {
    bool? result = await HttpUtil.post<bool>(
      Api.setChatRoomAttributes,
      data: {
        "groupId": roomId,
        "type": type,
        "groupAttr": seatInfo.toJson(),
        "applyIndex": applyIndex,
        "applyUserId": applyUserId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取房间内自己的角色形象配置
  Future<ChatRoomSeatInfoUser?> getMyRoleInfoInRoom() async {
    ChatRoomSeatInfoUser? result = await HttpUtil.post<ChatRoomSeatInfoUser>(
      Api.getRandomRoomUserRoleInfo,
      data: {},
      showLoading: false,
    );
    return result;
  }

  ///获取房间列表
  Future<ChatRoomListResultEntity?> getChatRoomList(int page) async {
    ChatRoomListResultEntity? result =
        await HttpUtil.post<ChatRoomListResultEntity>(
      Api.chatRoomList,
      data: {
        "page": page,
        "limit": 10,
      },
      showLoading: page == 1,
    );
    return result;
  }

  ///转让房主
  Future<bool> transformRoomOwner(String roomId, String userId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.chatRoomChangeOwner,
      data: {
        "roomNo": roomId,
        "userId": userId,
      },
      showLoading: false,
    );
    return result == true;
  }

  ///解散房间（仅房主）
  Future<bool> dismissRoom(String roomId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.deleteChatRoom,
      data: roomId,
      showLoading: false,
    );
    return result == true;
  }

  ///修改房间名称
  Future<bool> editRoomName(
      {required String roomId, required String roomName}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.updateChatRoom,
      data: {
        "roomNo": roomId,
        "roomName": roomName,
      },
      showLoading: false,
    );
    return result == true;
  }

  ///更新房间话题
  Future<bool> updateRoomTopic(
      {required String roomId,
      required List<TopicItemEntity> topicList}) async {
    List<Map<String, dynamic>> topicMapList =
        topicList.map((topic) => topic.toJson()).toList();
    bool? result = await HttpUtil.put<bool>(
      Api.updateRoomTopic,
      data: {
        "roomNo": roomId,
        "roomTopicList": topicMapList,
      },
      showLoading: false,
    );
    return result == true;
  }

  /// 使用道具（送搭棒、搭币、角色换装）（propId必传）
  /// 1.赠送搭搭棒的情况下 传递 propNo num giftsUserId 参数
  /// 2.使用染色剂的情况下 传递 propId dressNo colorId
  /// 3.使用全域喇叭的情况下 传递 propId text num
  /// 4.使用头像框、对话框的情况下 传递要使用的道具propId
  /// 5.切换衣服的情况下 传递要切换衣服的 propId dressNo useType == 0
  /// 6.切换当前衣服颜色的情况下。传递 propId useType == 0
  /// 7.在背包中使用衣服道具的情况下 传递 propId/propNo useType == 1
  /// 8.在背包中赠送衣服道具给其他用户的情况下 传递 propId/propNo useType == 2
  /// 9.在背包中染色剂粉末的情况下 传递propId
  /// 10.在背包中使用宝箱的情况下 传递propId num
  Future<UsePropResultEntity?> useProp(
      {String? propId,
      int? num,
      String? toUserId,
      String? dressNo,
      String? text,
      String? propNo,
      int? useType,
      int? colorId,
      required PropType propType}) async {
    UsePropResultEntity? result = await HttpUtil.post<UsePropResultEntity>(
      Api.useProp,
      data: {
        "propId": propId,
        "propNo": propNo,
        "giftsUserId": toUserId,
        "text": text,
        "dressNo": dressNo,
        "num": num,
        "useType": useType,
        "colorId": colorId,
      },
      showLoading: true,
    );
    PropManager().checkShouldShowUsePropDialog(result, propType);
    return result;
  }

  ///首页搜索房间、话题
  Future<ChatRoomListResultEntity?> searchRoomWithKeyword(
      String keyword) async {
    ChatRoomListResultEntity? result =
        await HttpUtil.post<ChatRoomListResultEntity>(
      Api.searchRoom,
      data: {
        "keyword": keyword,
      },
      showLoading: true,
    );
    return result;
  }

  ///首页搜索房间历史记录
  Future<SearchRoomHistoryResultEntity?> getSearchRoomHistory(
      {bool? showLoading}) async {
    SearchRoomHistoryResultEntity? result =
        await HttpUtil.get<SearchRoomHistoryResultEntity>(
      Api.searchRoomHistoryList,
      queryParameters: {},
      showLoading: showLoading ?? true,
    );
    return result;
  }

  ///话题提名
  Future<bool> topicNomination({required String keyword}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.topicNominate,
      queryParameters: {
        "keyword": keyword,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///账户明细
  Future<List<RechargeDetailListItemEntity>?> getRechargeDetailList(
      {required int page}) async {
    List<RechargeDetailListItemEntity>? result =
        await HttpUtil.post<List<RechargeDetailListItemEntity>>(
      Api.billDetail,
      data: {
        "page": page,
        "limit": 20,
      },
      showLoading: false,
    );
    return result;
  }

  ///商城货品列表
  Future<List<StoreGoodsItemEntity>?> getStoreGoodsList(
      {required int shopType, int? propType}) async {
    List<StoreGoodsItemEntity>? result =
        await HttpUtil.post<List<StoreGoodsItemEntity>>(
      Api.storeList,
      data: {
        "shopType": shopType,
        "propType": propType,
      },
      showLoading: false,
    );
    return result;
  }

  ///删除首页房间搜索历史
  Future<bool> deleteHomeSearchHistoryList(List<String> list) async {
    bool? result = await HttpUtil.post<bool>(
      Api.deleteHomeSearchList,
      data: list,
      showLoading: false,
    );
    return result == true;
  }

  ///获取背包道具列表
  ///
  /// queryType: 0: 背包消耗类型道具；1：角色装扮道具
  ///
  /// queryType 为 1 时，propType类型：0：服装 1：头像框 2：聊天框 3：特效
  ///
  Future<List<PropEntity>?> getBackpackPropList(
      {required int queryType, required int page, int? propType}) async {
    List<PropEntity>? result =
        await HttpUtil.post<List<PropEntity>>(Api.getBackpackPropList, data: {
      "page": page,
      "limit": 12,
      "queryType": queryType,
      "roleType": propType ?? 0,
    });
    return result;
  }

  ///获取共鸣列表
  Future<List<DynamicResonanceEntity>?> getDynamicResonanceList(
      {required int page}) async {
    List<DynamicResonanceEntity>? result =
        await HttpUtil.post<List<DynamicResonanceEntity>>(
      Api.dynamicResonanceList,
      data: {
        "page": page,
      },
      showLoading: false,
    );
    return result;
  }

  ///获取用户账户余额
  Future<AccountBalanceEntity?> getUserAccountBalance() async {
    AccountBalanceEntity? result = await HttpUtil.get<AccountBalanceEntity>(
      Api.getUserAccountBalance,
      showLoading: false,
    );
    return result;
  }

  ///收藏道具
  Future<bool> collectProp(
      {required String propId, required bool favorites}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.collectProp,
      data: {
        "propId": propId,
        "favorites": favorites == true ? 1 : 0,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///赠送道具
  Future<bool> sendProp(
      {required String propId, required String userId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.sendProp,
      data: {
        "propId": propId,
        "userId": userId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///根据动态id查询共鸣列表
  Future<List<PostEntity>?> getResonancePostList(
      {required String postId}) async {
    List<PostEntity>? result = await HttpUtil.post<List<PostEntity>?>(
      Api.dynamicResonancePostList,
      queryParameters: {
        "postId": postId,
      },
      showLoading: true,
    );
    return result;
  }

  ///批量删除共鸣数据
  Future<bool> deleteResonancePost({required List<String> postIds}) async {
    bool? result = await HttpUtil.delete<bool>(
      Api.deleteResonancePost,
      queryParameters: {
        "ids": postIds,
      },
      showLoading: false,
    );
    return result == true;
  }

  ///获取搭圈消息未读数
  Future<int?> getDynamicUnreadCount() async {
    int? result = await HttpUtil.post<int>(
      Api.getDynamicMsgUnreadCount,
      showLoading: false,
    );
    return result;
  }

  ///获取语音盒子列表
  Future<List<AudioBoxListItemEntity>?> getAudioBoxList(
      {required int page}) async {
    List<AudioBoxListItemEntity>? result =
        await HttpUtil.get<List<AudioBoxListItemEntity>>(
      Api.audioBoxList,
      queryParameters: {
        "page": page,
        "limit": 10,
      },
      showLoading: page == 1,
    );
    return result;
  }

  ///删除语音盒子
  Future<bool> deleteAudioBox({required String audioId}) async {
    bool? result = await HttpUtil.delete<bool>(
      Api.deleteAudioToBox,
      queryParameters: {
        "ids": [audioId],
      },
      showLoading: true,
    );
    return result == true;
  }

  ///分享搭圈成功调一下接口
  Future<bool> sendSharePost({required String postId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.sharePostSuccess,
      queryParameters: {
        "postId": postId,
      },
      showLoading: false,
    );
    return result == true;
  }

  ///填写邀请码
  Future<bool> submitInvitationCode({required String code}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.submitInvitationCode,
      queryParameters: {
        "inviteCode": code,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///修改绑定手机号
  Future<bool> changeBindPhone(
      {required String phone, required String code}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.changePhone,
      queryParameters: {
        "phone": phone,
        "code": code,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///实名认证
  Future<bool> realNameAuth(
      {required String name, required String idCard}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.realAuth,
      data: {
        "name": name,
        "identifyNum": idCard,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///查询是否开启接收陌生人消息
  Future<bool> getStrangerMsgState({required String userId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.getStrangerMsgState,
      queryParameters: {
        "userId": userId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///修改是否开启接收陌生人消息
  Future<bool> updateStrangerMsgState({required bool state}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.updateStrangerMsgState,
      queryParameters: {
        "state": state,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取充值商品列表
  Future<List<GoodsEntity>?> getGoodsList({required int type}) async {
    List<GoodsEntity>? result = await HttpUtil.get<List<GoodsEntity>>(
      Api.rechargeList,
      queryParameters: {
        "type": type,
      },
      showLoading: true,
    );
    return result;
  }

  ///创建订单
  Future<PayInfo?> createOrder(
      {required String goodsId,
      required PayType payType,
      required int buyNum,
      required double payAmount,
      String? remark}) async {
    PayInfo? result = await HttpUtil.post<PayInfo>(
      Api.createOrder,
      data: {
        "goodsId": goodsId,
        "buyNum": buyNum,
        "remark": remark,
        "payType": payType.index,
        "goodsPrice": payAmount,
      },
      showLoading: true,
    );
    return result;
  }

  ///获取小屋探索信息
  Future<SmallRoomExploreInfoEntity?> getExploreInfo() async {
    SmallRoomExploreInfoEntity? result =
        await HttpUtil.post<SmallRoomExploreInfoEntity>(
      Api.getExploreInfo,
      showLoading: false,
    );
    return result;
  }

  ///开始探索
  Future<String?> startExplore() async {
    String? result = await HttpUtil.post<String>(
      Api.startExplore,
      showLoading: false,
    );
    return result;
  }

  ///收获探索
  Future<SmallRoomExploreInfoEntity?> getExplore() async {
    SmallRoomExploreInfoEntity? result =
        await HttpUtil.post<SmallRoomExploreInfoEntity>(
      Api.getExplore,
      showLoading: false,
    );
    return result;
  }

  ///苹果支付验证结果
  Future<bool> verifyApplePayResult(
      {required String orderNo,
      required String productId,
      required String receiptData}) async {
    int? result = await HttpUtil.post<int>(
      Api.applePayVerify,
      data: {
        "orderCode": orderNo,
        "productId": productId,
        "receiptDate": receiptData,
      },
      showLoading: true,
    );
    return result == 1;
  }

  ///邀请用户列表
  Future<InviteListEntity?> getInviteList() async {
    InviteListEntity? result = await HttpUtil.get<InviteListEntity>(
      Api.getInviteList,
      showLoading: false,
    );
    return result;
  }

  ///注销账号
  Future<bool> logoffAccount() async {
    bool? result = await HttpUtil.post<bool>(
      Api.logOff,
      showLoading: true,
    );
    return result == true;
  }

  ///做任务
  Future<bool> doTask({required String taskId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.doTask,
      data: {
        "roomTaskId": taskId,
      },
      showLoading: false,
    );
    return result == true;
  }

  ///衣服转换性别
  Future<bool> clothesConvert({required int packageId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.clothConvert,
      queryParameters: {
        "packageId": packageId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取水晶球记录
  Future<List<CrystalBallLifeRecordEntity>?> getCrystalBallLifeRecordList(
      {required String userId}) async {
    List<CrystalBallLifeRecordEntity>? result =
        await HttpUtil.post<List<CrystalBallLifeRecordEntity>>(
      Api.crystalBallRecord,
      data: {
        "userId": userId,
      },
      showLoading: true,
    );
    return result;
  }

  ///获取水晶球记录
  Future<CrystalBallLifeRecordEntity?> getCrystalBallLifeRecordDetail(
      {required String userId, required int year}) async {
    CrystalBallLifeRecordEntity? result =
        await HttpUtil.post<CrystalBallLifeRecordEntity>(
      Api.crystalBallRecordDetail,
      data: {
        "userId": userId,
        "year": year,
      },
      showLoading: true,
    );
    return result;
  }

  ///发布新的水晶球记录
  Future<bool> publishCrystalBallLifeRecord(
      {required String userId,
      required int year,
      required List<String> imgUrls,
      required String content}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.crystalBallAddRecord,
      data: {
        "year": year,
        "imgUrls": imgUrls,
        "content": content,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///领取任务奖励
  Future<bool> getTaskReward({required String taskId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.getTaskReward,
      data: {
        "roomTaskId": taskId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///头像列表
  Future<List<UserAvatarEntity>?> getAvatarList(
      {required int type, int? price}) async {
    List<UserAvatarEntity>? result =
        await HttpUtil.post<List<UserAvatarEntity>>(
      Api.getAvatarList,
      queryParameters: {"type": type, "price": price},
      showLoading: false,
    );
    return result;
  }

  ///兑换头像
  Future<UserAvatarEntity?> buyAvatar({required String avatarId}) async {
    UserAvatarEntity? result = await HttpUtil.post<UserAvatarEntity>(
      Api.bugAvatar,
      data: {"avatarId": avatarId},
      showLoading: false,
    );
    return result;
  }

  ///获取共鸣未读数量
  Future<int> getPostResonateUnreadCount({bool? showLoading}) async {
    int? result = await HttpUtil.get<int>(
      Api.getPostResonateUnreadCount,
      showLoading: false,
    );
    return result ?? 0;
  }

  ///获取是否设置密码状态
  Future<bool> isSetPassword({bool? showLoading}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.isSetPassword,
      showLoading: false,
    );
    return result == true;
  }

  ///设置密码
  Future<bool> setPassword(String password, {String? phone}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.setPassword,
      data: {"password": password, "phone": phone},
      showLoading: false,
    );
    return result == true;
  }

  ///设置密码(个人页，需要登录)
  Future<bool> passwordSet(String password) async {
    bool? result = await HttpUtil.post<bool>(
      Api.passwordSet,
      data: {"password": password},
      showLoading: false,
    );
    return result == true;
  }

  ///修改密码
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    bool? result = await HttpUtil.post<bool>(
      Api.changePassword,
      data: {"oldPassword": oldPassword, "newPassword": newPassword},
      showLoading: false,
    );
    return result == true;
  }

  ///找回共鸣
  Future<bool> backPostResonate(String postId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.backPostResonate,
      queryParameters: {"postId": postId},
      showLoading: false,
    );
    return result == true;
  }

  ///兑换商城购买
  Future<bool> buyProp(String shopId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.buyProp,
      data: {"shopId": shopId, "number": 1},
      showLoading: true,
    );
    return result == true;
  }

  ///获取用户卡盒卡片数量
  Future<List<UserCardBoxEntity>?> getUserCardBoxCardCount(
      String userId) async {
    List<UserCardBoxEntity>? result =
        await HttpUtil.post<List<UserCardBoxEntity>>(
      Api.userCardNum,
      queryParameters: {"userId": userId},
      showLoading: false,
    );
    return result;
  }

  ///更新用户搭搭号
  Future<bool> updateUserDadaNo(int type) async {
    bool? result = await HttpUtil.post<bool>(
      Api.updateDadaNo,
      queryParameters: {
        "type": type,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///离开他人小屋
  Future<bool> leaveFromOtherRoom(String userId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.leaveSmallRoom,
      data: {
        "userId": userId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///解除搭搭关系
  Future<bool> relieveDaziRelation(String userId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.relieveDadaRelation,
      data: {
        "userFriendId": userId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取小屋信箱未读数
  Future<SmallRoomMailUnreadMsgEntity?> getSmallRoomMailUnreadCount() async {
    SmallRoomMailUnreadMsgEntity? result =
        await HttpUtil.post<SmallRoomMailUnreadMsgEntity>(
      Api.getSmallRoomMsgUnreadCount,
      showLoading: false,
    );
    return result;
  }

  ///填写兑换码
  Future<bool> receiptCdk({required String code}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.receiptCdk,
      queryParameters: {
        "code": code,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///给新用户发送打招呼消息
  Future<bool> sendWelcomeMsgToNewUser() async {
    bool? result = await HttpUtil.post<bool>(
      Api.sendWelcomeGreeting,
      showLoading: true,
    );
    return result == true;
  }

  ///校验验证码
  Future<bool> checkPhoneCode(String phone, String code) async {
    bool? result = await HttpUtil.post<bool>(
      Api.checkCode,
      data: {
        "phone": phone,
        "code": code,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///举报
  Future<bool> report(
      {required int reasonType,
      required int reportType,
      String? businessId,
      String? content,
      List<String>? imgUrls}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.report,
      data: {
        "reportId": UserService().user?.id,
        "businessId": businessId,
        "remark": content,
        "reasonType": reasonType,
        "reportType": reportType,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取系统开关配置
  Future<SystemConfigEntity?> getSystemConfig() async {
    SystemConfigEntity? result = await HttpUtil.get<SystemConfigEntity>(
      Api.systemConfig,
      showLoading: false,
    );
    return result;
  }

  ///获取青少年模式状态
  Future<TeenModeStatusEntity?> getTeenModeStatus({bool? showLoading}) async {
    TeenModeStatusEntity? result = await HttpUtil.get<TeenModeStatusEntity>(
      Api.teenModeStatus,
      showLoading: showLoading ?? true,
    );
    return result;
  }

  ///打开/关闭青少年模式
  Future<bool> changeTeenModeStatus(
      {required String isOpen, String? password, bool? showLoading}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.changeTeenModeStatus,
      data: {
        "toggle": isOpen,
        "password": password,
      },
      showLoading: showLoading ?? true,
    );
    return result == true;
  }

  ///获取首页banner
  Future<List<BannerEntity>?> getBannerList() async {
    List<BannerEntity>? result = await HttpUtil.get<List<BannerEntity>>(
      Api.bannerList,
      showLoading: false,
    );
    return result;
  }

  ///小屋戳一下
  Future<bool> smallRoomPoke({required String userId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.smallRoomPoke,
      data: {
        "userId": userId,
      },
      showLoading: true,
    );
    return result == true;
  }

  ///获取小屋访客列表
  Future<List<SmallRoomSeeMeEntity>?> getSmallRoomSeeMeList(
      {required int pageIndex}) async {
    List<SmallRoomSeeMeEntity>? result =
        await HttpUtil.post<List<SmallRoomSeeMeEntity>?>(
      Api.seeMeList,
      data: {"limit": 10, "page": pageIndex},
      showLoading: true,
    );
    return result;
  }

  ///获取一起做件事列表
  ///[state] 0-显示已满房间 1-不显示已满房间
  ///[content] 搜索内容，支持模糊搜索
  Future<List<TodoTogetherListEntity>?> getTodoTogetherList({
    required int pageIndex,
    int? limit,
    String? state = "0",
    String? content,
  }) async {
    List<TodoTogetherListEntity>? result =
        await HttpUtil.post<List<TodoTogetherListEntity>>(
      Api.todoTogetherList,
      data: {
        "page": pageIndex,
        "limit": limit ?? 10,
        "state": state,
        "content": content
      },
      showLoading: false,
    );
    return result;
  }

  Future<List<TodoTogetherListEntity>?> getTodoTogetherHistoryList({
    required int pageIndex,
  }) async {
    List<TodoTogetherListEntity>? result =
        await HttpUtil.post<List<TodoTogetherListEntity>>(
      Api.todoTogetherTaskHistory,
      data: {"page": pageIndex, "limit": 10},
      showLoading: false,
    );
    return result;
  }

  /// 一起做件事发布
  Future<bool> todoTogetherPublish(
      {required String content, required int sex}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.todoTogetherPublish,
      data: {"content": content, "sex": sex},
      showLoading: true,
    );
    return result == true;
  }

  ///一起做件事删除
  Future<bool> todoTogetherDelete({required int userTaskId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.todoTogetherDelete,
      data: {"userTaskId": userTaskId},
      showLoading: true,
    );
    return result == true;
  }

  ///一起做件事加入
  Future<bool> todoTogetherJoin({required String userTaskId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.todoTogetherJoin,
      data: {"userTaskId": userTaskId},
      showLoading: true,
    );
    return result == true;
  }

  ///一起做件事开始
  Future<bool> todoTogetherStart({String? userTaskId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.todoTogetherStart,
      data: {"userTaskId": userTaskId},
      showLoading: true,
    );
    return result == true;
  }

  Future<bool?> todoTogetherUploadImg(
      {String? userTaskId, String? imgurl}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.todoTogetherStartImg,
      data: {'userTaskId': userTaskId, 'url': imgurl},
      showLoading: true,
    );
    return result == true;
  }

  ///一起做件事跳过
  Future<bool> todoTogetherSkip({String? userTaskId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.todoTogetherSkip,
      data: {"userTaskId": userTaskId},
      showLoading: true,
    );
    return result == true;
  }

  Future<Map?> todoTogetherOtherImg({String? userTaskId}) async {
    Map? res = await HttpUtil.post<Map>(
      Api.todoTogetherStartImgOther,
      data: {'userTaskId': userTaskId},
      showLoading: true,
    );
    return res;
  }

  Future<bool?> todoTogetherStartImgContinue({String? userTaskId}) async {
    bool? res = await HttpUtil.post<bool>(
      Api.todoTogetherStartImgContinue,
      data: {'userTaskId': userTaskId},
      showLoading: true,
    );
    return res;
  }

  Future<bool?> todoTogetherPoke({String? userTaskId}) async {
    bool? res = await HttpUtil.post<bool>(
      Api.todoTogetherPoke,
      data: {'userTaskId': userTaskId},
      showLoading: true,
    );
    return res;
  }

  Future<bool?> todoTogetherRespond({String? userTaskId}) async {
    bool? res = await HttpUtil.post<bool>(
      Api.todoTogetherRespond,
      data: {'userTaskId': userTaskId},
      showLoading: true,
    );
    return res;
  }

  Future<TodoTogetherDetailEntity?> todoTogetherDetail(
      {String? userTaskId, bool showLoading = false}) async {
    TodoTogetherDetailEntity? res =
        await HttpUtil.post<TodoTogetherDetailEntity>(
      Api.todoTogetherDetail,
      data: {'userTaskId': userTaskId},
      showLoading: showLoading,
    );
    return res;
  }

  Future<bool?> todoTogetherComplete({String? userTaskId}) async {
    bool? res = await HttpUtil.post<bool>(
      Api.todoTogetherComplete,
      data: {'userTaskId': userTaskId},
      showLoading: true,
    );
    return res;
  }

  Future<bool?> todoTogetherCompleteToMsg(String userTaskId,
      {String? msg, int? msgLength, String? imageUrl, String? audioUrl}) async {
    bool? res = await HttpUtil.post<bool>(
      Api.todoTogetherCompleteToMsg,
      data: {
        'userTaskId': userTaskId,
        'msg': msg,
        'imgUrl': imageUrl,
        'audioUrl': audioUrl,
        'msgLength': msgLength,
      },
      showLoading: true,
    );
    return res;
  }

  Future<Map?> todoTogetherEndSelect(
      {String? userTaskId, String? endSelect}) async {
    Map? res = await HttpUtil.post<Map>(
      Api.todoTogetherEndSelect,
      data: {'userTaskId': userTaskId, 'endSelect': endSelect},
      showLoading: true,
    );
    return res;
  }

  Future<bool?> todoTogetherPropose(String? userTaskId, String? propose) async {
    bool? res = await HttpUtil.post<bool>(
      Api.todoTogetherPropose,
      data: {'userTaskId': userTaskId, 'propose': propose},
      showLoading: true,
    );
    return res;
  }

  Future<String?> todoTogetherProposeSelect(
      {String? userTaskId, String? proposeSelect}) async {
    String? res = await HttpUtil.post<String>(
      Api.todoTogetherProposeSelect,
      data: {'userTaskId': userTaskId, 'propose': proposeSelect},
      showLoading: true,
    );
    return res;
  }

  ///一起做件事终止
  Future<bool> todoTogetherStop({String? userTaskId}) async {
    bool? result = await HttpUtil.post<bool>(
      Api.todoTogetherStop,
      data: {"userTaskId": userTaskId},
      showLoading: true,
    );
    return result == true;
  }

  ///一起做件事完结
  Future<bool> todoTogetherEnd(String? userTaskId) async {
    bool? result = await HttpUtil.post<bool>(
      Api.todoTogetherEnd,
      data: {"userTaskId": userTaskId},
      showLoading: true,
    );
    return result == true;
  }
}
