import 'dart:convert';
import 'package:dada/utils/login_utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:dada/model/api_exception.dart';
import 'package:dada/model/api_response_entity.dart';
import 'package:dada/model/raw_data.dart';
import 'package:dada/services/network/interceptor/token_interceptor.dart';
import 'package:dada/utils/log.dart';
import 'package:dada/utils/toast_utils.dart';
import 'api.dart';
import 'interceptor/interceptor_proxy.dart';

class HttpUtil {
  HttpUtil();

  static final BaseOptions baseOptions = BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: const Duration(milliseconds: ApiConfig.timeout));
  static final dio = Dio(baseOptions)
    ..interceptors.add(dioInter)
    ..interceptors.add(TokenInterceptor());

  static void addLog() {
    if (kDebugMode) {
      dio.interceptors.add(PrettyDioLogger(
          requestHeader: true, requestBody: true, responseHeader: true));
      dio.interceptors.add(InterceptorProxy(dio));
    }
  }

  static Future<T?> get<T>(
    String url, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    bool showLoading = false,
    Function(ApiResponseEntity<T>)? onResponse,
    Function(ApiException)? onError,
  }) {
    return request<T>(url,
        queryParameters: queryParameters,
        headers: headers,
        onResponse: onResponse,
        showLoading: showLoading,
        onError: onError);
  }

  static Future<T?> post<T>(
    String url, {
    Map<String, dynamic>? queryParameters,
    data,
    Map<String, dynamic>? headers,
    bool showLoading = false,
    Function(ApiResponseEntity<T>)? onResponse,
    Function(ApiException)? onError,
  }) {
    return request<T>(url,
        method: "POST",
        queryParameters: queryParameters,
        data: data,
        headers: headers,
        onResponse: onResponse,
        showLoading: showLoading,
        onError: onError);
  }

  static Future<T?> delete<T>(
    String url, {
    Map<String, dynamic>? queryParameters,
    data,
    Map<String, dynamic>? headers,
    bool showLoading = false,
    Function(ApiResponseEntity<T>)? onResponse,
    Function(ApiException)? onError,
  }) {
    return request<T>(url,
        method: "DELETE",
        queryParameters: queryParameters,
        data: data,
        headers: headers,
        onResponse: onResponse,
        showLoading: showLoading,
        onError: onError);
  }

  static Future<T?> put<T>(
    String url, {
    Map<String, dynamic>? queryParameters,
    data,
    Map<String, dynamic>? headers,
    bool showLoading = false,
    Function(ApiResponseEntity<T>)? onResponse,
    Function(ApiException)? onError,
  }) {
    return request<T>(url,
        method: "PUT",
        queryParameters: queryParameters,
        data: data,
        headers: headers,
        showLoading: showLoading,
        onResponse: onResponse,
        onError: onError);
  }

  static Future<T?> uploadFile<T>(
    String filePath,
    String url, {
    String method = "POST",
    bool showLoading = false,
    Map<String, dynamic>? headers,
    Function(ApiResponseEntity<T>)? onResponse,
    Function(ApiException)? onError,
  }) async {
    try {
      if (showLoading) {
        ToastUtils.showLoading();
      }
      try {
        Options options = Options()
          ..method = method
          ..headers = headers;

        final String name = filePath.substring(filePath.lastIndexOf('/') + 1);
        final FormData formData = FormData.fromMap(<String, dynamic>{
          'file': await MultipartFile.fromFile(filePath, filename: name)
        });
        Response response =
            await dio.request(url, data: formData, options: options);

        return _handleResponse<T>(response, onResponse, onError);
      } catch (e) {
        ToastUtils.showToast('上传失败');
        return null;
      } finally {
        ToastUtils.hideLoading();
      }
    } catch (e) {
      var exception = ApiException.from(e);
      if (onError != null) {
        onError.call(exception);
      }
      return null;
    }
  }

  static Future<dynamic> upLoadVideo(String imagePath) async {
    Options requestOptions = Options();
    FormData formData = FormData.fromMap({
      "file": await MultipartFile.fromFile(
        imagePath,
        filename: "video.mp4",
      ),
    });
    try {
      var result = await dio.post(
        "/file/uploadVideo",
        data: formData,
        options: requestOptions,
        onSendProgress: (int count, int total) {
          Log.d('$total+++++');
        },
      );
      Log.d("---->$result");
      return result.data;
    } catch (e) {
      ToastUtils.showToast('上传失败');
      var exception = ApiException.from(e);
      return exception;
    }
  }

  static Future<T?> request<T>(
    String url, {
    String method = "Get",
    bool showLoading = false,
    Map<String, dynamic>? queryParameters,
    data,
    Map<String, dynamic>? headers,
    Function(ApiResponseEntity<T>)? onResponse,
    Function(ApiException)? onError,
  }) async {
    if (ApiConfig.baseUrl.isEmpty) {
      return null;
    }
    if (showLoading) {
      ToastUtils.showLoading();
    }
    try {
      Options options = Options()
        ..method = method
        ..headers = headers;

      data = _convertRequestData(data, options);

      Response response = await dio.request(url,
          queryParameters: queryParameters, data: data, options: options);
      ToastUtils.hideLoading();
      return _handleResponse<T>(response, onResponse, onError);
    } catch (e) {
      // ToastUtils.hideLoading();
      Log.e(e);
      var exception = ApiException.from(e);
      if (onError != null) {
        onError.call(exception);
      }
      if (T == bool) {
        return false as T;
      }
      return null;
    }
  }

  static _convertRequestData(data, Options options) {
    if (data != null) {
      if (data is List) {
        return jsonEncode(data);
      }
      if (data is String) {
        return data;
      }
      String jsonString = jsonEncode(data);
      if (kDebugMode) {
        print("Post Request data: $jsonString");
      }
      data = jsonDecode(jsonEncode(data));
    }
    return data;
  }

  ///请求响应内容处理
  static T? _handleResponse<T>(
      Response response,
      Function(ApiResponseEntity<T>)? onResponse,
      Function(ApiException)? onError) {
    if (response.statusCode == 200) {
      if (T.toString() == (RawData).toString()) {
        RawData raw = RawData();
        raw.value = response.data;
        return raw as T;
      } else {
        return _handleBusinessResponse<T>(response, onResponse, onError);
      }
    } else {
      var exception =
          ApiException(response.statusCode, ApiException.unknownException);
      if (onError != null) {
        onError.call(exception);
      } else {
        ToastUtils.showToast(exception.msg ?? "");
      }
      if (T == bool) {
        return false as T;
      }
      return null;
    }
  }

  ///业务内容处理
  static T? _handleBusinessResponse<T>(
      Response response,
      Function(ApiResponseEntity<T>)? onResponse,
      Function(ApiException)? onError) {
    ApiResponseEntity<T> apiResponse =
        ApiResponseEntity<T>.fromJson(response.data);
    if (apiResponse.code == ApiConfig.successCode) {
      onResponse?.call(apiResponse);
      if ((T == bool || T is bool?) && response.data['data'] is! bool) {
        return true as T;
      } else if (response.data['data'] is! Map &&
          response.data['data'] is! List &&
          response.data['data'] is! T) {
        if (T == int && response.data['data'] is String) {
          return int.parse(response.data['data']) as T;
        }
        return response.data['data'];
      }
      return apiResponse.data;
    } else {
      ApiException apiException =
          ApiException(apiResponse.code, apiResponse.msg, apiResponse.data);
      if (apiResponse.code == ApiConfig.tokenExpired) {
        LoginUtils.logOut(force: true);
        return null;
      } else if (apiResponse.code == ApiConfig.accountBanned) {
        _showErrorToast(apiResponse.msg ?? "");
        LoginUtils.logOut(force: true);
        return null;
      }
      if (onError != null) {
        onError.call(apiException);
      } else {
        Future.delayed(const Duration(milliseconds: 200), () {
          ToastUtils.showToast(apiException.msg ?? "");
        });
      }
      if (T == bool || T is bool?) {
        return false as T;
      }
      return null;
    }
  }

  // 拦截器处理请求返回异常
  static final Interceptor dioInter =
      InterceptorsWrapper(onRequest: (options, handler) {
    // Log.v("onResponse url: $url, code: ${options}");
    return handler.next(options);
  }, onResponse: (response, handler) {
    // Log.v("onResponse url: $url, code: ${response.data}");
    if (response.statusCode != null && response.statusCode != 200) {
      var msg = _handleHttpError(response.statusCode!, response.statusMessage);
      // _showErrorToast(msg);
    } else if (response.data is Map<String, dynamic> &&
        response.data?["code"] != ApiConfig.successCode) {
      // var msg = response.data["msg"];
      // _showErrorToast(msg);
    }
    return handler.next(response);
  }, onError: (DioException e, handler) {
    // Log.v("onError url: $url, statusCode: ${e.response?.statusCode}");
    Log.e("$e");
    if (e.response?.statusCode != 200 && e.response?.statusCode != 201) {
      if (e.response?.statusCode == 401) {
        ToastUtils.showToast("登录已过期，请重新登录");
        LoginUtils.logOut();
        return handler.next(e);
      }
      if (e.response?.statusCode == 404) {
        ToastUtils.showToast("功能未上线");
        return handler.next(e);
      }
      var msg = _dioError(e);
      if (kDebugMode) {
        msg = "$msg, request:${e.requestOptions.uri}";
      }
      ToastUtils.hideLoading();
      _showErrorToast(msg);
      return handler.next(e);
    } else {
      ToastUtils.hideLoading();
      return handler.next(e);
    }
  });

  // 处理 Dio 异常
  static String _dioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return "网络连接超时，请检查网络设置";
      case DioExceptionType.connectionError:
        return "服务器异常，请稍后重试!";
      case DioExceptionType.receiveTimeout:
        return "服务器异常，请稍后重试！";
      case DioExceptionType.sendTimeout:
        return "网络连接超时，请检查网络设置";
      case DioExceptionType.badResponse:
        return "服务器异常，请稍后重试！";
      case DioExceptionType.cancel:
        return "请求已被取消，请重新请求";
      case DioExceptionType.unknown:
        return "网络异常，请稍后重试！";
      default:
        return error.message ?? "Dio异常";
    }
  }

  // 处理 Http 错误码
  static String _handleHttpError(int statusCode, String? statusMessage) {
    String message;
    switch (statusCode) {
      case 400:
        message = '请求语法错误';
        break;
      case 401:
        message = '未授权，请登录';
        break;
      case 403:
        message = '拒绝访问';
        break;
      case 404:
        message = '请求出错';
        break;
      case 408:
        message = '请求超时';
        break;
      case 500:
        message = '服务器异常';
        break;
      case 501:
        message = '服务未实现';
        break;
      case 502:
        message = '网关错误';
        break;
      case 503:
        message = '服务不可用';
        break;
      case 504:
        message = '网关超时';
        break;
      case 505:
        message = 'HTTP版本不受支持';
        break;
      default:
        message = '请求失败，$statusCode $statusMessage';
    }
    return message;
  }

  static String _lastErrorToastMsg = "";
  static int _lastErrorToastTime = 0;

  ///1秒内只弹一次相同的toast
  static _showErrorToast(String msg) {
    var now = DateTime.now().millisecondsSinceEpoch;
    if (msg != _lastErrorToastMsg || now - _lastErrorToastTime > 1000) {
      ToastUtils.showToast(msg);
      _lastErrorToastMsg = msg;
      _lastErrorToastTime = now;
    }
  }
}
