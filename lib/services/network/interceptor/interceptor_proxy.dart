import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:system_proxy/system_proxy.dart';

class InterceptorProxy extends Interceptor {
  final Dio? _dio;

  Map<String, String>? _proxy;
  InterceptorProxy(this._dio);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    if (_dio != null) {
      _proxy = _proxy ?? await SystemProxy.getProxySettings();
      if (_proxy != null && _dio.httpClientAdapter is IOHttpClientAdapter) {
        (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
          final client = HttpClient(context: SecurityContext(withTrustedRoots: false));
          client.findProxy = (uri) {
            return 'PROXY ${_proxy!['host']}:${_proxy!['port']}';
          };
          client.badCertificateCallback =
              (cert, host, port) => true;
          return client;
        };
      }
    }
    return handler.next(options);
  }
}