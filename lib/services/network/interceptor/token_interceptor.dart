import 'dart:io';

import 'package:dio/dio.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sp_util/sp_util.dart';

class TokenInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    String? token = SpUtil.getString("token");
    if (token != null && token.isNotEmpty) {
      options.headers["token"] = token;
    }
    options.headers["platform"] = Platform.isAndroid ? "android" : "ios";
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    options.headers["version"] = packageInfo.version;
    options.headers["buildNumber"] = packageInfo.buildNumber;
    //TODO:
    // String deviceId = "";
    // options.headers["deviceId"] = deviceId;
    super.onRequest(options, handler);
  }
}
