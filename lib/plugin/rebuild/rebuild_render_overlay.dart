import 'package:flutter/material.dart';

class RebuildRenderOverlay extends RenderBox {
  List<Element> rebuildList = [];

  final Map<Element, int> countMap;
  final Map<int, int> widgetMap;

  //将painter缓存起来，可以加快绘制速度
  final Map<int, TextPainter> painterMap = {};
  final Paint textColorPainter = Paint()..color = Colors.yellow;
  final Paint framePainter = Paint()
    ..color = Colors.red
    ..style = PaintingStyle.stroke
    ..strokeWidth = 1;

  RebuildRenderOverlay(this.countMap, this.widgetMap);

  @override
  bool get sizedByParent => true;

  @override
  bool get alwaysNeedsCompositing => true;

  @override
  Size computeDryLayout(BoxConstraints constraints) {
    return constraints.constrain(const Size(double.infinity, double.infinity));
  }

  Element? rootElement;

  @override
  void paint(PaintingContext context, Offset offset) {
    super.paint(context, offset);
    //找出当前rebuild的element的和屏幕一样大的element,主要为了防止多页面element重叠
    if (rebuildList.isNotEmpty) {
      var rebuild = rebuildList[0];
      var chain = rebuild.debugGetDiagnosticChain();
      for (Element element in chain) {
        if (!element.mounted || !element.debugIsActive) {
          continue;
        }
        var findRenderObject = element.findRenderObject();
        if (findRenderObject is RenderBox) {
          var eleSize = findRenderObject.size;
          if (eleSize.width == size.width && eleSize.height == size.height) {
            rootElement = element;
            break;
          }
        }
      }
    }
    countMap.removeWhere((key, value) {
      bool defunct = key.debugIsDefunct;
      if (defunct) {
        widgetMap.remove(key.hashCode);
      }
      return defunct;
    });

    Map<RenderObject, int> renderMap = {};

    void gatherRenderObject(Element element) {
      int? buildCount = countMap[element];
      if (buildCount != null) {
        int lastCount = renderMap[element.renderObject!] ?? 0;
        if (buildCount > lastCount) {
          renderMap[element.renderObject!] = buildCount;
        }
      }
    }

    void visit(Element element) {
      if (element.debugIsDefunct || element.renderObject == null) {
        return;
      }
      gatherRenderObject(element);
      element.visitChildren(visit);
    }

    if (rootElement != null) {
      if (!rootElement!.debugIsDefunct && rootElement!.renderObject != null) {
        gatherRenderObject(rootElement!);
      }
      rootElement!.visitChildren(visit);
    }
    renderMap.forEach((key, value) {
      _drawElement(context, offset, key, value);
    });

  }

  void _drawElement(PaintingContext context, Offset offset,
      RenderObject renderObject, int count) {
    if (!renderObject.attached) {
      return;
    }
    var transformTo = renderObject.getTransformTo(null);
    var translation = transformTo.getTranslation();

    var eleOffset = Offset(translation.x, translation.y);
    //框
    context.canvas.drawRect(
        offset + eleOffset & renderObject.semanticBounds.size, framePainter);
    //文字
    TextPainter? cachePainter = painterMap[count];
    var textPainter = cachePainter ??
        TextPainter(
            text: TextSpan(
                text: count.toString(),
                style: const TextStyle(color: Colors.black, fontSize: 10)),
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.center,
            maxLines: 1);
    if (cachePainter == null) {
      textPainter.layout();
      painterMap[count] = textPainter;
    }
    context.canvas.drawRect(
        offset + eleOffset & Size(textPainter.width, textPainter.height),
        textColorPainter);
    textPainter.paint(context.canvas, offset + eleOffset);
  }
}
