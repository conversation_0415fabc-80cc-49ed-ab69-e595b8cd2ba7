import 'dart:convert';

import 'package:dada/plugin/rebuild/rebuild_overlay.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';

import 'package:flutter_ume/flutter_ume.dart';

class RebuildInspector extends StatefulWidget implements Pluggable {
  RebuildInspector({Key? key}) : super(key: key);

  static const String iconData =
      "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";

  final iconBytes = base64Decode(iconData);

  @override
  State<RebuildInspector> createState() => _RebuildInspectorState();

  @override
  Widget? buildWidget(BuildContext? context) => this;

  @override
  String get displayName => "Rebuild";

  @override
  ImageProvider<Object> get iconImageProvider => MemoryImage(iconBytes);

  @override
  String get name => "Rebuild";

  @override
  void onTrigger() {}
}

class _RebuildInspectorState extends State<RebuildInspector> {
  List<Element> rebuildList = [];
  List<Element> tempList = [];
  Map<int, int> lastFrameWidgets = {};
  Map<Element, int> countMap = {};

  void onRebuild(Element e, bool builtOnce) {
    if (e.debugIsDefunct) {
      return;
    }
    if (debugIsWidgetLocalCreation(e.widget)) {
      if (e.widget.runtimeType != widget.runtimeType) {
        //不添加自身
        tempList.add(e);
      }
    }
  }

  void _onFrameEnd(Duration timeStamp) {
    if (tempList.isNotEmpty) {
      for (int i = tempList.length - 1; i >= 0; i--) {
        var element = tempList[i];
        if (element.debugIsDefunct ||
            element.widget.hashCode == lastFrameWidgets[element.hashCode]) {
          //widget 没有变化
          tempList.removeAt(i);
        }
      }
      for (var element in tempList) {
        countMap[element] = (countMap[element] ?? 0) + 1;
      }
      if (mounted) {
        setState(() {
          rebuildList = tempList;
        });
      }
      tempList = [];
    } else if (mounted) {
      if (rebuildList.isNotEmpty) {
        setState(() {
          rebuildList = tempList;
        });
      } else {
        setState(() {});
      }
    }

    lastFrameWidgets.clear();
    void visit(Element element) {
      if (element.debugIsDefunct) {
        return;
      }
      lastFrameWidgets[element.hashCode] = element.widget.hashCode;
      element.visitChildren(visit);
    }

    WidgetsBinding.instance.renderViewElement?.visitChildren(visit);
    SchedulerBinding.instance.addPostFrameCallback(_onFrameEnd);
  }

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback(_onFrameEnd);
    debugOnRebuildDirtyWidget = onRebuild;
  }

  @override
  void dispose() {
    super.dispose();
    debugOnRebuildDirtyWidget = null;
  }

  @override
  Widget build(BuildContext context) {
    return RebuildOverlay(rebuildList, countMap, lastFrameWidgets);
  }
}
