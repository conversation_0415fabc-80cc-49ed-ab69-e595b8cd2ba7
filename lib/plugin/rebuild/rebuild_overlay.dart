import 'package:dada/plugin/rebuild/rebuild_render_overlay.dart';
import 'package:flutter/widgets.dart';

class RebuildOverlay extends LeafRenderObjectWidget {
  final List<Element> rebuildList;
  final Map<Element, int> countMap;
  final Map<int, int> widgetMap;

  const RebuildOverlay(this.rebuildList, this.countMap, this.widgetMap,
      {Key? key})
      : super(key: key);

  @override
  RebuildRenderOverlay createRenderObject(BuildContext context) {
    return RebuildRenderOverlay(countMap, widgetMap);
  }

  @override
  void updateRenderObject(
      BuildContext context, covariant RebuildRenderOverlay renderObject) {
    super.updateRenderObject(context, renderObject);
    renderObject.rebuildList = rebuildList;
    renderObject.markNeedsPaint();
  }
}
