import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Themes {
  //*****************************亮色主题配置*************************************//
  static const BadgeThemeData lightBadgeTheme =
      BadgeThemeData(backgroundColor: Color(0xFFFF2D55));

  static const DialogTheme lightDialogTheme = DialogTheme(
      backgroundColor: Color(0xFFF6F7F8),
      contentTextStyle: TextStyle(color: Color(0xFF111111)),
      titleTextStyle: TextStyle(color: Color(0xFF111111)));

  static const TextTheme lightTextTheme = TextTheme(
    displayLarge: TextStyle(color: Color(0xFF333333)),
    displayMedium: TextStyle(color: Color(0xFF3D7EFF)),
    displaySmall: TextStyle(color: Color(0xFFFF5B00)),
    titleLarge: TextStyle(color: Color(0xFF000000)),
    titleMedium: TextStyle(
        color: Color(0xFF3D3D3D), fontSize: 16, fontWeight: FontWeight.w500),
    titleSmall: TextStyle(color: Color(0xFF999999)),
    bodyLarge: TextStyle(color: Color(0xFF666666)),
    bodyMedium: TextStyle(color: Color(0xFF666666)),
    bodySmall: TextStyle(color: Color(0xFF039233)),
    labelLarge: TextStyle(color: Color(0xFFFF3B30)),
    labelMedium: TextStyle(color: Color(0xFF3D3D3D)),
    labelSmall: TextStyle(color: Color(0xFF999999)),
    headlineMedium: TextStyle(color: Color(0xFF666666)),
    headlineSmall: TextStyle(color: Color(0xFF82392A)),
    headlineLarge: TextStyle(color: Color(0xFF333333)),
  );

  static const TabBarTheme lightTabBarTheme = TabBarTheme(
    labelColor: Color(0xFF3D3D3D),
    unselectedLabelColor: Color(0xFF4DC151),
    indicatorColor: Color(0xFFF8D84A),
  );

  static const BottomNavigationBarThemeData lightBottomNavigationBarThemeData =
      BottomNavigationBarThemeData(
    backgroundColor: Colors.white,
    selectedLabelStyle: TextStyle(
      color: Color(0xFF4DC151),
    ),
    unselectedLabelStyle: TextStyle(
      color: Color(0xFF3D3D3D),
    ),
  );

  /*亮色主题配置*/
  static final ThemeData lightAppTheme = ThemeData(
    badgeTheme: lightBadgeTheme,
    useMaterial3: true,
    dialogTheme: lightDialogTheme,
    bottomNavigationBarTheme: lightBottomNavigationBarThemeData,
    elevatedButtonTheme: ElevatedButtonThemeData(
        style: TextButton.styleFrom(
      iconColor: Colors.white,
      backgroundColor: const Color(0xFF039233),
      disabledBackgroundColor: const Color(0xFFA3C8A3),
      textStyle: const TextStyle(color: Colors.white),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(4))),
    )),
    datePickerTheme: DatePickerThemeData(
      todayBorder: const BorderSide(color: Colors.black),
      dayBackgroundColor:
          MaterialStateProperty.resolveWith((Set<MaterialState> states) {
        if (states.contains(MaterialState.selected)) {
          return Colors.black.withOpacity(0.12);
        }
        return null;
      }),
      todayForegroundColor:
          MaterialStateProperty.resolveWith((Set<MaterialState> states) {
        if (states.contains(MaterialState.selected)) {
          return Colors.black;
        } else if (states.contains(MaterialState.disabled)) {
          return Colors.black.withOpacity(0.38);
        }
        return Colors.black;
      }),
      cancelButtonStyle: ButtonStyle(
        foregroundColor:
            MaterialStateProperty.all<Color>(const Color(0xFF999999)),
      ),
      confirmButtonStyle: ButtonStyle(
        foregroundColor: MaterialStateProperty.all<Color>(Colors.blueAccent),
      ),
    ),

    textTheme: lightTextTheme,

    tabBarTheme: lightTabBarTheme,

    inputDecorationTheme: const InputDecorationTheme(
      fillColor: Color(0xFFF5F6F7),
      hintStyle: TextStyle(color: Color(0xFF999999),),
    ),
    scaffoldBackgroundColor: Colors.white,
    dividerColor: const Color(0xFFD8D8D8),
    hoverColor: Colors.blue,

    textSelectionTheme: const TextSelectionThemeData(
      cursorColor: Colors.blue,
      selectionColor: Colors.blue,
      selectionHandleColor: Colors.blue,
    ),

    // 通过 https://m3.material.io/theme-builder#/custom 导出来的明细，便于我们查找颜色
    colorScheme: const ColorScheme(
      brightness: Brightness.light,

      ///已注释的是之前的颜色  可以对照一下改动。
      ///改完会把注释掉的删除；
      //基色，也就是我们主要的颜色
      primary: Colors.white,
      //主要基色之上的文字和icon
      onPrimary: Color(0xFF3D3D3D),
      // secondary: Color(0xFF039233),
      //第二基色也叫做辅助颜色
      secondary: Color(0xFF408F40),
      //在第二基色上的文字和icon
      onSecondary: Color(0xFF3D3D3D),
      // error: Color(0xFFBA1A1A),
      //错误警告⚠️时候的颜色，
      error: Color(0xFFFF2D55),
      // surface: Color(0xFFFFFBFF),
      //主要的页面背景颜色
      surface: Color(0xFFFFFFFF),
      //在主要背景颜色之上文字
      onSurface: Color(0xFF333333),
      // 在错误颜色上的文字和icon
      onError: Color(0xFFFFFFFF),
      // onPrimary: Color(0xFFFFFFFF),

      // 蒙版等
      surfaceTint: Color(0xFFF5F5F5),
      background: Color(0xFFF1F3F6),
      onBackground: Color(0xFF3D3D3D),

      /// UI 未确定
      // 主基色容器组件颜色
      primaryContainer: Color(0xFFF9E534),
      // 在主基色容器组件上的文字和icon
      onPrimaryContainer: Color(0xFF201C00),
      // 第二级容器组件颜色
      secondaryContainer: Color(0xFFF5F6F7),
      // 在第二级容器组件颜色之上的icon和文字颜色
      onSecondaryContainer: Color(0xFF344F3D),
      //tertiary 第三色阶，暂时没定义，可以视情况定义使用
      tertiary: Color(0xFFF5F6F7),
      //在第三色阶上的icon和文字
      onTertiary: Color(0xFF23AF28),
      //第三色阶容器组件颜色
      tertiaryContainer: Color(0xFFCCFFF0),
      //在第三色阶容器组件上的icon和文字颜色
      onTertiaryContainer: Color(0xFF23AF28),
      //错误容器背景色
      errorContainer: Color(0xFFFFDAD6),
      //在错误容器之上的icon和文字
      onErrorContainer: Color(0xFF410002),

      //  outline 控件颜色
      outline: Color(0xFF7B7768),

      // 阴影颜色
      shadow: Color(0xFFEDEDED),

      onSurfaceVariant: Color(0xFF0F5982),
      onInverseSurface: Color(0xFF8B4800),
      inverseSurface: Color(0xFFF8FAFD),

      //下面的这些暂时也没弄清在哪使用，后续有用在加上
      inversePrimary: Color(0xFFDBC90A),
      outlineVariant: Color(0xFFE6E6E6),
      scrim: Color(0xFF000000),
    ),
    hintColor: const Color(0xFF999999),
    //例如设置等Divider widget分割线
    dividerTheme: const DividerThemeData(color: Color(0xFFE5EEE5)),
    visualDensity: VisualDensity.adaptivePlatformDensity,
    // scaffoldBackgroundColor: Color(0xFFF6F7FB)
  );

  //*****************************暗色主题配置*************************************//
  static const BadgeThemeData darkBadgeTheme = BadgeThemeData(
      backgroundColor: Color(0xFFFF375F), textColor: Color(0xFFF6F7FB));

  static const TextTheme darkTextTheme = TextTheme(
    displayLarge: TextStyle(color: Color(0xFFE6E1DF)),
    bodyMedium: TextStyle(color: Color(0xFF83868C)),
    bodyLarge: TextStyle(color: Color(0xFFE6E1DF)),
    bodySmall: TextStyle(color: Color(0xFF039233)),
    displaySmall: TextStyle(color: Color(0xFFFF5C0A)),
    titleLarge: TextStyle(color: Color(0xFF83868C)),
  );

  static const TabBarTheme darkTabBarTheme = TabBarTheme(
    labelColor: Color(0xFFE6E1DF),
    unselectedLabelColor: Color(0xFF83868C),
    indicatorColor: Color(0xFF039233),
  );

  static const BottomNavigationBarThemeData darkBottomNavigationBarThemeData =
      BottomNavigationBarThemeData(
    backgroundColor: Colors.black,
    selectedItemColor: Color(0xFF4DC151),
    unselectedItemColor: Colors.white,
  );

  static final ThemeData darkAppTheme = ThemeData(
    badgeTheme: darkBadgeTheme,
    useMaterial3: true,
    elevatedButtonTheme: ElevatedButtonThemeData(
        style: TextButton.styleFrom(
      backgroundColor: const Color(0xFF039233),
      disabledBackgroundColor: const Color(0xFFA3C8A3),
      iconColor: Colors.white,
      textStyle: const TextStyle(color: Colors.white),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(4))),
    )),
    textTheme: darkTextTheme,
    tabBarTheme: darkTabBarTheme,
    // colorScheme: ColorScheme.fromSeed(
    //   seedColor: Color(0xFFFFD60A),
    //   secondary: Color(0xFF039233),
    //   brightness: Brightness.dark,
    //   background: Color(0xFF252222),
    // ),
    scaffoldBackgroundColor: Colors.black,
    colorScheme: const ColorScheme(
      primary: Colors.black,
      secondary: Color(0xFF1b6d22),
      error: Color(0xFFbe0036),
      surface: Color(0xFF383635),
      onSurface: Color(0xFFE6E1DF),
      onError: Color(0xFFE6E1DF),
      onPrimary: Color(0xFF3B2F00),
      onSecondary: Color(0xFFFFFFFF),
      surfaceTint: Color(0xFF2B2A29),
      background: Color(0xFF000000),
      onBackground: Colors.white,

      /// UI 未确定
      brightness: Brightness.dark,
      primaryContainer: Color(0xFF4F4800),
      onPrimaryContainer: Color(0xFFF9E534),
      secondaryContainer: Color(0xFF005319),
      onSecondaryContainer: Color(0xFF85FC8E),
      tertiary: Color(0xFFA7D0B7),
      onTertiary: Color(0xFF103726),
      tertiaryContainer: Color(0xFF294E3B),
      onTertiaryContainer: Color(0xFFC2ECD3),
      errorContainer: Color(0xFF93000A),
      onErrorContainer: Color(0xFFFFDAD6),
      onSurfaceVariant: Color(0xFFCBC6B5),
      outline: Color(0xFF959181),
      onInverseSurface: Color(0xFF1D1C16),
      inverseSurface: Color(0xFFE7E2D9),
      inversePrimary: Color(0xFF695F00),
      shadow: Color(0x40000000),
      outlineVariant: Color(0xFF4A473A),
      scrim: Color(0xFF000000),
    ),

    dividerTheme: const DividerThemeData(color: Color(0x20A5A5A6)),
    visualDensity: VisualDensity.adaptivePlatformDensity,

    // scaffoldBackgroundColor: Color(0xFFF6F7FB)
  );
}

class AppTheme {
  static ThemeData themeData = Theme.of(Get.context!);
}
