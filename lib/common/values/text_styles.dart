import 'package:dada/common/values/themes.dart';
import 'package:flutter/material.dart';

class TextStyles {
  static TextStyle normal(double e,
          {double a = 1, Color? c, double? h, String? f}) =>
      TextStyle(
          fontSize: e,
          fontWeight: FontWeight.normal,
          fontFamily: f,
          color: c ?? AppTheme.themeData.textTheme.labelMedium?.color,
          height: h);

  static TextStyle medium(double e,
          {double a = 1, Color? c, double? h, String? f, FontWeight? w}) =>
      TextStyle(
        fontSize: e,
        fontWeight: w ?? FontWeight.w600,
        height: h,
        color: c ?? AppTheme.themeData.textTheme.labelMedium?.color,
      );

  static TextStyle bold(double e,
          {double a = 1, Color? c, String? f, FontWeight? w}) =>
      TextStyle(
        fontSize: e,
        fontWeight: w ?? FontWeight.bold,
        fontFamily: f,
        color: c ?? AppTheme.themeData.textTheme.labelMedium?.color,
      );

  static TextStyle common(double e, Color? c,
          {FontWeight? w, double a = 1, double h = 1.0, String? f}) =>
      TextStyle(
        fontSize: e,
        fontWeight: w ?? FontWeight.normal,
        fontFamily: f,
        color: c ?? AppTheme.themeData.textTheme.labelMedium?.color,
        height: h,
      );
}
