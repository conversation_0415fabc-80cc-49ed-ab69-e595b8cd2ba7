import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/account_balance_entity.dart';

AccountBalanceEntity $AccountBalanceEntityFromJson(Map<String, dynamic> json) {
  final AccountBalanceEntity accountBalanceEntity = AccountBalanceEntity();
  final int? daCoinNum = jsonConvert.convert<int>(json['daCoinNum']);
  if (daCoinNum != null) {
    accountBalanceEntity.daCoinNum = daCoinNum;
  }
  final int? daStickNum = jsonConvert.convert<int>(json['daStickNum']);
  if (daStickNum != null) {
    accountBalanceEntity.daStickNum = daStickNum;
  }
  final int? stains = jsonConvert.convert<int>(json['stains']);
  if (stains != null) {
    accountBalanceEntity.stains = stains;
  }
  final int? skinNum = jsonConvert.convert<int>(json['skinNum']);
  if (skinNum != null) {
    accountBalanceEntity.skinNum = skinNum;
  }
  return accountBalanceEntity;
}

Map<String, dynamic> $AccountBalanceEntityToJson(AccountBalanceEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['daCoinNum'] = entity.daCoinNum;
  data['daStickNum'] = entity.daStickNum;
  data['stains'] = entity.stains;
  data['skinNum'] = entity.skinNum;
  return data;
}

extension AccountBalanceEntityExtension on AccountBalanceEntity {
  AccountBalanceEntity copyWith({
    int? daCoinNum,
    int? daStickNum,
    int? stains,
    int? skinNum,
  }) {
    return AccountBalanceEntity()
      ..daCoinNum = daCoinNum ?? this.daCoinNum
      ..daStickNum = daStickNum ?? this.daStickNum
      ..stains = stains ?? this.stains
      ..skinNum = skinNum ?? this.skinNum;
  }
}