import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/dynamic_resonance_entity.dart';

DynamicResonanceEntity $DynamicResonanceEntityFromJson(
    Map<String, dynamic> json) {
  final DynamicResonanceEntity dynamicResonanceEntity = DynamicResonanceEntity();
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    dynamicResonanceEntity.content = content;
  }
  final String? imageUrl = jsonConvert.convert<String>(json['imageUrl']);
  if (imageUrl != null) {
    dynamicResonanceEntity.imageUrl = imageUrl;
  }
  final String? resonateDate = jsonConvert.convert<String>(
      json['resonateDate']);
  if (resonateDate != null) {
    dynamicResonanceEntity.resonateDate = resonateDate;
  }
  final String? postId = jsonConvert.convert<String>(json['postId']);
  if (postId != null) {
    dynamicResonanceEntity.postId = postId;
  }
  final int? resonateCount = jsonConvert.convert<int>(json['resonateCount']);
  if (resonateCount != null) {
    dynamicResonanceEntity.resonateCount = resonateCount;
  }
  final int? postType = jsonConvert.convert<int>(json['postType']);
  if (postType != null) {
    dynamicResonanceEntity.postType = postType;
  }
  final int? notReadResonateCount = jsonConvert.convert<int>(
      json['notReadResonateCount']);
  if (notReadResonateCount != null) {
    dynamicResonanceEntity.notReadResonateCount = notReadResonateCount;
  }
  return dynamicResonanceEntity;
}

Map<String, dynamic> $DynamicResonanceEntityToJson(
    DynamicResonanceEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['content'] = entity.content;
  data['imageUrl'] = entity.imageUrl;
  data['resonateDate'] = entity.resonateDate;
  data['postId'] = entity.postId;
  data['resonateCount'] = entity.resonateCount;
  data['postType'] = entity.postType;
  data['notReadResonateCount'] = entity.notReadResonateCount;
  return data;
}

extension DynamicResonanceEntityExtension on DynamicResonanceEntity {
  DynamicResonanceEntity copyWith({
    String? content,
    String? imageUrl,
    String? resonateDate,
    String? postId,
    int? resonateCount,
    int? postType,
    int? notReadResonateCount,
  }) {
    return DynamicResonanceEntity()
      ..content = content ?? this.content
      ..imageUrl = imageUrl ?? this.imageUrl
      ..resonateDate = resonateDate ?? this.resonateDate
      ..postId = postId ?? this.postId
      ..resonateCount = resonateCount ?? this.resonateCount
      ..postType = postType ?? this.postType
      ..notReadResonateCount = notReadResonateCount ??
          this.notReadResonateCount;
  }
}