import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';

import 'package:dada/model/topic_item_entity.dart';


ChatRoomInfoEntity $ChatRoomInfoEntityFromJson(Map<String, dynamic> json) {
  final ChatRoomInfoEntity chatRoomInfoEntity = ChatRoomInfoEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    chatRoomInfoEntity.id = id;
  }
  final String? roomNo = jsonConvert.convert<String>(json['roomNo']);
  if (roomNo != null) {
    chatRoomInfoEntity.roomNo = roomNo;
  }
  final String? roomName = jsonConvert.convert<String>(json['roomName']);
  if (roomName != null) {
    chatRoomInfoEntity.roomName = roomName;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    chatRoomInfoEntity.createDate = createDate;
  }
  final String? createRoomUserId = jsonConvert.convert<String>(
      json['createRoomUserId']);
  if (createRoomUserId != null) {
    chatRoomInfoEntity.createRoomUserId = createRoomUserId;
  }
  final String? currentRoomUserId = jsonConvert.convert<String>(
      json['currentRoomUserId']);
  if (currentRoomUserId != null) {
    chatRoomInfoEntity.currentRoomUserId = currentRoomUserId;
  }
  final int? roomType = jsonConvert.convert<int>(json['roomType']);
  if (roomType != null) {
    chatRoomInfoEntity.roomType = roomType;
  }
  final List<TopicItemEntity>? roomTopicList = (json['roomTopicList'] as List<
      dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<TopicItemEntity>(e) as TopicItemEntity)
      .toList();
  if (roomTopicList != null) {
    chatRoomInfoEntity.roomTopicList = roomTopicList;
  }
  final List<ChatRoomSeatInfoEntity>? seatList = (json['seatList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ChatRoomSeatInfoEntity>(e) as ChatRoomSeatInfoEntity)
      .toList();
  if (seatList != null) {
    chatRoomInfoEntity.seatList = seatList;
  }
  final int? onlineNumber = jsonConvert.convert<int>(json['onlineNumber']);
  if (onlineNumber != null) {
    chatRoomInfoEntity.onlineNumber = onlineNumber;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    chatRoomInfoEntity.avatar = avatar;
  }
  return chatRoomInfoEntity;
}

Map<String, dynamic> $ChatRoomInfoEntityToJson(ChatRoomInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['roomNo'] = entity.roomNo;
  data['roomName'] = entity.roomName;
  data['createDate'] = entity.createDate;
  data['createRoomUserId'] = entity.createRoomUserId;
  data['currentRoomUserId'] = entity.currentRoomUserId;
  data['roomType'] = entity.roomType;
  data['roomTopicList'] = entity.roomTopicList?.map((v) => v.toJson()).toList();
  data['seatList'] = entity.seatList?.map((v) => v.toJson()).toList();
  data['onlineNumber'] = entity.onlineNumber;
  data['avatar'] = entity.avatar;
  return data;
}

extension ChatRoomInfoEntityExtension on ChatRoomInfoEntity {
  ChatRoomInfoEntity copyWith({
    String? id,
    String? roomNo,
    String? roomName,
    String? createDate,
    String? createRoomUserId,
    String? currentRoomUserId,
    int? roomType,
    List<TopicItemEntity>? roomTopicList,
    List<ChatRoomSeatInfoEntity>? seatList,
    int? onlineNumber,
    String? avatar,
  }) {
    return ChatRoomInfoEntity()
      ..id = id ?? this.id
      ..roomNo = roomNo ?? this.roomNo
      ..roomName = roomName ?? this.roomName
      ..createDate = createDate ?? this.createDate
      ..createRoomUserId = createRoomUserId ?? this.createRoomUserId
      ..currentRoomUserId = currentRoomUserId ?? this.currentRoomUserId
      ..roomType = roomType ?? this.roomType
      ..roomTopicList = roomTopicList ?? this.roomTopicList
      ..seatList = seatList ?? this.seatList
      ..onlineNumber = onlineNumber ?? this.onlineNumber
      ..avatar = avatar ?? this.avatar;
  }
}