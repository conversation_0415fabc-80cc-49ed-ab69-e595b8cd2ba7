import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/comment_reply_item_entity.dart';

CommentReplyItemEntity $CommentReplyItemEntityFromJson(
    Map<String, dynamic> json) {
  final CommentReplyItemEntity commentReplyItemEntity = CommentReplyItemEntity();
  final String? replyId = jsonConvert.convert<String>(json['replyId']);
  if (replyId != null) {
    commentReplyItemEntity.replyId = replyId;
  }
  final String? commentsId = jsonConvert.convert<String>(json['commentsId']);
  if (commentsId != null) {
    commentReplyItemEntity.commentsId = commentsId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    commentReplyItemEntity.userId = userId;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    commentReplyItemEntity.avatar = avatar;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    commentReplyItemEntity.nickname = nickname;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    commentReplyItemEntity.content = content;
  }
  final int? likeNo = jsonConvert.convert<int>(json['likeNo']);
  if (likeNo != null) {
    commentReplyItemEntity.likeNo = likeNo;
  }
  final bool? likeState = jsonConvert.convert<bool>(json['likeState']);
  if (likeState != null) {
    commentReplyItemEntity.likeState = likeState;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    commentReplyItemEntity.createdDate = createdDate;
  }
  final bool? isAuthor = jsonConvert.convert<bool>(json['isAuthor']);
  if (isAuthor != null) {
    commentReplyItemEntity.isAuthor = isAuthor;
  }
  final String? replayUser = jsonConvert.convert<String>(json['replayUser']);
  if (replayUser != null) {
    commentReplyItemEntity.replayUser = replayUser;
  }
  final String? replayUserName = jsonConvert.convert<String>(
      json['replayUserName']);
  if (replayUserName != null) {
    commentReplyItemEntity.replayUserName = replayUserName;
  }
  return commentReplyItemEntity;
}

Map<String, dynamic> $CommentReplyItemEntityToJson(
    CommentReplyItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['replyId'] = entity.replyId;
  data['commentsId'] = entity.commentsId;
  data['userId'] = entity.userId;
  data['avatar'] = entity.avatar;
  data['nickname'] = entity.nickname;
  data['content'] = entity.content;
  data['likeNo'] = entity.likeNo;
  data['likeState'] = entity.likeState;
  data['createdDate'] = entity.createdDate;
  data['isAuthor'] = entity.isAuthor;
  data['replayUser'] = entity.replayUser;
  data['replayUserName'] = entity.replayUserName;
  return data;
}

extension CommentReplyItemEntityExtension on CommentReplyItemEntity {
  CommentReplyItemEntity copyWith({
    String? replyId,
    String? commentsId,
    String? userId,
    String? avatar,
    String? nickname,
    String? content,
    int? likeNo,
    bool? likeState,
    String? createdDate,
    bool? isAuthor,
    String? replayUser,
    String? replayUserName,
  }) {
    return CommentReplyItemEntity()
      ..replyId = replyId ?? this.replyId
      ..commentsId = commentsId ?? this.commentsId
      ..userId = userId ?? this.userId
      ..avatar = avatar ?? this.avatar
      ..nickname = nickname ?? this.nickname
      ..content = content ?? this.content
      ..likeNo = likeNo ?? this.likeNo
      ..likeState = likeState ?? this.likeState
      ..createdDate = createdDate ?? this.createdDate
      ..isAuthor = isAuthor ?? this.isAuthor
      ..replayUser = replayUser ?? this.replayUser
      ..replayUserName = replayUserName ?? this.replayUserName;
  }
}