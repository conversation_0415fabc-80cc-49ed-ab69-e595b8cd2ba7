import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/todo_together_detail_entity.dart';

TodoTogetherDetailEntity $TodoTogetherDetailEntityFromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailEntity todoTogetherDetailEntity = TodoTogetherDetailEntity();
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    todoTogetherDetailEntity.createdDate = createdDate;
  }
  final int? stage = jsonConvert.convert<int>(json['stage']);
  if (stage != null) {
    todoTogetherDetailEntity.stage = stage;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    todoTogetherDetailEntity.content = content;
  }
  final String? otherUserId = jsonConvert.convert<String>(json['otherUserId']);
  if (otherUserId != null) {
    todoTogetherDetailEntity.otherUserId = otherUserId;
  }
  final String? beforeTaskId = jsonConvert.convert<String>(
      json['beforeTaskId']);
  if (beforeTaskId != null) {
    todoTogetherDetailEntity.beforeTaskId = beforeTaskId;
  }
  final int? countCommonTasks = jsonConvert.convert<int>(
      json['countCommonTasks']);
  if (countCommonTasks != null) {
    todoTogetherDetailEntity.countCommonTasks = countCommonTasks;
  }
  final TodoTogetherDetailStage6? stage6 = jsonConvert.convert<
      TodoTogetherDetailStage6>(json['stage6']);
  if (stage6 != null) {
    todoTogetherDetailEntity.stage6 = stage6;
  }
  final TodoTogetherDetailStage8? stage8 = jsonConvert.convert<
      TodoTogetherDetailStage8>(json['stage8']);
  if (stage8 != null) {
    todoTogetherDetailEntity.stage8 = stage8;
  }
  final TodoTogetherDetailStage3? stage3 = jsonConvert.convert<
      TodoTogetherDetailStage3>(json['stage3']);
  if (stage3 != null) {
    todoTogetherDetailEntity.stage3 = stage3;
  }
  final TodoTogetherDetailStage2? stage2 = jsonConvert.convert<
      TodoTogetherDetailStage2>(json['stage2']);
  if (stage2 != null) {
    todoTogetherDetailEntity.stage2 = stage2;
  }
  final TodoTogetherDetailStage5? stage5 = jsonConvert.convert<
      TodoTogetherDetailStage5>(json['stage5']);
  if (stage5 != null) {
    todoTogetherDetailEntity.stage5 = stage5;
  }
  final TodoTogetherDetailStage4? stage4 = jsonConvert.convert<
      TodoTogetherDetailStage4>(json['stage4']);
  if (stage4 != null) {
    todoTogetherDetailEntity.stage4 = stage4;
  }
  final TodoTogetherDetailStage1? stage1 = jsonConvert.convert<
      TodoTogetherDetailStage1>(json['stage1']);
  if (stage1 != null) {
    todoTogetherDetailEntity.stage1 = stage1;
  }
  return todoTogetherDetailEntity;
}

Map<String, dynamic> $TodoTogetherDetailEntityToJson(
    TodoTogetherDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['createdDate'] = entity.createdDate;
  data['stage'] = entity.stage;
  data['content'] = entity.content;
  data['otherUserId'] = entity.otherUserId;
  data['beforeTaskId'] = entity.beforeTaskId;
  data['countCommonTasks'] = entity.countCommonTasks;
  data['stage6'] = entity.stage6?.toJson();
  data['stage8'] = entity.stage8?.toJson();
  data['stage3'] = entity.stage3?.toJson();
  data['stage2'] = entity.stage2?.toJson();
  data['stage5'] = entity.stage5?.toJson();
  data['stage4'] = entity.stage4?.toJson();
  data['stage1'] = entity.stage1?.toJson();
  return data;
}

extension TodoTogetherDetailEntityExtension on TodoTogetherDetailEntity {
  TodoTogetherDetailEntity copyWith({
    String? createdDate,
    int? stage,
    String? content,
    String? otherUserId,
    String? beforeTaskId,
    int? countCommonTasks,
    TodoTogetherDetailStage6? stage6,
    TodoTogetherDetailStage8? stage8,
    TodoTogetherDetailStage3? stage3,
    TodoTogetherDetailStage2? stage2,
    TodoTogetherDetailStage5? stage5,
    TodoTogetherDetailStage4? stage4,
    TodoTogetherDetailStage1? stage1,
  }) {
    return TodoTogetherDetailEntity()
      ..createdDate = createdDate ?? this.createdDate
      ..stage = stage ?? this.stage
      ..content = content ?? this.content
      ..otherUserId = otherUserId ?? this.otherUserId
      ..beforeTaskId = beforeTaskId ?? this.beforeTaskId
      ..countCommonTasks = countCommonTasks ?? this.countCommonTasks
      ..stage6 = stage6 ?? this.stage6
      ..stage8 = stage8 ?? this.stage8
      ..stage3 = stage3 ?? this.stage3
      ..stage2 = stage2 ?? this.stage2
      ..stage5 = stage5 ?? this.stage5
      ..stage4 = stage4 ?? this.stage4
      ..stage1 = stage1 ?? this.stage1;
  }
}

TodoTogetherDetailStage6 $TodoTogetherDetailStage6FromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage6 todoTogetherDetailStage6 = TodoTogetherDetailStage6();
  final TodoTogetherDetailStage6OtherEndMsg? otherEndMsg = jsonConvert.convert<
      TodoTogetherDetailStage6OtherEndMsg>(json['otherEndMsg']);
  if (otherEndMsg != null) {
    todoTogetherDetailStage6.otherEndMsg = otherEndMsg;
  }
  final dynamic isMyEndSelect = json['isMyEndSelect'];
  if (isMyEndSelect != null) {
    todoTogetherDetailStage6.isMyEndSelect = isMyEndSelect;
  }
  final dynamic isOtherEndSelect = json['isOtherEndSelect'];
  if (isOtherEndSelect != null) {
    todoTogetherDetailStage6.isOtherEndSelect = isOtherEndSelect;
  }
  return todoTogetherDetailStage6;
}

Map<String, dynamic> $TodoTogetherDetailStage6ToJson(
    TodoTogetherDetailStage6 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['otherEndMsg'] = entity.otherEndMsg?.toJson();
  data['isMyEndSelect'] = entity.isMyEndSelect;
  data['isOtherEndSelect'] = entity.isOtherEndSelect;
  return data;
}

extension TodoTogetherDetailStage6Extension on TodoTogetherDetailStage6 {
  TodoTogetherDetailStage6 copyWith({
    TodoTogetherDetailStage6OtherEndMsg? otherEndMsg,
    dynamic isMyEndSelect,
    dynamic isOtherEndSelect,
  }) {
    return TodoTogetherDetailStage6()
      ..otherEndMsg = otherEndMsg ?? this.otherEndMsg
      ..isMyEndSelect = isMyEndSelect ?? this.isMyEndSelect
      ..isOtherEndSelect = isOtherEndSelect ?? this.isOtherEndSelect;
  }
}

TodoTogetherDetailStage6OtherEndMsg $TodoTogetherDetailStage6OtherEndMsgFromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage6OtherEndMsg todoTogetherDetailStage6OtherEndMsg = TodoTogetherDetailStage6OtherEndMsg();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    todoTogetherDetailStage6OtherEndMsg.id = id;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    todoTogetherDetailStage6OtherEndMsg.userId = userId;
  }
  final String? userTaskId = jsonConvert.convert<String>(json['userTaskId']);
  if (userTaskId != null) {
    todoTogetherDetailStage6OtherEndMsg.userTaskId = userTaskId;
  }
  final String? toUserId = jsonConvert.convert<String>(json['toUserId']);
  if (toUserId != null) {
    todoTogetherDetailStage6OtherEndMsg.toUserId = toUserId;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    todoTogetherDetailStage6OtherEndMsg.msg = msg;
  }
  final dynamic msgType = json['msgType'];
  if (msgType != null) {
    todoTogetherDetailStage6OtherEndMsg.msgType = msgType;
  }
  final dynamic msgLength = json['msgLength'];
  if (msgLength != null) {
    todoTogetherDetailStage6OtherEndMsg.msgLength = msgLength;
  }
  final dynamic imgUrl = json['imgUrl'];
  if (imgUrl != null) {
    todoTogetherDetailStage6OtherEndMsg.imgUrl = imgUrl;
  }
  final dynamic audioUrl = json['audioUrl'];
  if (audioUrl != null) {
    todoTogetherDetailStage6OtherEndMsg.audioUrl = audioUrl;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    todoTogetherDetailStage6OtherEndMsg.type = type;
  }
  final int? isRespond = jsonConvert.convert<int>(json['isRespond']);
  if (isRespond != null) {
    todoTogetherDetailStage6OtherEndMsg.isRespond = isRespond;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    todoTogetherDetailStage6OtherEndMsg.createdDate = createdDate;
  }
  return todoTogetherDetailStage6OtherEndMsg;
}

Map<String, dynamic> $TodoTogetherDetailStage6OtherEndMsgToJson(
    TodoTogetherDetailStage6OtherEndMsg entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['userTaskId'] = entity.userTaskId;
  data['toUserId'] = entity.toUserId;
  data['msg'] = entity.msg;
  data['msgType'] = entity.msgType;
  data['msgLength'] = entity.msgLength;
  data['imgUrl'] = entity.imgUrl;
  data['audioUrl'] = entity.audioUrl;
  data['type'] = entity.type;
  data['isRespond'] = entity.isRespond;
  data['createdDate'] = entity.createdDate;
  return data;
}

extension TodoTogetherDetailStage6OtherEndMsgExtension on TodoTogetherDetailStage6OtherEndMsg {
  TodoTogetherDetailStage6OtherEndMsg copyWith({
    String? id,
    String? userId,
    String? userTaskId,
    String? toUserId,
    String? msg,
    dynamic msgType,
    dynamic msgLength,
    dynamic imgUrl,
    dynamic audioUrl,
    int? type,
    int? isRespond,
    String? createdDate,
  }) {
    return TodoTogetherDetailStage6OtherEndMsg()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..userTaskId = userTaskId ?? this.userTaskId
      ..toUserId = toUserId ?? this.toUserId
      ..msg = msg ?? this.msg
      ..msgType = msgType ?? this.msgType
      ..msgLength = msgLength ?? this.msgLength
      ..imgUrl = imgUrl ?? this.imgUrl
      ..audioUrl = audioUrl ?? this.audioUrl
      ..type = type ?? this.type
      ..isRespond = isRespond ?? this.isRespond
      ..createdDate = createdDate ?? this.createdDate;
  }
}

TodoTogetherDetailStage8 $TodoTogetherDetailStage8FromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage8 todoTogetherDetailStage8 = TodoTogetherDetailStage8();
  final dynamic otherPropose = json['otherPropose'];
  if (otherPropose != null) {
    todoTogetherDetailStage8.otherPropose = otherPropose;
  }
  final dynamic myPropose = json['myPropose'];
  if (myPropose != null) {
    todoTogetherDetailStage8.myPropose = myPropose;
  }
  return todoTogetherDetailStage8;
}

Map<String, dynamic> $TodoTogetherDetailStage8ToJson(
    TodoTogetherDetailStage8 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['otherPropose'] = entity.otherPropose;
  data['myPropose'] = entity.myPropose;
  return data;
}

extension TodoTogetherDetailStage8Extension on TodoTogetherDetailStage8 {
  TodoTogetherDetailStage8 copyWith({
    dynamic otherPropose,
    dynamic myPropose,
  }) {
    return TodoTogetherDetailStage8()
      ..otherPropose = otherPropose ?? this.otherPropose
      ..myPropose = myPropose ?? this.myPropose;
  }
}

TodoTogetherDetailStage3 $TodoTogetherDetailStage3FromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage3 todoTogetherDetailStage3 = TodoTogetherDetailStage3();
  final String? otherImg = jsonConvert.convert<String>(json['otherImg']);
  if (otherImg != null) {
    todoTogetherDetailStage3.otherImg = otherImg;
  }
  final bool? Continue = jsonConvert.convert<bool>(json['Continue']);
  if (Continue != null) {
    todoTogetherDetailStage3.Continue = Continue;
  }
  final bool? otherContinueState = jsonConvert.convert<bool>(
      json['otherContinueState']);
  if (otherContinueState != null) {
    todoTogetherDetailStage3.otherContinueState = otherContinueState;
  }
  return todoTogetherDetailStage3;
}

Map<String, dynamic> $TodoTogetherDetailStage3ToJson(
    TodoTogetherDetailStage3 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['otherImg'] = entity.otherImg;
  data['Continue'] = entity.Continue;
  data['otherContinueState'] = entity.otherContinueState;
  return data;
}

extension TodoTogetherDetailStage3Extension on TodoTogetherDetailStage3 {
  TodoTogetherDetailStage3 copyWith({
    String? otherImg,
    bool? Continue,
    bool? otherContinueState,
  }) {
    return TodoTogetherDetailStage3()
      ..otherImg = otherImg ?? this.otherImg
      ..Continue = Continue ?? this.Continue
      ..otherContinueState = otherContinueState ?? this.otherContinueState;
  }
}

TodoTogetherDetailStage2 $TodoTogetherDetailStage2FromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage2 todoTogetherDetailStage2 = TodoTogetherDetailStage2();
  final String? myImg = jsonConvert.convert<String>(json['myImg']);
  if (myImg != null) {
    todoTogetherDetailStage2.myImg = myImg;
  }
  final bool? otherState = jsonConvert.convert<bool>(json['otherState']);
  if (otherState != null) {
    todoTogetherDetailStage2.otherState = otherState;
  }
  return todoTogetherDetailStage2;
}

Map<String, dynamic> $TodoTogetherDetailStage2ToJson(
    TodoTogetherDetailStage2 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['myImg'] = entity.myImg;
  data['otherState'] = entity.otherState;
  return data;
}

extension TodoTogetherDetailStage2Extension on TodoTogetherDetailStage2 {
  TodoTogetherDetailStage2 copyWith({
    String? myImg,
    bool? otherState,
  }) {
    return TodoTogetherDetailStage2()
      ..myImg = myImg ?? this.myImg
      ..otherState = otherState ?? this.otherState;
  }
}

TodoTogetherDetailStage5 $TodoTogetherDetailStage5FromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage5 todoTogetherDetailStage5 = TodoTogetherDetailStage5();
  final TodoTogetherDetailStage5MyEndMsg? myEndMsg = jsonConvert.convert<
      TodoTogetherDetailStage5MyEndMsg>(json['myEndMsg']);
  if (myEndMsg != null) {
    todoTogetherDetailStage5.myEndMsg = myEndMsg;
  }
  return todoTogetherDetailStage5;
}

Map<String, dynamic> $TodoTogetherDetailStage5ToJson(
    TodoTogetherDetailStage5 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['myEndMsg'] = entity.myEndMsg?.toJson();
  return data;
}

extension TodoTogetherDetailStage5Extension on TodoTogetherDetailStage5 {
  TodoTogetherDetailStage5 copyWith({
    TodoTogetherDetailStage5MyEndMsg? myEndMsg,
  }) {
    return TodoTogetherDetailStage5()
      ..myEndMsg = myEndMsg ?? this.myEndMsg;
  }
}

TodoTogetherDetailStage5MyEndMsg $TodoTogetherDetailStage5MyEndMsgFromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage5MyEndMsg todoTogetherDetailStage5MyEndMsg = TodoTogetherDetailStage5MyEndMsg();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    todoTogetherDetailStage5MyEndMsg.id = id;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    todoTogetherDetailStage5MyEndMsg.userId = userId;
  }
  final String? userTaskId = jsonConvert.convert<String>(json['userTaskId']);
  if (userTaskId != null) {
    todoTogetherDetailStage5MyEndMsg.userTaskId = userTaskId;
  }
  final String? toUserId = jsonConvert.convert<String>(json['toUserId']);
  if (toUserId != null) {
    todoTogetherDetailStage5MyEndMsg.toUserId = toUserId;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    todoTogetherDetailStage5MyEndMsg.msg = msg;
  }
  final dynamic msgType = json['msgType'];
  if (msgType != null) {
    todoTogetherDetailStage5MyEndMsg.msgType = msgType;
  }
  final dynamic msgLength = json['msgLength'];
  if (msgLength != null) {
    todoTogetherDetailStage5MyEndMsg.msgLength = msgLength;
  }
  final dynamic imgUrl = json['imgUrl'];
  if (imgUrl != null) {
    todoTogetherDetailStage5MyEndMsg.imgUrl = imgUrl;
  }
  final String? audioUrl = jsonConvert.convert<String>(json['audioUrl']);
  if (audioUrl != null) {
    todoTogetherDetailStage5MyEndMsg.audioUrl = audioUrl;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    todoTogetherDetailStage5MyEndMsg.type = type;
  }
  final int? isRespond = jsonConvert.convert<int>(json['isRespond']);
  if (isRespond != null) {
    todoTogetherDetailStage5MyEndMsg.isRespond = isRespond;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    todoTogetherDetailStage5MyEndMsg.createdDate = createdDate;
  }
  return todoTogetherDetailStage5MyEndMsg;
}

Map<String, dynamic> $TodoTogetherDetailStage5MyEndMsgToJson(
    TodoTogetherDetailStage5MyEndMsg entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['userTaskId'] = entity.userTaskId;
  data['toUserId'] = entity.toUserId;
  data['msg'] = entity.msg;
  data['msgType'] = entity.msgType;
  data['msgLength'] = entity.msgLength;
  data['imgUrl'] = entity.imgUrl;
  data['audioUrl'] = entity.audioUrl;
  data['type'] = entity.type;
  data['isRespond'] = entity.isRespond;
  data['createdDate'] = entity.createdDate;
  return data;
}

extension TodoTogetherDetailStage5MyEndMsgExtension on TodoTogetherDetailStage5MyEndMsg {
  TodoTogetherDetailStage5MyEndMsg copyWith({
    String? id,
    String? userId,
    String? userTaskId,
    String? toUserId,
    String? msg,
    dynamic msgType,
    dynamic msgLength,
    dynamic imgUrl,
    String? audioUrl,
    int? type,
    int? isRespond,
    String? createdDate,
  }) {
    return TodoTogetherDetailStage5MyEndMsg()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..userTaskId = userTaskId ?? this.userTaskId
      ..toUserId = toUserId ?? this.toUserId
      ..msg = msg ?? this.msg
      ..msgType = msgType ?? this.msgType
      ..msgLength = msgLength ?? this.msgLength
      ..imgUrl = imgUrl ?? this.imgUrl
      ..audioUrl = audioUrl ?? this.audioUrl
      ..type = type ?? this.type
      ..isRespond = isRespond ?? this.isRespond
      ..createdDate = createdDate ?? this.createdDate;
  }
}

TodoTogetherDetailStage4 $TodoTogetherDetailStage4FromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage4 todoTogetherDetailStage4 = TodoTogetherDetailStage4();
  final bool? isChuoState = jsonConvert.convert<bool>(json['isChuoState']);
  if (isChuoState != null) {
    todoTogetherDetailStage4.isChuoState = isChuoState;
  }
  final bool? isOtherChuoResponse = jsonConvert.convert<bool>(
      json['isOtherChuoResponse']);
  if (isOtherChuoResponse != null) {
    todoTogetherDetailStage4.isOtherChuoResponse = isOtherChuoResponse;
  }
  final int? isOtherComplete = jsonConvert.convert<int>(
      json['isOtherComplete']);
  if (isOtherComplete != null) {
    todoTogetherDetailStage4.isOtherComplete = isOtherComplete;
  }
  final bool? isChuoStateResponse = jsonConvert.convert<bool>(
      json['isChuoStateResponse']);
  if (isChuoStateResponse != null) {
    todoTogetherDetailStage4.isChuoStateResponse = isChuoStateResponse;
  }
  final int? isMyComplete = jsonConvert.convert<int>(json['isMyComplete']);
  if (isMyComplete != null) {
    todoTogetherDetailStage4.isMyComplete = isMyComplete;
  }
  final bool? isOtherChuo = jsonConvert.convert<bool>(json['isOtherChuo']);
  if (isOtherChuo != null) {
    todoTogetherDetailStage4.isOtherChuo = isOtherChuo;
  }
  return todoTogetherDetailStage4;
}

Map<String, dynamic> $TodoTogetherDetailStage4ToJson(
    TodoTogetherDetailStage4 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['isChuoState'] = entity.isChuoState;
  data['isOtherChuoResponse'] = entity.isOtherChuoResponse;
  data['isOtherComplete'] = entity.isOtherComplete;
  data['isChuoStateResponse'] = entity.isChuoStateResponse;
  data['isMyComplete'] = entity.isMyComplete;
  data['isOtherChuo'] = entity.isOtherChuo;
  return data;
}

extension TodoTogetherDetailStage4Extension on TodoTogetherDetailStage4 {
  TodoTogetherDetailStage4 copyWith({
    bool? isChuoState,
    bool? isOtherChuoResponse,
    int? isOtherComplete,
    bool? isChuoStateResponse,
    int? isMyComplete,
    bool? isOtherChuo,
  }) {
    return TodoTogetherDetailStage4()
      ..isChuoState = isChuoState ?? this.isChuoState
      ..isOtherChuoResponse = isOtherChuoResponse ?? this.isOtherChuoResponse
      ..isOtherComplete = isOtherComplete ?? this.isOtherComplete
      ..isChuoStateResponse = isChuoStateResponse ?? this.isChuoStateResponse
      ..isMyComplete = isMyComplete ?? this.isMyComplete
      ..isOtherChuo = isOtherChuo ?? this.isOtherChuo;
  }
}

TodoTogetherDetailStage1 $TodoTogetherDetailStage1FromJson(
    Map<String, dynamic> json) {
  final TodoTogetherDetailStage1 todoTogetherDetailStage1 = TodoTogetherDetailStage1();
  final bool? otherConfirmState = jsonConvert.convert<bool>(
      json['otherConfirmState']);
  if (otherConfirmState != null) {
    todoTogetherDetailStage1.otherConfirmState = otherConfirmState;
  }
  final int? mySex = jsonConvert.convert<int>(json['mySex']);
  if (mySex != null) {
    todoTogetherDetailStage1.mySex = mySex;
  }
  final int? otherSex = jsonConvert.convert<int>(json['otherSex']);
  if (otherSex != null) {
    todoTogetherDetailStage1.otherSex = otherSex;
  }
  final String? myName = jsonConvert.convert<String>(json['myName']);
  if (myName != null) {
    todoTogetherDetailStage1.myName = myName;
  }
  final String? otherName = jsonConvert.convert<String>(json['otherName']);
  if (otherName != null) {
    todoTogetherDetailStage1.otherName = otherName;
  }
  final String? otherAvatar = jsonConvert.convert<String>(json['otherAvatar']);
  if (otherAvatar != null) {
    todoTogetherDetailStage1.otherAvatar = otherAvatar;
  }
  final int? myAge = jsonConvert.convert<int>(json['myAge']);
  if (myAge != null) {
    todoTogetherDetailStage1.myAge = myAge;
  }
  final String? myAvatar = jsonConvert.convert<String>(json['myAvatar']);
  if (myAvatar != null) {
    todoTogetherDetailStage1.myAvatar = myAvatar;
  }
  final bool? myConfirmState = jsonConvert.convert<bool>(
      json['myConfirmState']);
  if (myConfirmState != null) {
    todoTogetherDetailStage1.myConfirmState = myConfirmState;
  }
  final int? otherAge = jsonConvert.convert<int>(json['otherAge']);
  if (otherAge != null) {
    todoTogetherDetailStage1.otherAge = otherAge;
  }
  return todoTogetherDetailStage1;
}

Map<String, dynamic> $TodoTogetherDetailStage1ToJson(
    TodoTogetherDetailStage1 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['otherConfirmState'] = entity.otherConfirmState;
  data['mySex'] = entity.mySex;
  data['otherSex'] = entity.otherSex;
  data['myName'] = entity.myName;
  data['otherName'] = entity.otherName;
  data['otherAvatar'] = entity.otherAvatar;
  data['myAge'] = entity.myAge;
  data['myAvatar'] = entity.myAvatar;
  data['myConfirmState'] = entity.myConfirmState;
  data['otherAge'] = entity.otherAge;
  return data;
}

extension TodoTogetherDetailStage1Extension on TodoTogetherDetailStage1 {
  TodoTogetherDetailStage1 copyWith({
    bool? otherConfirmState,
    int? mySex,
    int? otherSex,
    String? myName,
    String? otherName,
    String? otherAvatar,
    int? myAge,
    String? myAvatar,
    bool? myConfirmState,
    int? otherAge,
  }) {
    return TodoTogetherDetailStage1()
      ..otherConfirmState = otherConfirmState ?? this.otherConfirmState
      ..mySex = mySex ?? this.mySex
      ..otherSex = otherSex ?? this.otherSex
      ..myName = myName ?? this.myName
      ..otherName = otherName ?? this.otherName
      ..otherAvatar = otherAvatar ?? this.otherAvatar
      ..myAge = myAge ?? this.myAge
      ..myAvatar = myAvatar ?? this.myAvatar
      ..myConfirmState = myConfirmState ?? this.myConfirmState
      ..otherAge = otherAge ?? this.otherAge;
  }
}