import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/goods_entity.dart';
import 'package:dada/model/prop_entity.dart';


GoodsEntity $GoodsEntityFromJson(Map<String, dynamic> json) {
  final GoodsEntity goodsEntity = GoodsEntity();
  final String? goodsId = jsonConvert.convert<String>(json['goodsId']);
  if (goodsId != null) {
    goodsEntity.goodsId = goodsId;
  }
  final int? goodsType = jsonConvert.convert<int>(json['goodsType']);
  if (goodsType != null) {
    goodsEntity.goodsType = goodsType;
  }
  final double? price = jsonConvert.convert<double>(json['price']);
  if (price != null) {
    goodsEntity.price = price;
  }
  final int? createdUserId = jsonConvert.convert<int>(json['createdUserId']);
  if (createdUserId != null) {
    goodsEntity.createdUserId = createdUserId;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    goodsEntity.createdDate = createdDate;
  }
  final String? updateDate = jsonConvert.convert<String>(json['updateDate']);
  if (updateDate != null) {
    goodsEntity.updateDate = updateDate;
  }
  final String? goodsName = jsonConvert.convert<String>(json['goodsName']);
  if (goodsName != null) {
    goodsEntity.goodsName = goodsName;
  }
  final String? image = jsonConvert.convert<String>(json['image']);
  if (image != null) {
    goodsEntity.image = image;
  }
  final int? count = jsonConvert.convert<int>(json['count']);
  if (count != null) {
    goodsEntity.count = count;
  }
  final List<
      GoodsRewardEntity>? goodsDetailsList = (json['goodsDetailsList'] as List<
      dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<GoodsRewardEntity>(e) as GoodsRewardEntity)
      .toList();
  if (goodsDetailsList != null) {
    goodsEntity.goodsDetailsList = goodsDetailsList;
  }
  final int? isBuy = jsonConvert.convert<int>(json['isBuy']);
  if (isBuy != null) {
    goodsEntity.isBuy = isBuy;
  }
  final int? buyLimit = jsonConvert.convert<int>(json['buyLimit']);
  if (buyLimit != null) {
    goodsEntity.buyLimit = buyLimit;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    goodsEntity.endTime = endTime;
  }
  final double? originalPrice = jsonConvert.convert<double>(
      json['originalPrice']);
  if (originalPrice != null) {
    goodsEntity.originalPrice = originalPrice;
  }
  final String? goodsDescription = jsonConvert.convert<String>(
      json['goodsDescription']);
  if (goodsDescription != null) {
    goodsEntity.goodsDescription = goodsDescription;
  }
  return goodsEntity;
}

Map<String, dynamic> $GoodsEntityToJson(GoodsEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['goodsId'] = entity.goodsId;
  data['goodsType'] = entity.goodsType;
  data['price'] = entity.price;
  data['createdUserId'] = entity.createdUserId;
  data['createdDate'] = entity.createdDate;
  data['updateDate'] = entity.updateDate;
  data['goodsName'] = entity.goodsName;
  data['image'] = entity.image;
  data['count'] = entity.count;
  data['goodsDetailsList'] =
      entity.goodsDetailsList?.map((v) => v.toJson()).toList();
  data['isBuy'] = entity.isBuy;
  data['buyLimit'] = entity.buyLimit;
  data['endTime'] = entity.endTime;
  data['originalPrice'] = entity.originalPrice;
  data['goodsDescription'] = entity.goodsDescription;
  return data;
}

extension GoodsEntityExtension on GoodsEntity {
  GoodsEntity copyWith({
    String? goodsId,
    int? goodsType,
    double? price,
    int? createdUserId,
    String? createdDate,
    String? updateDate,
    String? goodsName,
    String? image,
    int? count,
    List<GoodsRewardEntity>? goodsDetailsList,
    int? isBuy,
    int? buyLimit,
    String? endTime,
    double? originalPrice,
    String? goodsDescription,
  }) {
    return GoodsEntity()
      ..goodsId = goodsId ?? this.goodsId
      ..goodsType = goodsType ?? this.goodsType
      ..price = price ?? this.price
      ..createdUserId = createdUserId ?? this.createdUserId
      ..createdDate = createdDate ?? this.createdDate
      ..updateDate = updateDate ?? this.updateDate
      ..goodsName = goodsName ?? this.goodsName
      ..image = image ?? this.image
      ..count = count ?? this.count
      ..goodsDetailsList = goodsDetailsList ?? this.goodsDetailsList
      ..isBuy = isBuy ?? this.isBuy
      ..buyLimit = buyLimit ?? this.buyLimit
      ..endTime = endTime ?? this.endTime
      ..originalPrice = originalPrice ?? this.originalPrice
      ..goodsDescription = goodsDescription ?? this.goodsDescription;
  }
}

GoodsRewardEntity $GoodsRewardEntityFromJson(Map<String, dynamic> json) {
  final GoodsRewardEntity goodsRewardEntity = GoodsRewardEntity();
  final int? goodsDetailId = jsonConvert.convert<int>(json['goodsDetailId']);
  if (goodsDetailId != null) {
    goodsRewardEntity.goodsDetailId = goodsDetailId;
  }
  final int? goodsId = jsonConvert.convert<int>(json['goodsId']);
  if (goodsId != null) {
    goodsRewardEntity.goodsId = goodsId;
  }
  final int? propId = jsonConvert.convert<int>(json['propId']);
  if (propId != null) {
    goodsRewardEntity.propId = propId;
  }
  final int? num = jsonConvert.convert<int>(json['num']);
  if (num != null) {
    goodsRewardEntity.num = num;
  }
  final int? giftsNum = jsonConvert.convert<int>(json['giftsNum']);
  if (giftsNum != null) {
    goodsRewardEntity.giftsNum = giftsNum;
  }
  final String? updateDate = jsonConvert.convert<String>(json['updateDate']);
  if (updateDate != null) {
    goodsRewardEntity.updateDate = updateDate;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    goodsRewardEntity.createdDate = createdDate;
  }
  final int? expiredDate = jsonConvert.convert<int>(json['expiredDate']);
  if (expiredDate != null) {
    goodsRewardEntity.expiredDate = expiredDate;
  }
  final PropEntity? prop = jsonConvert.convert<PropEntity>(json['prop']);
  if (prop != null) {
    goodsRewardEntity.prop = prop;
  }
  return goodsRewardEntity;
}

Map<String, dynamic> $GoodsRewardEntityToJson(GoodsRewardEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['goodsDetailId'] = entity.goodsDetailId;
  data['goodsId'] = entity.goodsId;
  data['propId'] = entity.propId;
  data['num'] = entity.num;
  data['giftsNum'] = entity.giftsNum;
  data['updateDate'] = entity.updateDate;
  data['createdDate'] = entity.createdDate;
  data['expiredDate'] = entity.expiredDate;
  data['prop'] = entity.prop?.toJson();
  return data;
}

extension GoodsRewardEntityExtension on GoodsRewardEntity {
  GoodsRewardEntity copyWith({
    int? goodsDetailId,
    int? goodsId,
    int? propId,
    int? num,
    int? giftsNum,
    String? updateDate,
    String? createdDate,
    int? expiredDate,
    PropEntity? prop,
  }) {
    return GoodsRewardEntity()
      ..goodsDetailId = goodsDetailId ?? this.goodsDetailId
      ..goodsId = goodsId ?? this.goodsId
      ..propId = propId ?? this.propId
      ..num = num ?? this.num
      ..giftsNum = giftsNum ?? this.giftsNum
      ..updateDate = updateDate ?? this.updateDate
      ..createdDate = createdDate ?? this.createdDate
      ..expiredDate = expiredDate ?? this.expiredDate
      ..prop = prop ?? this.prop;
  }
}