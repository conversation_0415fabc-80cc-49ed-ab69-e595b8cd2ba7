import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/group_list_item_entity.dart';

GroupListItemEntity $GroupListItemEntityFromJson(Map<String, dynamic> json) {
  final GroupListItemEntity groupListItemEntity = GroupListItemEntity();
  final String? chatsName = jsonConvert.convert<String>(json['chatsName']);
  if (chatsName != null) {
    groupListItemEntity.chatsName = chatsName;
  }
  final String? creatorId = jsonConvert.convert<String>(json['creatorId']);
  if (creatorId != null) {
    groupListItemEntity.creatorId = creatorId;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    groupListItemEntity.description = description;
  }
  final String? avatarUrl = jsonConvert.convert<String>(json['avatarUrl']);
  if (avatarUrl != null) {
    groupListItemEntity.avatarUrl = avatarUrl;
  }
  final String? notice = jsonConvert.convert<String>(json['notice']);
  if (notice != null) {
    groupListItemEntity.notice = notice;
  }
  final String? inviteConfirm = jsonConvert.convert<String>(
      json['inviteConfirm']);
  if (inviteConfirm != null) {
    groupListItemEntity.inviteConfirm = inviteConfirm;
  }
  return groupListItemEntity;
}

Map<String, dynamic> $GroupListItemEntityToJson(GroupListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['chatsName'] = entity.chatsName;
  data['creatorId'] = entity.creatorId;
  data['description'] = entity.description;
  data['avatarUrl'] = entity.avatarUrl;
  data['notice'] = entity.notice;
  data['inviteConfirm'] = entity.inviteConfirm;
  return data;
}

extension GroupListItemEntityExtension on GroupListItemEntity {
  GroupListItemEntity copyWith({
    String? chatsName,
    String? creatorId,
    String? description,
    String? avatarUrl,
    String? notice,
    String? inviteConfirm,
  }) {
    return GroupListItemEntity()
      ..chatsName = chatsName ?? this.chatsName
      ..creatorId = creatorId ?? this.creatorId
      ..description = description ?? this.description
      ..avatarUrl = avatarUrl ?? this.avatarUrl
      ..notice = notice ?? this.notice
      ..inviteConfirm = inviteConfirm ?? this.inviteConfirm;
  }
}