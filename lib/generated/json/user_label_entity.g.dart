import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/user_label_entity.dart';

UserLabelEntity $UserLabelEntityFromJson(Map<String, dynamic> json) {
  final UserLabelEntity userLabelEntity = UserLabelEntity();
  final String? labelId = jsonConvert.convert<String>(json['labelId']);
  if (labelId != null) {
    userLabelEntity.labelId = labelId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    userLabelEntity.userId = userId;
  }
  final String? labelName = jsonConvert.convert<String>(json['labelName']);
  if (labelName != null) {
    userLabelEntity.labelName = labelName;
  }
  final List<String>? labelText = (json['labelText'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (labelText != null) {
    userLabelEntity.labelText = labelText;
  }
  final List<String>? labelImgs = (json['labelImgs'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (labelImgs != null) {
    userLabelEntity.labelImgs = labelImgs;
  }
  return userLabelEntity;
}

Map<String, dynamic> $UserLabelEntityToJson(UserLabelEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['labelId'] = entity.labelId;
  data['userId'] = entity.userId;
  data['labelName'] = entity.labelName;
  data['labelText'] = entity.labelText;
  data['labelImgs'] = entity.labelImgs;
  return data;
}

extension UserLabelEntityExtension on UserLabelEntity {
  UserLabelEntity copyWith({
    String? labelId,
    String? userId,
    String? labelName,
    List<String>? labelText,
    List<String>? labelImgs,
  }) {
    return UserLabelEntity()
      ..labelId = labelId ?? this.labelId
      ..userId = userId ?? this.userId
      ..labelName = labelName ?? this.labelName
      ..labelText = labelText ?? this.labelText
      ..labelImgs = labelImgs ?? this.labelImgs;
  }
}