import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';

ChatRoomSeatInfoEntity $ChatRoomSeatInfoEntityFromJson(
    Map<String, dynamic> json) {
  final ChatRoomSeatInfoEntity chatRoomSeatInfoEntity = ChatRoomSeatInfoEntity();
  final int? index = jsonConvert.convert<int>(json['index']);
  if (index != null) {
    chatRoomSeatInfoEntity.index = index;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    chatRoomSeatInfoEntity.status = status;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    chatRoomSeatInfoEntity.type = type;
  }
  final String? takeSeatTime = jsonConvert.convert<String>(
      json['takeSeatTime']);
  if (takeSeatTime != null) {
    chatRoomSeatInfoEntity.takeSeatTime = takeSeatTime;
  }
  final int? faction = jsonConvert.convert<int>(json['faction']);
  if (faction != null) {
    chatRoomSeatInfoEntity.faction = faction;
  }
  final ChatRoomSeatInfoUser? user = jsonConvert.convert<ChatRoomSeatInfoUser>(
      json['user']);
  if (user != null) {
    chatRoomSeatInfoEntity.user = user;
  }
  return chatRoomSeatInfoEntity;
}

Map<String, dynamic> $ChatRoomSeatInfoEntityToJson(
    ChatRoomSeatInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['index'] = entity.index;
  data['status'] = entity.status;
  data['type'] = entity.type;
  data['takeSeatTime'] = entity.takeSeatTime;
  data['faction'] = entity.faction;
  data['user'] = entity.user?.toJson();
  return data;
}

extension ChatRoomSeatInfoEntityExtension on ChatRoomSeatInfoEntity {
  ChatRoomSeatInfoEntity copyWith({
    int? index,
    int? status,
    int? type,
    String? takeSeatTime,
    int? faction,
    ChatRoomSeatInfoUser? user,
  }) {
    return ChatRoomSeatInfoEntity()
      ..index = index ?? this.index
      ..status = status ?? this.status
      ..type = type ?? this.type
      ..takeSeatTime = takeSeatTime ?? this.takeSeatTime
      ..faction = faction ?? this.faction
      ..user = user ?? this.user;
  }
}

ChatRoomSeatInfoUser $ChatRoomSeatInfoUserFromJson(Map<String, dynamic> json) {
  final ChatRoomSeatInfoUser chatRoomSeatInfoUser = ChatRoomSeatInfoUser();
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    chatRoomSeatInfoUser.userId = userId;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    chatRoomSeatInfoUser.nickname = nickname;
  }
  final int? imageIndex = jsonConvert.convert<int>(json['imageIndex']);
  if (imageIndex != null) {
    chatRoomSeatInfoUser.imageIndex = imageIndex;
  }
  final String? dressNo = jsonConvert.convert<String>(json['dressNo']);
  if (dressNo != null) {
    chatRoomSeatInfoUser.dressNo = dressNo;
  }
  return chatRoomSeatInfoUser;
}

Map<String, dynamic> $ChatRoomSeatInfoUserToJson(ChatRoomSeatInfoUser entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userId'] = entity.userId;
  data['nickname'] = entity.nickname;
  data['imageIndex'] = entity.imageIndex;
  data['dressNo'] = entity.dressNo;
  return data;
}

extension ChatRoomSeatInfoUserExtension on ChatRoomSeatInfoUser {
  ChatRoomSeatInfoUser copyWith({
    String? userId,
    String? nickname,
    int? imageIndex,
    String? dressNo,
  }) {
    return ChatRoomSeatInfoUser()
      ..userId = userId ?? this.userId
      ..nickname = nickname ?? this.nickname
      ..imageIndex = imageIndex ?? this.imageIndex
      ..dressNo = dressNo ?? this.dressNo;
  }
}