import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_group_label_entity.dart';

ChatGroupLabelEntity $ChatGroupLabelEntityFromJson(Map<String, dynamic> json) {
  final ChatGroupLabelEntity chatGroupLabelEntity = ChatGroupLabelEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    chatGroupLabelEntity.id = id;
  }
  final String? tagName = jsonConvert.convert<String>(json['tagName']);
  if (tagName != null) {
    chatGroupLabelEntity.tagName = tagName;
  }
  return chatGroupLabelEntity;
}

Map<String, dynamic> $ChatGroupLabelEntityToJson(ChatGroupLabelEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['tagName'] = entity.tagName;
  return data;
}

extension ChatGroupLabelEntityExtension on ChatGroupLabelEntity {
  ChatGroupLabelEntity copyWith({
    String? id,
    String? tagName,
  }) {
    return ChatGroupLabelEntity()
      ..id = id ?? this.id
      ..tagName = tagName ?? this.tagName;
  }
}