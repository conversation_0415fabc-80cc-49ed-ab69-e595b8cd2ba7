import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/model/user_label_entity.dart';


UserInfoEntity $UserInfoEntityFromJson(Map<String, dynamic> json) {
  final UserInfoEntity userInfoEntity = UserInfoEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    userInfoEntity.id = id;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    userInfoEntity.avatar = avatar;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    userInfoEntity.nickname = nickname;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    userInfoEntity.sex = sex;
  }
  final int? age = jsonConvert.convert<int>(json['age']);
  if (age != null) {
    userInfoEntity.age = age;
  }
  final int? level = jsonConvert.convert<int>(json['level']);
  if (level != null) {
    userInfoEntity.level = level;
  }
  final int? dadaLevel = jsonConvert.convert<int>(json['dadaLevel']);
  if (dadaLevel != null) {
    userInfoEntity.dadaLevel = dadaLevel;
  }
  final String? place = jsonConvert.convert<String>(json['place']);
  if (place != null) {
    userInfoEntity.place = place;
  }
  final bool? isAuthIdentity = jsonConvert.convert<bool>(
      json['isAuthIdentity']);
  if (isAuthIdentity != null) {
    userInfoEntity.isAuthIdentity = isAuthIdentity;
  }
  final String? voiceSignature = jsonConvert.convert<String>(
      json['voiceSignature']);
  if (voiceSignature != null) {
    userInfoEntity.voiceSignature = voiceSignature;
  }
  final String? txtSignature = jsonConvert.convert<String>(
      json['txtSignature']);
  if (txtSignature != null) {
    userInfoEntity.txtSignature = txtSignature;
  }
  final String? hometown = jsonConvert.convert<String>(json['hometown']);
  if (hometown != null) {
    userInfoEntity.hometown = hometown;
  }
  final String? vocation = jsonConvert.convert<String>(json['vocation']);
  if (vocation != null) {
    userInfoEntity.vocation = vocation;
  }
  final String? birthday = jsonConvert.convert<String>(json['birthday']);
  if (birthday != null) {
    userInfoEntity.birthday = birthday;
  }
  final int? voiceLength = jsonConvert.convert<int>(json['voiceLength']);
  if (voiceLength != null) {
    userInfoEntity.voiceLength = voiceLength;
  }
  final int? socialState = jsonConvert.convert<int>(json['socialState']);
  if (socialState != null) {
    userInfoEntity.socialState = socialState;
  }
  final String? dadaNo = jsonConvert.convert<String>(json['dadaNo']);
  if (dadaNo != null) {
    userInfoEntity.dadaNo = dadaNo;
  }
  final String? work = jsonConvert.convert<String>(json['work']);
  if (work != null) {
    userInfoEntity.work = work;
  }
  final List<UserLabelEntity>? labels = (json['labels'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<UserLabelEntity>(e) as UserLabelEntity)
      .toList();
  if (labels != null) {
    userInfoEntity.labels = labels;
  }
  final int? ties = jsonConvert.convert<int>(json['ties']);
  if (ties != null) {
    userInfoEntity.ties = ties;
  }
  final String? avatarFrame = jsonConvert.convert<String>(json['avatarFrame']);
  if (avatarFrame != null) {
    userInfoEntity.avatarFrame = avatarFrame;
  }
  final String? chatBubble = jsonConvert.convert<String>(json['dialogBox']);
  if (chatBubble != null) {
    userInfoEntity.chatBubble = chatBubble;
  }
  final int? isAccept = jsonConvert.convert<int>(json['isAccept']);
  if (isAccept != null) {
    userInfoEntity.isAccept = isAccept;
  }
  final bool? monthlyPassUser = jsonConvert.convert<bool>(
      json['monthlyPassUser']);
  if (monthlyPassUser != null) {
    userInfoEntity.monthlyPassUser = monthlyPassUser;
  }
  final UserInfoEntity? inviteUserInfo = jsonConvert.convert<UserInfoEntity>(
      json['inviteUserInfo']);
  if (inviteUserInfo != null) {
    userInfoEntity.inviteUserInfo = inviteUserInfo;
  }
  final String? inviteCode = jsonConvert.convert<String>(json['inviteCode']);
  if (inviteCode != null) {
    userInfoEntity.inviteCode = inviteCode;
  }
  final String? phone = jsonConvert.convert<String>(json['phone']);
  if (phone != null) {
    userInfoEntity.phone = phone;
  }
  final String? recentState = jsonConvert.convert<String>(json['recentState']);
  if (recentState != null) {
    userInfoEntity.recentState = recentState;
  }
  final int? isFriend = jsonConvert.convert<int>(json['isFriend']);
  if (isFriend != null) {
    userInfoEntity.isFriend = isFriend;
  }
  final String? beginKnowdDate = jsonConvert.convert<String>(
      json['beginKnowdDate']);
  if (beginKnowdDate != null) {
    userInfoEntity.beginKnowdDate = beginKnowdDate;
  }
  final int? state = jsonConvert.convert<int>(json['state']);
  if (state != null) {
    userInfoEntity.state = state;
  }
  final int? genderChangeCount = jsonConvert.convert<int>(
      json['genderChangeCount']);
  if (genderChangeCount != null) {
    userInfoEntity.genderChangeCount = genderChangeCount;
  }
  final bool? isRealName = jsonConvert.convert<bool>(json['isRealName']);
  if (isRealName != null) {
    userInfoEntity.isRealName = isRealName;
  }
  final String? currentDressNo = jsonConvert.convert<String>(
      json['currentDressNo']);
  if (currentDressNo != null) {
    userInfoEntity.currentDressNo = currentDressNo;
  }
  final String? ipRegion = jsonConvert.convert<String>(json['ipRegion']);
  if (ipRegion != null) {
    userInfoEntity.ipRegion = ipRegion;
  }
  final String? friendLevel = jsonConvert.convert<String>(json['friendLevel']);
  if (friendLevel != null) {
    userInfoEntity.friendLevel = friendLevel;
  }
  final String? monthlyPassDay = jsonConvert.convert<String>(
      json['monthlyPassDay']);
  if (monthlyPassDay != null) {
    userInfoEntity.monthlyPassDay = monthlyPassDay;
  }
  final int? daStickNum = jsonConvert.convert<int>(json['daStickNum']);
  if (daStickNum != null) {
    userInfoEntity.daStickNum = daStickNum;
  }
  final String? constellation = jsonConvert.convert<String>(
      json['constellation']);
  if (constellation != null) {
    userInfoEntity.constellation = constellation;
  }
  final int? isPioneer = jsonConvert.convert<int>(json['isPioneer']);
  if (isPioneer != null) {
    userInfoEntity.isPioneer = isPioneer;
  }
  final int? isInitUser = jsonConvert.convert<int>(json['isInitUser']);
  if (isInitUser != null) {
    userInfoEntity.isInitUser = isInitUser;
  }
  final int? isFireKeeper = jsonConvert.convert<int>(json['isFireKeeper']);
  if (isFireKeeper != null) {
    userInfoEntity.isFireKeeper = isFireKeeper;
  }
  final int? isFirstRecharge = jsonConvert.convert<int>(
      json['isFirstRecharge']);
  if (isFirstRecharge != null) {
    userInfoEntity.isFirstRecharge = isFirstRecharge;
  }
  final String? chatFontColor = jsonConvert.convert<String>(
      json['chatFontColor']);
  if (chatFontColor != null) {
    userInfoEntity.chatFontColor = chatFontColor;
  }
  final String? chatOffset = jsonConvert.convert<String>(json['chatOffset']);
  if (chatOffset != null) {
    userInfoEntity.chatOffset = chatOffset;
  }
  return userInfoEntity;
}

Map<String, dynamic> $UserInfoEntityToJson(UserInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['avatar'] = entity.avatar;
  data['nickname'] = entity.nickname;
  data['sex'] = entity.sex;
  data['age'] = entity.age;
  data['level'] = entity.level;
  data['dadaLevel'] = entity.dadaLevel;
  data['place'] = entity.place;
  data['isAuthIdentity'] = entity.isAuthIdentity;
  data['voiceSignature'] = entity.voiceSignature;
  data['txtSignature'] = entity.txtSignature;
  data['hometown'] = entity.hometown;
  data['vocation'] = entity.vocation;
  data['birthday'] = entity.birthday;
  data['voiceLength'] = entity.voiceLength;
  data['socialState'] = entity.socialState;
  data['dadaNo'] = entity.dadaNo;
  data['work'] = entity.work;
  data['labels'] = entity.labels?.map((v) => v.toJson()).toList();
  data['ties'] = entity.ties;
  data['avatarFrame'] = entity.avatarFrame;
  data['dialogBox'] = entity.chatBubble;
  data['isAccept'] = entity.isAccept;
  data['monthlyPassUser'] = entity.monthlyPassUser;
  data['inviteUserInfo'] = entity.inviteUserInfo?.toJson();
  data['inviteCode'] = entity.inviteCode;
  data['phone'] = entity.phone;
  data['recentState'] = entity.recentState;
  data['isFriend'] = entity.isFriend;
  data['beginKnowdDate'] = entity.beginKnowdDate;
  data['state'] = entity.state;
  data['genderChangeCount'] = entity.genderChangeCount;
  data['isRealName'] = entity.isRealName;
  data['currentDressNo'] = entity.currentDressNo;
  data['ipRegion'] = entity.ipRegion;
  data['friendLevel'] = entity.friendLevel;
  data['monthlyPassDay'] = entity.monthlyPassDay;
  data['daStickNum'] = entity.daStickNum;
  data['constellation'] = entity.constellation;
  data['isPioneer'] = entity.isPioneer;
  data['isInitUser'] = entity.isInitUser;
  data['isFireKeeper'] = entity.isFireKeeper;
  data['isFirstRecharge'] = entity.isFirstRecharge;
  data['chatFontColor'] = entity.chatFontColor;
  data['chatOffset'] = entity.chatOffset;
  return data;
}

extension UserInfoEntityExtension on UserInfoEntity {
  UserInfoEntity copyWith({
    String? id,
    String? avatar,
    String? nickname,
    int? sex,
    int? age,
    int? level,
    int? dadaLevel,
    String? place,
    bool? isAuthIdentity,
    String? voiceSignature,
    String? txtSignature,
    String? hometown,
    String? vocation,
    String? birthday,
    int? voiceLength,
    int? socialState,
    String? dadaNo,
    String? work,
    List<UserLabelEntity>? labels,
    int? ties,
    String? avatarFrame,
    String? chatBubble,
    int? isAccept,
    bool? monthlyPassUser,
    UserInfoEntity? inviteUserInfo,
    String? inviteCode,
    String? phone,
    String? recentState,
    int? isFriend,
    String? beginKnowdDate,
    int? state,
    int? genderChangeCount,
    bool? isRealName,
    String? currentDressNo,
    String? ipRegion,
    String? friendLevel,
    String? monthlyPassDay,
    int? daStickNum,
    String? constellation,
    int? isPioneer,
    int? isInitUser,
    int? isFireKeeper,
    int? isFirstRecharge,
    String? chatFontColor,
    String? chatOffset,
  }) {
    return UserInfoEntity()
      ..id = id ?? this.id
      ..avatar = avatar ?? this.avatar
      ..nickname = nickname ?? this.nickname
      ..sex = sex ?? this.sex
      ..age = age ?? this.age
      ..level = level ?? this.level
      ..dadaLevel = dadaLevel ?? this.dadaLevel
      ..place = place ?? this.place
      ..isAuthIdentity = isAuthIdentity ?? this.isAuthIdentity
      ..voiceSignature = voiceSignature ?? this.voiceSignature
      ..txtSignature = txtSignature ?? this.txtSignature
      ..hometown = hometown ?? this.hometown
      ..vocation = vocation ?? this.vocation
      ..birthday = birthday ?? this.birthday
      ..voiceLength = voiceLength ?? this.voiceLength
      ..socialState = socialState ?? this.socialState
      ..dadaNo = dadaNo ?? this.dadaNo
      ..work = work ?? this.work
      ..labels = labels ?? this.labels
      ..ties = ties ?? this.ties
      ..avatarFrame = avatarFrame ?? this.avatarFrame
      ..chatBubble = chatBubble ?? this.chatBubble
      ..isAccept = isAccept ?? this.isAccept
      ..monthlyPassUser = monthlyPassUser ?? this.monthlyPassUser
      ..inviteUserInfo = inviteUserInfo ?? this.inviteUserInfo
      ..inviteCode = inviteCode ?? this.inviteCode
      ..phone = phone ?? this.phone
      ..recentState = recentState ?? this.recentState
      ..isFriend = isFriend ?? this.isFriend
      ..beginKnowdDate = beginKnowdDate ?? this.beginKnowdDate
      ..state = state ?? this.state
      ..genderChangeCount = genderChangeCount ?? this.genderChangeCount
      ..isRealName = isRealName ?? this.isRealName
      ..currentDressNo = currentDressNo ?? this.currentDressNo
      ..ipRegion = ipRegion ?? this.ipRegion
      ..friendLevel = friendLevel ?? this.friendLevel
      ..monthlyPassDay = monthlyPassDay ?? this.monthlyPassDay
      ..daStickNum = daStickNum ?? this.daStickNum
      ..constellation = constellation ?? this.constellation
      ..isPioneer = isPioneer ?? this.isPioneer
      ..isInitUser = isInitUser ?? this.isInitUser
      ..isFireKeeper = isFireKeeper ?? this.isFireKeeper
      ..isFirstRecharge = isFirstRecharge ?? this.isFirstRecharge
      ..chatFontColor = chatFontColor ?? this.chatFontColor
      ..chatOffset = chatOffset ?? this.chatOffset;
  }
}