import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/teen_mode_status_entity.dart';

TeenModeStatusEntity $TeenModeStatusEntityFromJson(Map<String, dynamic> json) {
  final TeenModeStatusEntity teenModeStatusEntity = TeenModeStatusEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    teenModeStatusEntity.id = id;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    teenModeStatusEntity.userId = userId;
  }
  final String? password = jsonConvert.convert<String>(json['password']);
  if (password != null) {
    teenModeStatusEntity.password = password;
  }
  final String? toggle = jsonConvert.convert<String>(json['toggle']);
  if (toggle != null) {
    teenModeStatusEntity.toggle = toggle;
  }
  return teenModeStatusEntity;
}

Map<String, dynamic> $TeenModeStatusEntityToJson(TeenModeStatusEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['password'] = entity.password;
  data['toggle'] = entity.toggle;
  return data;
}

extension TeenModeStatusEntityExtension on TeenModeStatusEntity {
  TeenModeStatusEntity copyWith({
    int? id,
    int? userId,
    String? password,
    String? toggle,
  }) {
    return TeenModeStatusEntity()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..password = password ?? this.password
      ..toggle = toggle ?? this.toggle;
  }
}