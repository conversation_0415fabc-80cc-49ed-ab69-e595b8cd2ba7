import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/prop_entity.dart';

PropEntity $PropEntityFromJson(Map<String, dynamic> json) {
  final PropEntity propEntity = PropEntity();
  final String? propId = jsonConvert.convert<String>(json['propId']);
  if (propId != null) {
    propEntity.propId = propId;
  }
  final int? isUse = jsonConvert.convert<int>(json['isUse']);
  if (isUse != null) {
    propEntity.isUse = isUse;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    propEntity.type = type;
  }
  final int? propType = jsonConvert.convert<int>(json['propType']);
  if (propType != null) {
    propEntity.propType = propType;
  }
  final String? propName = jsonConvert.convert<String>(json['propName']);
  if (propName != null) {
    propEntity.propName = propName;
  }
  final int? packageId = jsonConvert.convert<int>(json['packageId']);
  if (packageId != null) {
    propEntity.packageId = packageId;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    propEntity.url = url;
  }
  final int? isExist = jsonConvert.convert<int>(json['isExist']);
  if (isExist != null) {
    propEntity.isExist = isExist;
  }
  final int? num = jsonConvert.convert<int>(json['num']);
  if (num != null) {
    propEntity.num = num;
  }
  final String? propNo = jsonConvert.convert<String>(json['propNo']);
  if (propNo != null) {
    propEntity.propNo = propNo;
  }
  final String? dressNo = jsonConvert.convert<String>(json['dressNo']);
  if (dressNo != null) {
    propEntity.dressNo = dressNo;
  }
  final int? isFavorites = jsonConvert.convert<int>(json['isFavorites']);
  if (isFavorites != null) {
    propEntity.isFavorites = isFavorites;
  }
  final List<
      PropDressColorEntity>? dressColorList = (json['dressColorList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<PropDressColorEntity>(e) as PropDressColorEntity)
      .toList();
  if (dressColorList != null) {
    propEntity.dressColorList = dressColorList;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    propEntity.description = description;
  }
  return propEntity;
}

Map<String, dynamic> $PropEntityToJson(PropEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['propId'] = entity.propId;
  data['isUse'] = entity.isUse;
  data['type'] = entity.type;
  data['propType'] = entity.propType;
  data['propName'] = entity.propName;
  data['packageId'] = entity.packageId;
  data['url'] = entity.url;
  data['isExist'] = entity.isExist;
  data['num'] = entity.num;
  data['propNo'] = entity.propNo;
  data['dressNo'] = entity.dressNo;
  data['isFavorites'] = entity.isFavorites;
  data['dressColorList'] =
      entity.dressColorList?.map((v) => v.toJson()).toList();
  data['description'] = entity.description;
  return data;
}

extension PropEntityExtension on PropEntity {
  PropEntity copyWith({
    String? propId,
    int? isUse,
    int? type,
    int? propType,
    String? propName,
    int? packageId,
    String? url,
    int? isExist,
    int? num,
    String? propNo,
    String? dressNo,
    int? isFavorites,
    List<PropDressColorEntity>? dressColorList,
    String? description,
  }) {
    return PropEntity()
      ..propId = propId ?? this.propId
      ..isUse = isUse ?? this.isUse
      ..type = type ?? this.type
      ..propType = propType ?? this.propType
      ..propName = propName ?? this.propName
      ..packageId = packageId ?? this.packageId
      ..url = url ?? this.url
      ..isExist = isExist ?? this.isExist
      ..num = num ?? this.num
      ..propNo = propNo ?? this.propNo
      ..dressNo = dressNo ?? this.dressNo
      ..isFavorites = isFavorites ?? this.isFavorites
      ..dressColorList = dressColorList ?? this.dressColorList
      ..description = description ?? this.description;
  }
}

PropDressColorEntity $PropDressColorEntityFromJson(Map<String, dynamic> json) {
  final PropDressColorEntity propDressColorEntity = PropDressColorEntity();
  final int? colorId = jsonConvert.convert<int>(json['colorId']);
  if (colorId != null) {
    propDressColorEntity.colorId = colorId;
  }
  final String? colorNo = jsonConvert.convert<String>(json['colorNo']);
  if (colorNo != null) {
    propDressColorEntity.colorNo = colorNo;
  }
  final String? propNo = jsonConvert.convert<String>(json['propNo']);
  if (propNo != null) {
    propDressColorEntity.propNo = propNo;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    propDressColorEntity.createDate = createDate;
  }
  final int? depletionNo = jsonConvert.convert<int>(json['depletionNo']);
  if (depletionNo != null) {
    propDressColorEntity.depletionNo = depletionNo;
  }
  final int? isExist = jsonConvert.convert<int>(json['isExist']);
  if (isExist != null) {
    propDressColorEntity.isExist = isExist;
  }
  final int? isUse = jsonConvert.convert<int>(json['isUse']);
  if (isUse != null) {
    propDressColorEntity.isUse = isUse;
  }
  final String? dressNo = jsonConvert.convert<String>(json['dressNo']);
  if (dressNo != null) {
    propDressColorEntity.dressNo = dressNo;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    propDressColorEntity.remark = remark;
  }
  return propDressColorEntity;
}

Map<String, dynamic> $PropDressColorEntityToJson(PropDressColorEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['colorId'] = entity.colorId;
  data['colorNo'] = entity.colorNo;
  data['propNo'] = entity.propNo;
  data['createDate'] = entity.createDate;
  data['depletionNo'] = entity.depletionNo;
  data['isExist'] = entity.isExist;
  data['isUse'] = entity.isUse;
  data['dressNo'] = entity.dressNo;
  data['remark'] = entity.remark;
  return data;
}

extension PropDressColorEntityExtension on PropDressColorEntity {
  PropDressColorEntity copyWith({
    int? colorId,
    String? colorNo,
    String? propNo,
    String? createDate,
    int? depletionNo,
    int? isExist,
    int? isUse,
    String? dressNo,
    String? remark,
  }) {
    return PropDressColorEntity()
      ..colorId = colorId ?? this.colorId
      ..colorNo = colorNo ?? this.colorNo
      ..propNo = propNo ?? this.propNo
      ..createDate = createDate ?? this.createDate
      ..depletionNo = depletionNo ?? this.depletionNo
      ..isExist = isExist ?? this.isExist
      ..isUse = isUse ?? this.isUse
      ..dressNo = dressNo ?? this.dressNo
      ..remark = remark ?? this.remark;
  }
}