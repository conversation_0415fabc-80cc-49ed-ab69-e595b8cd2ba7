import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/small_room_mail_list_item_entity.dart';

SmallRoomMailListItemEntity $SmallRoomMailListItemEntityFromJson(
    Map<String, dynamic> json) {
  final SmallRoomMailListItemEntity smallRoomMailListItemEntity = SmallRoomMailListItemEntity();
  final String? roomMsgId = jsonConvert.convert<String>(json['roomMsgId']);
  if (roomMsgId != null) {
    smallRoomMailListItemEntity.roomMsgId = roomMsgId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    smallRoomMailListItemEntity.userId = userId;
  }
  final int? dadaRoomId = jsonConvert.convert<int>(json['dadaRoomId']);
  if (dadaRoomId != null) {
    smallRoomMailListItemEntity.dadaRoomId = dadaRoomId;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    smallRoomMailListItemEntity.type = type;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    smallRoomMailListItemEntity.content = content;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    smallRoomMailListItemEntity.createdDate = createdDate;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    smallRoomMailListItemEntity.nickname = nickname;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    smallRoomMailListItemEntity.avatar = avatar;
  }
  final int? state = jsonConvert.convert<int>(json['state']);
  if (state != null) {
    smallRoomMailListItemEntity.state = state;
  }
  final int? friendType = jsonConvert.convert<int>(json['friendType']);
  if (friendType != null) {
    smallRoomMailListItemEntity.friendType = friendType;
  }
  return smallRoomMailListItemEntity;
}

Map<String, dynamic> $SmallRoomMailListItemEntityToJson(
    SmallRoomMailListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['roomMsgId'] = entity.roomMsgId;
  data['userId'] = entity.userId;
  data['dadaRoomId'] = entity.dadaRoomId;
  data['type'] = entity.type;
  data['content'] = entity.content;
  data['createdDate'] = entity.createdDate;
  data['nickname'] = entity.nickname;
  data['avatar'] = entity.avatar;
  data['state'] = entity.state;
  data['friendType'] = entity.friendType;
  return data;
}

extension SmallRoomMailListItemEntityExtension on SmallRoomMailListItemEntity {
  SmallRoomMailListItemEntity copyWith({
    String? roomMsgId,
    String? userId,
    int? dadaRoomId,
    String? type,
    String? content,
    String? createdDate,
    String? nickname,
    String? avatar,
    int? state,
    int? friendType,
  }) {
    return SmallRoomMailListItemEntity()
      ..roomMsgId = roomMsgId ?? this.roomMsgId
      ..userId = userId ?? this.userId
      ..dadaRoomId = dadaRoomId ?? this.dadaRoomId
      ..type = type ?? this.type
      ..content = content ?? this.content
      ..createdDate = createdDate ?? this.createdDate
      ..nickname = nickname ?? this.nickname
      ..avatar = avatar ?? this.avatar
      ..state = state ?? this.state
      ..friendType = friendType ?? this.friendType;
  }
}