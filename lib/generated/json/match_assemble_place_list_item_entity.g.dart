import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/match_assemble_place_list_item_entity.dart';

MatchAssemblePlaceListItemEntity $MatchAssemblePlaceListItemEntityFromJson(
    Map<String, dynamic> json) {
  final MatchAssemblePlaceListItemEntity matchAssemblePlaceListItemEntity = MatchAssemblePlaceListItemEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    matchAssemblePlaceListItemEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    matchAssemblePlaceListItemEntity.name = name;
  }
  final List<dynamic>? labels = (json['labels'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (labels != null) {
    matchAssemblePlaceListItemEntity.labels = labels;
  }
  final bool? locked = jsonConvert.convert<bool>(json['locked']);
  if (locked != null) {
    matchAssemblePlaceListItemEntity.locked = locked;
  }
  return matchAssemblePlaceListItemEntity;
}

Map<String, dynamic> $MatchAssemblePlaceListItemEntityToJson(
    MatchAssemblePlaceListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['labels'] = entity.labels;
  data['locked'] = entity.locked;
  return data;
}

extension MatchAssemblePlaceListItemEntityExtension on MatchAssemblePlaceListItemEntity {
  MatchAssemblePlaceListItemEntity copyWith({
    String? id,
    String? name,
    List<dynamic>? labels,
    bool? locked,
  }) {
    return MatchAssemblePlaceListItemEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..labels = labels ?? this.labels
      ..locked = locked ?? this.locked;
  }
}