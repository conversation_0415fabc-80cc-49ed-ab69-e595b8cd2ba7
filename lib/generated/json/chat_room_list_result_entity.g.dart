import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_room_list_result_entity.dart';
import 'package:dada/model/chat_room_info_entity.dart';


ChatRoomListResultEntity $ChatRoomListResultEntityFromJson(
    Map<String, dynamic> json) {
  final ChatRoomListResultEntity chatRoomListResultEntity = ChatRoomListResultEntity();
  final int? isSearchResult = jsonConvert.convert<int>(json['isSearchResult']);
  if (isSearchResult != null) {
    chatRoomListResultEntity.isSearchResult = isSearchResult;
  }
  final List<ChatRoomInfoEntity>? roomVoLists = (json['roomVoLists'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ChatRoomInfoEntity>(e) as ChatRoomInfoEntity)
      .toList();
  if (roomVoLists != null) {
    chatRoomListResultEntity.roomVoLists = roomVoLists;
  }
  return chatRoomListResultEntity;
}

Map<String, dynamic> $ChatRoomListResultEntityToJson(
    ChatRoomListResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['isSearchResult'] = entity.isSearchResult;
  data['roomVoLists'] = entity.roomVoLists?.map((v) => v.toJson()).toList();
  return data;
}

extension ChatRoomListResultEntityExtension on ChatRoomListResultEntity {
  ChatRoomListResultEntity copyWith({
    int? isSearchResult,
    List<ChatRoomInfoEntity>? roomVoLists,
  }) {
    return ChatRoomListResultEntity()
      ..isSearchResult = isSearchResult ?? this.isSearchResult
      ..roomVoLists = roomVoLists ?? this.roomVoLists;
  }
}