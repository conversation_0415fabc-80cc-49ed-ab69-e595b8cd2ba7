import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/match_team_result_entity.dart';
import 'package:dada/model/match_team_member_entity.dart';


MatchTeamResultEntity $MatchTeamResultEntityFromJson(
    Map<String, dynamic> json) {
  final MatchTeamResultEntity matchTeamResultEntity = MatchTeamResultEntity();
  final String? teamId = jsonConvert.convert<String>(json['teamId']);
  if (teamId != null) {
    matchTeamResultEntity.teamId = teamId;
  }
  final String? teamName = jsonConvert.convert<String>(json['teamName']);
  if (teamName != null) {
    matchTeamResultEntity.teamName = teamName;
  }
  final String? teamNo = jsonConvert.convert<String>(json['teamNo']);
  if (teamNo != null) {
    matchTeamResultEntity.teamNo = teamNo;
  }
  final List<String>? teamLabels = (json['teamLabels'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (teamLabels != null) {
    matchTeamResultEntity.teamLabels = teamLabels;
  }
  final int? teamLockState = jsonConvert.convert<int>(json['teamLockState']);
  if (teamLockState != null) {
    matchTeamResultEntity.teamLockState = teamLockState;
  }
  final int? teamNum = jsonConvert.convert<int>(json['teamNum']);
  if (teamNum != null) {
    matchTeamResultEntity.teamNum = teamNum;
  }
  final int? teamTotalNum = jsonConvert.convert<int>(json['teamTotalNum']);
  if (teamTotalNum != null) {
    matchTeamResultEntity.teamTotalNum = teamTotalNum;
  }
  final String? teamPassword = jsonConvert.convert<String>(
      json['teamPassword']);
  if (teamPassword != null) {
    matchTeamResultEntity.teamPassword = teamPassword;
  }
  final int? teamMatchState = jsonConvert.convert<int>(json['teamMatchState']);
  if (teamMatchState != null) {
    matchTeamResultEntity.teamMatchState = teamMatchState;
  }
  final String? teamImg = jsonConvert.convert<String>(json['teamImg']);
  if (teamImg != null) {
    matchTeamResultEntity.teamImg = teamImg;
  }
  final List<MatchTeamMemberEntity>? teamMembers = (json['teamMembers'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<MatchTeamMemberEntity>(e) as MatchTeamMemberEntity)
      .toList();
  if (teamMembers != null) {
    matchTeamResultEntity.teamMembers = teamMembers;
  }
  return matchTeamResultEntity;
}

Map<String, dynamic> $MatchTeamResultEntityToJson(
    MatchTeamResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['teamId'] = entity.teamId;
  data['teamName'] = entity.teamName;
  data['teamNo'] = entity.teamNo;
  data['teamLabels'] = entity.teamLabels;
  data['teamLockState'] = entity.teamLockState;
  data['teamNum'] = entity.teamNum;
  data['teamTotalNum'] = entity.teamTotalNum;
  data['teamPassword'] = entity.teamPassword;
  data['teamMatchState'] = entity.teamMatchState;
  data['teamImg'] = entity.teamImg;
  data['teamMembers'] = entity.teamMembers?.map((v) => v.toJson()).toList();
  return data;
}

extension MatchTeamResultEntityExtension on MatchTeamResultEntity {
  MatchTeamResultEntity copyWith({
    String? teamId,
    String? teamName,
    String? teamNo,
    List<String>? teamLabels,
    int? teamLockState,
    int? teamNum,
    int? teamTotalNum,
    String? teamPassword,
    int? teamMatchState,
    String? teamImg,
    List<MatchTeamMemberEntity>? teamMembers,
  }) {
    return MatchTeamResultEntity()
      ..teamId = teamId ?? this.teamId
      ..teamName = teamName ?? this.teamName
      ..teamNo = teamNo ?? this.teamNo
      ..teamLabels = teamLabels ?? this.teamLabels
      ..teamLockState = teamLockState ?? this.teamLockState
      ..teamNum = teamNum ?? this.teamNum
      ..teamTotalNum = teamTotalNum ?? this.teamTotalNum
      ..teamPassword = teamPassword ?? this.teamPassword
      ..teamMatchState = teamMatchState ?? this.teamMatchState
      ..teamImg = teamImg ?? this.teamImg
      ..teamMembers = teamMembers ?? this.teamMembers;
  }
}