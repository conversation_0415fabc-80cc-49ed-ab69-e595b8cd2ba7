import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/user_avatar_entity.dart';

UserAvatarEntity $UserAvatarEntityFromJson(Map<String, dynamic> json) {
  final UserAvatarEntity userAvatarEntity = UserAvatarEntity();
  final String? avatarId = jsonConvert.convert<String>(json['avatarId']);
  if (avatarId != null) {
    userAvatarEntity.avatarId = avatarId;
  }
  final String? expireTime = jsonConvert.convert<String>(json['expireTime']);
  if (expireTime != null) {
    userAvatarEntity.expireTime = expireTime;
  }
  final int? expireDay = jsonConvert.convert<int>(json['expireDay']);
  if (expireDay != null) {
    userAvatarEntity.expireDay = expireDay;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    userAvatarEntity.createdDate = createdDate;
  }
  final int? isUse = jsonConvert.convert<int>(json['isUse']);
  if (isUse != null) {
    userAvatarEntity.isUse = isUse;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    userAvatarEntity.url = url;
  }
  final String? avatarName = jsonConvert.convert<String>(json['avatarName']);
  if (avatarName != null) {
    userAvatarEntity.avatarName = avatarName;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    userAvatarEntity.type = type;
  }
  final int? useTime = jsonConvert.convert<int>(json['useTime']);
  if (useTime != null) {
    userAvatarEntity.useTime = useTime;
  }
  final int? price = jsonConvert.convert<int>(json['price']);
  if (price != null) {
    userAvatarEntity.price = price;
  }
  final int? priceType = jsonConvert.convert<int>(json['priceType']);
  if (priceType != null) {
    userAvatarEntity.priceType = priceType;
  }
  return userAvatarEntity;
}

Map<String, dynamic> $UserAvatarEntityToJson(UserAvatarEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['avatarId'] = entity.avatarId;
  data['expireTime'] = entity.expireTime;
  data['expireDay'] = entity.expireDay;
  data['createdDate'] = entity.createdDate;
  data['isUse'] = entity.isUse;
  data['url'] = entity.url;
  data['avatarName'] = entity.avatarName;
  data['type'] = entity.type;
  data['useTime'] = entity.useTime;
  data['price'] = entity.price;
  data['priceType'] = entity.priceType;
  return data;
}

extension UserAvatarEntityExtension on UserAvatarEntity {
  UserAvatarEntity copyWith({
    String? avatarId,
    String? expireTime,
    int? expireDay,
    String? createdDate,
    int? isUse,
    String? url,
    String? avatarName,
    int? type,
    int? useTime,
    int? price,
    int? priceType,
  }) {
    return UserAvatarEntity()
      ..avatarId = avatarId ?? this.avatarId
      ..expireTime = expireTime ?? this.expireTime
      ..expireDay = expireDay ?? this.expireDay
      ..createdDate = createdDate ?? this.createdDate
      ..isUse = isUse ?? this.isUse
      ..url = url ?? this.url
      ..avatarName = avatarName ?? this.avatarName
      ..type = type ?? this.type
      ..useTime = useTime ?? this.useTime
      ..price = price ?? this.price
      ..priceType = priceType ?? this.priceType;
  }
}