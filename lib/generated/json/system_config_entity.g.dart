import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/system_config_entity.dart';

SystemConfigEntity $SystemConfigEntityFromJson(Map<String, dynamic> json) {
  final SystemConfigEntity systemConfigEntity = SystemConfigEntity();
  final String? isShowCdk = jsonConvert.convert<String>(json['isShowCdk']);
  if (isShowCdk != null) {
    systemConfigEntity.isShowCdk = isShowCdk;
  }
  final String? worldChatLimit = jsonConvert.convert<String>(
      json['worldChatLimit']);
  if (worldChatLimit != null) {
    systemConfigEntity.worldChatLimit = worldChatLimit;
  }
  return systemConfigEntity;
}

Map<String, dynamic> $SystemConfigEntityToJson(SystemConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['isShowCdk'] = entity.isShowCdk;
  data['worldChatLimit'] = entity.worldChatLimit;
  return data;
}

extension SystemConfigEntityExtension on SystemConfigEntity {
  SystemConfigEntity copyWith({
    String? isShowCdk,
    String? worldChatLimit,
  }) {
    return SystemConfigEntity()
      ..isShowCdk = isShowCdk ?? this.isShowCdk
      ..worldChatLimit = worldChatLimit ?? this.worldChatLimit;
  }
}