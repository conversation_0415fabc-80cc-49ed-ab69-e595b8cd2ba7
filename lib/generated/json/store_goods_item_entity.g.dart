import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/store_goods_item_entity.dart';

StoreGoodsItemEntity $StoreGoodsItemEntityFromJson(Map<String, dynamic> json) {
  final StoreGoodsItemEntity storeGoodsItemEntity = StoreGoodsItemEntity();
  final String? shopId = jsonConvert.convert<String>(json['shopId']);
  if (shopId != null) {
    storeGoodsItemEntity.shopId = shopId;
  }
  final String? propId = jsonConvert.convert<String>(json['propId']);
  if (propId != null) {
    storeGoodsItemEntity.propId = propId;
  }
  final String? number = jsonConvert.convert<String>(json['number']);
  if (number != null) {
    storeGoodsItemEntity.number = number;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    storeGoodsItemEntity.type = type;
  }
  final int? price = jsonConvert.convert<int>(json['price']);
  if (price != null) {
    storeGoodsItemEntity.price = price;
  }
  final int? limitNo = jsonConvert.convert<int>(json['limitNo']);
  if (limitNo != null) {
    storeGoodsItemEntity.limitNo = limitNo;
  }
  final String? propName = jsonConvert.convert<String>(json['propName']);
  if (propName != null) {
    storeGoodsItemEntity.propName = propName;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    storeGoodsItemEntity.url = url;
  }
  final int? propType = jsonConvert.convert<int>(json['propType']);
  if (propType != null) {
    storeGoodsItemEntity.propType = propType;
  }
  final int? isHave = jsonConvert.convert<int>(json['isHave']);
  if (isHave != null) {
    storeGoodsItemEntity.isHave = isHave;
  }
  return storeGoodsItemEntity;
}

Map<String, dynamic> $StoreGoodsItemEntityToJson(StoreGoodsItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['shopId'] = entity.shopId;
  data['propId'] = entity.propId;
  data['number'] = entity.number;
  data['type'] = entity.type;
  data['price'] = entity.price;
  data['limitNo'] = entity.limitNo;
  data['propName'] = entity.propName;
  data['url'] = entity.url;
  data['propType'] = entity.propType;
  data['isHave'] = entity.isHave;
  return data;
}

extension StoreGoodsItemEntityExtension on StoreGoodsItemEntity {
  StoreGoodsItemEntity copyWith({
    String? shopId,
    String? propId,
    String? number,
    int? type,
    int? price,
    int? limitNo,
    String? propName,
    String? url,
    int? propType,
    int? isHave,
  }) {
    return StoreGoodsItemEntity()
      ..shopId = shopId ?? this.shopId
      ..propId = propId ?? this.propId
      ..number = number ?? this.number
      ..type = type ?? this.type
      ..price = price ?? this.price
      ..limitNo = limitNo ?? this.limitNo
      ..propName = propName ?? this.propName
      ..url = url ?? this.url
      ..propType = propType ?? this.propType
      ..isHave = isHave ?? this.isHave;
  }
}