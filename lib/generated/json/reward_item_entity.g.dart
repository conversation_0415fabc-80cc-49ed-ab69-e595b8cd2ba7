import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/reward_item_entity.dart';

RewardItemEntity $RewardItemEntityFromJson(Map<String, dynamic> json) {
  final RewardItemEntity rewardItemEntity = RewardItemEntity();
  final String? prizeId = jsonConvert.convert<String>(json['prizeId']);
  if (prizeId != null) {
    rewardItemEntity.prizeId = prizeId;
  }
  final String? imageUrl = jsonConvert.convert<String>(json['imageUrl']);
  if (imageUrl != null) {
    rewardItemEntity.imageUrl = imageUrl;
  }
  final String? prizeName = jsonConvert.convert<String>(json['prizeName']);
  if (prizeName != null) {
    rewardItemEntity.prizeName = prizeName;
  }
  final int? number = jsonConvert.convert<int>(json['number']);
  if (number != null) {
    rewardItemEntity.number = number;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    rewardItemEntity.price = price;
  }
  return rewardItemEntity;
}

Map<String, dynamic> $RewardItemEntityToJson(RewardItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['prizeId'] = entity.prizeId;
  data['imageUrl'] = entity.imageUrl;
  data['prizeName'] = entity.prizeName;
  data['number'] = entity.number;
  data['price'] = entity.price;
  return data;
}

extension RewardItemEntityExtension on RewardItemEntity {
  RewardItemEntity copyWith({
    String? prizeId,
    String? imageUrl,
    String? prizeName,
    int? number,
    String? price,
  }) {
    return RewardItemEntity()
      ..prizeId = prizeId ?? this.prizeId
      ..imageUrl = imageUrl ?? this.imageUrl
      ..prizeName = prizeName ?? this.prizeName
      ..number = number ?? this.number
      ..price = price ?? this.price;
  }
}