import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/trtc_custom_msg_entity.dart';

TRTCCustomMsgEntity $TRTCCustomMsgEntityFromJson(Map<String, dynamic> json) {
  final TRTCCustomMsgEntity tRTCCustomMsgEntity = TRTCCustomMsgEntity();
  final String? event = jsonConvert.convert<String>(json['event']);
  if (event != null) {
    tRTCCustomMsgEntity.event = event;
  }
  final dynamic data = json['data'];
  if (data != null) {
    tRTCCustomMsgEntity.data = data;
  }
  return tRTCCustomMsgEntity;
}

Map<String, dynamic> $TRTCCustomMsgEntityToJson(TRTCCustomMsgEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['event'] = entity.event;
  data['data'] = entity.data;
  return data;
}

extension TRTCCustomMsgEntityExtension on TRTCCustomMsgEntity {
  TRTCCustomMsgEntity copyWith({
    String? event,
    dynamic data,
  }) {
    return TRTCCustomMsgEntity()
      ..event = event ?? this.event
      ..data = data ?? this.data;
  }
}