import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';

ChatImCustomMsgEntity $ChatImCustomMsgEntityFromJson(
    Map<String, dynamic> json) {
  final ChatImCustomMsgEntity chatImCustomMsgEntity = ChatImCustomMsgEntity();
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    chatImCustomMsgEntity.type = type;
  }
  final dynamic data = json['data'];
  if (data != null) {
    chatImCustomMsgEntity.data = data;
  }
  return chatImCustomMsgEntity;
}

Map<String, dynamic> $ChatImCustomMsgEntityToJson(
    ChatImCustomMsgEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['type'] = entity.type;
  data['data'] = entity.data;
  return data;
}

extension ChatImCustomMsgEntityExtension on ChatImCustomMsgEntity {
  ChatImCustomMsgEntity copyWith({
    String? type,
    dynamic data,
  }) {
    return ChatImCustomMsgEntity()
      ..type = type ?? this.type
      ..data = data ?? this.data;
  }
}