import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/global_notice_entity.dart';

GlobalNoticeEntity $GlobalNoticeEntityFromJson(Map<String, dynamic> json) {
  final GlobalNoticeEntity globalNoticeEntity = GlobalNoticeEntity();
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    globalNoticeEntity.msg = msg;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    globalNoticeEntity.avatar = avatar;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    globalNoticeEntity.nickname = nickname;
  }
  final int? time = jsonConvert.convert<int>(json['time']);
  if (time != null) {
    globalNoticeEntity.time = time;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    globalNoticeEntity.type = type;
  }
  return globalNoticeEntity;
}

Map<String, dynamic> $GlobalNoticeEntityToJson(GlobalNoticeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['msg'] = entity.msg;
  data['avatar'] = entity.avatar;
  data['nickname'] = entity.nickname;
  data['time'] = entity.time;
  data['type'] = entity.type;
  return data;
}

extension GlobalNoticeEntityExtension on GlobalNoticeEntity {
  GlobalNoticeEntity copyWith({
    String? msg,
    String? avatar,
    String? nickname,
    int? time,
    int? type,
  }) {
    return GlobalNoticeEntity()
      ..msg = msg ?? this.msg
      ..avatar = avatar ?? this.avatar
      ..nickname = nickname ?? this.nickname
      ..time = time ?? this.time
      ..type = type ?? this.type;
  }
}