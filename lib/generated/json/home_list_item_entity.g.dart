import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/home_list_item_entity.dart';

HomeListItemEntity $HomeListItemEntityFromJson(Map<String, dynamic> json) {
  final HomeListItemEntity homeListItemEntity = HomeListItemEntity();
  final String? groupID = jsonConvert.convert<String>(json['groupID']);
  if (groupID != null) {
    homeListItemEntity.groupID = groupID;
  }
  final String? groupName = jsonConvert.convert<String>(json['groupName']);
  if (groupName != null) {
    homeListItemEntity.groupName = groupName;
  }
  final String? groupDesc = jsonConvert.convert<String>(json['groupDesc']);
  if (groupDesc != null) {
    homeListItemEntity.groupDesc = groupDesc;
  }
  final String? groupNotice = jsonConvert.convert<String>(json['groupNotice']);
  if (groupNotice != null) {
    homeListItemEntity.groupNotice = groupNotice;
  }
  return homeListItemEntity;
}

Map<String, dynamic> $HomeListItemEntityToJson(HomeListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['groupID'] = entity.groupID;
  data['groupName'] = entity.groupName;
  data['groupDesc'] = entity.groupDesc;
  data['groupNotice'] = entity.groupNotice;
  return data;
}

extension HomeListItemEntityExtension on HomeListItemEntity {
  HomeListItemEntity copyWith({
    String? groupID,
    String? groupName,
    String? groupDesc,
    String? groupNotice,
  }) {
    return HomeListItemEntity()
      ..groupID = groupID ?? this.groupID
      ..groupName = groupName ?? this.groupName
      ..groupDesc = groupDesc ?? this.groupDesc
      ..groupNotice = groupNotice ?? this.groupNotice;
  }
}