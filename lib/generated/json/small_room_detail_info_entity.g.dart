import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/small_room_detail_info_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';

import 'package:dada/model/small_room_barrage_item_entity.dart';

import 'package:dada/model/small_room_bubble_word_entity.dart';


SmallRoomDetailInfoEntity $SmallRoomDetailInfoEntityFromJson(
    Map<String, dynamic> json) {
  final SmallRoomDetailInfoEntity smallRoomDetailInfoEntity = SmallRoomDetailInfoEntity();
  final List<
      FriendUserInfoEntity>? beginKnowList = (json['beginKnowList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (beginKnowList != null) {
    smallRoomDetailInfoEntity.beginKnowList = beginKnowList;
  }
  final List<FriendUserInfoEntity>? dadaList = (json['dadaList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (dadaList != null) {
    smallRoomDetailInfoEntity.dadaList = dadaList;
  }
  final List<SmallRoomBarrageItemEntity>? msgList = (json['msgList'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<SmallRoomBarrageItemEntity>(
          e) as SmallRoomBarrageItemEntity).toList();
  if (msgList != null) {
    smallRoomDetailInfoEntity.msgList = msgList;
  }
  final SmallRoomInfoRoom? room = jsonConvert.convert<SmallRoomInfoRoom>(
      json['room']);
  if (room != null) {
    smallRoomDetailInfoEntity.room = room;
  }
  final String? imageVo = jsonConvert.convert<String>(json['imageVo']);
  if (imageVo != null) {
    smallRoomDetailInfoEntity.imageVo = imageVo;
  }
  final int? noReadNum = jsonConvert.convert<int>(json['noReadNum']);
  if (noReadNum != null) {
    smallRoomDetailInfoEntity.noReadNum = noReadNum;
  }
  final List<
      SmallRoomBubbleWordEntity>? bubbleDTOList = (json['bubbleDTOList'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<SmallRoomBubbleWordEntity>(
          e) as SmallRoomBubbleWordEntity).toList();
  if (bubbleDTOList != null) {
    smallRoomDetailInfoEntity.bubbleDTOList = bubbleDTOList;
  }
  return smallRoomDetailInfoEntity;
}

Map<String, dynamic> $SmallRoomDetailInfoEntityToJson(
    SmallRoomDetailInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['beginKnowList'] = entity.beginKnowList?.map((v) => v.toJson()).toList();
  data['dadaList'] = entity.dadaList?.map((v) => v.toJson()).toList();
  data['msgList'] = entity.msgList?.map((v) => v.toJson()).toList();
  data['room'] = entity.room?.toJson();
  data['imageVo'] = entity.imageVo;
  data['noReadNum'] = entity.noReadNum;
  data['bubbleDTOList'] = entity.bubbleDTOList?.map((v) => v.toJson()).toList();
  return data;
}

extension SmallRoomDetailInfoEntityExtension on SmallRoomDetailInfoEntity {
  SmallRoomDetailInfoEntity copyWith({
    List<FriendUserInfoEntity>? beginKnowList,
    List<FriendUserInfoEntity>? dadaList,
    List<SmallRoomBarrageItemEntity>? msgList,
    SmallRoomInfoRoom? room,
    String? imageVo,
    int? noReadNum,
    List<SmallRoomBubbleWordEntity>? bubbleDTOList,
  }) {
    return SmallRoomDetailInfoEntity()
      ..beginKnowList = beginKnowList ?? this.beginKnowList
      ..dadaList = dadaList ?? this.dadaList
      ..msgList = msgList ?? this.msgList
      ..room = room ?? this.room
      ..imageVo = imageVo ?? this.imageVo
      ..noReadNum = noReadNum ?? this.noReadNum
      ..bubbleDTOList = bubbleDTOList ?? this.bubbleDTOList;
  }
}

SmallRoomInfoRoom $SmallRoomInfoRoomFromJson(Map<String, dynamic> json) {
  final SmallRoomInfoRoom smallRoomInfoRoom = SmallRoomInfoRoom();
  final String? dadaRoomId = jsonConvert.convert<String>(json['dadaRoomId']);
  if (dadaRoomId != null) {
    smallRoomInfoRoom.dadaRoomId = dadaRoomId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    smallRoomInfoRoom.userId = userId;
  }
  final String? computerContent = jsonConvert.convert<String>(
      json['computerContent']);
  if (computerContent != null) {
    smallRoomInfoRoom.computerContent = computerContent;
  }
  final String? musicContent = jsonConvert.convert<String>(
      json['musicContent']);
  if (musicContent != null) {
    smallRoomInfoRoom.musicContent = musicContent;
  }
  final String? bookContent = jsonConvert.convert<String>(json['bookContent']);
  if (bookContent != null) {
    smallRoomInfoRoom.bookContent = bookContent;
  }
  final int? roomLifeState = jsonConvert.convert<int>(json['roomLifeState']);
  if (roomLifeState != null) {
    smallRoomInfoRoom.roomLifeState = roomLifeState;
  }
  return smallRoomInfoRoom;
}

Map<String, dynamic> $SmallRoomInfoRoomToJson(SmallRoomInfoRoom entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['dadaRoomId'] = entity.dadaRoomId;
  data['userId'] = entity.userId;
  data['computerContent'] = entity.computerContent;
  data['musicContent'] = entity.musicContent;
  data['bookContent'] = entity.bookContent;
  data['roomLifeState'] = entity.roomLifeState;
  return data;
}

extension SmallRoomInfoRoomExtension on SmallRoomInfoRoom {
  SmallRoomInfoRoom copyWith({
    String? dadaRoomId,
    String? userId,
    String? computerContent,
    String? musicContent,
    String? bookContent,
    int? roomLifeState,
  }) {
    return SmallRoomInfoRoom()
      ..dadaRoomId = dadaRoomId ?? this.dadaRoomId
      ..userId = userId ?? this.userId
      ..computerContent = computerContent ?? this.computerContent
      ..musicContent = musicContent ?? this.musicContent
      ..bookContent = bookContent ?? this.bookContent
      ..roomLifeState = roomLifeState ?? this.roomLifeState;
  }
}