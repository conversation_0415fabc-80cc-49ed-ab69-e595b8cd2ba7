import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/contact_group_list_item_entity.dart';
import 'package:dada/model/chat_group_info_entity.dart';

import 'package:dada/model/friend_group_entity.dart';

import 'package:dada/model/friend_user_info_entity.dart';


ContactGroupListItemEntity $ContactGroupListItemEntityFromJson(
    Map<String, dynamic> json) {
  final ContactGroupListItemEntity contactGroupListItemEntity = ContactGroupListItemEntity();
  final String? groupName = jsonConvert.convert<String>(json['groupName']);
  if (groupName != null) {
    contactGroupListItemEntity.groupName = groupName;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    contactGroupListItemEntity.id = id;
  }
  final int? groupType = jsonConvert.convert<int>(json['listType']);
  if (groupType != null) {
    contactGroupListItemEntity.groupType = groupType;
  }
  final FriendGroupEntity? friendGroupInfo = jsonConvert.convert<
      FriendGroupEntity>(json['userGroupFriendListVo']);
  if (friendGroupInfo != null) {
    contactGroupListItemEntity.friendGroupInfo = friendGroupInfo;
  }
  final List<FriendUserInfoEntity>? daziList = (json['daziList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (daziList != null) {
    contactGroupListItemEntity.daziList = daziList;
  }
  final List<ChatGroupInfoEntity>? groupsList = (json['crowdChatsDTOS'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ChatGroupInfoEntity>(e) as ChatGroupInfoEntity)
      .toList();
  if (groupsList != null) {
    contactGroupListItemEntity.groupsList = groupsList;
  }
  final List<FriendUserInfoEntity>? blacklist = (json['userBlacklist'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (blacklist != null) {
    contactGroupListItemEntity.blacklist = blacklist;
  }
  final List<
      FriendUserInfoEntity>? newFriendsList = (json['newFriendList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (newFriendsList != null) {
    contactGroupListItemEntity.newFriendsList = newFriendsList;
  }
  final bool? unfold = jsonConvert.convert<bool>(json['unfold']);
  if (unfold != null) {
    contactGroupListItemEntity.unfold = unfold;
  }
  return contactGroupListItemEntity;
}

Map<String, dynamic> $ContactGroupListItemEntityToJson(
    ContactGroupListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['groupName'] = entity.groupName;
  data['id'] = entity.id;
  data['listType'] = entity.groupType;
  data['userGroupFriendListVo'] = entity.friendGroupInfo?.toJson();
  data['daziList'] = entity.daziList?.map((v) => v.toJson()).toList();
  data['crowdChatsDTOS'] = entity.groupsList?.map((v) => v.toJson()).toList();
  data['userBlacklist'] = entity.blacklist?.map((v) => v.toJson()).toList();
  data['newFriendList'] =
      entity.newFriendsList?.map((v) => v.toJson()).toList();
  data['unfold'] = entity.unfold;
  return data;
}

extension ContactGroupListItemEntityExtension on ContactGroupListItemEntity {
  ContactGroupListItemEntity copyWith({
    String? groupName,
    String? id,
    int? groupType,
    FriendGroupEntity? friendGroupInfo,
    List<FriendUserInfoEntity>? daziList,
    List<ChatGroupInfoEntity>? groupsList,
    List<FriendUserInfoEntity>? blacklist,
    List<FriendUserInfoEntity>? newFriendsList,
    bool? unfold,
  }) {
    return ContactGroupListItemEntity()
      ..groupName = groupName ?? this.groupName
      ..id = id ?? this.id
      ..groupType = groupType ?? this.groupType
      ..friendGroupInfo = friendGroupInfo ?? this.friendGroupInfo
      ..daziList = daziList ?? this.daziList
      ..groupsList = groupsList ?? this.groupsList
      ..blacklist = blacklist ?? this.blacklist
      ..newFriendsList = newFriendsList ?? this.newFriendsList
      ..unfold = unfold ?? this.unfold;
  }
}