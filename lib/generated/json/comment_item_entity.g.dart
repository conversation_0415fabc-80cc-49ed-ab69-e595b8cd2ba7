import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/comment_item_entity.dart';
import 'package:dada/model/comment_reply_list_entity.dart';


CommentItemEntity $CommentItemEntityFromJson(Map<String, dynamic> json) {
  final CommentItemEntity commentItemEntity = CommentItemEntity();
  final String? commentsId = jsonConvert.convert<String>(json['commentsId']);
  if (commentsId != null) {
    commentItemEntity.commentsId = commentsId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    commentItemEntity.userId = userId;
  }
  final String? postId = jsonConvert.convert<String>(json['postId']);
  if (postId != null) {
    commentItemEntity.postId = postId;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    commentItemEntity.content = content;
  }
  final bool? isAuthor = jsonConvert.convert<bool>(json['isAuthor']);
  if (isAuthor != null) {
    commentItemEntity.isAuthor = isAuthor;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    commentItemEntity.createdDate = createdDate;
  }
  final String? commentsType = jsonConvert.convert<String>(
      json['commentsType']);
  if (commentsType != null) {
    commentItemEntity.commentsType = commentsType;
  }
  final int? likeNo = jsonConvert.convert<int>(json['likeNo']);
  if (likeNo != null) {
    commentItemEntity.likeNo = likeNo;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    commentItemEntity.nickname = nickname;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    commentItemEntity.avatar = avatar;
  }
  final int? replyNo = jsonConvert.convert<int>(json['replyNo']);
  if (replyNo != null) {
    commentItemEntity.replyNo = replyNo;
  }
  final bool? likeState = jsonConvert.convert<bool>(json['likeState']);
  if (likeState != null) {
    commentItemEntity.likeState = likeState;
  }
  final CommentReplyListEntity? replayList = jsonConvert.convert<
      CommentReplyListEntity>(json['replayList']);
  if (replayList != null) {
    commentItemEntity.replayList = replayList;
  }
  return commentItemEntity;
}

Map<String, dynamic> $CommentItemEntityToJson(CommentItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['commentsId'] = entity.commentsId;
  data['userId'] = entity.userId;
  data['postId'] = entity.postId;
  data['content'] = entity.content;
  data['isAuthor'] = entity.isAuthor;
  data['createdDate'] = entity.createdDate;
  data['commentsType'] = entity.commentsType;
  data['likeNo'] = entity.likeNo;
  data['nickname'] = entity.nickname;
  data['avatar'] = entity.avatar;
  data['replyNo'] = entity.replyNo;
  data['likeState'] = entity.likeState;
  data['replayList'] = entity.replayList?.toJson();
  return data;
}

extension CommentItemEntityExtension on CommentItemEntity {
  CommentItemEntity copyWith({
    String? commentsId,
    String? userId,
    String? postId,
    String? content,
    bool? isAuthor,
    String? createdDate,
    String? commentsType,
    int? likeNo,
    String? nickname,
    String? avatar,
    int? replyNo,
    bool? likeState,
    CommentReplyListEntity? replayList,
  }) {
    return CommentItemEntity()
      ..commentsId = commentsId ?? this.commentsId
      ..userId = userId ?? this.userId
      ..postId = postId ?? this.postId
      ..content = content ?? this.content
      ..isAuthor = isAuthor ?? this.isAuthor
      ..createdDate = createdDate ?? this.createdDate
      ..commentsType = commentsType ?? this.commentsType
      ..likeNo = likeNo ?? this.likeNo
      ..nickname = nickname ?? this.nickname
      ..avatar = avatar ?? this.avatar
      ..replyNo = replyNo ?? this.replyNo
      ..likeState = likeState ?? this.likeState
      ..replayList = replayList ?? this.replayList;
  }
}