import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/comment_list_entity.dart';
import 'package:dada/model/comment_item_entity.dart';


CommentListEntity $CommentListEntityFromJson(Map<String, dynamic> json) {
  final CommentListEntity commentListEntity = CommentListEntity();
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    commentListEntity.total = total;
  }
  final List<CommentItemEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<CommentItemEntity>(e) as CommentItemEntity)
      .toList();
  if (list != null) {
    commentListEntity.list = list;
  }
  return commentListEntity;
}

Map<String, dynamic> $CommentListEntityToJson(CommentListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CommentListEntityExtension on CommentListEntity {
  CommentListEntity copyWith({
    int? total,
    List<CommentItemEntity>? list,
  }) {
    return CommentListEntity()
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}