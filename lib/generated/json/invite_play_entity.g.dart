import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/invite_play_entity.dart';

InvitePlayEntity $InvitePlayEntityFromJson(Map<String, dynamic> json) {
  final InvitePlayEntity invitePlayEntity = InvitePlayEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    invitePlayEntity.id = id;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    invitePlayEntity.userId = userId;
  }
  final String? text = jsonConvert.convert<String>(json['text']);
  if (text != null) {
    invitePlayEntity.text = text;
  }
  return invitePlayEntity;
}

Map<String, dynamic> $InvitePlayEntityToJson(InvitePlayEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['text'] = entity.text;
  return data;
}

extension InvitePlayEntityExtension on InvitePlayEntity {
  InvitePlayEntity copyWith({
    String? id,
    String? userId,
    String? text,
  }) {
    return InvitePlayEntity()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..text = text ?? this.text;
  }
}