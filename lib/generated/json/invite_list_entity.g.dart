import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/invite_list_entity.dart';

InviteListEntity $InviteListEntityFromJson(Map<String, dynamic> json) {
  final InviteListEntity inviteListEntity = InviteListEntity();
  final String? inviteCode = jsonConvert.convert<String>(json['inviteCode']);
  if (inviteCode != null) {
    inviteListEntity.inviteCode = inviteCode;
  }
  final int? inviteCount = jsonConvert.convert<int>(json['inviteCount']);
  if (inviteCount != null) {
    inviteListEntity.inviteCount = inviteCount;
  }
  final List<
      InviteListInviteRecordDetailsVos>? inviteRecordDetailsVos = (json['inviteRecordDetailsVos'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<InviteListInviteRecordDetailsVos>(
          e) as InviteListInviteRecordDetailsVos).toList();
  if (inviteRecordDetailsVos != null) {
    inviteListEntity.inviteRecordDetailsVos = inviteRecordDetailsVos;
  }
  return inviteListEntity;
}

Map<String, dynamic> $InviteListEntityToJson(InviteListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['inviteCode'] = entity.inviteCode;
  data['inviteCount'] = entity.inviteCount;
  data['inviteRecordDetailsVos'] =
      entity.inviteRecordDetailsVos?.map((v) => v.toJson()).toList();
  return data;
}

extension InviteListEntityExtension on InviteListEntity {
  InviteListEntity copyWith({
    String? inviteCode,
    int? inviteCount,
    List<InviteListInviteRecordDetailsVos>? inviteRecordDetailsVos,
  }) {
    return InviteListEntity()
      ..inviteCode = inviteCode ?? this.inviteCode
      ..inviteCount = inviteCount ?? this.inviteCount
      ..inviteRecordDetailsVos = inviteRecordDetailsVos ??
          this.inviteRecordDetailsVos;
  }
}

InviteListInviteRecordDetailsVos $InviteListInviteRecordDetailsVosFromJson(
    Map<String, dynamic> json) {
  final InviteListInviteRecordDetailsVos inviteListInviteRecordDetailsVos = InviteListInviteRecordDetailsVos();
  final String? inviteDate = jsonConvert.convert<String>(json['inviteDate']);
  if (inviteDate != null) {
    inviteListInviteRecordDetailsVos.inviteDate = inviteDate;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    inviteListInviteRecordDetailsVos.nickname = nickname;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    inviteListInviteRecordDetailsVos.avatar = avatar;
  }
  final List<
      InviteRecordRewardItem>? inviteReward = (json['inviteReward'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<InviteRecordRewardItem>(e) as InviteRecordRewardItem)
      .toList();
  if (inviteReward != null) {
    inviteListInviteRecordDetailsVos.inviteReward = inviteReward;
  }
  return inviteListInviteRecordDetailsVos;
}

Map<String, dynamic> $InviteListInviteRecordDetailsVosToJson(
    InviteListInviteRecordDetailsVos entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['inviteDate'] = entity.inviteDate;
  data['nickname'] = entity.nickname;
  data['avatar'] = entity.avatar;
  data['inviteReward'] = entity.inviteReward?.map((v) => v.toJson()).toList();
  return data;
}

extension InviteListInviteRecordDetailsVosExtension on InviteListInviteRecordDetailsVos {
  InviteListInviteRecordDetailsVos copyWith({
    String? inviteDate,
    String? nickname,
    String? avatar,
    List<InviteRecordRewardItem>? inviteReward,
  }) {
    return InviteListInviteRecordDetailsVos()
      ..inviteDate = inviteDate ?? this.inviteDate
      ..nickname = nickname ?? this.nickname
      ..avatar = avatar ?? this.avatar
      ..inviteReward = inviteReward ?? this.inviteReward;
  }
}

InviteRecordRewardItem $InviteRecordRewardItemFromJson(
    Map<String, dynamic> json) {
  final InviteRecordRewardItem inviteRecordRewardItem = InviteRecordRewardItem();
  final String? rewardUrl = jsonConvert.convert<String>(json['rewardUrl']);
  if (rewardUrl != null) {
    inviteRecordRewardItem.rewardUrl = rewardUrl;
  }
  final int? rewardNum = jsonConvert.convert<int>(json['rewardNum']);
  if (rewardNum != null) {
    inviteRecordRewardItem.rewardNum = rewardNum;
  }
  final String? rewardName = jsonConvert.convert<String>(json['rewardName']);
  if (rewardName != null) {
    inviteRecordRewardItem.rewardName = rewardName;
  }
  return inviteRecordRewardItem;
}

Map<String, dynamic> $InviteRecordRewardItemToJson(
    InviteRecordRewardItem entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['rewardUrl'] = entity.rewardUrl;
  data['rewardNum'] = entity.rewardNum;
  data['rewardName'] = entity.rewardName;
  return data;
}

extension InviteRecordRewardItemExtension on InviteRecordRewardItem {
  InviteRecordRewardItem copyWith({
    String? rewardUrl,
    int? rewardNum,
    String? rewardName,
  }) {
    return InviteRecordRewardItem()
      ..rewardUrl = rewardUrl ?? this.rewardUrl
      ..rewardNum = rewardNum ?? this.rewardNum
      ..rewardName = rewardName ?? this.rewardName;
  }
}