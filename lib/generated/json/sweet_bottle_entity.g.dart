import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/sweet_bottle_entity.dart';

SweetBottleEntity $SweetBottleEntityFromJson(Map<String, dynamic> json) {
  final SweetBottleEntity sweetBottleEntity = SweetBottleEntity();
  final String? sweetBottleId = jsonConvert.convert<String>(
      json['sweetBottleId']);
  if (sweetBottleId != null) {
    sweetBottleEntity.sweetBottleId = sweetBottleId;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    sweetBottleEntity.url = url;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    sweetBottleEntity.content = content;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    sweetBottleEntity.userId = userId;
  }
  final String? daUserId = jsonConvert.convert<String>(json['daUserId']);
  if (daUserId != null) {
    sweetBottleEntity.daUserId = daUserId;
  }
  return sweetBottleEntity;
}

Map<String, dynamic> $SweetBottleEntityToJson(SweetBottleEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['sweetBottleId'] = entity.sweetBottleId;
  data['url'] = entity.url;
  data['content'] = entity.content;
  data['userId'] = entity.userId;
  data['daUserId'] = entity.daUserId;
  return data;
}

extension SweetBottleEntityExtension on SweetBottleEntity {
  SweetBottleEntity copyWith({
    String? sweetBottleId,
    String? url,
    String? content,
    String? userId,
    String? daUserId,
  }) {
    return SweetBottleEntity()
      ..sweetBottleId = sweetBottleId ?? this.sweetBottleId
      ..url = url ?? this.url
      ..content = content ?? this.content
      ..userId = userId ?? this.userId
      ..daUserId = daUserId ?? this.daUserId;
  }
}