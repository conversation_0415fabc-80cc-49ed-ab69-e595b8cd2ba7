import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_room_online_member_entity.dart';

ChatRoomOnlineMemberEntity $ChatRoomOnlineMemberEntityFromJson(
    Map<String, dynamic> json) {
  final ChatRoomOnlineMemberEntity chatRoomOnlineMemberEntity = ChatRoomOnlineMemberEntity();
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    chatRoomOnlineMemberEntity.userId = userId;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    chatRoomOnlineMemberEntity.nickname = nickname;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    chatRoomOnlineMemberEntity.avatar = avatar;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    chatRoomOnlineMemberEntity.sex = sex;
  }
  final int? age = jsonConvert.convert<int>(json['age']);
  if (age != null) {
    chatRoomOnlineMemberEntity.age = age;
  }
  final bool? onSeat = jsonConvert.convert<bool>(json['onSeat']);
  if (onSeat != null) {
    chatRoomOnlineMemberEntity.onSeat = onSeat;
  }
  final int? muteUtil = jsonConvert.convert<int>(json['muteUtil']);
  if (muteUtil != null) {
    chatRoomOnlineMemberEntity.muteUtil = muteUtil;
  }
  final bool? isMute = jsonConvert.convert<bool>(json['isMute']);
  if (isMute != null) {
    chatRoomOnlineMemberEntity.isMute = isMute;
  }
  final int? seatIndex = jsonConvert.convert<int>(json['seatIndex']);
  if (seatIndex != null) {
    chatRoomOnlineMemberEntity.seatIndex = seatIndex;
  }
  return chatRoomOnlineMemberEntity;
}

Map<String, dynamic> $ChatRoomOnlineMemberEntityToJson(
    ChatRoomOnlineMemberEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userId'] = entity.userId;
  data['nickname'] = entity.nickname;
  data['avatar'] = entity.avatar;
  data['sex'] = entity.sex;
  data['age'] = entity.age;
  data['onSeat'] = entity.onSeat;
  data['muteUtil'] = entity.muteUtil;
  data['isMute'] = entity.isMute;
  data['seatIndex'] = entity.seatIndex;
  return data;
}

extension ChatRoomOnlineMemberEntityExtension on ChatRoomOnlineMemberEntity {
  ChatRoomOnlineMemberEntity copyWith({
    String? userId,
    String? nickname,
    String? avatar,
    int? sex,
    int? age,
    bool? onSeat,
    int? muteUtil,
    bool? isMute,
    int? seatIndex,
  }) {
    return ChatRoomOnlineMemberEntity()
      ..userId = userId ?? this.userId
      ..nickname = nickname ?? this.nickname
      ..avatar = avatar ?? this.avatar
      ..sex = sex ?? this.sex
      ..age = age ?? this.age
      ..onSeat = onSeat ?? this.onSeat
      ..muteUtil = muteUtil ?? this.muteUtil
      ..isMute = isMute ?? this.isMute
      ..seatIndex = seatIndex ?? this.seatIndex;
  }
}