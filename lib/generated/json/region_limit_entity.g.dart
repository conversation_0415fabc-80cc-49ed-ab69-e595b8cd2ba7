import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/region_limit_entity.dart';

RegionLimitEntity $RegionLimitEntityFromJson(Map<String, dynamic> json) {
  final RegionLimitEntity regionLimitEntity = RegionLimitEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    regionLimitEntity.id = id;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    regionLimitEntity.userId = userId;
  }
  final String? region = jsonConvert.convert<String>(json['region']);
  if (region != null) {
    regionLimitEntity.region = region;
  }
  return regionLimitEntity;
}

Map<String, dynamic> $RegionLimitEntityToJson(RegionLimitEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['region'] = entity.region;
  return data;
}

extension RegionLimitEntityExtension on RegionLimitEntity {
  RegionLimitEntity copyWith({
    String? id,
    String? userId,
    String? region,
  }) {
    return RegionLimitEntity()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..region = region ?? this.region;
  }
}