import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/search_room_history_result_entity.dart';

SearchRoomHistoryResultEntity $SearchRoomHistoryResultEntityFromJson(
    Map<String, dynamic> json) {
  final SearchRoomHistoryResultEntity searchRoomHistoryResultEntity = SearchRoomHistoryResultEntity();
  final List<String>? searchHistoryList = (json['searchHistoryList'] as List<
      dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (searchHistoryList != null) {
    searchRoomHistoryResultEntity.searchHistoryList = searchHistoryList;
  }
  final List<String>? hotTopicList = (json['hotTopicList'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<String>(e) as String)
      .toList();
  if (hotTopicList != null) {
    searchRoomHistoryResultEntity.hotTopicList = hotTopicList;
  }
  final List<
      SearchRoomHistoryResultTopicBoosterList>? topicBoosterList = (json['topicBoosterList'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<SearchRoomHistoryResultTopicBoosterList>(
          e) as SearchRoomHistoryResultTopicBoosterList).toList();
  if (topicBoosterList != null) {
    searchRoomHistoryResultEntity.topicBoosterList = topicBoosterList;
  }
  return searchRoomHistoryResultEntity;
}

Map<String, dynamic> $SearchRoomHistoryResultEntityToJson(
    SearchRoomHistoryResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['searchHistoryList'] = entity.searchHistoryList;
  data['hotTopicList'] = entity.hotTopicList;
  data['topicBoosterList'] =
      entity.topicBoosterList?.map((v) => v.toJson()).toList();
  return data;
}

extension SearchRoomHistoryResultEntityExtension on SearchRoomHistoryResultEntity {
  SearchRoomHistoryResultEntity copyWith({
    List<String>? searchHistoryList,
    List<String>? hotTopicList,
    List<SearchRoomHistoryResultTopicBoosterList>? topicBoosterList,
  }) {
    return SearchRoomHistoryResultEntity()
      ..searchHistoryList = searchHistoryList ?? this.searchHistoryList
      ..hotTopicList = hotTopicList ?? this.hotTopicList
      ..topicBoosterList = topicBoosterList ?? this.topicBoosterList;
  }
}

SearchRoomHistoryResultTopicBoosterList $SearchRoomHistoryResultTopicBoosterListFromJson(
    Map<String, dynamic> json) {
  final SearchRoomHistoryResultTopicBoosterList searchRoomHistoryResultTopicBoosterList = SearchRoomHistoryResultTopicBoosterList();
  final String? topic = jsonConvert.convert<String>(json['topic']);
  if (topic != null) {
    searchRoomHistoryResultTopicBoosterList.topic = topic;
  }
  final int? roomCount = jsonConvert.convert<int>(json['roomCount']);
  if (roomCount != null) {
    searchRoomHistoryResultTopicBoosterList.roomCount = roomCount;
  }
  final int? topicCount = jsonConvert.convert<int>(json['topicCount']);
  if (topicCount != null) {
    searchRoomHistoryResultTopicBoosterList.topicCount = topicCount;
  }
  return searchRoomHistoryResultTopicBoosterList;
}

Map<String, dynamic> $SearchRoomHistoryResultTopicBoosterListToJson(
    SearchRoomHistoryResultTopicBoosterList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['topic'] = entity.topic;
  data['roomCount'] = entity.roomCount;
  data['topicCount'] = entity.topicCount;
  return data;
}

extension SearchRoomHistoryResultTopicBoosterListExtension on SearchRoomHistoryResultTopicBoosterList {
  SearchRoomHistoryResultTopicBoosterList copyWith({
    String? topic,
    int? roomCount,
    int? topicCount,
  }) {
    return SearchRoomHistoryResultTopicBoosterList()
      ..topic = topic ?? this.topic
      ..roomCount = roomCount ?? this.roomCount
      ..topicCount = topicCount ?? this.topicCount;
  }
}