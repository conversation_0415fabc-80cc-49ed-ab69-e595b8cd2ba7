import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/match_team_member_entity.dart';

MatchTeamMemberEntity $MatchTeamMemberEntityFromJson(
    Map<String, dynamic> json) {
  final MatchTeamMemberEntity matchTeamMemberEntity = MatchTeamMemberEntity();
  final String? teamMemberId = jsonConvert.convert<String>(
      json['teamMemberId']);
  if (teamMemberId != null) {
    matchTeamMemberEntity.teamMemberId = teamMemberId;
  }
  final String? teamId = jsonConvert.convert<String>(json['teamId']);
  if (teamId != null) {
    matchTeamMemberEntity.teamId = teamId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    matchTeamMemberEntity.userId = userId;
  }
  final int? role = jsonConvert.convert<int>(json['role']);
  if (role != null) {
    matchTeamMemberEntity.role = role;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    matchTeamMemberEntity.nickname = nickname;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    matchTeamMemberEntity.avatar = avatar;
  }
  return matchTeamMemberEntity;
}

Map<String, dynamic> $MatchTeamMemberEntityToJson(
    MatchTeamMemberEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['teamMemberId'] = entity.teamMemberId;
  data['teamId'] = entity.teamId;
  data['userId'] = entity.userId;
  data['role'] = entity.role;
  data['nickname'] = entity.nickname;
  data['avatar'] = entity.avatar;
  return data;
}

extension MatchTeamMemberEntityExtension on MatchTeamMemberEntity {
  MatchTeamMemberEntity copyWith({
    String? teamMemberId,
    String? teamId,
    String? userId,
    int? role,
    String? nickname,
    String? avatar,
  }) {
    return MatchTeamMemberEntity()
      ..teamMemberId = teamMemberId ?? this.teamMemberId
      ..teamId = teamId ?? this.teamId
      ..userId = userId ?? this.userId
      ..role = role ?? this.role
      ..nickname = nickname ?? this.nickname
      ..avatar = avatar ?? this.avatar;
  }
}