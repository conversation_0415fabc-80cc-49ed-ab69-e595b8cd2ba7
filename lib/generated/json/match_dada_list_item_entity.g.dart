import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/match_dada_list_item_entity.dart';
import 'package:dada/model/user_label_entity.dart';


MatchDadaListItemEntity $MatchDadaListItemEntityFromJson(
    Map<String, dynamic> json) {
  final MatchDadaListItemEntity matchDadaListItemEntity = MatchDadaListItemEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    matchDadaListItemEntity.id = id;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    matchDadaListItemEntity.nickname = nickname;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    matchDadaListItemEntity.sex = sex;
  }
  final String? voiceSignature = jsonConvert.convert<String>(
      json['voiceSignature']);
  if (voiceSignature != null) {
    matchDadaListItemEntity.voiceSignature = voiceSignature;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    matchDadaListItemEntity.avatar = avatar;
  }
  final String? dadaNo = jsonConvert.convert<String>(json['dadaNo']);
  if (dadaNo != null) {
    matchDadaListItemEntity.dadaNo = dadaNo;
  }
  final String? birthday = jsonConvert.convert<String>(json['birthday']);
  if (birthday != null) {
    matchDadaListItemEntity.birthday = birthday;
  }
  final String? txtSignature = jsonConvert.convert<String>(
      json['txtSignature']);
  if (txtSignature != null) {
    matchDadaListItemEntity.txtSignature = txtSignature;
  }
  final String? hometown = jsonConvert.convert<String>(json['hometown']);
  if (hometown != null) {
    matchDadaListItemEntity.hometown = hometown;
  }
  final String? work = jsonConvert.convert<String>(json['work']);
  if (work != null) {
    matchDadaListItemEntity.work = work;
  }
  final int? voiceLength = jsonConvert.convert<int>(json['voiceLength']);
  if (voiceLength != null) {
    matchDadaListItemEntity.voiceLength = voiceLength;
  }
  final List<UserLabelEntity>? labels = (json['labels'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<UserLabelEntity>(e) as UserLabelEntity)
      .toList();
  if (labels != null) {
    matchDadaListItemEntity.labels = labels;
  }
  final int? isAccept = jsonConvert.convert<int>(json['isAccept']);
  if (isAccept != null) {
    matchDadaListItemEntity.isAccept = isAccept;
  }
  final int? daState = jsonConvert.convert<int>(json['daState']);
  if (daState != null) {
    matchDadaListItemEntity.daState = daState;
  }
  final int? age = jsonConvert.convert<int>(json['age']);
  if (age != null) {
    matchDadaListItemEntity.age = age;
  }
  final bool? isFriend = jsonConvert.convert<bool>(json['isFriend']);
  if (isFriend != null) {
    matchDadaListItemEntity.isFriend = isFriend;
  }
  return matchDadaListItemEntity;
}

Map<String, dynamic> $MatchDadaListItemEntityToJson(
    MatchDadaListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['nickname'] = entity.nickname;
  data['sex'] = entity.sex;
  data['voiceSignature'] = entity.voiceSignature;
  data['avatar'] = entity.avatar;
  data['dadaNo'] = entity.dadaNo;
  data['birthday'] = entity.birthday;
  data['txtSignature'] = entity.txtSignature;
  data['hometown'] = entity.hometown;
  data['work'] = entity.work;
  data['voiceLength'] = entity.voiceLength;
  data['labels'] = entity.labels?.map((v) => v.toJson()).toList();
  data['isAccept'] = entity.isAccept;
  data['daState'] = entity.daState;
  data['age'] = entity.age;
  data['isFriend'] = entity.isFriend;
  return data;
}

extension MatchDadaListItemEntityExtension on MatchDadaListItemEntity {
  MatchDadaListItemEntity copyWith({
    String? id,
    String? nickname,
    int? sex,
    String? voiceSignature,
    String? avatar,
    String? dadaNo,
    String? birthday,
    String? txtSignature,
    String? hometown,
    String? work,
    int? voiceLength,
    List<UserLabelEntity>? labels,
    int? isAccept,
    int? daState,
    int? age,
    bool? isFriend,
  }) {
    return MatchDadaListItemEntity()
      ..id = id ?? this.id
      ..nickname = nickname ?? this.nickname
      ..sex = sex ?? this.sex
      ..voiceSignature = voiceSignature ?? this.voiceSignature
      ..avatar = avatar ?? this.avatar
      ..dadaNo = dadaNo ?? this.dadaNo
      ..birthday = birthday ?? this.birthday
      ..txtSignature = txtSignature ?? this.txtSignature
      ..hometown = hometown ?? this.hometown
      ..work = work ?? this.work
      ..voiceLength = voiceLength ?? this.voiceLength
      ..labels = labels ?? this.labels
      ..isAccept = isAccept ?? this.isAccept
      ..daState = daState ?? this.daState
      ..age = age ?? this.age
      ..isFriend = isFriend ?? this.isFriend;
  }
}