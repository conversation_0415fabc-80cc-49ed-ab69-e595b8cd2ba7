import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/dynamic_message_list_entity.dart';
import 'package:dada/model/dynamic_message_item_entity.dart';


DynamicMessageListEntity $DynamicMessageListEntityFromJson(
    Map<String, dynamic> json) {
  final DynamicMessageListEntity dynamicMessageListEntity = DynamicMessageListEntity();
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    dynamicMessageListEntity.total = total;
  }
  final List<DynamicMessageItemEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<DynamicMessageItemEntity>(
          e) as DynamicMessageItemEntity)
      .toList();
  if (list != null) {
    dynamicMessageListEntity.list = list;
  }
  return dynamicMessageListEntity;
}

Map<String, dynamic> $DynamicMessageListEntityToJson(
    DynamicMessageListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension DynamicMessageListEntityExtension on DynamicMessageListEntity {
  DynamicMessageListEntity copyWith({
    int? total,
    List<DynamicMessageItemEntity>? list,
  }) {
    return DynamicMessageListEntity()
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}