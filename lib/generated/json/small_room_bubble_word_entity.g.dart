import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';

SmallRoomBubbleWordEntity $SmallRoomBubbleWordEntityFromJson(
    Map<String, dynamic> json) {
  final SmallRoomBubbleWordEntity smallRoomBubbleWordEntity = SmallRoomBubbleWordEntity();
  final String? bubbleId = jsonConvert.convert<String>(json['bubbleId']);
  if (bubbleId != null) {
    smallRoomBubbleWordEntity.bubbleId = bubbleId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    smallRoomBubbleWordEntity.userId = userId;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    smallRoomBubbleWordEntity.content = content;
  }
  final String? dadaRoomId = jsonConvert.convert<String>(json['dadaRoomId']);
  if (dadaRoomId != null) {
    smallRoomBubbleWordEntity.dadaRoomId = dadaRoomId;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    smallRoomBubbleWordEntity.type = type;
  }
  final int? voiceLength = jsonConvert.convert<int>(json['voiceLength']);
  if (voiceLength != null) {
    smallRoomBubbleWordEntity.voiceLength = voiceLength;
  }
  return smallRoomBubbleWordEntity;
}

Map<String, dynamic> $SmallRoomBubbleWordEntityToJson(
    SmallRoomBubbleWordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bubbleId'] = entity.bubbleId;
  data['userId'] = entity.userId;
  data['content'] = entity.content;
  data['dadaRoomId'] = entity.dadaRoomId;
  data['type'] = entity.type;
  data['voiceLength'] = entity.voiceLength;
  return data;
}

extension SmallRoomBubbleWordEntityExtension on SmallRoomBubbleWordEntity {
  SmallRoomBubbleWordEntity copyWith({
    String? bubbleId,
    String? userId,
    String? content,
    String? dadaRoomId,
    int? type,
    int? voiceLength,
  }) {
    return SmallRoomBubbleWordEntity()
      ..bubbleId = bubbleId ?? this.bubbleId
      ..userId = userId ?? this.userId
      ..content = content ?? this.content
      ..dadaRoomId = dadaRoomId ?? this.dadaRoomId
      ..type = type ?? this.type
      ..voiceLength = voiceLength ?? this.voiceLength;
  }
}