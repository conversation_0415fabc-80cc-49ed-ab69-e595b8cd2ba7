import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/match_team_list_entity.dart';
import 'package:dada/model/match_team_result_entity.dart';


MatchTeamListEntity $MatchTeamListEntityFromJson(Map<String, dynamic> json) {
  final MatchTeamListEntity matchTeamListEntity = MatchTeamListEntity();
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    matchTeamListEntity.total = total;
  }
  final List<MatchTeamResultEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<MatchTeamResultEntity>(e) as MatchTeamResultEntity)
      .toList();
  if (list != null) {
    matchTeamListEntity.list = list;
  }
  return matchTeamListEntity;
}

Map<String, dynamic> $MatchTeamListEntityToJson(MatchTeamListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension MatchTeamListEntityExtension on MatchTeamListEntity {
  MatchTeamListEntity copyWith({
    int? total,
    List<MatchTeamResultEntity>? list,
  }) {
    return MatchTeamListEntity()
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}