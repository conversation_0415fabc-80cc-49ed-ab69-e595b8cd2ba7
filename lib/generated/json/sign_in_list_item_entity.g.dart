import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/sign_in_list_item_entity.dart';
import 'package:dada/model/reward_item_entity.dart';


SignInListItemEntity $SignInListItemEntityFromJson(Map<String, dynamic> json) {
  final SignInListItemEntity signInListItemEntity = SignInListItemEntity();
  final int? weekDay = jsonConvert.convert<int>(json['weekDay']);
  if (weekDay != null) {
    signInListItemEntity.weekDay = weekDay;
  }
  final int? isSign = jsonConvert.convert<int>(json['isSign']);
  if (isSign != null) {
    signInListItemEntity.isSign = isSign;
  }
  final List<RewardItemEntity>? prizesDTOList = (json['prizesDTOList'] as List<
      dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<RewardItemEntity>(e) as RewardItemEntity)
      .toList();
  if (prizesDTOList != null) {
    signInListItemEntity.prizesDTOList = prizesDTOList;
  }
  return signInListItemEntity;
}

Map<String, dynamic> $SignInListItemEntityToJson(SignInListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['weekDay'] = entity.weekDay;
  data['isSign'] = entity.isSign;
  data['prizesDTOList'] = entity.prizesDTOList?.map((v) => v.toJson()).toList();
  return data;
}

extension SignInListItemEntityExtension on SignInListItemEntity {
  SignInListItemEntity copyWith({
    int? weekDay,
    int? isSign,
    List<RewardItemEntity>? prizesDTOList,
  }) {
    return SignInListItemEntity()
      ..weekDay = weekDay ?? this.weekDay
      ..isSign = isSign ?? this.isSign
      ..prizesDTOList = prizesDTOList ?? this.prizesDTOList;
  }
}