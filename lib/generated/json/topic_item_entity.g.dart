import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/topic_item_entity.dart';

TopicItemEntity $TopicItemEntityFromJson(Map<String, dynamic> json) {
  final TopicItemEntity topicItemEntity = TopicItemEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    topicItemEntity.id = id;
  }
  final String? roomNo = jsonConvert.convert<String>(json['roomNo']);
  if (roomNo != null) {
    topicItemEntity.roomNo = roomNo;
  }
  final String? sort = jsonConvert.convert<String>(json['sort']);
  if (sort != null) {
    topicItemEntity.sort = sort;
  }
  final int? topicType = jsonConvert.convert<int>(json['topicType']);
  if (topicType != null) {
    topicItemEntity.topicType = topicType;
  }
  final String? topicText = jsonConvert.convert<String>(json['topicText']);
  if (topicText != null) {
    topicItemEntity.topicText = topicText;
  }
  return topicItemEntity;
}

Map<String, dynamic> $TopicItemEntityToJson(TopicItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['roomNo'] = entity.roomNo;
  data['sort'] = entity.sort;
  data['topicType'] = entity.topicType;
  data['topicText'] = entity.topicText;
  return data;
}

extension TopicItemEntityExtension on TopicItemEntity {
  TopicItemEntity copyWith({
    String? id,
    String? roomNo,
    String? sort,
    int? topicType,
    String? topicText,
  }) {
    return TopicItemEntity()
      ..id = id ?? this.id
      ..roomNo = roomNo ?? this.roomNo
      ..sort = sort ?? this.sort
      ..topicType = topicType ?? this.topicType
      ..topicText = topicText ?? this.topicText;
  }
}