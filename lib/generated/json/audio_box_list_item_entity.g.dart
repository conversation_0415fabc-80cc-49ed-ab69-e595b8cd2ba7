import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/audio_box_list_item_entity.dart';

AudioBoxListItemEntity $AudioBoxListItemEntityFromJson(
    Map<String, dynamic> json) {
  final AudioBoxListItemEntity audioBoxListItemEntity = AudioBoxListItemEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    audioBoxListItemEntity.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['remark']);
  if (title != null) {
    audioBoxListItemEntity.title = title;
  }
  final String? url = jsonConvert.convert<String>(json['videoUrl']);
  if (url != null) {
    audioBoxListItemEntity.url = url;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    audioBoxListItemEntity.createDate = createDate;
  }
  final String? username = jsonConvert.convert<String>(json['userNickname']);
  if (username != null) {
    audioBoxListItemEntity.username = username;
  }
  final int? duration = jsonConvert.convert<int>(json['voiceLength']);
  if (duration != null) {
    audioBoxListItemEntity.duration = duration;
  }
  return audioBoxListItemEntity;
}

Map<String, dynamic> $AudioBoxListItemEntityToJson(
    AudioBoxListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['remark'] = entity.title;
  data['videoUrl'] = entity.url;
  data['createDate'] = entity.createDate;
  data['userNickname'] = entity.username;
  data['voiceLength'] = entity.duration;
  return data;
}

extension AudioBoxListItemEntityExtension on AudioBoxListItemEntity {
  AudioBoxListItemEntity copyWith({
    String? id,
    String? title,
    String? url,
    String? createDate,
    String? username,
    int? duration,
  }) {
    return AudioBoxListItemEntity()
      ..id = id ?? this.id
      ..title = title ?? this.title
      ..url = url ?? this.url
      ..createDate = createDate ?? this.createDate
      ..username = username ?? this.username
      ..duration = duration ?? this.duration;
  }
}