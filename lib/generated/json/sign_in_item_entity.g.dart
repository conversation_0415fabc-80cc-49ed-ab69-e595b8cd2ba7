import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/sign_in_item_entity.dart';
import 'package:dada/model/sign_in_list_item_entity.dart';


SignInItemEntity $SignInItemEntityFromJson(Map<String, dynamic> json) {
  final SignInItemEntity signInItemEntity = SignInItemEntity();
  final List<SignInListItemEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<SignInListItemEntity>(e) as SignInListItemEntity)
      .toList();
  if (list != null) {
    signInItemEntity.list = list;
  }
  final bool? today = jsonConvert.convert<bool>(json['today']);
  if (today != null) {
    signInItemEntity.today = today;
  }
  final bool? isWeekSignOver = jsonConvert.convert<bool>(
      json['isWeekSignOver']);
  if (isWeekSignOver != null) {
    signInItemEntity.isWeekSignOver = isWeekSignOver;
  }
  return signInItemEntity;
}

Map<String, dynamic> $SignInItemEntityToJson(SignInItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  data['today'] = entity.today;
  data['isWeekSignOver'] = entity.isWeekSignOver;
  return data;
}

extension SignInItemEntityExtension on SignInItemEntity {
  SignInItemEntity copyWith({
    List<SignInListItemEntity>? list,
    bool? today,
    bool? isWeekSignOver,
  }) {
    return SignInItemEntity()
      ..list = list ?? this.list
      ..today = today ?? this.today
      ..isWeekSignOver = isWeekSignOver ?? this.isWeekSignOver;
  }
}