import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/pay_info.dart';

PayInfo $PayInfoFromJson(Map<String, dynamic> json) {
  final PayInfo payInfo = PayInfo();
  final String? applePayProductID = jsonConvert.convert<String>(
      json['applePayProductID']);
  if (applePayProductID != null) {
    payInfo.applePayProductID = applePayProductID;
  }
  final String? alipayOrderString = jsonConvert.convert<String>(
      json['orderStr']);
  if (alipayOrderString != null) {
    payInfo.alipayOrderString = alipayOrderString;
  }
  final String? wxPayPartnerId = jsonConvert.convert<String>(
      json['wxPayPartnerId']);
  if (wxPayPartnerId != null) {
    payInfo.wxPayPartnerId = wxPayPartnerId;
  }
  final String? wxPayPrepayId = jsonConvert.convert<String>(
      json['wxPayPrepayId']);
  if (wxPayPrepayId != null) {
    payInfo.wxPayPrepayId = wxPayPrepayId;
  }
  final String? wxPayNonceStr = jsonConvert.convert<String>(
      json['wxPayNonceStr']);
  if (wxPayNonceStr != null) {
    payInfo.wxPayNonceStr = wxPayNonceStr;
  }
  final String? wxPayTimeStamp = jsonConvert.convert<String>(
      json['wxPayTimeStamp']);
  if (wxPayTimeStamp != null) {
    payInfo.wxPayTimeStamp = wxPayTimeStamp;
  }
  final String? wxPaySign = jsonConvert.convert<String>(json['wxPaySign']);
  if (wxPaySign != null) {
    payInfo.wxPaySign = wxPaySign;
  }
  final String? orderCode = jsonConvert.convert<String>(json['orderCode']);
  if (orderCode != null) {
    payInfo.orderCode = orderCode;
  }
  return payInfo;
}

Map<String, dynamic> $PayInfoToJson(PayInfo entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['applePayProductID'] = entity.applePayProductID;
  data['orderStr'] = entity.alipayOrderString;
  data['wxPayPartnerId'] = entity.wxPayPartnerId;
  data['wxPayPrepayId'] = entity.wxPayPrepayId;
  data['wxPayNonceStr'] = entity.wxPayNonceStr;
  data['wxPayTimeStamp'] = entity.wxPayTimeStamp;
  data['wxPaySign'] = entity.wxPaySign;
  data['orderCode'] = entity.orderCode;
  return data;
}

extension PayInfoExtension on PayInfo {
  PayInfo copyWith({
    String? applePayProductID,
    String? alipayOrderString,
    String? wxPayPartnerId,
    String? wxPayPrepayId,
    String? wxPayNonceStr,
    String? wxPayTimeStamp,
    String? wxPaySign,
    String? orderCode,
  }) {
    return PayInfo()
      ..applePayProductID = applePayProductID ?? this.applePayProductID
      ..alipayOrderString = alipayOrderString ?? this.alipayOrderString
      ..wxPayPartnerId = wxPayPartnerId ?? this.wxPayPartnerId
      ..wxPayPrepayId = wxPayPrepayId ?? this.wxPayPrepayId
      ..wxPayNonceStr = wxPayNonceStr ?? this.wxPayNonceStr
      ..wxPayTimeStamp = wxPayTimeStamp ?? this.wxPayTimeStamp
      ..wxPaySign = wxPaySign ?? this.wxPaySign
      ..orderCode = orderCode ?? this.orderCode;
  }
}