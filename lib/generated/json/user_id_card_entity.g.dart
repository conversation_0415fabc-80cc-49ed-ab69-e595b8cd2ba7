import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/user_id_card_entity.dart';

UserIdCardEntity $UserIdCardEntityFromJson(Map<String, dynamic> json) {
  final UserIdCardEntity userIdCardEntity = UserIdCardEntity();
  final String? cardId = jsonConvert.convert<String>(json['cardId']);
  if (cardId != null) {
    userIdCardEntity.cardId = cardId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    userIdCardEntity.userId = userId;
  }
  final String? cardName = jsonConvert.convert<String>(json['cardName']);
  if (cardName != null) {
    userIdCardEntity.cardName = cardName;
  }
  final String? cardNickname = jsonConvert.convert<String>(
      json['cardNickname']);
  if (cardNickname != null) {
    userIdCardEntity.cardNickname = cardNickname;
  }
  final String? gameId = jsonConvert.convert<String>(json['gameId']);
  if (gameId != null) {
    userIdCardEntity.gameId = gameId;
  }
  final String? serverName = jsonConvert.convert<String>(json['serverName']);
  if (serverName != null) {
    userIdCardEntity.serverName = serverName;
  }
  final List<String>? cardText = (json['cardText'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (cardText != null) {
    userIdCardEntity.cardText = cardText;
  }
  final int? cardState = jsonConvert.convert<int>(json['cardState']);
  if (cardState != null) {
    userIdCardEntity.cardState = cardState;
  }
  final int? cardType = jsonConvert.convert<int>(json['cardType']);
  if (cardType != null) {
    userIdCardEntity.cardType = cardType;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    userIdCardEntity.createdDate = createdDate;
  }
  final bool? liked = jsonConvert.convert<bool>(json['liked']);
  if (liked != null) {
    userIdCardEntity.liked = liked;
  }
  final bool? known = jsonConvert.convert<bool>(json['known']);
  if (known != null) {
    userIdCardEntity.known = known;
  }
  final bool? read = jsonConvert.convert<bool>(json['read']);
  if (read != null) {
    userIdCardEntity.read = read;
  }
  final int? likeNo = jsonConvert.convert<int>(json['likeNo']);
  if (likeNo != null) {
    userIdCardEntity.likeNo = likeNo;
  }
  final int? readNo = jsonConvert.convert<int>(json['readNo']);
  if (readNo != null) {
    userIdCardEntity.readNo = readNo;
  }
  final int? knowNo = jsonConvert.convert<int>(json['knowNo']);
  if (knowNo != null) {
    userIdCardEntity.knowNo = knowNo;
  }
  final String? cardUrl = jsonConvert.convert<String>(json['cardUrl']);
  if (cardUrl != null) {
    userIdCardEntity.cardUrl = cardUrl;
  }
  return userIdCardEntity;
}

Map<String, dynamic> $UserIdCardEntityToJson(UserIdCardEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['cardId'] = entity.cardId;
  data['userId'] = entity.userId;
  data['cardName'] = entity.cardName;
  data['cardNickname'] = entity.cardNickname;
  data['gameId'] = entity.gameId;
  data['serverName'] = entity.serverName;
  data['cardText'] = entity.cardText;
  data['cardState'] = entity.cardState;
  data['cardType'] = entity.cardType;
  data['createdDate'] = entity.createdDate;
  data['liked'] = entity.liked;
  data['known'] = entity.known;
  data['read'] = entity.read;
  data['likeNo'] = entity.likeNo;
  data['readNo'] = entity.readNo;
  data['knowNo'] = entity.knowNo;
  data['cardUrl'] = entity.cardUrl;
  return data;
}

extension UserIdCardEntityExtension on UserIdCardEntity {
  UserIdCardEntity copyWith({
    String? cardId,
    String? userId,
    String? cardName,
    String? cardNickname,
    String? gameId,
    String? serverName,
    List<String>? cardText,
    int? cardState,
    int? cardType,
    String? createdDate,
    bool? liked,
    bool? known,
    bool? read,
    int? likeNo,
    int? readNo,
    int? knowNo,
    String? cardUrl,
  }) {
    return UserIdCardEntity()
      ..cardId = cardId ?? this.cardId
      ..userId = userId ?? this.userId
      ..cardName = cardName ?? this.cardName
      ..cardNickname = cardNickname ?? this.cardNickname
      ..gameId = gameId ?? this.gameId
      ..serverName = serverName ?? this.serverName
      ..cardText = cardText ?? this.cardText
      ..cardState = cardState ?? this.cardState
      ..cardType = cardType ?? this.cardType
      ..createdDate = createdDate ?? this.createdDate
      ..liked = liked ?? this.liked
      ..known = known ?? this.known
      ..read = read ?? this.read
      ..likeNo = likeNo ?? this.likeNo
      ..readNo = readNo ?? this.readNo
      ..knowNo = knowNo ?? this.knowNo
      ..cardUrl = cardUrl ?? this.cardUrl;
  }
}