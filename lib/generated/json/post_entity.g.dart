import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/post_entity.dart';

PostEntity $PostEntityFromJson(Map<String, dynamic> json) {
  final PostEntity postEntity = PostEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    postEntity.id = id;
  }
  final String? postId = jsonConvert.convert<String>(json['postId']);
  if (postId != null) {
    postEntity.postId = postId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    postEntity.userId = userId;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    postEntity.title = title;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    postEntity.content = content;
  }
  final int? state = jsonConvert.convert<int>(json['state']);
  if (state != null) {
    postEntity.state = state;
  }
  final int? isTop = jsonConvert.convert<int>(json['isTop']);
  if (isTop != null) {
    postEntity.isTop = isTop;
  }
  final int? forwardNo = jsonConvert.convert<int>(json['forwardNo']);
  if (forwardNo != null) {
    postEntity.forwardNo = forwardNo;
  }
  final String? topic = jsonConvert.convert<String>(json['topic']);
  if (topic != null) {
    postEntity.topic = topic;
  }
  final List<String>? imgUrls = (json['imgUrls'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (imgUrls != null) {
    postEntity.imgUrls = imgUrls;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    postEntity.createdDate = createdDate;
  }
  final int? postType = jsonConvert.convert<int>(json['postType']);
  if (postType != null) {
    postEntity.postType = postType;
  }
  final int? likeNo = jsonConvert.convert<int>(json['likeNo']);
  if (likeNo != null) {
    postEntity.likeNo = likeNo;
  }
  final int? commentNo = jsonConvert.convert<int>(json['commentNo']);
  if (commentNo != null) {
    postEntity.commentNo = commentNo;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    postEntity.nickname = nickname;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    postEntity.avatar = avatar;
  }
  final bool? likeState = jsonConvert.convert<bool>(json['likeState']);
  if (likeState != null) {
    postEntity.likeState = likeState;
  }
  final bool? isFriend = jsonConvert.convert<bool>(json['isFriend']);
  if (isFriend != null) {
    postEntity.isFriend = isFriend;
  }
  final bool? isChat = jsonConvert.convert<bool>(json['isChat']);
  if (isChat != null) {
    postEntity.isChat = isChat;
  }
  final String? resonateUserId = jsonConvert.convert<String>(
      json['resonateUserId']);
  if (resonateUserId != null) {
    postEntity.resonateUserId = resonateUserId;
  }
  return postEntity;
}

Map<String, dynamic> $PostEntityToJson(PostEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['postId'] = entity.postId;
  data['userId'] = entity.userId;
  data['title'] = entity.title;
  data['content'] = entity.content;
  data['state'] = entity.state;
  data['isTop'] = entity.isTop;
  data['forwardNo'] = entity.forwardNo;
  data['topic'] = entity.topic;
  data['imgUrls'] = entity.imgUrls;
  data['createdDate'] = entity.createdDate;
  data['postType'] = entity.postType;
  data['likeNo'] = entity.likeNo;
  data['commentNo'] = entity.commentNo;
  data['nickname'] = entity.nickname;
  data['avatar'] = entity.avatar;
  data['likeState'] = entity.likeState;
  data['isFriend'] = entity.isFriend;
  data['isChat'] = entity.isChat;
  data['resonateUserId'] = entity.resonateUserId;
  return data;
}

extension PostEntityExtension on PostEntity {
  PostEntity copyWith({
    String? id,
    String? postId,
    String? userId,
    String? title,
    String? content,
    int? state,
    int? isTop,
    int? forwardNo,
    String? topic,
    List<String>? imgUrls,
    String? createdDate,
    int? postType,
    int? likeNo,
    int? commentNo,
    String? nickname,
    String? avatar,
    bool? likeState,
    bool? isFriend,
    bool? isChat,
    String? resonateUserId,
  }) {
    return PostEntity()
      ..id = id ?? this.id
      ..postId = postId ?? this.postId
      ..userId = userId ?? this.userId
      ..title = title ?? this.title
      ..content = content ?? this.content
      ..state = state ?? this.state
      ..isTop = isTop ?? this.isTop
      ..forwardNo = forwardNo ?? this.forwardNo
      ..topic = topic ?? this.topic
      ..imgUrls = imgUrls ?? this.imgUrls
      ..createdDate = createdDate ?? this.createdDate
      ..postType = postType ?? this.postType
      ..likeNo = likeNo ?? this.likeNo
      ..commentNo = commentNo ?? this.commentNo
      ..nickname = nickname ?? this.nickname
      ..avatar = avatar ?? this.avatar
      ..likeState = likeState ?? this.likeState
      ..isFriend = isFriend ?? this.isFriend
      ..isChat = isChat ?? this.isChat
      ..resonateUserId = resonateUserId ?? this.resonateUserId;
  }
}