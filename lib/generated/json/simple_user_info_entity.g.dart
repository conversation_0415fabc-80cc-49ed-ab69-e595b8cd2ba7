import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/simple_user_info_entity.dart';

SimpleUserInfoEntity $SimpleUserInfoEntityFromJson(Map<String, dynamic> json) {
  final SimpleUserInfoEntity simpleUserInfoEntity = SimpleUserInfoEntity();
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    simpleUserInfoEntity.userId = userId;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    simpleUserInfoEntity.avatar = avatar;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    simpleUserInfoEntity.nickname = nickname;
  }
  return simpleUserInfoEntity;
}

Map<String, dynamic> $SimpleUserInfoEntityToJson(SimpleUserInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userId'] = entity.userId;
  data['avatar'] = entity.avatar;
  data['nickname'] = entity.nickname;
  return data;
}

extension SimpleUserInfoEntityExtension on SimpleUserInfoEntity {
  SimpleUserInfoEntity copyWith({
    String? userId,
    String? avatar,
    String? nickname,
  }) {
    return SimpleUserInfoEntity()
      ..userId = userId ?? this.userId
      ..avatar = avatar ?? this.avatar
      ..nickname = nickname ?? this.nickname;
  }
}