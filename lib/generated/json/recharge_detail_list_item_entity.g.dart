import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/recharge_detail_list_item_entity.dart';

RechargeDetailListItemEntity $RechargeDetailListItemEntityFromJson(
    Map<String, dynamic> json) {
  final RechargeDetailListItemEntity rechargeDetailListItemEntity = RechargeDetailListItemEntity();
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    rechargeDetailListItemEntity.createdDate = createdDate;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    rechargeDetailListItemEntity.content = content;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    rechargeDetailListItemEntity.type = type;
  }
  final int? billType = jsonConvert.convert<int>(json['billType']);
  if (billType != null) {
    rechargeDetailListItemEntity.billType = billType;
  }
  final int? billNo = jsonConvert.convert<int>(json['billNo']);
  if (billNo != null) {
    rechargeDetailListItemEntity.billNo = billNo;
  }
  return rechargeDetailListItemEntity;
}

Map<String, dynamic> $RechargeDetailListItemEntityToJson(
    RechargeDetailListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['createdDate'] = entity.createdDate;
  data['content'] = entity.content;
  data['type'] = entity.type;
  data['billType'] = entity.billType;
  data['billNo'] = entity.billNo;
  return data;
}

extension RechargeDetailListItemEntityExtension on RechargeDetailListItemEntity {
  RechargeDetailListItemEntity copyWith({
    String? createdDate,
    String? content,
    int? type,
    int? billType,
    int? billNo,
  }) {
    return RechargeDetailListItemEntity()
      ..createdDate = createdDate ?? this.createdDate
      ..content = content ?? this.content
      ..type = type ?? this.type
      ..billType = billType ?? this.billType
      ..billNo = billNo ?? this.billNo;
  }
}