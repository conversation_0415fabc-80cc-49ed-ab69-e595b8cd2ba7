import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';


FriendUserInfoEntity $FriendUserInfoEntityFromJson(Map<String, dynamic> json) {
  final FriendUserInfoEntity friendUserInfoEntity = FriendUserInfoEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    friendUserInfoEntity.id = id;
  }
  final String? userFriendId = jsonConvert.convert<String>(
      json['userFriendId']);
  if (userFriendId != null) {
    friendUserInfoEntity.userFriendId = userFriendId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    friendUserInfoEntity.userId = userId;
  }
  final String? userBlackId = jsonConvert.convert<String>(json['userBlackId']);
  if (userBlackId != null) {
    friendUserInfoEntity.userBlackId = userBlackId;
  }
  final String? daUserId = jsonConvert.convert<String>(json['daUserId']);
  if (daUserId != null) {
    friendUserInfoEntity.daUserId = daUserId;
  }
  final String? friendLevel = jsonConvert.convert<String>(json['friendLevel']);
  if (friendLevel != null) {
    friendUserInfoEntity.friendLevel = friendLevel;
  }
  final String? friendRemark = jsonConvert.convert<String>(
      json['friendRemark']);
  if (friendRemark != null) {
    friendUserInfoEntity.friendRemark = friendRemark;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    friendUserInfoEntity.nickname = nickname;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    friendUserInfoEntity.avatar = avatar;
  }
  final String? friendGroupId = jsonConvert.convert<String>(
      json['friendGroupId']);
  if (friendGroupId != null) {
    friendUserInfoEntity.friendGroupId = friendGroupId;
  }
  final String? friendGroupName = jsonConvert.convert<String>(
      json['friendGroupName']);
  if (friendGroupName != null) {
    friendUserInfoEntity.friendGroupName = friendGroupName;
  }
  final String? friendSubGroupName = jsonConvert.convert<String>(
      json['friendSubGroupName']);
  if (friendSubGroupName != null) {
    friendUserInfoEntity.friendSubGroupName = friendSubGroupName;
  }
  final String? userDescribes = jsonConvert.convert<String>(
      json['userDescribes']);
  if (userDescribes != null) {
    friendUserInfoEntity.userDescribes = userDescribes;
  }
  final int? describesFlag = jsonConvert.convert<int>(json['describesFlag']);
  if (describesFlag != null) {
    friendUserInfoEntity.describesFlag = describesFlag;
  }
  final int? voiceLength = jsonConvert.convert<int>(json['voiceLength']);
  if (voiceLength != null) {
    friendUserInfoEntity.voiceLength = voiceLength;
  }
  final int? friendType = jsonConvert.convert<int>(json['friendType']);
  if (friendType != null) {
    friendUserInfoEntity.friendType = friendType;
  }
  final String? senderId = jsonConvert.convert<String>(json['senderId']);
  if (senderId != null) {
    friendUserInfoEntity.senderId = senderId;
  }
  final String? applyMsg = jsonConvert.convert<String>(json['applyMsg']);
  if (applyMsg != null) {
    friendUserInfoEntity.applyMsg = applyMsg;
  }
  final String? daName = jsonConvert.convert<String>(json['daName']);
  if (daName != null) {
    friendUserInfoEntity.daName = daName;
  }
  final String? dadaNo = jsonConvert.convert<String>(json['dadaNo']);
  if (dadaNo != null) {
    friendUserInfoEntity.dadaNo = dadaNo;
  }
  final int? isFriend = jsonConvert.convert<int>(json['isFriend']);
  if (isFriend != null) {
    friendUserInfoEntity.isFriend = isFriend;
  }
  final int? daCircleLimit = jsonConvert.convert<int>(json['daCircleLimit']);
  if (daCircleLimit != null) {
    friendUserInfoEntity.daCircleLimit = daCircleLimit;
  }
  final int? isDisturb = jsonConvert.convert<int>(json['isDisturb']);
  if (isDisturb != null) {
    friendUserInfoEntity.isDisturb = isDisturb;
  }
  final int? lookMeState = jsonConvert.convert<int>(json['lookMeState']);
  if (lookMeState != null) {
    friendUserInfoEntity.lookMeState = lookMeState;
  }
  final int? lookFriendState = jsonConvert.convert<int>(
      json['lookFriendState']);
  if (lookFriendState != null) {
    friendUserInfoEntity.lookFriendState = lookFriendState;
  }
  final int? isBlack = jsonConvert.convert<int>(json['isBlack']);
  if (isBlack != null) {
    friendUserInfoEntity.isBlack = isBlack;
  }
  final List<
      SmallRoomBubbleWordEntity>? bubbleDTOList = (json['bubbleDTOList'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<SmallRoomBubbleWordEntity>(
          e) as SmallRoomBubbleWordEntity).toList();
  if (bubbleDTOList != null) {
    friendUserInfoEntity.bubbleDTOList = bubbleDTOList;
  }
  final String? knowId = jsonConvert.convert<String>(json['knowId']);
  if (knowId != null) {
    friendUserInfoEntity.knowId = knowId;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    friendUserInfoEntity.sex = sex;
  }
  final String? expreDate = jsonConvert.convert<String>(json['expreDate']);
  if (expreDate != null) {
    friendUserInfoEntity.expreDate = expreDate;
  }
  final String? userImageNo = jsonConvert.convert<String>(json['userImageNo']);
  if (userImageNo != null) {
    friendUserInfoEntity.userImageNo = userImageNo;
  }
  final String? currentDressNo = jsonConvert.convert<String>(
      json['currentDressNo']);
  if (currentDressNo != null) {
    friendUserInfoEntity.currentDressNo = currentDressNo;
  }
  final int? isPioneer = jsonConvert.convert<int>(json['isPioneer']);
  if (isPioneer != null) {
    friendUserInfoEntity.isPioneer = isPioneer;
  }
  final int? isInitUser = jsonConvert.convert<int>(json['isInitUser']);
  if (isInitUser != null) {
    friendUserInfoEntity.isInitUser = isInitUser;
  }
  final int? isOnline = jsonConvert.convert<int>(json['isOnline']);
  if (isOnline != null) {
    friendUserInfoEntity.isOnline = isOnline;
  }
  return friendUserInfoEntity;
}

Map<String, dynamic> $FriendUserInfoEntityToJson(FriendUserInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userFriendId'] = entity.userFriendId;
  data['userId'] = entity.userId;
  data['userBlackId'] = entity.userBlackId;
  data['daUserId'] = entity.daUserId;
  data['friendLevel'] = entity.friendLevel;
  data['friendRemark'] = entity.friendRemark;
  data['nickname'] = entity.nickname;
  data['avatar'] = entity.avatar;
  data['friendGroupId'] = entity.friendGroupId;
  data['friendGroupName'] = entity.friendGroupName;
  data['friendSubGroupName'] = entity.friendSubGroupName;
  data['userDescribes'] = entity.userDescribes;
  data['describesFlag'] = entity.describesFlag;
  data['voiceLength'] = entity.voiceLength;
  data['friendType'] = entity.friendType;
  data['senderId'] = entity.senderId;
  data['applyMsg'] = entity.applyMsg;
  data['daName'] = entity.daName;
  data['dadaNo'] = entity.dadaNo;
  data['isFriend'] = entity.isFriend;
  data['daCircleLimit'] = entity.daCircleLimit;
  data['isDisturb'] = entity.isDisturb;
  data['lookMeState'] = entity.lookMeState;
  data['lookFriendState'] = entity.lookFriendState;
  data['isBlack'] = entity.isBlack;
  data['bubbleDTOList'] = entity.bubbleDTOList?.map((v) => v.toJson()).toList();
  data['knowId'] = entity.knowId;
  data['sex'] = entity.sex;
  data['expreDate'] = entity.expreDate;
  data['userImageNo'] = entity.userImageNo;
  data['currentDressNo'] = entity.currentDressNo;
  data['isPioneer'] = entity.isPioneer;
  data['isInitUser'] = entity.isInitUser;
  data['isOnline'] = entity.isOnline;
  return data;
}

extension FriendUserInfoEntityExtension on FriendUserInfoEntity {
  FriendUserInfoEntity copyWith({
    String? id,
    String? userFriendId,
    String? userId,
    String? userBlackId,
    String? daUserId,
    String? friendLevel,
    String? friendRemark,
    String? nickname,
    String? avatar,
    String? friendGroupId,
    String? friendGroupName,
    String? friendSubGroupName,
    String? userDescribes,
    int? describesFlag,
    int? voiceLength,
    int? friendType,
    String? senderId,
    String? applyMsg,
    String? daName,
    String? dadaNo,
    int? isFriend,
    int? daCircleLimit,
    int? isDisturb,
    int? lookMeState,
    int? lookFriendState,
    int? isBlack,
    List<SmallRoomBubbleWordEntity>? bubbleDTOList,
    String? knowId,
    int? sex,
    String? expreDate,
    String? userImageNo,
    String? currentDressNo,
    int? isPioneer,
    int? isInitUser,
    int? isOnline,
  }) {
    return FriendUserInfoEntity()
      ..id = id ?? this.id
      ..userFriendId = userFriendId ?? this.userFriendId
      ..userId = userId ?? this.userId
      ..userBlackId = userBlackId ?? this.userBlackId
      ..daUserId = daUserId ?? this.daUserId
      ..friendLevel = friendLevel ?? this.friendLevel
      ..friendRemark = friendRemark ?? this.friendRemark
      ..nickname = nickname ?? this.nickname
      ..avatar = avatar ?? this.avatar
      ..friendGroupId = friendGroupId ?? this.friendGroupId
      ..friendGroupName = friendGroupName ?? this.friendGroupName
      ..friendSubGroupName = friendSubGroupName ?? this.friendSubGroupName
      ..userDescribes = userDescribes ?? this.userDescribes
      ..describesFlag = describesFlag ?? this.describesFlag
      ..voiceLength = voiceLength ?? this.voiceLength
      ..friendType = friendType ?? this.friendType
      ..senderId = senderId ?? this.senderId
      ..applyMsg = applyMsg ?? this.applyMsg
      ..daName = daName ?? this.daName
      ..dadaNo = dadaNo ?? this.dadaNo
      ..isFriend = isFriend ?? this.isFriend
      ..daCircleLimit = daCircleLimit ?? this.daCircleLimit
      ..isDisturb = isDisturb ?? this.isDisturb
      ..lookMeState = lookMeState ?? this.lookMeState
      ..lookFriendState = lookFriendState ?? this.lookFriendState
      ..isBlack = isBlack ?? this.isBlack
      ..bubbleDTOList = bubbleDTOList ?? this.bubbleDTOList
      ..knowId = knowId ?? this.knowId
      ..sex = sex ?? this.sex
      ..expreDate = expreDate ?? this.expreDate
      ..userImageNo = userImageNo ?? this.userImageNo
      ..currentDressNo = currentDressNo ?? this.currentDressNo
      ..isPioneer = isPioneer ?? this.isPioneer
      ..isInitUser = isInitUser ?? this.isInitUser
      ..isOnline = isOnline ?? this.isOnline;
  }
}