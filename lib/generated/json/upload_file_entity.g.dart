import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/upload_file_entity.dart';

UploadFileEntity $UploadFileEntityFromJson(Map<String, dynamic> json) {
  final UploadFileEntity uploadFileEntity = UploadFileEntity();
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    uploadFileEntity.url = url;
  }
  return uploadFileEntity;
}

Map<String, dynamic> $UploadFileEntityToJson(UploadFileEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['url'] = entity.url;
  return data;
}

extension UploadFileEntityExtension on UploadFileEntity {
  UploadFileEntity copyWith({
    String? url,
  }) {
    return UploadFileEntity()
      ..url = url ?? this.url;
  }
}