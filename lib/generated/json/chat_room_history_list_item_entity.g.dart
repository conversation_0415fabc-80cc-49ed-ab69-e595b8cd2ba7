import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_room_history_list_item_entity.dart';

ChatRoomHistoryListItemEntity $ChatRoomHistoryListItemEntityFromJson(
    Map<String, dynamic> json) {
  final ChatRoomHistoryListItemEntity chatRoomHistoryListItemEntity = ChatRoomHistoryListItemEntity();
  final String? msgId = jsonConvert.convert<String>(json['msgId']);
  if (msgId != null) {
    chatRoomHistoryListItemEntity.msgId = msgId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    chatRoomHistoryListItemEntity.userId = userId;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    chatRoomHistoryListItemEntity.nickname = nickname;
  }
  final int? seatIndex = jsonConvert.convert<int>(json['seatIndex']);
  if (seatIndex != null) {
    chatRoomHistoryListItemEntity.seatIndex = seatIndex;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    chatRoomHistoryListItemEntity.content = content;
  }
  final String? sendDate = jsonConvert.convert<String>(json['sendDate']);
  if (sendDate != null) {
    chatRoomHistoryListItemEntity.sendDate = sendDate;
  }
  return chatRoomHistoryListItemEntity;
}

Map<String, dynamic> $ChatRoomHistoryListItemEntityToJson(
    ChatRoomHistoryListItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['msgId'] = entity.msgId;
  data['userId'] = entity.userId;
  data['nickname'] = entity.nickname;
  data['seatIndex'] = entity.seatIndex;
  data['content'] = entity.content;
  data['sendDate'] = entity.sendDate;
  return data;
}

extension ChatRoomHistoryListItemEntityExtension on ChatRoomHistoryListItemEntity {
  ChatRoomHistoryListItemEntity copyWith({
    String? msgId,
    String? userId,
    String? nickname,
    int? seatIndex,
    String? content,
    String? sendDate,
  }) {
    return ChatRoomHistoryListItemEntity()
      ..msgId = msgId ?? this.msgId
      ..userId = userId ?? this.userId
      ..nickname = nickname ?? this.nickname
      ..seatIndex = seatIndex ?? this.seatIndex
      ..content = content ?? this.content
      ..sendDate = sendDate ?? this.sendDate;
  }
}