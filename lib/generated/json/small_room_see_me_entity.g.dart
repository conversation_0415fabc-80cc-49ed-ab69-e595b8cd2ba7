import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/small_room_see_me_entity.dart';

SmallRoomSeeMeEntity $SmallRoomSeeMeEntityFromJson(Map<String, dynamic> json) {
  final SmallRoomSeeMeEntity smallRoomSeeMeEntity = SmallRoomSeeMeEntity();
  final String? visitId = jsonConvert.convert<String>(json['visitId']);
  if (visitId != null) {
    smallRoomSeeMeEntity.visitId = visitId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    smallRoomSeeMeEntity.userId = userId;
  }
  final String? visitedId = jsonConvert.convert<String>(json['visitedId']);
  if (visitedId != null) {
    smallRoomSeeMeEntity.visitedId = visitedId;
  }
  final String? visitTime = jsonConvert.convert<String>(json['visitTime']);
  if (visitTime != null) {
    smallRoomSeeMeEntity.visitTime = visitTime;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    smallRoomSeeMeEntity.avatar = avatar;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    smallRoomSeeMeEntity.nickname = nickname;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    smallRoomSeeMeEntity.sex = sex;
  }
  final int? age = jsonConvert.convert<int>(json['age']);
  if (age != null) {
    smallRoomSeeMeEntity.age = age;
  }
  final int? isPioneer = jsonConvert.convert<int>(json['isPioneer']);
  if (isPioneer != null) {
    smallRoomSeeMeEntity.isPioneer = isPioneer;
  }
  final int? isInitUser = jsonConvert.convert<int>(json['isInitUser']);
  if (isInitUser != null) {
    smallRoomSeeMeEntity.isInitUser = isInitUser;
  }
  final int? isFireKeeper = jsonConvert.convert<int>(json['isFireKeeper']);
  if (isFireKeeper != null) {
    smallRoomSeeMeEntity.isFireKeeper = isFireKeeper;
  }
  return smallRoomSeeMeEntity;
}

Map<String, dynamic> $SmallRoomSeeMeEntityToJson(SmallRoomSeeMeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['visitId'] = entity.visitId;
  data['userId'] = entity.userId;
  data['visitedId'] = entity.visitedId;
  data['visitTime'] = entity.visitTime;
  data['avatar'] = entity.avatar;
  data['nickname'] = entity.nickname;
  data['sex'] = entity.sex;
  data['age'] = entity.age;
  data['isPioneer'] = entity.isPioneer;
  data['isInitUser'] = entity.isInitUser;
  data['isFireKeeper'] = entity.isFireKeeper;
  return data;
}

extension SmallRoomSeeMeEntityExtension on SmallRoomSeeMeEntity {
  SmallRoomSeeMeEntity copyWith({
    String? visitId,
    String? userId,
    String? visitedId,
    String? visitTime,
    String? avatar,
    String? nickname,
    int? sex,
    int? age,
    int? isPioneer,
    int? isInitUser,
    int? isFireKeeper,
  }) {
    return SmallRoomSeeMeEntity()
      ..visitId = visitId ?? this.visitId
      ..userId = userId ?? this.userId
      ..visitedId = visitedId ?? this.visitedId
      ..visitTime = visitTime ?? this.visitTime
      ..avatar = avatar ?? this.avatar
      ..nickname = nickname ?? this.nickname
      ..sex = sex ?? this.sex
      ..age = age ?? this.age
      ..isPioneer = isPioneer ?? this.isPioneer
      ..isInitUser = isInitUser ?? this.isInitUser
      ..isFireKeeper = isFireKeeper ?? this.isFireKeeper;
  }
}