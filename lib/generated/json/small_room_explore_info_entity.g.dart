import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/small_room_explore_info_entity.dart';
import 'package:dada/model/prop_entity.dart';


SmallRoomExploreInfoEntity $SmallRoomExploreInfoEntityFromJson(
    Map<String, dynamic> json) {
  final SmallRoomExploreInfoEntity smallRoomExploreInfoEntity = SmallRoomExploreInfoEntity();
  final String? eventName = jsonConvert.convert<String>(json['eventName']);
  if (eventName != null) {
    smallRoomExploreInfoEntity.eventName = eventName;
  }
  final String? eventDesc = jsonConvert.convert<String>(json['eventDesc']);
  if (eventDesc != null) {
    smallRoomExploreInfoEntity.eventDesc = eventDesc;
  }
  final String? exploreDate = jsonConvert.convert<String>(json['exploreDate']);
  if (exploreDate != null) {
    smallRoomExploreInfoEntity.exploreDate = exploreDate;
  }
  final int? state = jsonConvert.convert<int>(json['state']);
  if (state != null) {
    smallRoomExploreInfoEntity.state = state;
  }
  final SmallRoomExploreInfoExploreProp1? exploreProp1 = jsonConvert.convert<
      SmallRoomExploreInfoExploreProp1>(json['exploreProp1']);
  if (exploreProp1 != null) {
    smallRoomExploreInfoEntity.exploreProp1 = exploreProp1;
  }
  final SmallRoomExploreInfoExploreProp2? exploreProp2 = jsonConvert.convert<
      SmallRoomExploreInfoExploreProp2>(json['exploreProp2']);
  if (exploreProp2 != null) {
    smallRoomExploreInfoEntity.exploreProp2 = exploreProp2;
  }
  final List<PropEntity>? propList = (json['propList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<PropEntity>(e) as PropEntity).toList();
  if (propList != null) {
    smallRoomExploreInfoEntity.propList = propList;
  }
  return smallRoomExploreInfoEntity;
}

Map<String, dynamic> $SmallRoomExploreInfoEntityToJson(
    SmallRoomExploreInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['eventName'] = entity.eventName;
  data['eventDesc'] = entity.eventDesc;
  data['exploreDate'] = entity.exploreDate;
  data['state'] = entity.state;
  data['exploreProp1'] = entity.exploreProp1?.toJson();
  data['exploreProp2'] = entity.exploreProp2?.toJson();
  data['propList'] = entity.propList?.map((v) => v.toJson()).toList();
  return data;
}

extension SmallRoomExploreInfoEntityExtension on SmallRoomExploreInfoEntity {
  SmallRoomExploreInfoEntity copyWith({
    String? eventName,
    String? eventDesc,
    String? exploreDate,
    int? state,
    SmallRoomExploreInfoExploreProp1? exploreProp1,
    SmallRoomExploreInfoExploreProp2? exploreProp2,
    List<PropEntity>? propList,
  }) {
    return SmallRoomExploreInfoEntity()
      ..eventName = eventName ?? this.eventName
      ..eventDesc = eventDesc ?? this.eventDesc
      ..exploreDate = exploreDate ?? this.exploreDate
      ..state = state ?? this.state
      ..exploreProp1 = exploreProp1 ?? this.exploreProp1
      ..exploreProp2 = exploreProp2 ?? this.exploreProp2
      ..propList = propList ?? this.propList;
  }
}

SmallRoomExploreInfoExploreProp1 $SmallRoomExploreInfoExploreProp1FromJson(
    Map<String, dynamic> json) {
  final SmallRoomExploreInfoExploreProp1 smallRoomExploreInfoExploreProp1 = SmallRoomExploreInfoExploreProp1();
  final String? userExplorePropId = jsonConvert.convert<String>(
      json['userExplorePropId']);
  if (userExplorePropId != null) {
    smallRoomExploreInfoExploreProp1.userExplorePropId = userExplorePropId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    smallRoomExploreInfoExploreProp1.userId = userId;
  }
  final String? explorePropId = jsonConvert.convert<String>(
      json['explorePropId']);
  if (explorePropId != null) {
    smallRoomExploreInfoExploreProp1.explorePropId = explorePropId;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    smallRoomExploreInfoExploreProp1.createdDate = createdDate;
  }
  final String? explorePropName = jsonConvert.convert<String>(
      json['explorePropName']);
  if (explorePropName != null) {
    smallRoomExploreInfoExploreProp1.explorePropName = explorePropName;
  }
  final int? explorePropType = jsonConvert.convert<int>(
      json['explorePropType']);
  if (explorePropType != null) {
    smallRoomExploreInfoExploreProp1.explorePropType = explorePropType;
  }
  final String? img = jsonConvert.convert<String>(json['img']);
  if (img != null) {
    smallRoomExploreInfoExploreProp1.img = img;
  }
  final String? probability = jsonConvert.convert<String>(json['probability']);
  if (probability != null) {
    smallRoomExploreInfoExploreProp1.probability = probability;
  }
  final int? explorePropLevel = jsonConvert.convert<int>(
      json['explorePropLevel']);
  if (explorePropLevel != null) {
    smallRoomExploreInfoExploreProp1.explorePropLevel = explorePropLevel;
  }
  return smallRoomExploreInfoExploreProp1;
}

Map<String, dynamic> $SmallRoomExploreInfoExploreProp1ToJson(
    SmallRoomExploreInfoExploreProp1 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userExplorePropId'] = entity.userExplorePropId;
  data['userId'] = entity.userId;
  data['explorePropId'] = entity.explorePropId;
  data['createdDate'] = entity.createdDate;
  data['explorePropName'] = entity.explorePropName;
  data['explorePropType'] = entity.explorePropType;
  data['img'] = entity.img;
  data['probability'] = entity.probability;
  data['explorePropLevel'] = entity.explorePropLevel;
  return data;
}

extension SmallRoomExploreInfoExploreProp1Extension on SmallRoomExploreInfoExploreProp1 {
  SmallRoomExploreInfoExploreProp1 copyWith({
    String? userExplorePropId,
    String? userId,
    String? explorePropId,
    String? createdDate,
    String? explorePropName,
    int? explorePropType,
    String? img,
    String? probability,
    int? explorePropLevel,
  }) {
    return SmallRoomExploreInfoExploreProp1()
      ..userExplorePropId = userExplorePropId ?? this.userExplorePropId
      ..userId = userId ?? this.userId
      ..explorePropId = explorePropId ?? this.explorePropId
      ..createdDate = createdDate ?? this.createdDate
      ..explorePropName = explorePropName ?? this.explorePropName
      ..explorePropType = explorePropType ?? this.explorePropType
      ..img = img ?? this.img
      ..probability = probability ?? this.probability
      ..explorePropLevel = explorePropLevel ?? this.explorePropLevel;
  }
}

SmallRoomExploreInfoExploreProp2 $SmallRoomExploreInfoExploreProp2FromJson(
    Map<String, dynamic> json) {
  final SmallRoomExploreInfoExploreProp2 smallRoomExploreInfoExploreProp2 = SmallRoomExploreInfoExploreProp2();
  final String? userExplorePropId = jsonConvert.convert<String>(
      json['userExplorePropId']);
  if (userExplorePropId != null) {
    smallRoomExploreInfoExploreProp2.userExplorePropId = userExplorePropId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    smallRoomExploreInfoExploreProp2.userId = userId;
  }
  final String? explorePropId = jsonConvert.convert<String>(
      json['explorePropId']);
  if (explorePropId != null) {
    smallRoomExploreInfoExploreProp2.explorePropId = explorePropId;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    smallRoomExploreInfoExploreProp2.createdDate = createdDate;
  }
  final String? explorePropName = jsonConvert.convert<String>(
      json['explorePropName']);
  if (explorePropName != null) {
    smallRoomExploreInfoExploreProp2.explorePropName = explorePropName;
  }
  final int? explorePropType = jsonConvert.convert<int>(
      json['explorePropType']);
  if (explorePropType != null) {
    smallRoomExploreInfoExploreProp2.explorePropType = explorePropType;
  }
  final String? img = jsonConvert.convert<String>(json['img']);
  if (img != null) {
    smallRoomExploreInfoExploreProp2.img = img;
  }
  final String? probability = jsonConvert.convert<String>(json['probability']);
  if (probability != null) {
    smallRoomExploreInfoExploreProp2.probability = probability;
  }
  final int? explorePropLevel = jsonConvert.convert<int>(
      json['explorePropLevel']);
  if (explorePropLevel != null) {
    smallRoomExploreInfoExploreProp2.explorePropLevel = explorePropLevel;
  }
  return smallRoomExploreInfoExploreProp2;
}

Map<String, dynamic> $SmallRoomExploreInfoExploreProp2ToJson(
    SmallRoomExploreInfoExploreProp2 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userExplorePropId'] = entity.userExplorePropId;
  data['userId'] = entity.userId;
  data['explorePropId'] = entity.explorePropId;
  data['createdDate'] = entity.createdDate;
  data['explorePropName'] = entity.explorePropName;
  data['explorePropType'] = entity.explorePropType;
  data['img'] = entity.img;
  data['probability'] = entity.probability;
  data['explorePropLevel'] = entity.explorePropLevel;
  return data;
}

extension SmallRoomExploreInfoExploreProp2Extension on SmallRoomExploreInfoExploreProp2 {
  SmallRoomExploreInfoExploreProp2 copyWith({
    String? userExplorePropId,
    String? userId,
    String? explorePropId,
    String? createdDate,
    String? explorePropName,
    int? explorePropType,
    String? img,
    String? probability,
    int? explorePropLevel,
  }) {
    return SmallRoomExploreInfoExploreProp2()
      ..userExplorePropId = userExplorePropId ?? this.userExplorePropId
      ..userId = userId ?? this.userId
      ..explorePropId = explorePropId ?? this.explorePropId
      ..createdDate = createdDate ?? this.createdDate
      ..explorePropName = explorePropName ?? this.explorePropName
      ..explorePropType = explorePropType ?? this.explorePropType
      ..img = img ?? this.img
      ..probability = probability ?? this.probability
      ..explorePropLevel = explorePropLevel ?? this.explorePropLevel;
  }
}