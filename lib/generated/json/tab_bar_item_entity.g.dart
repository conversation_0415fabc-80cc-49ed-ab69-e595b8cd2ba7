import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/tab_bar_item_entity.dart';

TabBarItemEntity $TabBarItemEntityFromJson(Map<String, dynamic> json) {
  final TabBarItemEntity tabBarItemEntity = TabBarItemEntity();
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    tabBarItemEntity.title = title;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    tabBarItemEntity.icon = icon;
  }
  final String? selectedIcon = jsonConvert.convert<String>(
      json['selectedIcon']);
  if (selectedIcon != null) {
    tabBarItemEntity.selectedIcon = selectedIcon;
  }
  final double? width = jsonConvert.convert<double>(json['width']);
  if (width != null) {
    tabBarItemEntity.width = width;
  }
  final String? badgeCount = jsonConvert.convert<String>(json['badgeCount']);
  if (badgeCount != null) {
    tabBarItemEntity.badgeCount = badgeCount;
  }
  final double? selectedWidth = jsonConvert.convert<double>(
      json['selectedWidth']);
  if (selectedWidth != null) {
    tabBarItemEntity.selectedWidth = selectedWidth;
  }
  return tabBarItemEntity;
}

Map<String, dynamic> $TabBarItemEntityToJson(TabBarItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['title'] = entity.title;
  data['icon'] = entity.icon;
  data['selectedIcon'] = entity.selectedIcon;
  data['width'] = entity.width;
  data['badgeCount'] = entity.badgeCount;
  data['selectedWidth'] = entity.selectedWidth;
  return data;
}

extension TabBarItemEntityExtension on TabBarItemEntity {
  TabBarItemEntity copyWith({
    String? title,
    String? icon,
    String? selectedIcon,
    double? width,
    String? badgeCount,
    double? selectedWidth,
  }) {
    return TabBarItemEntity()
      ..title = title ?? this.title
      ..icon = icon ?? this.icon
      ..selectedIcon = selectedIcon ?? this.selectedIcon
      ..width = width ?? this.width
      ..badgeCount = badgeCount ?? this.badgeCount
      ..selectedWidth = selectedWidth ?? this.selectedWidth;
  }
}