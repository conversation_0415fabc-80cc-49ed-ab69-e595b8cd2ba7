import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/use_prop_result_entity.dart';

UsePropResultEntity $UsePropResultEntityFromJson(Map<String, dynamic> json) {
  final UsePropResultEntity usePropResultEntity = UsePropResultEntity();
  final String? dadaNo = jsonConvert.convert<String>(json['dadaNo']);
  if (dadaNo != null) {
    usePropResultEntity.dadaNo = dadaNo;
  }
  final String? oldDadaNo = jsonConvert.convert<String>(json['oldDadaNo']);
  if (oldDadaNo != null) {
    usePropResultEntity.oldDadaNo = oldDadaNo;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    usePropResultEntity.msg = msg;
  }
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    usePropResultEntity.code = code;
  }
  return usePropResultEntity;
}

Map<String, dynamic> $UsePropResultEntityToJson(UsePropResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['dadaNo'] = entity.dadaNo;
  data['oldDadaNo'] = entity.oldDadaNo;
  data['msg'] = entity.msg;
  data['code'] = entity.code;
  return data;
}

extension UsePropResultEntityExtension on UsePropResultEntity {
  UsePropResultEntity copyWith({
    String? dadaNo,
    String? oldDadaNo,
    String? msg,
    int? code,
  }) {
    return UsePropResultEntity()
      ..dadaNo = dadaNo ?? this.dadaNo
      ..oldDadaNo = oldDadaNo ?? this.oldDadaNo
      ..msg = msg ?? this.msg
      ..code = code ?? this.code;
  }
}