import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/post_list_entity.dart';
import 'package:dada/model/post_entity.dart';


PostListEntity $PostListEntityFromJson(Map<String, dynamic> json) {
  final PostListEntity postListEntity = PostListEntity();
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    postListEntity.total = total;
  }
  final List<PostEntity>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<PostEntity>(e) as PostEntity).toList();
  if (list != null) {
    postListEntity.list = list;
  }
  return postListEntity;
}

Map<String, dynamic> $PostListEntityToJson(PostListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension PostListEntityExtension on PostListEntity {
  PostListEntity copyWith({
    int? total,
    List<PostEntity>? list,
  }) {
    return PostListEntity()
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}