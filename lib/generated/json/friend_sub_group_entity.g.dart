import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';


FriendSubGroupEntity $FriendSubGroupEntityFromJson(Map<String, dynamic> json) {
  final FriendSubGroupEntity friendSubGroupEntity = FriendSubGroupEntity();
  final String? groupName = jsonConvert.convert<String>(json['groupName']);
  if (groupName != null) {
    friendSubGroupEntity.groupName = groupName;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    friendSubGroupEntity.id = id;
  }
  final int? description = jsonConvert.convert<int>(json['description']);
  if (description != null) {
    friendSubGroupEntity.description = description;
  }
  final int? onlineCount = jsonConvert.convert<int>(json['onlineCount']);
  if (onlineCount != null) {
    friendSubGroupEntity.onlineCount = onlineCount;
  }
  final List<FriendUserInfoEntity>? friendList = (json['friendDTOList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (friendList != null) {
    friendSubGroupEntity.friendList = friendList;
  }
  return friendSubGroupEntity;
}

Map<String, dynamic> $FriendSubGroupEntityToJson(FriendSubGroupEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['groupName'] = entity.groupName;
  data['id'] = entity.id;
  data['description'] = entity.description;
  data['onlineCount'] = entity.onlineCount;
  data['friendDTOList'] = entity.friendList?.map((v) => v.toJson()).toList();
  return data;
}

extension FriendSubGroupEntityExtension on FriendSubGroupEntity {
  FriendSubGroupEntity copyWith({
    String? groupName,
    String? id,
    int? description,
    int? onlineCount,
    List<FriendUserInfoEntity>? friendList,
  }) {
    return FriendSubGroupEntity()
      ..groupName = groupName ?? this.groupName
      ..id = id ?? this.id
      ..description = description ?? this.description
      ..onlineCount = onlineCount ?? this.onlineCount
      ..friendList = friendList ?? this.friendList;
  }
}