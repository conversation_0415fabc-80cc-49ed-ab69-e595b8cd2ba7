import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/banner_entity.dart';

BannerEntity $BannerEntityFromJson(Map<String, dynamic> json) {
  final BannerEntity bannerEntity = BannerEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    bannerEntity.id = id;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    bannerEntity.url = url;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    bannerEntity.status = status;
  }
  final String? page = jsonConvert.convert<String>(json['page']);
  if (page != null) {
    bannerEntity.page = page;
  }
  final int? isClick = jsonConvert.convert<int>(json['isClick']);
  if (isClick != null) {
    bannerEntity.isClick = isClick;
  }
  final String? bannerName = jsonConvert.convert<String>(json['bannerName']);
  if (bannerName != null) {
    bannerEntity.bannerName = bannerName;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    bannerEntity.createDate = createDate;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    bannerEntity.sort = sort;
  }
  return bannerEntity;
}

Map<String, dynamic> $BannerEntityToJson(BannerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['url'] = entity.url;
  data['status'] = entity.status;
  data['page'] = entity.page;
  data['isClick'] = entity.isClick;
  data['bannerName'] = entity.bannerName;
  data['createDate'] = entity.createDate;
  data['sort'] = entity.sort;
  return data;
}

extension BannerEntityExtension on BannerEntity {
  BannerEntity copyWith({
    int? id,
    String? url,
    int? status,
    String? page,
    int? isClick,
    String? bannerName,
    String? createDate,
    int? sort,
  }) {
    return BannerEntity()
      ..id = id ?? this.id
      ..url = url ?? this.url
      ..status = status ?? this.status
      ..page = page ?? this.page
      ..isClick = isClick ?? this.isClick
      ..bannerName = bannerName ?? this.bannerName
      ..createDate = createDate ?? this.createDate
      ..sort = sort ?? this.sort;
  }
}