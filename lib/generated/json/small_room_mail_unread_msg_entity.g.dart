import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/small_room_mail_unread_msg_entity.dart';

SmallRoomMailUnreadMsgEntity $SmallRoomMailUnreadMsgEntityFromJson(
    Map<String, dynamic> json) {
  final SmallRoomMailUnreadMsgEntity smallRoomMailUnreadMsgEntity = SmallRoomMailUnreadMsgEntity();
  final int? daNo = jsonConvert.convert<int>(json['daNo']);
  if (daNo != null) {
    smallRoomMailUnreadMsgEntity.daNo = daNo;
  }
  final int? system = jsonConvert.convert<int>(json['system']);
  if (system != null) {
    smallRoomMailUnreadMsgEntity.system = system;
  }
  final int? barrage = jsonConvert.convert<int>(json['barrage']);
  if (barrage != null) {
    smallRoomMailUnreadMsgEntity.barrage = barrage;
  }
  return smallRoomMailUnreadMsgEntity;
}

Map<String, dynamic> $SmallRoomMailUnreadMsgEntityToJson(
    SmallRoomMailUnreadMsgEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['daNo'] = entity.daNo;
  data['system'] = entity.system;
  data['barrage'] = entity.barrage;
  return data;
}

extension SmallRoomMailUnreadMsgEntityExtension on SmallRoomMailUnreadMsgEntity {
  SmallRoomMailUnreadMsgEntity copyWith({
    int? daNo,
    int? system,
    int? barrage,
  }) {
    return SmallRoomMailUnreadMsgEntity()
      ..daNo = daNo ?? this.daNo
      ..system = system ?? this.system
      ..barrage = barrage ?? this.barrage;
  }
}