import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_group_info_entity.dart';
import 'package:dada/model/chat_group_label_entity.dart';

import 'package:dada/model/friend_user_info_entity.dart';


ChatGroupInfoEntity $ChatGroupInfoEntityFromJson(Map<String, dynamic> json) {
  final ChatGroupInfoEntity chatGroupInfoEntity = ChatGroupInfoEntity();
  final String? groupName = jsonConvert.convert<String>(json['chatsName']);
  if (groupName != null) {
    chatGroupInfoEntity.groupName = groupName;
  }
  final String? groupId = jsonConvert.convert<String>(json['id']);
  if (groupId != null) {
    chatGroupInfoEntity.groupId = groupId;
  }
  final String? faceUrl = jsonConvert.convert<String>(json['avatarUrl']);
  if (faceUrl != null) {
    chatGroupInfoEntity.faceUrl = faceUrl;
  }
  final int? memberCount = jsonConvert.convert<int>(json['chatsUserCount']);
  if (memberCount != null) {
    chatGroupInfoEntity.memberCount = memberCount;
  }
  final List<FriendUserInfoEntity>? memberList = (json['memberList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (memberList != null) {
    chatGroupInfoEntity.memberList = memberList;
  }
  final List<ChatGroupLabelEntity>? labels = (json['tagList'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ChatGroupLabelEntity>(e) as ChatGroupLabelEntity)
      .toList();
  if (labels != null) {
    chatGroupInfoEntity.labels = labels;
  }
  final int? isInGroup = jsonConvert.convert<int>(json['isAddChats']);
  if (isInGroup != null) {
    chatGroupInfoEntity.isInGroup = isInGroup;
  }
  final String? notice = jsonConvert.convert<String>(json['notice']);
  if (notice != null) {
    chatGroupInfoEntity.notice = notice;
  }
  final String? creatorId = jsonConvert.convert<String>(json['creatorId']);
  if (creatorId != null) {
    chatGroupInfoEntity.creatorId = creatorId;
  }
  final String? inGroupNickname = jsonConvert.convert<String>(
      json['inGroupNickname']);
  if (inGroupNickname != null) {
    chatGroupInfoEntity.inGroupNickname = inGroupNickname;
  }
  final List<String>? tagStrList = (json['tagStrList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (tagStrList != null) {
    chatGroupInfoEntity.tagStrList = tagStrList;
  }
  final String? chatsId = jsonConvert.convert<String>(json['chatsId']);
  if (chatsId != null) {
    chatGroupInfoEntity.chatsId = chatsId;
  }
  final String? chatsNo = jsonConvert.convert<String>(json['chatsNo']);
  if (chatsNo != null) {
    chatGroupInfoEntity.chatsNo = chatsNo;
  }
  return chatGroupInfoEntity;
}

Map<String, dynamic> $ChatGroupInfoEntityToJson(ChatGroupInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['chatsName'] = entity.groupName;
  data['id'] = entity.groupId;
  data['avatarUrl'] = entity.faceUrl;
  data['chatsUserCount'] = entity.memberCount;
  data['memberList'] = entity.memberList?.map((v) => v.toJson()).toList();
  data['tagList'] = entity.labels?.map((v) => v.toJson()).toList();
  data['isAddChats'] = entity.isInGroup;
  data['notice'] = entity.notice;
  data['creatorId'] = entity.creatorId;
  data['inGroupNickname'] = entity.inGroupNickname;
  data['tagStrList'] = entity.tagStrList;
  data['chatsId'] = entity.chatsId;
  data['chatsNo'] = entity.chatsNo;
  return data;
}

extension ChatGroupInfoEntityExtension on ChatGroupInfoEntity {
  ChatGroupInfoEntity copyWith({
    String? groupName,
    String? groupId,
    String? faceUrl,
    int? memberCount,
    List<FriendUserInfoEntity>? memberList,
    List<ChatGroupLabelEntity>? labels,
    int? isInGroup,
    String? notice,
    String? creatorId,
    String? inGroupNickname,
    List<String>? tagStrList,
    String? chatsId,
    String? chatsNo,
  }) {
    return ChatGroupInfoEntity()
      ..groupName = groupName ?? this.groupName
      ..groupId = groupId ?? this.groupId
      ..faceUrl = faceUrl ?? this.faceUrl
      ..memberCount = memberCount ?? this.memberCount
      ..memberList = memberList ?? this.memberList
      ..labels = labels ?? this.labels
      ..isInGroup = isInGroup ?? this.isInGroup
      ..notice = notice ?? this.notice
      ..creatorId = creatorId ?? this.creatorId
      ..inGroupNickname = inGroupNickname ?? this.inGroupNickname
      ..tagStrList = tagStrList ?? this.tagStrList
      ..chatsId = chatsId ?? this.chatsId
      ..chatsNo = chatsNo ?? this.chatsNo;
  }
}