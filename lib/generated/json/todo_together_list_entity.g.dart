import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/todo_together_list_entity.dart';

TodoTogetherListEntity $TodoTogetherListEntityFromJson(
    Map<String, dynamic> json) {
  final TodoTogetherListEntity todoTogetherListEntity = TodoTogetherListEntity();
  final String? userTaskId = jsonConvert.convert<String>(json['userTaskId']);
  if (userTaskId != null) {
    todoTogetherListEntity.userTaskId = userTaskId;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    todoTogetherListEntity.content = content;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    todoTogetherListEntity.sex = sex;
  }
  final int? taskSex = jsonConvert.convert<int>(json['taskSex']);
  if (taskSex != null) {
    todoTogetherListEntity.taskSex = taskSex;
  }
  final String? taskAvatar = jsonConvert.convert<String>(json['taskAvatar']);
  if (taskAvatar != null) {
    todoTogetherListEntity.taskAvatar = taskAvatar;
  }
  final String? taskNickname = jsonConvert.convert<String>(
      json['taskNickname']);
  if (taskNickname != null) {
    todoTogetherListEntity.taskNickname = taskNickname;
  }
  final int? taskAge = jsonConvert.convert<int>(json['taskAge']);
  if (taskAge != null) {
    todoTogetherListEntity.taskAge = taskAge;
  }
  final int? state = jsonConvert.convert<int>(json['state']);
  if (state != null) {
    todoTogetherListEntity.state = state;
  }
  final String? creatorId = jsonConvert.convert<String>(json['creatorId']);
  if (creatorId != null) {
    todoTogetherListEntity.creatorId = creatorId;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    todoTogetherListEntity.createdDate = createdDate;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    todoTogetherListEntity.type = type;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    todoTogetherListEntity.userId = userId;
  }
  return todoTogetherListEntity;
}

Map<String, dynamic> $TodoTogetherListEntityToJson(
    TodoTogetherListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userTaskId'] = entity.userTaskId;
  data['content'] = entity.content;
  data['sex'] = entity.sex;
  data['taskSex'] = entity.taskSex;
  data['taskAvatar'] = entity.taskAvatar;
  data['taskNickname'] = entity.taskNickname;
  data['taskAge'] = entity.taskAge;
  data['state'] = entity.state;
  data['creatorId'] = entity.creatorId;
  data['createdDate'] = entity.createdDate;
  data['type'] = entity.type;
  data['userId'] = entity.userId;
  return data;
}

extension TodoTogetherListEntityExtension on TodoTogetherListEntity {
  TodoTogetherListEntity copyWith({
    String? userTaskId,
    String? content,
    int? sex,
    int? taskSex,
    String? taskAvatar,
    String? taskNickname,
    int? taskAge,
    int? state,
    String? creatorId,
    String? createdDate,
    int? type,
    String? userId,
  }) {
    return TodoTogetherListEntity()
      ..userTaskId = userTaskId ?? this.userTaskId
      ..content = content ?? this.content
      ..sex = sex ?? this.sex
      ..taskSex = taskSex ?? this.taskSex
      ..taskAvatar = taskAvatar ?? this.taskAvatar
      ..taskNickname = taskNickname ?? this.taskNickname
      ..taskAge = taskAge ?? this.taskAge
      ..state = state ?? this.state
      ..creatorId = creatorId ?? this.creatorId
      ..createdDate = createdDate ?? this.createdDate
      ..type = type ?? this.type
      ..userId = userId ?? this.userId;
  }
}