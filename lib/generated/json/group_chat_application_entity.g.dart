import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/group_chat_application_entity.dart';

GroupChatApplicationEntity $GroupChatApplicationEntityFromJson(
    Map<String, dynamic> json) {
  final GroupChatApplicationEntity groupChatApplicationEntity = GroupChatApplicationEntity();
  final String? groupID = jsonConvert.convert<String>(json['groupID']);
  if (groupID != null) {
    groupChatApplicationEntity.groupID = groupID;
  }
  final String? fromUser = jsonConvert.convert<String>(json['fromUser']);
  if (fromUser != null) {
    groupChatApplicationEntity.fromUser = fromUser;
  }
  final String? fromUserNickName = jsonConvert.convert<String>(
      json['fromUserNickName']);
  if (fromUserNickName != null) {
    groupChatApplicationEntity.fromUserNickName = fromUserNickName;
  }
  final String? fromUserFaceUrl = jsonConvert.convert<String>(
      json['fromUserFaceUrl']);
  if (fromUserFaceUrl != null) {
    groupChatApplicationEntity.fromUserFaceUrl = fromUserFaceUrl;
  }
  final String? toUser = jsonConvert.convert<String>(json['toUser']);
  if (toUser != null) {
    groupChatApplicationEntity.toUser = toUser;
  }
  final int? handleResult = jsonConvert.convert<int>(json['handleResult']);
  if (handleResult != null) {
    groupChatApplicationEntity.handleResult = handleResult;
  }
  final String? requestMsg = jsonConvert.convert<String>(json['requestMsg']);
  if (requestMsg != null) {
    groupChatApplicationEntity.requestMsg = requestMsg;
  }
  final int? handleStatus = jsonConvert.convert<int>(json['handleStatus']);
  if (handleStatus != null) {
    groupChatApplicationEntity.handleStatus = handleStatus;
  }
  final String? groupName = jsonConvert.convert<String>(json['groupName']);
  if (groupName != null) {
    groupChatApplicationEntity.groupName = groupName;
  }
  final int? addTime = jsonConvert.convert<int>(json['addTime']);
  if (addTime != null) {
    groupChatApplicationEntity.addTime = addTime;
  }
  return groupChatApplicationEntity;
}

Map<String, dynamic> $GroupChatApplicationEntityToJson(
    GroupChatApplicationEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['groupID'] = entity.groupID;
  data['fromUser'] = entity.fromUser;
  data['fromUserNickName'] = entity.fromUserNickName;
  data['fromUserFaceUrl'] = entity.fromUserFaceUrl;
  data['toUser'] = entity.toUser;
  data['handleResult'] = entity.handleResult;
  data['requestMsg'] = entity.requestMsg;
  data['handleStatus'] = entity.handleStatus;
  data['groupName'] = entity.groupName;
  data['addTime'] = entity.addTime;
  return data;
}

extension GroupChatApplicationEntityExtension on GroupChatApplicationEntity {
  GroupChatApplicationEntity copyWith({
    String? groupID,
    String? fromUser,
    String? fromUserNickName,
    String? fromUserFaceUrl,
    String? toUser,
    int? handleResult,
    String? requestMsg,
    int? handleStatus,
    String? groupName,
    int? addTime,
  }) {
    return GroupChatApplicationEntity()
      ..groupID = groupID ?? this.groupID
      ..fromUser = fromUser ?? this.fromUser
      ..fromUserNickName = fromUserNickName ?? this.fromUserNickName
      ..fromUserFaceUrl = fromUserFaceUrl ?? this.fromUserFaceUrl
      ..toUser = toUser ?? this.toUser
      ..handleResult = handleResult ?? this.handleResult
      ..requestMsg = requestMsg ?? this.requestMsg
      ..handleStatus = handleStatus ?? this.handleStatus
      ..groupName = groupName ?? this.groupName
      ..addTime = addTime ?? this.addTime;
  }
}