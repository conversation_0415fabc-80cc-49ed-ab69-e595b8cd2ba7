import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/dynamic_message_item_entity.dart';

DynamicMessageItemEntity $DynamicMessageItemEntityFromJson(
    Map<String, dynamic> json) {
  final DynamicMessageItemEntity dynamicMessageItemEntity = DynamicMessageItemEntity();
  final String? msgId = jsonConvert.convert<String>(json['msgId']);
  if (msgId != null) {
    dynamicMessageItemEntity.msgId = msgId;
  }
  final String? senderId = jsonConvert.convert<String>(json['senderId']);
  if (senderId != null) {
    dynamicMessageItemEntity.senderId = senderId;
  }
  final String? recipientId = jsonConvert.convert<String>(json['recipientId']);
  if (recipientId != null) {
    dynamicMessageItemEntity.recipientId = recipientId;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    dynamicMessageItemEntity.type = type;
  }
  final int? postId = jsonConvert.convert<int>(json['postId']);
  if (postId != null) {
    dynamicMessageItemEntity.postId = postId;
  }
  final int? isRead = jsonConvert.convert<int>(json['isRead']);
  if (isRead != null) {
    dynamicMessageItemEntity.isRead = isRead;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    dynamicMessageItemEntity.avatar = avatar;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    dynamicMessageItemEntity.nickname = nickname;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    dynamicMessageItemEntity.content = content;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    dynamicMessageItemEntity.createdDate = createdDate;
  }
  final int? contentType = jsonConvert.convert<int>(json['contentType']);
  if (contentType != null) {
    dynamicMessageItemEntity.contentType = contentType;
  }
  final String? postMsg = jsonConvert.convert<String>(json['postMsg']);
  if (postMsg != null) {
    dynamicMessageItemEntity.postMsg = postMsg;
  }
  return dynamicMessageItemEntity;
}

Map<String, dynamic> $DynamicMessageItemEntityToJson(
    DynamicMessageItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['msgId'] = entity.msgId;
  data['senderId'] = entity.senderId;
  data['recipientId'] = entity.recipientId;
  data['type'] = entity.type;
  data['postId'] = entity.postId;
  data['isRead'] = entity.isRead;
  data['avatar'] = entity.avatar;
  data['nickname'] = entity.nickname;
  data['content'] = entity.content;
  data['createdDate'] = entity.createdDate;
  data['contentType'] = entity.contentType;
  data['postMsg'] = entity.postMsg;
  return data;
}

extension DynamicMessageItemEntityExtension on DynamicMessageItemEntity {
  DynamicMessageItemEntity copyWith({
    String? msgId,
    String? senderId,
    String? recipientId,
    int? type,
    int? postId,
    int? isRead,
    String? avatar,
    String? nickname,
    String? content,
    String? createdDate,
    int? contentType,
    String? postMsg,
  }) {
    return DynamicMessageItemEntity()
      ..msgId = msgId ?? this.msgId
      ..senderId = senderId ?? this.senderId
      ..recipientId = recipientId ?? this.recipientId
      ..type = type ?? this.type
      ..postId = postId ?? this.postId
      ..isRead = isRead ?? this.isRead
      ..avatar = avatar ?? this.avatar
      ..nickname = nickname ?? this.nickname
      ..content = content ?? this.content
      ..createdDate = createdDate ?? this.createdDate
      ..contentType = contentType ?? this.contentType
      ..postMsg = postMsg ?? this.postMsg;
  }
}