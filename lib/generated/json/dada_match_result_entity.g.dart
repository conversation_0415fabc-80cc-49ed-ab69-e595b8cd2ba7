import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/dada_match_result_entity.dart';
import 'package:dada/model/user_info_entity.dart';


DadaMatchResultEntity $DadaMatchResultEntityFromJson(
    Map<String, dynamic> json) {
  final DadaMatchResultEntity dadaMatchResultEntity = DadaMatchResultEntity();
  final List<UserInfoEntity>? matchDada = (json['matchDada'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<UserInfoEntity>(e) as UserInfoEntity)
      .toList();
  if (matchDada != null) {
    dadaMatchResultEntity.matchDada = matchDada;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    dadaMatchResultEntity.type = type;
  }
  return dadaMatchResultEntity;
}

Map<String, dynamic> $DadaMatchResultEntityToJson(
    DadaMatchResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['matchDada'] = entity.matchDada?.map((v) => v.toJson()).toList();
  data['type'] = entity.type;
  return data;
}

extension DadaMatchResultEntityExtension on DadaMatchResultEntity {
  DadaMatchResultEntity copyWith({
    List<UserInfoEntity>? matchDada,
    int? type,
  }) {
    return DadaMatchResultEntity()
      ..matchDada = matchDada ?? this.matchDada
      ..type = type ?? this.type;
  }
}