import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_searched_result_entity.dart';
import 'package:dada/model/chat_group_info_entity.dart';

import 'package:dada/model/friend_user_info_entity.dart';


ChatSearchedResultEntity $ChatSearchedResultEntityFromJson(
    Map<String, dynamic> json) {
  final ChatSearchedResultEntity chatSearchedResultEntity = ChatSearchedResultEntity();
  final List<FriendUserInfoEntity>? searchUsers = (json['searchUsers'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (searchUsers != null) {
    chatSearchedResultEntity.searchUsers = searchUsers;
  }
  final List<
      ChatGroupInfoEntity>? searchCrowdChats = (json['searchCrowdChats'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ChatGroupInfoEntity>(e) as ChatGroupInfoEntity)
      .toList();
  if (searchCrowdChats != null) {
    chatSearchedResultEntity.searchCrowdChats = searchCrowdChats;
  }
  return chatSearchedResultEntity;
}

Map<String, dynamic> $ChatSearchedResultEntityToJson(
    ChatSearchedResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['searchUsers'] = entity.searchUsers?.map((v) => v.toJson()).toList();
  data['searchCrowdChats'] =
      entity.searchCrowdChats?.map((v) => v.toJson()).toList();
  return data;
}

extension ChatSearchedResultEntityExtension on ChatSearchedResultEntity {
  ChatSearchedResultEntity copyWith({
    List<FriendUserInfoEntity>? searchUsers,
    List<ChatGroupInfoEntity>? searchCrowdChats,
  }) {
    return ChatSearchedResultEntity()
      ..searchUsers = searchUsers ?? this.searchUsers
      ..searchCrowdChats = searchCrowdChats ?? this.searchCrowdChats;
  }
}