import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/small_room_barrage_item_entity.dart';

SmallRoomBarrageItemEntity $SmallRoomBarrageItemEntityFromJson(
    Map<String, dynamic> json) {
  final SmallRoomBarrageItemEntity smallRoomBarrageItemEntity = SmallRoomBarrageItemEntity();
  final String? roomMsgId = jsonConvert.convert<String>(json['roomMsgId']);
  if (roomMsgId != null) {
    smallRoomBarrageItemEntity.roomMsgId = roomMsgId;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    smallRoomBarrageItemEntity.avatar = avatar;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    smallRoomBarrageItemEntity.nickname = nickname;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    smallRoomBarrageItemEntity.content = content;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    smallRoomBarrageItemEntity.userId = userId;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    smallRoomBarrageItemEntity.createDate = createDate;
  }
  final String? dadaRoomId = jsonConvert.convert<String>(json['dadaRoomId']);
  if (dadaRoomId != null) {
    smallRoomBarrageItemEntity.dadaRoomId = dadaRoomId;
  }
  return smallRoomBarrageItemEntity;
}

Map<String, dynamic> $SmallRoomBarrageItemEntityToJson(
    SmallRoomBarrageItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['roomMsgId'] = entity.roomMsgId;
  data['avatar'] = entity.avatar;
  data['nickname'] = entity.nickname;
  data['content'] = entity.content;
  data['userId'] = entity.userId;
  data['createDate'] = entity.createDate;
  data['dadaRoomId'] = entity.dadaRoomId;
  return data;
}

extension SmallRoomBarrageItemEntityExtension on SmallRoomBarrageItemEntity {
  SmallRoomBarrageItemEntity copyWith({
    String? roomMsgId,
    String? avatar,
    String? nickname,
    String? content,
    String? userId,
    String? createDate,
    String? dadaRoomId,
  }) {
    return SmallRoomBarrageItemEntity()
      ..roomMsgId = roomMsgId ?? this.roomMsgId
      ..avatar = avatar ?? this.avatar
      ..nickname = nickname ?? this.nickname
      ..content = content ?? this.content
      ..userId = userId ?? this.userId
      ..createDate = createDate ?? this.createDate
      ..dadaRoomId = dadaRoomId ?? this.dadaRoomId;
  }
}