import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/small_room_task_entity.dart';

SmallRoomTaskEntity $SmallRoomTaskEntityFromJson(Map<String, dynamic> json) {
  final SmallRoomTaskEntity smallRoomTaskEntity = SmallRoomTaskEntity();
  final String? roomTaskId = jsonConvert.convert<String>(json['roomTaskId']);
  if (roomTaskId != null) {
    smallRoomTaskEntity.roomTaskId = roomTaskId;
  }
  final String? taskUrl = jsonConvert.convert<String>(json['taskUrl']);
  if (taskUrl != null) {
    smallRoomTaskEntity.taskUrl = taskUrl;
  }
  final String? taskName = jsonConvert.convert<String>(json['taskName']);
  if (taskName != null) {
    smallRoomTaskEntity.taskName = taskName;
  }
  final int? taskTotalProgress = jsonConvert.convert<int>(
      json['taskTotalProgress']);
  if (taskTotalProgress != null) {
    smallRoomTaskEntity.taskTotalProgress = taskTotalProgress;
  }
  final String? taskText = jsonConvert.convert<String>(json['taskText']);
  if (taskText != null) {
    smallRoomTaskEntity.taskText = taskText;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    smallRoomTaskEntity.type = type;
  }
  final int? taskProgress = jsonConvert.convert<int>(json['taskProgress']);
  if (taskProgress != null) {
    smallRoomTaskEntity.taskProgress = taskProgress;
  }
  final int? completed = jsonConvert.convert<int>(json['completed']);
  if (completed != null) {
    smallRoomTaskEntity.completed = completed;
  }
  return smallRoomTaskEntity;
}

Map<String, dynamic> $SmallRoomTaskEntityToJson(SmallRoomTaskEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['roomTaskId'] = entity.roomTaskId;
  data['taskUrl'] = entity.taskUrl;
  data['taskName'] = entity.taskName;
  data['taskTotalProgress'] = entity.taskTotalProgress;
  data['taskText'] = entity.taskText;
  data['type'] = entity.type;
  data['taskProgress'] = entity.taskProgress;
  data['completed'] = entity.completed;
  return data;
}

extension SmallRoomTaskEntityExtension on SmallRoomTaskEntity {
  SmallRoomTaskEntity copyWith({
    String? roomTaskId,
    String? taskUrl,
    String? taskName,
    int? taskTotalProgress,
    String? taskText,
    int? type,
    int? taskProgress,
    int? completed,
  }) {
    return SmallRoomTaskEntity()
      ..roomTaskId = roomTaskId ?? this.roomTaskId
      ..taskUrl = taskUrl ?? this.taskUrl
      ..taskName = taskName ?? this.taskName
      ..taskTotalProgress = taskTotalProgress ?? this.taskTotalProgress
      ..taskText = taskText ?? this.taskText
      ..type = type ?? this.type
      ..taskProgress = taskProgress ?? this.taskProgress
      ..completed = completed ?? this.completed;
  }
}