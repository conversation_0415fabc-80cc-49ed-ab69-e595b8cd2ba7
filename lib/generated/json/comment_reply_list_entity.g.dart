import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/comment_reply_list_entity.dart';
import 'package:dada/model/comment_reply_item_entity.dart';


CommentReplyListEntity $CommentReplyListEntityFromJson(
    Map<String, dynamic> json) {
  final CommentReplyListEntity commentReplyListEntity = CommentReplyListEntity();
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    commentReplyListEntity.total = total;
  }
  final List<CommentReplyItemEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<CommentReplyItemEntity>(e) as CommentReplyItemEntity)
      .toList();
  if (list != null) {
    commentReplyListEntity.list = list;
  }
  return commentReplyListEntity;
}

Map<String, dynamic> $CommentReplyListEntityToJson(
    CommentReplyListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CommentReplyListEntityExtension on CommentReplyListEntity {
  CommentReplyListEntity copyWith({
    int? total,
    List<CommentReplyItemEntity>? list,
  }) {
    return CommentReplyListEntity()
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}