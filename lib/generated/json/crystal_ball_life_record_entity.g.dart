import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/crystal_ball_life_record_entity.dart';

CrystalBallLifeRecordEntity $CrystalBallLifeRecordEntityFromJson(
    Map<String, dynamic> json) {
  final CrystalBallLifeRecordEntity crystalBallLifeRecordEntity = CrystalBallLifeRecordEntity();
  final String? lifeId = jsonConvert.convert<String>(json['lifeId']);
  if (lifeId != null) {
    crystalBallLifeRecordEntity.lifeId = lifeId;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    crystalBallLifeRecordEntity.userId = userId;
  }
  final List<String>? imgUrls = (json['imgUrls'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (imgUrls != null) {
    crystalBallLifeRecordEntity.imgUrls = imgUrls;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    crystalBallLifeRecordEntity.content = content;
  }
  final int? year = jsonConvert.convert<int>(json['year']);
  if (year != null) {
    crystalBallLifeRecordEntity.year = year;
  }
  final String? month = jsonConvert.convert<String>(json['month']);
  if (month != null) {
    crystalBallLifeRecordEntity.month = month;
  }
  final String? createdDate = jsonConvert.convert<String>(json['createdDate']);
  if (createdDate != null) {
    crystalBallLifeRecordEntity.createdDate = createdDate;
  }
  final bool? isExist = jsonConvert.convert<bool>(json['isExist']);
  if (isExist != null) {
    crystalBallLifeRecordEntity.isExist = isExist;
  }
  return crystalBallLifeRecordEntity;
}

Map<String, dynamic> $CrystalBallLifeRecordEntityToJson(
    CrystalBallLifeRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['lifeId'] = entity.lifeId;
  data['userId'] = entity.userId;
  data['imgUrls'] = entity.imgUrls;
  data['content'] = entity.content;
  data['year'] = entity.year;
  data['month'] = entity.month;
  data['createdDate'] = entity.createdDate;
  data['isExist'] = entity.isExist;
  return data;
}

extension CrystalBallLifeRecordEntityExtension on CrystalBallLifeRecordEntity {
  CrystalBallLifeRecordEntity copyWith({
    String? lifeId,
    String? userId,
    List<String>? imgUrls,
    String? content,
    int? year,
    String? month,
    String? createdDate,
    bool? isExist,
  }) {
    return CrystalBallLifeRecordEntity()
      ..lifeId = lifeId ?? this.lifeId
      ..userId = userId ?? this.userId
      ..imgUrls = imgUrls ?? this.imgUrls
      ..content = content ?? this.content
      ..year = year ?? this.year
      ..month = month ?? this.month
      ..createdDate = createdDate ?? this.createdDate
      ..isExist = isExist ?? this.isExist;
  }
}