import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/user_card_box_entity.dart';

UserCardBoxEntity $UserCardBoxEntityFromJson(Map<String, dynamic> json) {
  final UserCardBoxEntity userCardBoxEntity = UserCardBoxEntity();
  final String? cardType = jsonConvert.convert<String>(json['cardType']);
  if (cardType != null) {
    userCardBoxEntity.cardType = cardType;
  }
  final int? cardNum = jsonConvert.convert<int>(json['cardNum']);
  if (cardNum != null) {
    userCardBoxEntity.cardNum = cardNum;
  }
  return userCardBoxEntity;
}

Map<String, dynamic> $UserCardBoxEntityToJson(UserCardBoxEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['cardType'] = entity.cardType;
  data['cardNum'] = entity.cardNum;
  return data;
}

extension UserCardBoxEntityExtension on UserCardBoxEntity {
  UserCardBoxEntity copyWith({
    String? cardType,
    int? cardNum,
  }) {
    return UserCardBoxEntity()
      ..cardType = cardType ?? this.cardType
      ..cardNum = cardNum ?? this.cardNum;
  }
}