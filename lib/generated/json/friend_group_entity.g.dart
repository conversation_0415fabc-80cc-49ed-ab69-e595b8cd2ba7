import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/friend_group_entity.dart';
import 'package:dada/model/friend_sub_group_entity.dart';

import 'package:dada/model/friend_user_info_entity.dart';


FriendGroupEntity $FriendGroupEntityFromJson(Map<String, dynamic> json) {
  final FriendGroupEntity friendGroupEntity = FriendGroupEntity();
  final List<
      FriendUserInfoEntity>? unGroupedFriendList = (json['userFriendList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendUserInfoEntity>(e) as FriendUserInfoEntity)
      .toList();
  if (unGroupedFriendList != null) {
    friendGroupEntity.unGroupedFriendList = unGroupedFriendList;
  }
  final List<
      FriendSubGroupEntity>? subGroupList = (json['userGroupVOS'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<FriendSubGroupEntity>(e) as FriendSubGroupEntity)
      .toList();
  if (subGroupList != null) {
    friendGroupEntity.subGroupList = subGroupList;
  }
  return friendGroupEntity;
}

Map<String, dynamic> $FriendGroupEntityToJson(FriendGroupEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userFriendList'] =
      entity.unGroupedFriendList?.map((v) => v.toJson()).toList();
  data['userGroupVOS'] = entity.subGroupList?.map((v) => v.toJson()).toList();
  return data;
}

extension FriendGroupEntityExtension on FriendGroupEntity {
  FriendGroupEntity copyWith({
    List<FriendUserInfoEntity>? unGroupedFriendList,
    List<FriendSubGroupEntity>? subGroupList,
  }) {
    return FriendGroupEntity()
      ..unGroupedFriendList = unGroupedFriendList ?? this.unGroupedFriendList
      ..subGroupList = subGroupList ?? this.subGroupList;
  }
}