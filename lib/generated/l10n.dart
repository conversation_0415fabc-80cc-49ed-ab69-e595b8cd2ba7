// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `搭搭`
  String get appName {
    return Intl.message(
      '搭搭',
      name: 'appName',
      desc: '',
      args: [],
    );
  }

  /// `凑热闹`
  String get homeTabTitle {
    return Intl.message(
      '凑热闹',
      name: 'homeTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `搭圈`
  String get circleTabTitle {
    return Intl.message(
      '搭圈',
      name: 'circleTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `搭搭`
  String get dadaTabTitle {
    return Intl.message(
      '搭搭',
      name: 'dadaTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `聊天`
  String get chatTabTitle {
    return Intl.message(
      '聊天',
      name: 'chatTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `个人`
  String get mineTabTitle {
    return Intl.message(
      '个人',
      name: 'mineTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `Hi~\n等你很久了\n欢迎来到搭搭！\n这里有很多有趣的人，\n快来加入吧！`
  String get welcomeTip {
    return Intl.message(
      'Hi~\n等你很久了\n欢迎来到搭搭！\n这里有很多有趣的人，\n快来加入吧！',
      name: 'welcomeTip',
      desc: '',
      args: [],
    );
  }

  /// `手机号注册登录`
  String get welcomeBtnTitle {
    return Intl.message(
      '手机号注册登录',
      name: 'welcomeBtnTitle',
      desc: '',
      args: [],
    );
  }

  /// `已阅读并同意 `
  String get welcomeBottomTipSub1 {
    return Intl.message(
      '已阅读并同意 ',
      name: 'welcomeBottomTipSub1',
      desc: '',
      args: [],
    );
  }

  /// ` 和 `
  String get welcomeBottomTipSub2 {
    return Intl.message(
      ' 和 ',
      name: 'welcomeBottomTipSub2',
      desc: '',
      args: [],
    );
  }

  /// `用户协议`
  String get userAgreement {
    return Intl.message(
      '用户协议',
      name: 'userAgreement',
      desc: '',
      args: [],
    );
  }

  /// `隐私政策`
  String get privacyPolicy {
    return Intl.message(
      '隐私政策',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `请先阅读并同意协议`
  String get checkPolicyToast {
    return Intl.message(
      '请先阅读并同意协议',
      name: 'checkPolicyToast',
      desc: '',
      args: [],
    );
  }

  /// `hi~ 请填写你的手机号`
  String get loginTitle {
    return Intl.message(
      'hi~ 请填写你的手机号',
      name: 'loginTitle',
      desc: '',
      args: [],
    );
  }

  /// `未注册手机号将自动注册`
  String get loginSubTitle {
    return Intl.message(
      '未注册手机号将自动注册',
      name: 'loginSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `请输入手机号`
  String get inputPhoneNumberTip {
    return Intl.message(
      '请输入手机号',
      name: 'inputPhoneNumberTip',
      desc: '',
      args: [],
    );
  }

  /// `请输入验证码`
  String get inputCodeTip {
    return Intl.message(
      '请输入验证码',
      name: 'inputCodeTip',
      desc: '',
      args: [],
    );
  }

  /// `获取验证码`
  String get fetchCodeBtnTitle {
    return Intl.message(
      '获取验证码',
      name: 'fetchCodeBtnTitle',
      desc: '',
      args: [],
    );
  }

  /// `手机无法使用`
  String get phoneUnUsedTip {
    return Intl.message(
      '手机无法使用',
      name: 'phoneUnUsedTip',
      desc: '',
      args: [],
    );
  }

  /// `下一步`
  String get next {
    return Intl.message(
      '下一步',
      name: 'next',
      desc: '',
      args: [],
    );
  }

  /// `+86`
  String get phoneAreaCode {
    return Intl.message(
      '+86',
      name: 'phoneAreaCode',
      desc: '',
      args: [],
    );
  }

  /// `请输入正确的手机号`
  String get inputPhoneErrorTip {
    return Intl.message(
      '请输入正确的手机号',
      name: 'inputPhoneErrorTip',
      desc: '',
      args: [],
    );
  }

  /// `完善你的资料\n开始搭搭之旅吧~`
  String get registerTitle {
    return Intl.message(
      '完善你的资料\n开始搭搭之旅吧~',
      name: 'registerTitle',
      desc: '',
      args: [],
    );
  }

  /// `昵称`
  String get nickname {
    return Intl.message(
      '昵称',
      name: 'nickname',
      desc: '',
      args: [],
    );
  }

  /// `请输入昵称`
  String get nicknamePlaceholder {
    return Intl.message(
      '请输入昵称',
      name: 'nicknamePlaceholder',
      desc: '',
      args: [],
    );
  }

  /// `请上传头像`
  String get inputAvatarTip {
    return Intl.message(
      '请上传头像',
      name: 'inputAvatarTip',
      desc: '',
      args: [],
    );
  }

  /// `随机`
  String get random {
    return Intl.message(
      '随机',
      name: 'random',
      desc: '',
      args: [],
    );
  }

  /// `生日`
  String get birthday {
    return Intl.message(
      '生日',
      name: 'birthday',
      desc: '',
      args: [],
    );
  }

  /// `请选择你的出生日期`
  String get birthdayPlaceholder {
    return Intl.message(
      '请选择你的出生日期',
      name: 'birthdayPlaceholder',
      desc: '',
      args: [],
    );
  }

  /// `性别`
  String get sex {
    return Intl.message(
      '性别',
      name: 'sex',
      desc: '',
      args: [],
    );
  }

  /// `注册后不可修改`
  String get sexTip {
    return Intl.message(
      '注册后不可修改',
      name: 'sexTip',
      desc: '',
      args: [],
    );
  }

  /// `男生`
  String get boy {
    return Intl.message(
      '男生',
      name: 'boy',
      desc: '',
      args: [],
    );
  }

  /// `女生`
  String get girl {
    return Intl.message(
      '女生',
      name: 'girl',
      desc: '',
      args: [],
    );
  }

  /// `男`
  String get man {
    return Intl.message(
      '男',
      name: 'man',
      desc: '',
      args: [],
    );
  }

  /// `女`
  String get woman {
    return Intl.message(
      '女',
      name: 'woman',
      desc: '',
      args: [],
    );
  }

  /// `个人社交习惯`
  String get socialHabits {
    return Intl.message(
      '个人社交习惯',
      name: 'socialHabits',
      desc: '',
      args: [],
    );
  }

  /// `冰冷`
  String get cool {
    return Intl.message(
      '冰冷',
      name: 'cool',
      desc: '',
      args: [],
    );
  }

  /// `慢热`
  String get slow {
    return Intl.message(
      '慢热',
      name: 'slow',
      desc: '',
      args: [],
    );
  }

  /// `适中`
  String get moderate {
    return Intl.message(
      '适中',
      name: 'moderate',
      desc: '',
      args: [],
    );
  }

  /// `热情`
  String get alive {
    return Intl.message(
      '热情',
      name: 'alive',
      desc: '',
      args: [],
    );
  }

  /// `非常热情`
  String get hospitable {
    return Intl.message(
      '非常热情',
      name: 'hospitable',
      desc: '',
      args: [],
    );
  }

  /// `请选择`
  String get pleaseSelect {
    return Intl.message(
      '请选择',
      name: 'pleaseSelect',
      desc: '',
      args: [],
    );
  }

  /// `确认`
  String get sure {
    return Intl.message(
      '确认',
      name: 'sure',
      desc: '',
      args: [],
    );
  }

  /// `取消`
  String get cancel {
    return Intl.message(
      '取消',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `相册`
  String get album {
    return Intl.message(
      '相册',
      name: 'album',
      desc: '',
      args: [],
    );
  }

  /// `相机`
  String get camera {
    return Intl.message(
      '相机',
      name: 'camera',
      desc: '',
      args: [],
    );
  }

  /// `开启搭搭`
  String get startDada {
    return Intl.message(
      '开启搭搭',
      name: 'startDada',
      desc: '',
      args: [],
    );
  }

  /// `搭搭号`
  String get dadaID {
    return Intl.message(
      '搭搭号',
      name: 'dadaID',
      desc: '',
      args: [],
    );
  }

  /// `IP属地`
  String get ipAddress {
    return Intl.message(
      'IP属地',
      name: 'ipAddress',
      desc: '',
      args: [],
    );
  }

  /// `月卡`
  String get monthCard {
    return Intl.message(
      '月卡',
      name: 'monthCard',
      desc: '',
      args: [],
    );
  }

  /// `搭币`
  String get dadaCoin {
    return Intl.message(
      '搭币',
      name: 'dadaCoin',
      desc: '',
      args: [],
    );
  }

  /// `谁看过我`
  String get seenMe {
    return Intl.message(
      '谁看过我',
      name: 'seenMe',
      desc: '',
      args: [],
    );
  }

  /// `我的钱包`
  String get myWallet {
    return Intl.message(
      '我的钱包',
      name: 'myWallet',
      desc: '',
      args: [],
    );
  }

  /// `个人隐私`
  String get myPrivacy {
    return Intl.message(
      '个人隐私',
      name: 'myPrivacy',
      desc: '',
      args: [],
    );
  }

  /// `设置`
  String get settings {
    return Intl.message(
      '设置',
      name: 'settings',
      desc: '',
      args: [],
    );
  }

  /// `语音盒子`
  String get audioBox {
    return Intl.message(
      '语音盒子',
      name: 'audioBox',
      desc: '',
      args: [],
    );
  }

  /// `卡盒`
  String get cardBox {
    return Intl.message(
      '卡盒',
      name: 'cardBox',
      desc: '',
      args: [],
    );
  }

  /// `我的成就`
  String get myAchievement {
    return Intl.message(
      '我的成就',
      name: 'myAchievement',
      desc: '',
      args: [],
    );
  }

  /// `兑换商城`
  String get exchangeMall {
    return Intl.message(
      '兑换商城',
      name: 'exchangeMall',
      desc: '',
      args: [],
    );
  }

  /// `我的账号`
  String get myAccount {
    return Intl.message(
      '我的账号',
      name: 'myAccount',
      desc: '',
      args: [],
    );
  }

  /// `实名认证`
  String get realNameAuth {
    return Intl.message(
      '实名认证',
      name: 'realNameAuth',
      desc: '',
      args: [],
    );
  }

  /// `主页`
  String get myPage {
    return Intl.message(
      '主页',
      name: 'myPage',
      desc: '',
      args: [],
    );
  }

  /// `请输入关键词`
  String get searchPlaceholder {
    return Intl.message(
      '请输入关键词',
      name: 'searchPlaceholder',
      desc: '',
      args: [],
    );
  }

  /// `签到`
  String get sign {
    return Intl.message(
      '签到',
      name: 'sign',
      desc: '',
      args: [],
    );
  }

  /// `推荐`
  String get recommend {
    return Intl.message(
      '推荐',
      name: 'recommend',
      desc: '',
      args: [],
    );
  }

  /// `化身村口老太，\n无压力畅聊！`
  String get fairSubTitle {
    return Intl.message(
      '化身村口老太，\n无压力畅聊！',
      name: 'fairSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `随时随地随意聊`
  String get teapotSubTitle {
    return Intl.message(
      '随时随地随意聊',
      name: 'teapotSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `去市集`
  String get goFair {
    return Intl.message(
      '去市集',
      name: 'goFair',
      desc: '',
      args: [],
    );
  }

  /// `去茶壶`
  String get goTeapot {
    return Intl.message(
      '去茶壶',
      name: 'goTeapot',
      desc: '',
      args: [],
    );
  }

  /// `睡前卧谈会`
  String get sleepTalk {
    return Intl.message(
      '睡前卧谈会',
      name: 'sleepTalk',
      desc: '',
      args: [],
    );
  }

  /// `群长`
  String get groupOwner {
    return Intl.message(
      '群长',
      name: 'groupOwner',
      desc: '',
      args: [],
    );
  }

  /// `嗨聊`
  String get onHiChat {
    return Intl.message(
      '嗨聊',
      name: 'onHiChat',
      desc: '',
      args: [],
    );
  }

  /// `在线`
  String get online {
    return Intl.message(
      '在线',
      name: 'online',
      desc: '',
      args: [],
    );
  }

  /// `人`
  String get people {
    return Intl.message(
      '人',
      name: 'people',
      desc: '',
      args: [],
    );
  }

  /// `加载中...`
  String get loading {
    return Intl.message(
      '加载中...',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `暂无数据~`
  String get emptyData {
    return Intl.message(
      '暂无数据~',
      name: 'emptyData',
      desc: '',
      args: [],
    );
  }

  /// `编辑资料`
  String get editUserInfo {
    return Intl.message(
      '编辑资料',
      name: 'editUserInfo',
      desc: '',
      args: [],
    );
  }

  /// `头像`
  String get avatar {
    return Intl.message(
      '头像',
      name: 'avatar',
      desc: '',
      args: [],
    );
  }

  /// `语音签名`
  String get audioSignature {
    return Intl.message(
      '语音签名',
      name: 'audioSignature',
      desc: '',
      args: [],
    );
  }

  /// `个性签名`
  String get textSignature {
    return Intl.message(
      '个性签名',
      name: 'textSignature',
      desc: '',
      args: [],
    );
  }

  /// `个人信息`
  String get userInfo {
    return Intl.message(
      '个人信息',
      name: 'userInfo',
      desc: '',
      args: [],
    );
  }

  /// `家乡`
  String get hometown {
    return Intl.message(
      '家乡',
      name: 'hometown',
      desc: '',
      args: [],
    );
  }

  /// `职业`
  String get vacation {
    return Intl.message(
      '职业',
      name: 'vacation',
      desc: '',
      args: [],
    );
  }

  /// `未设置`
  String get unSetting {
    return Intl.message(
      '未设置',
      name: 'unSetting',
      desc: '',
      args: [],
    );
  }

  /// `保存`
  String get save {
    return Intl.message(
      '保存',
      name: 'save',
      desc: '',
      args: [],
    );
  }

  /// `请输入`
  String get input {
    return Intl.message(
      '请输入',
      name: 'input',
      desc: '',
      args: [],
    );
  }

  /// `请输入您的个性签名~`
  String get signaturePlaceholder {
    return Intl.message(
      '请输入您的个性签名~',
      name: 'signaturePlaceholder',
      desc: '',
      args: [],
    );
  }

  /// `确定修改性别为`
  String get sexChangeDialogText {
    return Intl.message(
      '确定修改性别为',
      name: 'sexChangeDialogText',
      desc: '',
      args: [],
    );
  }

  /// `编辑语音签名`
  String get editAudioSignature {
    return Intl.message(
      '编辑语音签名',
      name: 'editAudioSignature',
      desc: '',
      args: [],
    );
  }

  /// `重新录制`
  String get repeatRecord {
    return Intl.message(
      '重新录制',
      name: 'repeatRecord',
      desc: '',
      args: [],
    );
  }

  /// `删除录音`
  String get deleteRecord {
    return Intl.message(
      '删除录音',
      name: 'deleteRecord',
      desc: '',
      args: [],
    );
  }

  /// `随便说说，唱一首歌或读一段话`
  String get readParagraph {
    return Intl.message(
      '随便说说，唱一首歌或读一段话',
      name: 'readParagraph',
      desc: '',
      args: [],
    );
  }

  /// `换一段`
  String get changeParagraph {
    return Intl.message(
      '换一段',
      name: 'changeParagraph',
      desc: '',
      args: [],
    );
  }

  /// `点击开始录音`
  String get startRecord {
    return Intl.message(
      '点击开始录音',
      name: 'startRecord',
      desc: '',
      args: [],
    );
  }

  /// `正在录音...`
  String get recording {
    return Intl.message(
      '正在录音...',
      name: 'recording',
      desc: '',
      args: [],
    );
  }

  /// `重录`
  String get recordAgain {
    return Intl.message(
      '重录',
      name: 'recordAgain',
      desc: '',
      args: [],
    );
  }

  /// `录音不能低于5s哦~`
  String get recordMinTip {
    return Intl.message(
      '录音不能低于5s哦~',
      name: 'recordMinTip',
      desc: '',
      args: [],
    );
  }

  /// `请输入关键词`
  String get audioBoxSearchPlaceholder {
    return Intl.message(
      '请输入关键词',
      name: 'audioBoxSearchPlaceholder',
      desc: '',
      args: [],
    );
  }

  /// `您未修改任何资料~`
  String get userInfoNoEditTip {
    return Intl.message(
      '您未修改任何资料~',
      name: 'userInfoNoEditTip',
      desc: '',
      args: [],
    );
  }

  /// `保存成功！`
  String get saveSuccess {
    return Intl.message(
      '保存成功！',
      name: 'saveSuccess',
      desc: '',
      args: [],
    );
  }

  /// `关于我`
  String get aboutMe {
    return Intl.message(
      '关于我',
      name: 'aboutMe',
      desc: '',
      args: [],
    );
  }

  /// `动态`
  String get dynamic {
    return Intl.message(
      '动态',
      name: 'dynamic',
      desc: '',
      args: [],
    );
  }

  /// `标签`
  String get label {
    return Intl.message(
      '标签',
      name: 'label',
      desc: '',
      args: [],
    );
  }

  /// `我的标签`
  String get myLabels {
    return Intl.message(
      '我的标签',
      name: 'myLabels',
      desc: '',
      args: [],
    );
  }

  /// `我的卡盒`
  String get myCardBox {
    return Intl.message(
      '我的卡盒',
      name: 'myCardBox',
      desc: '',
      args: [],
    );
  }

  /// `添加标签`
  String get addLabel {
    return Intl.message(
      '添加标签',
      name: 'addLabel',
      desc: '',
      args: [],
    );
  }

  /// `标签名称`
  String get labelName {
    return Intl.message(
      '标签名称',
      name: 'labelName',
      desc: '',
      args: [],
    );
  }

  /// `如：王者荣耀、星座`
  String get labelNameTip {
    return Intl.message(
      '如：王者荣耀、星座',
      name: 'labelNameTip',
      desc: '',
      args: [],
    );
  }

  /// `可以从区服、段位、分录、游戏风格等来描述`
  String get labelTextTip {
    return Intl.message(
      '可以从区服、段位、分录、游戏风格等来描述',
      name: 'labelTextTip',
      desc: '',
      args: [],
    );
  }

  /// `游戏截图`
  String get gameScreenShot {
    return Intl.message(
      '游戏截图',
      name: 'gameScreenShot',
      desc: '',
      args: [],
    );
  }

  /// `点击上传`
  String get uploadImage {
    return Intl.message(
      '点击上传',
      name: 'uploadImage',
      desc: '',
      args: [],
    );
  }

  /// `编辑标签`
  String get editLabel {
    return Intl.message(
      '编辑标签',
      name: 'editLabel',
      desc: '',
      args: [],
    );
  }

  /// `身份卡`
  String get idCard {
    return Intl.message(
      '身份卡',
      name: 'idCard',
      desc: '',
      args: [],
    );
  }

  /// `添加卡片`
  String get addCard {
    return Intl.message(
      '添加卡片',
      name: 'addCard',
      desc: '',
      args: [],
    );
  }

  /// `区服`
  String get serverName {
    return Intl.message(
      '区服',
      name: 'serverName',
      desc: '',
      args: [],
    );
  }

  /// `添加身份卡`
  String get addIDCard {
    return Intl.message(
      '添加身份卡',
      name: 'addIDCard',
      desc: '',
      args: [],
    );
  }

  /// `编辑身份卡`
  String get editIDCard {
    return Intl.message(
      '编辑身份卡',
      name: 'editIDCard',
      desc: '',
      args: [],
    );
  }

  /// `证件领域`
  String get idCardField {
    return Intl.message(
      '证件领域',
      name: 'idCardField',
      desc: '',
      args: [],
    );
  }

  /// `该身份卡信息未公开~`
  String get cardInfoUnOpen {
    return Intl.message(
      '该身份卡信息未公开~',
      name: 'cardInfoUnOpen',
      desc: '',
      args: [],
    );
  }

  /// `公开`
  String get open {
    return Intl.message(
      '公开',
      name: 'open',
      desc: '',
      args: [],
    );
  }

  /// `确定`
  String get ok {
    return Intl.message(
      '确定',
      name: 'ok',
      desc: '',
      args: [],
    );
  }

  /// `晒欧卡`
  String get goodCard {
    return Intl.message(
      '晒欧卡',
      name: 'goodCard',
      desc: '',
      args: [],
    );
  }

  /// `穿搭卡`
  String get dressCard {
    return Intl.message(
      '穿搭卡',
      name: 'dressCard',
      desc: '',
      args: [],
    );
  }

  /// `高光卡`
  String get strengthCard {
    return Intl.message(
      '高光卡',
      name: 'strengthCard',
      desc: '',
      args: [],
    );
  }

  /// `懂`
  String get know {
    return Intl.message(
      '懂',
      name: 'know',
      desc: '',
      args: [],
    );
  }

  /// `已阅`
  String get read {
    return Intl.message(
      '已阅',
      name: 'read',
      desc: '',
      args: [],
    );
  }

  /// `确定要删除吗？`
  String get dialogCommonDeleteTitle {
    return Intl.message(
      '确定要删除吗？',
      name: 'dialogCommonDeleteTitle',
      desc: '',
      args: [],
    );
  }

  /// `添加`
  String get add {
    return Intl.message(
      '添加',
      name: 'add',
      desc: '',
      args: [],
    );
  }

  /// `图片名称`
  String get picName {
    return Intl.message(
      '图片名称',
      name: 'picName',
      desc: '',
      args: [],
    );
  }

  /// `（如：王者荣耀）`
  String get picNameTip {
    return Intl.message(
      '（如：王者荣耀）',
      name: 'picNameTip',
      desc: '',
      args: [],
    );
  }

  /// `截图`
  String get screenshot {
    return Intl.message(
      '截图',
      name: 'screenshot',
      desc: '',
      args: [],
    );
  }

  /// `他的`
  String get his {
    return Intl.message(
      '他的',
      name: 'his',
      desc: '',
      args: [],
    );
  }

  /// `她的`
  String get hers {
    return Intl.message(
      '她的',
      name: 'hers',
      desc: '',
      args: [],
    );
  }

  /// `他`
  String get he {
    return Intl.message(
      '他',
      name: 'he',
      desc: '',
      args: [],
    );
  }

  /// `她`
  String get her {
    return Intl.message(
      '她',
      name: 'her',
      desc: '',
      args: [],
    );
  }

  /// `关于`
  String get about {
    return Intl.message(
      '关于',
      name: 'about',
      desc: '',
      args: [],
    );
  }

  /// `请填写身份卡领域`
  String get addCardEmptyTip {
    return Intl.message(
      '请填写身份卡领域',
      name: 'addCardEmptyTip',
      desc: '',
      args: [],
    );
  }

  /// `请填写图片名称`
  String get addOtherCardNameEmptyTip {
    return Intl.message(
      '请填写图片名称',
      name: 'addOtherCardNameEmptyTip',
      desc: '',
      args: [],
    );
  }

  /// `请上传图片`
  String get addOtherCardImageEmptyTip {
    return Intl.message(
      '请上传图片',
      name: 'addOtherCardImageEmptyTip',
      desc: '',
      args: [],
    );
  }

  /// `好友`
  String get friend {
    return Intl.message(
      '好友',
      name: 'friend',
      desc: '',
      args: [],
    );
  }

  /// `附近`
  String get nearby {
    return Intl.message(
      '附近',
      name: 'nearby',
      desc: '',
      args: [],
    );
  }

  /// `已签到`
  String get signedIn {
    return Intl.message(
      '已签到',
      name: 'signedIn',
      desc: '',
      args: [],
    );
  }

  /// `第{n}天`
  String signInDayIndex(Object n) {
    return Intl.message(
      '第$n天',
      name: 'signInDayIndex',
      desc: '',
      args: [n],
    );
  }

  /// `概率获得其他超值福利`
  String get signInRandomRewardTip {
    return Intl.message(
      '概率获得其他超值福利',
      name: 'signInRandomRewardTip',
      desc: '',
      args: [],
    );
  }

  /// `完成更多任务，可以获取丰富的奖励哦`
  String get signSuccessTaskTip {
    return Intl.message(
      '完成更多任务，可以获取丰富的奖励哦',
      name: 'signSuccessTaskTip',
      desc: '',
      args: [],
    );
  }

  /// `恭喜获得额外奖励`
  String get extraRewardTip {
    return Intl.message(
      '恭喜获得额外奖励',
      name: 'extraRewardTip',
      desc: '',
      args: [],
    );
  }

  /// `我知道了`
  String get known {
    return Intl.message(
      '我知道了',
      name: 'known',
      desc: '',
      args: [],
    );
  }

  /// `天`
  String get day {
    return Intl.message(
      '天',
      name: 'day',
      desc: '',
      args: [],
    );
  }

  /// `前`
  String get before {
    return Intl.message(
      '前',
      name: 'before',
      desc: '',
      args: [],
    );
  }

  /// `删除`
  String get delete {
    return Intl.message(
      '删除',
      name: 'delete',
      desc: '',
      args: [],
    );
  }

  /// `置顶`
  String get top {
    return Intl.message(
      '置顶',
      name: 'top',
      desc: '',
      args: [],
    );
  }

  /// `仅自己可见`
  String get readOnlyMySelf {
    return Intl.message(
      '仅自己可见',
      name: 'readOnlyMySelf',
      desc: '',
      args: [],
    );
  }

  /// `展开`
  String get unfold {
    return Intl.message(
      '展开',
      name: 'unfold',
      desc: '',
      args: [],
    );
  }

  /// `发布`
  String get publish {
    return Intl.message(
      '发布',
      name: 'publish',
      desc: '',
      args: [],
    );
  }

  /// `记录生活，分享美好时刻~`
  String get publishPlaceholder {
    return Intl.message(
      '记录生活，分享美好时刻~',
      name: 'publishPlaceholder',
      desc: '',
      args: [],
    );
  }

  /// `还可输入{n}个字`
  String leftInputWord(Object n) {
    return Intl.message(
      '还可输入$n个字',
      name: 'leftInputWord',
      desc: '',
      args: [n],
    );
  }

  /// `# 添加炒话题`
  String get addTopicTip {
    return Intl.message(
      '# 添加炒话题',
      name: 'addTopicTip',
      desc: '',
      args: [],
    );
  }

  /// `发布成功`
  String get publishSuccess {
    return Intl.message(
      '发布成功',
      name: 'publishSuccess',
      desc: '',
      args: [],
    );
  }

  /// `消息`
  String get message {
    return Intl.message(
      '消息',
      name: 'message',
      desc: '',
      args: [],
    );
  }

  /// `我的`
  String get mine {
    return Intl.message(
      '我的',
      name: 'mine',
      desc: '',
      args: [],
    );
  }

  /// `搭圈详情`
  String get postDetail {
    return Intl.message(
      '搭圈详情',
      name: 'postDetail',
      desc: '',
      args: [],
    );
  }

  /// `该动态已不可见~`
  String get postDetailNoView {
    return Intl.message(
      '该动态已不可见~',
      name: 'postDetailNoView',
      desc: '',
      args: [],
    );
  }

  /// `举报`
  String get report {
    return Intl.message(
      '举报',
      name: 'report',
      desc: '',
      args: [],
    );
  }

  /// `文明发言哦~`
  String get sendCommentPlaceholder {
    return Intl.message(
      '文明发言哦~',
      name: 'sendCommentPlaceholder',
      desc: '',
      args: [],
    );
  }

  /// `暂时还没有评论哦，去发布一个有趣的评论吧~`
  String get commentEmptyTip {
    return Intl.message(
      '暂时还没有评论哦，去发布一个有趣的评论吧~',
      name: 'commentEmptyTip',
      desc: '',
      args: [],
    );
  }

  /// `全部评论`
  String get allComments {
    return Intl.message(
      '全部评论',
      name: 'allComments',
      desc: '',
      args: [],
    );
  }

  /// `回复`
  String get reply {
    return Intl.message(
      '回复',
      name: 'reply',
      desc: '',
      args: [],
    );
  }

  /// `作者`
  String get author {
    return Intl.message(
      '作者',
      name: 'author',
      desc: '',
      args: [],
    );
  }

  /// `共{n}条回复`
  String totalCountComment(Object n) {
    return Intl.message(
      '共$n条回复',
      name: 'totalCountComment',
      desc: '',
      args: [n],
    );
  }

  /// `全部回复`
  String get allReplies {
    return Intl.message(
      '全部回复',
      name: 'allReplies',
      desc: '',
      args: [],
    );
  }

  /// `完善自己标签，匹配效果更佳哦~`
  String get matchBottomTip {
    return Intl.message(
      '完善自己标签，匹配效果更佳哦~',
      name: 'matchBottomTip',
      desc: '',
      args: [],
    );
  }

  /// `开始匹配`
  String get startMatch {
    return Intl.message(
      '开始匹配',
      name: 'startMatch',
      desc: '',
      args: [],
    );
  }

  /// `我希望和对方结为：`
  String get matchRelationTip {
    return Intl.message(
      '我希望和对方结为：',
      name: 'matchRelationTip',
      desc: '',
      args: [],
    );
  }

  /// `我希望搭子必须有：`
  String get matchLabels {
    return Intl.message(
      '我希望搭子必须有：',
      name: 'matchLabels',
      desc: '',
      args: [],
    );
  }

  /// `有这些更好：`
  String get matchOtherLabels {
    return Intl.message(
      '有这些更好：',
      name: 'matchOtherLabels',
      desc: '',
      args: [],
    );
  }

  /// `纯搭`
  String get relation1 {
    return Intl.message(
      '纯搭',
      name: 'relation1',
      desc: '',
      args: [],
    );
  }

  /// `浅搭`
  String get relation2 {
    return Intl.message(
      '浅搭',
      name: 'relation2',
      desc: '',
      args: [],
    );
  }

  /// `浅搭(随缘)`
  String get relation3 {
    return Intl.message(
      '浅搭(随缘)',
      name: 'relation3',
      desc: '',
      args: [],
    );
  }

  /// `深搭`
  String get relation4 {
    return Intl.message(
      '深搭',
      name: 'relation4',
      desc: '',
      args: [],
    );
  }

  /// `添加子标签`
  String get addSubLabel {
    return Intl.message(
      '添加子标签',
      name: 'addSubLabel',
      desc: '',
      args: [],
    );
  }

  /// `我要寻找小队`
  String get matchAssembleTitle {
    return Intl.message(
      '我要寻找小队',
      name: 'matchAssembleTitle',
      desc: '',
      args: [],
    );
  }

  /// `我需要的小队标签是：`
  String get matchAssembleLabelTitle {
    return Intl.message(
      '我需要的小队标签是：',
      name: 'matchAssembleLabelTitle',
      desc: '',
      args: [],
    );
  }

  /// `(如：永劫无间、三排、双排)`
  String get matchAssembleLabelTips {
    return Intl.message(
      '(如：永劫无间、三排、双排)',
      name: 'matchAssembleLabelTips',
      desc: '',
      args: [],
    );
  }

  /// `创建小队`
  String get createTeam {
    return Intl.message(
      '创建小队',
      name: 'createTeam',
      desc: '',
      args: [],
    );
  }

  /// `加入小队`
  String get joinTeam {
    return Intl.message(
      '加入小队',
      name: 'joinTeam',
      desc: '',
      args: [],
    );
  }

  /// `需要人数`
  String get needPeopleNumber {
    return Intl.message(
      '需要人数',
      name: 'needPeopleNumber',
      desc: '',
      args: [],
    );
  }

  /// `请输入集结处名字`
  String get createAssembleInputTip {
    return Intl.message(
      '请输入集结处名字',
      name: 'createAssembleInputTip',
      desc: '',
      args: [],
    );
  }

  /// `正在匹配中`
  String get inMatching {
    return Intl.message(
      '正在匹配中',
      name: 'inMatching',
      desc: '',
      args: [],
    );
  }

  /// `暂时未找到精准匹配，\n去集结处大厅看看吧~`
  String get matchTeamEmptyTip {
    return Intl.message(
      '暂时未找到精准匹配，\n去集结处大厅看看吧~',
      name: 'matchTeamEmptyTip',
      desc: '',
      args: [],
    );
  }

  /// `请输入集结处号码或名字`
  String get assembleSearchTip {
    return Intl.message(
      '请输入集结处号码或名字',
      name: 'assembleSearchTip',
      desc: '',
      args: [],
    );
  }

  /// `搜索`
  String get search {
    return Intl.message(
      '搜索',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `已锁`
  String get locked {
    return Intl.message(
      '已锁',
      name: 'locked',
      desc: '',
      args: [],
    );
  }

  /// `已满`
  String get fulled {
    return Intl.message(
      '已满',
      name: 'fulled',
      desc: '',
      args: [],
    );
  }

  /// `加入`
  String get join {
    return Intl.message(
      '加入',
      name: 'join',
      desc: '',
      args: [],
    );
  }

  /// `输入密码`
  String get inputPwd {
    return Intl.message(
      '输入密码',
      name: 'inputPwd',
      desc: '',
      args: [],
    );
  }

  /// `上锁密码`
  String get lockPwd {
    return Intl.message(
      '上锁密码',
      name: 'lockPwd',
      desc: '',
      args: [],
    );
  }

  /// `请设置邀请密码`
  String get setTeamPwd {
    return Intl.message(
      '请设置邀请密码',
      name: 'setTeamPwd',
      desc: '',
      args: [],
    );
  }

  /// `请输入邀请密码`
  String get inputTeamPwd {
    return Intl.message(
      '请输入邀请密码',
      name: 'inputTeamPwd',
      desc: '',
      args: [],
    );
  }

  /// `暂无标签哦~`
  String get labelsEmptyTip {
    return Intl.message(
      '暂无标签哦~',
      name: 'labelsEmptyTip',
      desc: '',
      args: [],
    );
  }

  /// `请输入匹配搭子标签`
  String get matchDadaLabelEmptyAlert {
    return Intl.message(
      '请输入匹配搭子标签',
      name: 'matchDadaLabelEmptyAlert',
      desc: '',
      args: [],
    );
  }

  /// `不限`
  String get unlimited {
    return Intl.message(
      '不限',
      name: 'unlimited',
      desc: '',
      args: [],
    );
  }

  /// `未找到对应集结处，请检查输入的集结处号码或名称是否正确`
  String get searchTeamNoResultTip {
    return Intl.message(
      '未找到对应集结处，请检查输入的集结处号码或名称是否正确',
      name: 'searchTeamNoResultTip',
      desc: '',
      args: [],
    );
  }

  /// `离开`
  String get leave {
    return Intl.message(
      '离开',
      name: 'leave',
      desc: '',
      args: [],
    );
  }

  /// `保存语音`
  String get saveAudio {
    return Intl.message(
      '保存语音',
      name: 'saveAudio',
      desc: '',
      args: [],
    );
  }

  /// `匹配中`
  String get matching {
    return Intl.message(
      '匹配中',
      name: 'matching',
      desc: '',
      args: [],
    );
  }

  /// `暂停匹配`
  String get matchPaused {
    return Intl.message(
      '暂停匹配',
      name: 'matchPaused',
      desc: '',
      args: [],
    );
  }

  /// `上锁`
  String get lock {
    return Intl.message(
      '上锁',
      name: 'lock',
      desc: '',
      args: [],
    );
  }

  /// `解锁`
  String get unLock {
    return Intl.message(
      '解锁',
      name: 'unLock',
      desc: '',
      args: [],
    );
  }

  /// `请填写备注`
  String get inputRemark {
    return Intl.message(
      '请填写备注',
      name: 'inputRemark',
      desc: '',
      args: [],
    );
  }

  /// `选择成员`
  String get selectMember {
    return Intl.message(
      '选择成员',
      name: 'selectMember',
      desc: '',
      args: [],
    );
  }

  /// `完成`
  String get finish {
    return Intl.message(
      '完成',
      name: 'finish',
      desc: '',
      args: [],
    );
  }

  /// `集结队`
  String get assembleTeam {
    return Intl.message(
      '集结队',
      name: 'assembleTeam',
      desc: '',
      args: [],
    );
  }

  /// `备注`
  String get remark {
    return Intl.message(
      '备注',
      name: 'remark',
      desc: '',
      args: [],
    );
  }

  /// `邀请已发送`
  String get inviteAlreadySend {
    return Intl.message(
      '邀请已发送',
      name: 'inviteAlreadySend',
      desc: '',
      args: [],
    );
  }

  /// `邀请发送失败，请稍后再试`
  String get inviteFailed {
    return Intl.message(
      '邀请发送失败，请稍后再试',
      name: 'inviteFailed',
      desc: '',
      args: [],
    );
  }

  /// `加入小队失败，请稍后再试！`
  String get joinTeamFailed {
    return Intl.message(
      '加入小队失败，请稍后再试！',
      name: 'joinTeamFailed',
      desc: '',
      args: [],
    );
  }

  /// `你已被踢出小队`
  String get beKitOutOfTeam {
    return Intl.message(
      '你已被踢出小队',
      name: 'beKitOutOfTeam',
      desc: '',
      args: [],
    );
  }

  /// `您已在其他小队中，请先退出其他小队`
  String get alreadyInOtherTeam {
    return Intl.message(
      '您已在其他小队中，请先退出其他小队',
      name: 'alreadyInOtherTeam',
      desc: '',
      args: [],
    );
  }

  /// `最近`
  String get recently {
    return Intl.message(
      '最近',
      name: 'recently',
      desc: '',
      args: [],
    );
  }

  /// `通讯录`
  String get contacts {
    return Intl.message(
      '通讯录',
      name: 'contacts',
      desc: '',
      args: [],
    );
  }

  /// `同城`
  String get sameCity {
    return Intl.message(
      '同城',
      name: 'sameCity',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'zh'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
