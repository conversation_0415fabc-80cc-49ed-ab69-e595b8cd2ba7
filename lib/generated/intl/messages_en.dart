// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(n) => "还可输入${n}个字";

  static String m1(n) => "第${n}天";

  static String m2(n) => "共${n}条回复";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("关于"),
        "aboutMe": MessageLookupByLibrary.simpleMessage("关于我"),
        "add": MessageLookupByLibrary.simpleMessage("添加"),
        "addCard": MessageLookupByLibrary.simpleMessage("添加卡片"),
        "addCardEmptyTip": MessageLookupByLibrary.simpleMessage("请填写身份卡领域"),
        "addIDCard": MessageLookupByLibrary.simpleMessage("添加身份卡"),
        "addLabel": MessageLookupByLibrary.simpleMessage("添加标签"),
        "addOtherCardImageEmptyTip":
            MessageLookupByLibrary.simpleMessage("请上传图片"),
        "addOtherCardNameEmptyTip":
            MessageLookupByLibrary.simpleMessage("请填写图片名称"),
        "addSubLabel": MessageLookupByLibrary.simpleMessage("添加子标签"),
        "addTopicTip": MessageLookupByLibrary.simpleMessage("# 添加炒话题"),
        "album": MessageLookupByLibrary.simpleMessage("相册"),
        "alive": MessageLookupByLibrary.simpleMessage("热情"),
        "allComments": MessageLookupByLibrary.simpleMessage("全部评论"),
        "allReplies": MessageLookupByLibrary.simpleMessage("全部回复"),
        "alreadyInOtherTeam":
            MessageLookupByLibrary.simpleMessage("您已在其他小队中，请先退出其他小队"),
        "appName": MessageLookupByLibrary.simpleMessage("搭搭"),
        "assembleSearchTip":
            MessageLookupByLibrary.simpleMessage("请输入集结处号码或名字"),
        "assembleTeam": MessageLookupByLibrary.simpleMessage("集结队"),
        "audioBox": MessageLookupByLibrary.simpleMessage("语音盒子"),
        "audioBoxSearchPlaceholder":
            MessageLookupByLibrary.simpleMessage("请输入关键词"),
        "audioSignature": MessageLookupByLibrary.simpleMessage("语音签名"),
        "author": MessageLookupByLibrary.simpleMessage("作者"),
        "avatar": MessageLookupByLibrary.simpleMessage("头像"),
        "beKitOutOfTeam": MessageLookupByLibrary.simpleMessage("你已被踢出小队"),
        "before": MessageLookupByLibrary.simpleMessage("前"),
        "birthday": MessageLookupByLibrary.simpleMessage("生日"),
        "birthdayPlaceholder":
            MessageLookupByLibrary.simpleMessage("请选择你的出生日期"),
        "boy": MessageLookupByLibrary.simpleMessage("男生"),
        "camera": MessageLookupByLibrary.simpleMessage("相机"),
        "cancel": MessageLookupByLibrary.simpleMessage("取消"),
        "cardBox": MessageLookupByLibrary.simpleMessage("卡盒"),
        "cardInfoUnOpen": MessageLookupByLibrary.simpleMessage("该身份卡信息未公开~"),
        "changeParagraph": MessageLookupByLibrary.simpleMessage("换一段"),
        "chatTabTitle": MessageLookupByLibrary.simpleMessage("聊天"),
        "checkPolicyToast": MessageLookupByLibrary.simpleMessage("请先阅读并同意协议"),
        "circleTabTitle": MessageLookupByLibrary.simpleMessage("搭圈"),
        "commentEmptyTip":
            MessageLookupByLibrary.simpleMessage("暂时还没有评论哦，去发布一个有趣的评论吧~"),
        "contacts": MessageLookupByLibrary.simpleMessage("通讯录"),
        "cool": MessageLookupByLibrary.simpleMessage("冰冷"),
        "createAssembleInputTip":
            MessageLookupByLibrary.simpleMessage("请输入集结处名字"),
        "createTeam": MessageLookupByLibrary.simpleMessage("创建小队"),
        "dadaCoin": MessageLookupByLibrary.simpleMessage("搭币"),
        "dadaID": MessageLookupByLibrary.simpleMessage("搭搭号"),
        "dadaTabTitle": MessageLookupByLibrary.simpleMessage("搭搭"),
        "day": MessageLookupByLibrary.simpleMessage("天"),
        "delete": MessageLookupByLibrary.simpleMessage("删除"),
        "deleteRecord": MessageLookupByLibrary.simpleMessage("删除录音"),
        "dialogCommonDeleteTitle":
            MessageLookupByLibrary.simpleMessage("确定要删除吗？"),
        "dressCard": MessageLookupByLibrary.simpleMessage("穿搭卡"),
        "dynamic": MessageLookupByLibrary.simpleMessage("动态"),
        "editAudioSignature": MessageLookupByLibrary.simpleMessage("编辑语音签名"),
        "editIDCard": MessageLookupByLibrary.simpleMessage("编辑身份卡"),
        "editLabel": MessageLookupByLibrary.simpleMessage("编辑标签"),
        "editUserInfo": MessageLookupByLibrary.simpleMessage("编辑资料"),
        "emptyData": MessageLookupByLibrary.simpleMessage("暂无数据~"),
        "exchangeMall": MessageLookupByLibrary.simpleMessage("兑换商城"),
        "extraRewardTip": MessageLookupByLibrary.simpleMessage("恭喜获得额外奖励"),
        "fairSubTitle": MessageLookupByLibrary.simpleMessage("化身村口老太，\n无压力畅聊！"),
        "fetchCodeBtnTitle": MessageLookupByLibrary.simpleMessage("获取验证码"),
        "finish": MessageLookupByLibrary.simpleMessage("完成"),
        "friend": MessageLookupByLibrary.simpleMessage("好友"),
        "fulled": MessageLookupByLibrary.simpleMessage("已满"),
        "gameScreenShot": MessageLookupByLibrary.simpleMessage("游戏截图"),
        "girl": MessageLookupByLibrary.simpleMessage("女生"),
        "goFair": MessageLookupByLibrary.simpleMessage("去市集"),
        "goTeapot": MessageLookupByLibrary.simpleMessage("去茶壶"),
        "goodCard": MessageLookupByLibrary.simpleMessage("晒欧卡"),
        "groupOwner": MessageLookupByLibrary.simpleMessage("群长"),
        "he": MessageLookupByLibrary.simpleMessage("他"),
        "her": MessageLookupByLibrary.simpleMessage("她"),
        "hers": MessageLookupByLibrary.simpleMessage("她的"),
        "his": MessageLookupByLibrary.simpleMessage("他的"),
        "homeTabTitle": MessageLookupByLibrary.simpleMessage("凑热闹"),
        "hometown": MessageLookupByLibrary.simpleMessage("家乡"),
        "hospitable": MessageLookupByLibrary.simpleMessage("非常热情"),
        "idCard": MessageLookupByLibrary.simpleMessage("身份卡"),
        "idCardField": MessageLookupByLibrary.simpleMessage("证件领域"),
        "inMatching": MessageLookupByLibrary.simpleMessage("正在匹配中"),
        "input": MessageLookupByLibrary.simpleMessage("请输入"),
        "inputAvatarTip": MessageLookupByLibrary.simpleMessage("请上传头像"),
        "inputCodeTip": MessageLookupByLibrary.simpleMessage("请输入验证码"),
        "inputPhoneErrorTip": MessageLookupByLibrary.simpleMessage("请输入正确的手机号"),
        "inputPhoneNumberTip": MessageLookupByLibrary.simpleMessage("请输入手机号"),
        "inputPwd": MessageLookupByLibrary.simpleMessage("输入密码"),
        "inputRemark": MessageLookupByLibrary.simpleMessage("请填写备注"),
        "inputTeamPwd": MessageLookupByLibrary.simpleMessage("请输入邀请密码"),
        "inviteAlreadySend": MessageLookupByLibrary.simpleMessage("邀请已发送"),
        "inviteFailed": MessageLookupByLibrary.simpleMessage("邀请发送失败，请稍后再试"),
        "ipAddress": MessageLookupByLibrary.simpleMessage("IP属地"),
        "join": MessageLookupByLibrary.simpleMessage("加入"),
        "joinTeam": MessageLookupByLibrary.simpleMessage("加入小队"),
        "joinTeamFailed": MessageLookupByLibrary.simpleMessage("加入小队失败，请稍后再试！"),
        "know": MessageLookupByLibrary.simpleMessage("懂"),
        "known": MessageLookupByLibrary.simpleMessage("我知道了"),
        "label": MessageLookupByLibrary.simpleMessage("标签"),
        "labelName": MessageLookupByLibrary.simpleMessage("标签名称"),
        "labelNameTip": MessageLookupByLibrary.simpleMessage("如：王者荣耀、星座"),
        "labelTextTip":
            MessageLookupByLibrary.simpleMessage("可以从区服、段位、分录、游戏风格等来描述"),
        "labelsEmptyTip": MessageLookupByLibrary.simpleMessage("暂无标签哦~"),
        "leave": MessageLookupByLibrary.simpleMessage("离开"),
        "leftInputWord": m0,
        "loading": MessageLookupByLibrary.simpleMessage("加载中..."),
        "lock": MessageLookupByLibrary.simpleMessage("上锁"),
        "lockPwd": MessageLookupByLibrary.simpleMessage("上锁密码"),
        "locked": MessageLookupByLibrary.simpleMessage("已锁"),
        "loginSubTitle": MessageLookupByLibrary.simpleMessage("未注册手机号将自动注册"),
        "loginTitle": MessageLookupByLibrary.simpleMessage("hi~ 请填写你的手机号"),
        "man": MessageLookupByLibrary.simpleMessage("男"),
        "matchAssembleLabelTips":
            MessageLookupByLibrary.simpleMessage("(如：永劫无间、三排、双排)"),
        "matchAssembleLabelTitle":
            MessageLookupByLibrary.simpleMessage("我需要的小队标签是："),
        "matchAssembleTitle": MessageLookupByLibrary.simpleMessage("我要寻找小队"),
        "matchBottomTip":
            MessageLookupByLibrary.simpleMessage("完善自己标签，匹配效果更佳哦~"),
        "matchDadaLabelEmptyAlert":
            MessageLookupByLibrary.simpleMessage("请输入匹配搭子标签"),
        "matchLabels": MessageLookupByLibrary.simpleMessage("我希望搭子必须有："),
        "matchOtherLabels": MessageLookupByLibrary.simpleMessage("有这些更好："),
        "matchPaused": MessageLookupByLibrary.simpleMessage("暂停匹配"),
        "matchRelationTip": MessageLookupByLibrary.simpleMessage("我希望和对方结为："),
        "matchTeamEmptyTip":
            MessageLookupByLibrary.simpleMessage("暂时未找到精准匹配，\n去集结处大厅看看吧~"),
        "matching": MessageLookupByLibrary.simpleMessage("匹配中"),
        "message": MessageLookupByLibrary.simpleMessage("消息"),
        "mine": MessageLookupByLibrary.simpleMessage("我的"),
        "mineTabTitle": MessageLookupByLibrary.simpleMessage("个人"),
        "moderate": MessageLookupByLibrary.simpleMessage("适中"),
        "monthCard": MessageLookupByLibrary.simpleMessage("月卡"),
        "myAccount": MessageLookupByLibrary.simpleMessage("我的账号"),
        "myAchievement": MessageLookupByLibrary.simpleMessage("我的成就"),
        "myCardBox": MessageLookupByLibrary.simpleMessage("我的卡盒"),
        "myLabels": MessageLookupByLibrary.simpleMessage("我的标签"),
        "myPage": MessageLookupByLibrary.simpleMessage("主页"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("个人隐私"),
        "myWallet": MessageLookupByLibrary.simpleMessage("我的钱包"),
        "nearby": MessageLookupByLibrary.simpleMessage("附近"),
        "needPeopleNumber": MessageLookupByLibrary.simpleMessage("需要人数"),
        "next": MessageLookupByLibrary.simpleMessage("下一步"),
        "nickname": MessageLookupByLibrary.simpleMessage("昵称"),
        "nicknamePlaceholder": MessageLookupByLibrary.simpleMessage("请输入昵称"),
        "ok": MessageLookupByLibrary.simpleMessage("确定"),
        "onHiChat": MessageLookupByLibrary.simpleMessage("嗨聊"),
        "online": MessageLookupByLibrary.simpleMessage("在线"),
        "open": MessageLookupByLibrary.simpleMessage("公开"),
        "people": MessageLookupByLibrary.simpleMessage("人"),
        "phoneAreaCode": MessageLookupByLibrary.simpleMessage("+86"),
        "phoneUnUsedTip": MessageLookupByLibrary.simpleMessage("手机无法使用"),
        "picName": MessageLookupByLibrary.simpleMessage("图片名称"),
        "picNameTip": MessageLookupByLibrary.simpleMessage("（如：王者荣耀）"),
        "pleaseSelect": MessageLookupByLibrary.simpleMessage("请选择"),
        "postDetail": MessageLookupByLibrary.simpleMessage("搭圈详情"),
        "postDetailNoView": MessageLookupByLibrary.simpleMessage("该动态已不可见~"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("隐私政策"),
        "publish": MessageLookupByLibrary.simpleMessage("发布"),
        "publishPlaceholder":
            MessageLookupByLibrary.simpleMessage("记录生活，分享美好时刻~"),
        "publishSuccess": MessageLookupByLibrary.simpleMessage("发布成功"),
        "random": MessageLookupByLibrary.simpleMessage("随机"),
        "read": MessageLookupByLibrary.simpleMessage("已阅"),
        "readOnlyMySelf": MessageLookupByLibrary.simpleMessage("仅自己可见"),
        "readParagraph": MessageLookupByLibrary.simpleMessage("随便说说，唱一首歌或读一段话"),
        "realNameAuth": MessageLookupByLibrary.simpleMessage("实名认证"),
        "recently": MessageLookupByLibrary.simpleMessage("最近"),
        "recommend": MessageLookupByLibrary.simpleMessage("推荐"),
        "recordAgain": MessageLookupByLibrary.simpleMessage("重录"),
        "recordMinTip": MessageLookupByLibrary.simpleMessage("录音不能低于5s哦~"),
        "recording": MessageLookupByLibrary.simpleMessage("正在录音..."),
        "registerTitle":
            MessageLookupByLibrary.simpleMessage("完善你的资料\n开始搭搭之旅吧~"),
        "relation1": MessageLookupByLibrary.simpleMessage("纯搭"),
        "relation2": MessageLookupByLibrary.simpleMessage("浅搭"),
        "relation3": MessageLookupByLibrary.simpleMessage("浅搭(随缘)"),
        "relation4": MessageLookupByLibrary.simpleMessage("深搭"),
        "remark": MessageLookupByLibrary.simpleMessage("备注"),
        "repeatRecord": MessageLookupByLibrary.simpleMessage("重新录制"),
        "reply": MessageLookupByLibrary.simpleMessage("回复"),
        "report": MessageLookupByLibrary.simpleMessage("举报"),
        "sameCity": MessageLookupByLibrary.simpleMessage("同城"),
        "save": MessageLookupByLibrary.simpleMessage("保存"),
        "saveAudio": MessageLookupByLibrary.simpleMessage("保存语音"),
        "saveSuccess": MessageLookupByLibrary.simpleMessage("保存成功！"),
        "screenshot": MessageLookupByLibrary.simpleMessage("截图"),
        "search": MessageLookupByLibrary.simpleMessage("搜索"),
        "searchPlaceholder": MessageLookupByLibrary.simpleMessage("请输入关键词"),
        "searchTeamNoResultTip":
            MessageLookupByLibrary.simpleMessage("未找到对应集结处，请检查输入的集结处号码或名称是否正确"),
        "seenMe": MessageLookupByLibrary.simpleMessage("谁看过我"),
        "selectMember": MessageLookupByLibrary.simpleMessage("选择成员"),
        "sendCommentPlaceholder":
            MessageLookupByLibrary.simpleMessage("文明发言哦~"),
        "serverName": MessageLookupByLibrary.simpleMessage("区服"),
        "setTeamPwd": MessageLookupByLibrary.simpleMessage("请设置邀请密码"),
        "settings": MessageLookupByLibrary.simpleMessage("设置"),
        "sex": MessageLookupByLibrary.simpleMessage("性别"),
        "sexChangeDialogText": MessageLookupByLibrary.simpleMessage("确定修改性别为"),
        "sexTip": MessageLookupByLibrary.simpleMessage("注册后不可修改"),
        "sign": MessageLookupByLibrary.simpleMessage("签到"),
        "signInDayIndex": m1,
        "signInRandomRewardTip":
            MessageLookupByLibrary.simpleMessage("概率获得其他超值福利"),
        "signSuccessTaskTip":
            MessageLookupByLibrary.simpleMessage("完成更多任务，可以获取丰富的奖励哦"),
        "signaturePlaceholder":
            MessageLookupByLibrary.simpleMessage("请输入您的个性签名~"),
        "signedIn": MessageLookupByLibrary.simpleMessage("已签到"),
        "sleepTalk": MessageLookupByLibrary.simpleMessage("睡前卧谈会"),
        "slow": MessageLookupByLibrary.simpleMessage("慢热"),
        "socialHabits": MessageLookupByLibrary.simpleMessage("个人社交习惯"),
        "startDada": MessageLookupByLibrary.simpleMessage("开启搭搭"),
        "startMatch": MessageLookupByLibrary.simpleMessage("开始匹配"),
        "startRecord": MessageLookupByLibrary.simpleMessage("点击开始录音"),
        "strengthCard": MessageLookupByLibrary.simpleMessage("高光卡"),
        "sure": MessageLookupByLibrary.simpleMessage("确认"),
        "teapotSubTitle": MessageLookupByLibrary.simpleMessage("随时随地随意聊"),
        "textSignature": MessageLookupByLibrary.simpleMessage("个性签名"),
        "top": MessageLookupByLibrary.simpleMessage("置顶"),
        "totalCountComment": m2,
        "unLock": MessageLookupByLibrary.simpleMessage("解锁"),
        "unSetting": MessageLookupByLibrary.simpleMessage("未设置"),
        "unfold": MessageLookupByLibrary.simpleMessage("展开"),
        "unlimited": MessageLookupByLibrary.simpleMessage("不限"),
        "uploadImage": MessageLookupByLibrary.simpleMessage("点击上传"),
        "userAgreement": MessageLookupByLibrary.simpleMessage("用户协议"),
        "userInfo": MessageLookupByLibrary.simpleMessage("个人信息"),
        "userInfoNoEditTip": MessageLookupByLibrary.simpleMessage("您未修改任何资料~"),
        "vacation": MessageLookupByLibrary.simpleMessage("职业"),
        "welcomeBottomTipSub1": MessageLookupByLibrary.simpleMessage("已阅读并同意 "),
        "welcomeBottomTipSub2": MessageLookupByLibrary.simpleMessage(" 和 "),
        "welcomeBtnTitle": MessageLookupByLibrary.simpleMessage("手机号注册登录"),
        "welcomeTip": MessageLookupByLibrary.simpleMessage(
            "Hi~\n等你很久了\n欢迎来到搭搭！\n这里有很多有趣的人，\n快来加入吧！"),
        "woman": MessageLookupByLibrary.simpleMessage("女")
      };
}
