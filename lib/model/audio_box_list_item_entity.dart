import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/audio_box_list_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/audio_box_list_item_entity.g.dart';

@JsonSerializable()
class AudioBoxListItemEntity {
	String? id;
	@J<PERSON><PERSON>ield(name: "remark")
	String? title;
	@J<PERSON><PERSON>ield(name: "videoUrl")
	String? url;
	String? createDate;
	@J<PERSON><PERSON>ield(name: "userNickname")
	String? username;
	@J<PERSON><PERSON>ield(name: "voiceLength")
	int? duration;

	AudioBoxListItemEntity();

	factory AudioBoxListItemEntity.fromJson(Map<String, dynamic> json) => $AudioBoxListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $AudioBoxListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}