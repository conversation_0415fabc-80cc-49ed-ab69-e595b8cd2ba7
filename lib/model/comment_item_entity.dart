import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/comment_item_entity.g.dart';
import 'dart:convert';
import 'package:dada/model/comment_reply_list_entity.dart';
export 'package:dada/generated/json/comment_item_entity.g.dart';

@JsonSerializable()
class CommentItemEntity {
  String? commentsId;
  String? userId;
  String? postId;
  String? content;
  bool? isAuthor;
  String? createdDate;
  String? commentsType;
  int? likeNo;
  String? nickname;
  String? avatar;
  int? replyNo;
  bool? likeState;
  CommentReplyListEntity? replayList;

  CommentItemEntity();

  factory CommentItemEntity.fromJson(Map<String, dynamic> json) =>
      $CommentItemEntityFromJson(json);

  Map<String, dynamic> toJson() => $CommentItemEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
