import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/group_chat_application_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/group_chat_application_entity.g.dart';

@JsonSerializable()
class GroupChatApplicationEntity {
	String? groupID;
	String? fromUser;
	String? fromUserNickName;
	String? fromUserFaceUrl;
	String? toUser;
	int? handleResult;
	String? requestMsg;
	int? handleStatus;
	String? groupName;
	int? addTime;

	GroupChatApplicationEntity();

	factory GroupChatApplicationEntity.fromJson(Map<String, dynamic> json) => $GroupChatApplicationEntityFromJson(json);

	Map<String, dynamic> toJson() => $GroupChatApplicationEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}