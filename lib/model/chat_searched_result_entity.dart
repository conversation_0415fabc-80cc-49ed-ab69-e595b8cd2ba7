import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_searched_result_entity.g.dart';
import 'package:dada/model/chat_group_info_entity.dart';
import 'dart:convert';

import 'package:dada/model/friend_user_info_entity.dart';

@JsonSerializable()
class ChatSearchedResultEntity {
  List<FriendUserInfoEntity>? searchUsers;
  List<ChatGroupInfoEntity>? searchCrowdChats;

  ChatSearchedResultEntity();

  factory ChatSearchedResultEntity.fromJson(Map<String, dynamic> json) =>
      $ChatSearchedResultEntityFromJson(json);

  Map<String, dynamic> toJson() => $ChatSearchedResultEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
