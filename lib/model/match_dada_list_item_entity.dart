import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/match_dada_list_item_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/user_label_entity.dart';
export 'package:dada/generated/json/match_dada_list_item_entity.g.dart';

@JsonSerializable()
class MatchDadaListItemEntity {
	String? id;
	String? nickname;
	int? sex;
	String? voiceSignature;
	String? avatar;
	String? dadaNo;
	String? birthday;
	String? txtSignature;
	String? hometown;
	String? work;
	int? voiceLength;
	List<UserLabelEntity>? labels;
	int? isAccept;
	int? daState;
	int? age;
	bool? isFriend;

	MatchDadaListItemEntity();

	factory MatchDadaListItemEntity.fromJson(Map<String, dynamic> json) => $MatchDadaListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $MatchDadaListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}