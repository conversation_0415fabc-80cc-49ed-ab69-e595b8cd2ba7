import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/todo_together_detail_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/todo_together_detail_entity.g.dart';

@JsonSerializable()
class TodoTogetherDetailEntity {
	String? createdDate;
	int? stage;
	String? content;
	String? otherUserId;
	String? beforeTaskId;
	int? countCommonTasks;
	TodoTogetherDetailStage6? stage6;
	TodoTogetherDetailStage8? stage8;
	TodoTogetherDetailStage3? stage3;
	TodoTogetherDetailStage2? stage2;
	TodoTogetherDetailStage5? stage5;
	TodoTogetherDetailStage4? stage4;
	TodoTogetherDetailStage1? stage1;

	TodoTogetherDetailEntity();

	factory TodoTogetherDetailEntity.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailEntityFromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage6 {
	TodoTogetherDetailStage6OtherEndMsg? otherEndMsg;
	dynamic isMyEndSelect;
	dynamic isOtherEndSelect;

	TodoTogetherDetailStage6();

	factory TodoTogetherDetailStage6.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage6FromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage6ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage6OtherEndMsg {
	String? id;
	String? userId;
	String? userTaskId;
	String? toUserId;
	String? msg;
	dynamic msgType;
	dynamic msgLength;
	dynamic imgUrl;
	dynamic audioUrl;
	int? type;
	int? isRespond;
	String? createdDate;

	TodoTogetherDetailStage6OtherEndMsg();

	factory TodoTogetherDetailStage6OtherEndMsg.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage6OtherEndMsgFromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage6OtherEndMsgToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage8 {
	dynamic otherPropose;
	dynamic myPropose;

	TodoTogetherDetailStage8();

	factory TodoTogetherDetailStage8.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage8FromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage8ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage3 {
	String? otherImg;
	bool? Continue;
	bool? otherContinueState;

	TodoTogetherDetailStage3();

	factory TodoTogetherDetailStage3.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage3FromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage3ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage2 {
	String? myImg;
	bool? otherState;

	TodoTogetherDetailStage2();

	factory TodoTogetherDetailStage2.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage2FromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage2ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage5 {
	TodoTogetherDetailStage5MyEndMsg? myEndMsg;

	TodoTogetherDetailStage5();

	factory TodoTogetherDetailStage5.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage5FromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage5ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage5MyEndMsg {
	String? id;
	String? userId;
	String? userTaskId;
	String? toUserId;
	String? msg;
	dynamic msgType;
	dynamic msgLength;
	dynamic imgUrl;
	String? audioUrl;
	int? type;
	int? isRespond;
	String? createdDate;

	TodoTogetherDetailStage5MyEndMsg();

	factory TodoTogetherDetailStage5MyEndMsg.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage5MyEndMsgFromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage5MyEndMsgToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage4 {
	bool? isChuoState;
	bool? isOtherChuoResponse;
	int? isOtherComplete;
	bool? isChuoStateResponse;
	int? isMyComplete;
	bool? isOtherChuo;

	TodoTogetherDetailStage4();

	factory TodoTogetherDetailStage4.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage4FromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage4ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TodoTogetherDetailStage1 {
	bool? otherConfirmState;
	int? mySex;
	int? otherSex;
	String? myName;
	String? otherName;
	String? otherAvatar;
	int? myAge;
	String? myAvatar;
	bool? myConfirmState;
	int? otherAge;

	TodoTogetherDetailStage1();

	factory TodoTogetherDetailStage1.fromJson(Map<String, dynamic> json) => $TodoTogetherDetailStage1FromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherDetailStage1ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}