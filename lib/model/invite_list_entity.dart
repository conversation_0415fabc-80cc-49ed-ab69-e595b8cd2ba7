import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/invite_list_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/invite_list_entity.g.dart';

@JsonSerializable()
class InviteListEntity {
	String? inviteCode;
	int? inviteCount;
	List<InviteListInviteRecordDetailsVos>? inviteRecordDetailsVos;

	InviteListEntity();

	factory InviteListEntity.fromJson(Map<String, dynamic> json) => $InviteListEntityFromJson(json);

	Map<String, dynamic> toJson() => $InviteListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InviteListInviteRecordDetailsVos {
	String? inviteDate;
	String? nickname;
	String? avatar;
	List<InviteRecordRewardItem>? inviteReward;

	InviteListInviteRecordDetailsVos();

	factory InviteListInviteRecordDetailsVos.fromJson(Map<String, dynamic> json) => $InviteListInviteRecordDetailsVosFromJson(json);

	Map<String, dynamic> toJson() => $InviteListInviteRecordDetailsVosToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InviteRecordRewardItem {
	String? rewardUrl;
	int? rewardNum;
	String? rewardName;

	InviteRecordRewardItem();

	factory InviteRecordRewardItem.fromJson(Map<String, dynamic> json) => $InviteRecordRewardItemFromJson(json);

	Map<String, dynamic> toJson() => $InviteRecordRewardItemToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}