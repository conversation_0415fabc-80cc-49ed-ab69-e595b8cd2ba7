import 'dart:convert';
import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/pay_info.g.dart';

@JsonSerializable()
class PayInfo {
  String? applePayProductID;
  @JSONField(name: 'orderStr')
  String? alipayOrderString;
  String? wxPayPartnerId;
  String? wxPayPrepayId;
  String? wxPayNonceStr;
  String? wxPayTimeStamp;
  String? wxPaySign;
  String? orderCode;

  PayInfo();

  factory PayInfo.fromJson(Map<String, dynamic> json) => $PayInfoFromJson(json);

  Map<String, dynamic> toJson() => $PayInfoToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
