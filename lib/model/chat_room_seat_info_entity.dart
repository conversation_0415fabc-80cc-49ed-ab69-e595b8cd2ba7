import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_room_seat_info_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/chat_room_seat_info_entity.g.dart';

@JsonSerializable()
class ChatRoomSeatInfoEntity {
	int? index; // 麦位序列，默认 1->8
	int? status; //0：未上麦；1：已上麦；2：闭麦；3：禁言；4：锁麦
	int? type; //1：普通席位；2.流动席位
	String? takeSeatTime; //流动麦上麦时间
	int? faction; //阵容（1.红房；2.蓝方.）
	ChatRoomSeatInfoUser? user;

	ChatRoomSeatInfoEntity();

	factory ChatRoomSeatInfoEntity.fromJson(Map<String, dynamic> json) => $ChatRoomSeatInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $ChatRoomSeatInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ChatRoomSeatInfoUser {
	String? userId;
	String? nickname;
	int? imageIndex;
	String? dressNo;

	ChatRoomSeatInfoUser();

	factory ChatRoomSeatInfoUser.fromJson(Map<String, dynamic> json) => $ChatRoomSeatInfoUserFromJson(json);

	Map<String, dynamic> toJson() => $ChatRoomSeatInfoUserToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}