import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/friend_sub_group_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/friend_user_info_entity.dart';
export 'package:dada/generated/json/friend_sub_group_entity.g.dart';

@JsonSerializable()
class FriendSubGroupEntity {
	String? groupName;
	String? id;
	int? description;
	int? onlineCount;
	@JSONField(name: "friendDTOList")
	List<FriendUserInfoEntity>? friendList;

	FriendSubGroupEntity();

	factory FriendSubGroupEntity.fromJson(Map<String, dynamic> json) => $FriendSubGroupEntityFromJson(json);

	Map<String, dynamic> toJson() => $FriendSubGroupEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}