import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/post_list_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/post_entity.dart';
export 'package:dada/generated/json/post_list_entity.g.dart';

@JsonSerializable()
class PostListEntity {
	int? total;
	List<PostEntity>? list;

	PostListEntity();

	factory PostListEntity.fromJson(Map<String, dynamic> json) => $PostListEntityFromJson(json);

	Map<String, dynamic> toJson() => $PostListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}