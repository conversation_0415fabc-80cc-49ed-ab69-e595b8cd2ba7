import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/match_assemble_place_list_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/match_assemble_place_list_item_entity.g.dart';

@JsonSerializable()
class MatchAssemblePlaceListItemEntity {
	String? id;
	String? name;
	List<dynamic>? labels;
	bool? locked;

	MatchAssemblePlaceListItemEntity();

	factory MatchAssemblePlaceListItemEntity.fromJson(Map<String, dynamic> json) => $MatchAssemblePlaceListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $MatchAssemblePlaceListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}