import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/goods_entity.g.dart';
import 'dart:convert';
import 'package:dada/model/prop_entity.dart';

@JsonSerializable()
class GoodsEntity {
	String? goodsId;
	int? goodsType; // 1: 搭币 2: 礼包
	double? price;
	int? createdUserId;
	String? createdDate;
	String? updateDate;
	String? goodsName;
	String? image;
	int? count;
	List<GoodsRewardEntity>? goodsDetailsList;
	int? isBuy; //已购数量
	int? buyLimit; //购买限制数量
	String? endTime; //购买截止时间
	double? originalPrice; //原价
	String? goodsDescription;

	GoodsEntity();

	factory GoodsEntity.fromJson(Map<String, dynamic> json) => $GoodsEntityFromJson(json);

	Map<String, dynamic> toJson() => $GoodsEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GoodsRewardEntity {
	int? goodsDetailId;
	int? goodsId;
	int? propId;
	int? num;
	int? giftsNum;
	String? updateDate;
	String? createdDate;
	int? expiredDate;
	PropEntity? prop;

	GoodsRewardEntity();

	factory GoodsRewardEntity.fromJson(Map<String, dynamic> json) => $GoodsRewardEntityFromJson(json);

	Map<String, dynamic> toJson() => $GoodsRewardEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}