import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/user_card_box_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/user_card_box_entity.g.dart';

@JsonSerializable()
class UserCardBoxEntity {
	String? cardType;
	int? cardNum;

	UserCardBoxEntity();

	factory UserCardBoxEntity.fromJson(Map<String, dynamic> json) => $UserCardBoxEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserCardBoxEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}