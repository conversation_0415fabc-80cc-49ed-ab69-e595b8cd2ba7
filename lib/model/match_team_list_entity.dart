import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/match_team_list_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/match_team_result_entity.dart';
export 'package:dada/generated/json/match_team_list_entity.g.dart';

@JsonSerializable()
class MatchTeamListEntity {
	int? total;
	List<MatchTeamResultEntity>? list;

	MatchTeamListEntity();

	factory MatchTeamListEntity.fromJson(Map<String, dynamic> json) => $MatchTeamListEntityFromJson(json);

	Map<String, dynamic> toJson() => $MatchTeamListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}