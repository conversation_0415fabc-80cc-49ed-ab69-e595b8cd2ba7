import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/account_balance_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/account_balance_entity.g.dart';

@JsonSerializable()
class AccountBalanceEntity {
	int? daCoinNum;
	int? daStickNum;
	int? stains;
	int? skinNum;

	AccountBalanceEntity();

	factory AccountBalanceEntity.fromJson(Map<String, dynamic> json) => $AccountBalanceEntityFromJson(json);

	Map<String, dynamic> toJson() => $AccountBalanceEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}