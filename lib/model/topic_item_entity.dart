import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/topic_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/topic_item_entity.g.dart';

@JsonSerializable()
class TopicItemEntity {
	String? id;
	String? roomNo;
	String? sort;
	int? topicType; //1.文本；2.图片
	String? topicText;

	TopicItemEntity();

	factory TopicItemEntity.fromJson(Map<String, dynamic> json) => $TopicItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $TopicItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}