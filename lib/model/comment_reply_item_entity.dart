import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/comment_reply_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/comment_reply_item_entity.g.dart';

@JsonSerializable()
class CommentReplyItemEntity {
	String? replyId;
	String? commentsId;
	String? userId;
	String? avatar;
	String? nickname;
	String? content;
	int? likeNo;
	bool? likeState;
	String? createdDate;
	bool? isAuthor;
	String? replayUser;
	String? replayUserName;

	CommentReplyItemEntity();

	factory CommentReplyItemEntity.fromJson(Map<String, dynamic> json) => $CommentReplyItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $CommentReplyItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}