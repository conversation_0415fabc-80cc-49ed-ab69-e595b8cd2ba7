import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/small_room_barrage_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/small_room_barrage_item_entity.g.dart';

@JsonSerializable()
class SmallRoomBarrageItemEntity {
	String? roomMsgId;
	String? avatar;
	String? nickname;
	String? content;
	String? userId;
	String? createDate;
	String? dadaRoomId;

	SmallRoomBarrageItemEntity();

	factory SmallRoomBarrageItemEntity.fromJson(Map<String, dynamic> json) => $SmallRoomBarrageItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomBarrageItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}