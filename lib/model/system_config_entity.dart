import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/system_config_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/system_config_entity.g.dart';

@JsonSerializable()
class SystemConfigEntity {
  String? isShowCdk;
  String? worldChatLimit;

  SystemConfigEntity();

  factory SystemConfigEntity.fromJson(Map<String, dynamic> json) =>
      $SystemConfigEntityFromJson(json);

  Map<String, dynamic> toJson() => $SystemConfigEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
