import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/recharge_detail_list_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/recharge_detail_list_item_entity.g.dart';

@JsonSerializable()
class RechargeDetailListItemEntity {
	String? createdDate;
	String? content;
	int? type;
	int? billType;
	int? billNo;

	RechargeDetailListItemEntity();

	factory RechargeDetailListItemEntity.fromJson(Map<String, dynamic> json) => $RechargeDetailListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $RechargeDetailListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}