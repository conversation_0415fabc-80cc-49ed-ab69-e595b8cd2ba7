import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/small_room_mail_list_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/small_room_mail_list_item_entity.g.dart';

@JsonSerializable()
class SmallRoomMailListItemEntity {
	String? roomMsgId;
	String? userId;
	int? dadaRoomId;
	String? type;
	String? content;
	String? createdDate;
	String? nickname;
	String? avatar;
	int? state;
	int? friendType;

	SmallRoomMailListItemEntity();

	factory SmallRoomMailListItemEntity.fromJson(Map<String, dynamic> json) => $SmallRoomMailListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomMailListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}