import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/reward_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/reward_item_entity.g.dart';

@JsonSerializable()
class RewardItemEntity {
	String? prizeId;
	String? imageUrl;
	String? prizeName;
	int? number;
	String? price;

	RewardItemEntity();

	factory RewardItemEntity.fromJson(Map<String, dynamic> json) => $RewardItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $RewardItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}