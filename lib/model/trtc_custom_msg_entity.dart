import 'package:dada/generated/json/base/json_field.dart';
import 'dart:convert';
import 'package:dada/generated/json/trtc_custom_msg_entity.g.dart';

@JsonSerializable()
class TRTCCustomMsgEntity {
  String? event;
  dynamic data;

  TRTCCustomMsgEntity();

  factory TRTCCustomMsgEntity.fromJson(Map<String, dynamic> json) =>
      $TRTCCustomMsgEntityFromJson(json);

  Map<String, dynamic> toJson() => $TRTCCustomMsgEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
