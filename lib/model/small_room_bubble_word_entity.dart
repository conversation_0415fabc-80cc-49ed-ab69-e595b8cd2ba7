import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/small_room_bubble_word_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/small_room_bubble_word_entity.g.dart';

@JsonSerializable()
class SmallRoomBubbleWordEntity {
	String? bubbleId;
	String? userId;
	String? content;
	String? dadaRoomId;
	int? type;
	int? voiceLength;

	SmallRoomBubbleWordEntity();

	factory SmallRoomBubbleWordEntity.fromJson(Map<String, dynamic> json) => $SmallRoomBubbleWordEntityFromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomBubbleWordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}