import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/comment_list_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/comment_item_entity.dart';
export 'package:dada/generated/json/comment_list_entity.g.dart';

@JsonSerializable()
class CommentListEntity {
	int? total;
	List<CommentItemEntity>? list;

	CommentListEntity();

	factory CommentListEntity.fromJson(Map<String, dynamic> json) => $CommentListEntityFromJson(json);

	Map<String, dynamic> toJson() => $CommentListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}