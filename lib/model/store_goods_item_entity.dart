import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/store_goods_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/store_goods_item_entity.g.dart';

@JsonSerializable()
class StoreGoodsItemEntity {
	String? shopId;
	String? propId;
	String? number;
	int? type;
	int? price;
	int? limitNo;
	String? propName;
	String? url;
	int? propType;
	int? isHave;

	StoreGoodsItemEntity();

	factory StoreGoodsItemEntity.fromJson(Map<String, dynamic> json) => $StoreGoodsItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $StoreGoodsItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}