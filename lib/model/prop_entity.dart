import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/prop_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/prop_entity.g.dart';

@JsonSerializable()
class PropEntity {
  String? propId;
  int? isUse;
  int? type; //0: 背包；1：角色
  int?
      propType; //1搭币 2搭棒 3染色剂粉末 4染色剂 5皮肤碎片 6全城喇叭 7头像框 8对话框 9世界聊天 10月卡 11女形象 12男形象 16靓号转换卡
  String? propName;
  int? packageId;
  String? url;
  int? isExist; //是否拥有
  int? num;
  String? propNo;
  String? dressNo;
  int? isFavorites;
  List<PropDressColorEntity>? dressColorList;
  String? description;

  PropEntity();

  factory PropEntity.fromJson(Map<String, dynamic> json) =>
      $PropEntityFromJson(json);

  Map<String, dynamic> toJson() => $PropEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PropDressColorEntity {
  int? colorId;
  String? colorNo;
  String? propNo;
  String? createDate;
  int? depletionNo; //需要消耗染色剂数量
  int? isExist; //是否拥有该颜色
  int? isUse; //是否使用
  String? dressNo;
  String? remark;

  PropDressColorEntity();

  factory PropDressColorEntity.fromJson(Map<String, dynamic> json) =>
      $PropDressColorEntityFromJson(json);

  Map<String, dynamic> toJson() => $PropDressColorEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
