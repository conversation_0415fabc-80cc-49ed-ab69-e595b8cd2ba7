import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/match_team_result_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/match_team_member_entity.dart';
export 'package:dada/generated/json/match_team_result_entity.g.dart';

@JsonSerializable()
class MatchTeamResultEntity {
	String? teamId;
	String? teamName;
	String? teamNo;
	List<String>? teamLabels;
	int? teamLockState;
	int? teamNum;
	int? teamTotalNum;
	String? teamPassword;
	int? teamMatchState;
	String? teamImg;
	List<MatchTeamMemberEntity>? teamMembers;

	MatchTeamResultEntity();

	factory MatchTeamResultEntity.fromJson(Map<String, dynamic> json) => $MatchTeamResultEntityFromJson(json);

	Map<String, dynamic> toJson() => $MatchTeamResultEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}