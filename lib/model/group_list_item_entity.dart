import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/group_list_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/group_list_item_entity.g.dart';

@JsonSerializable()
class GroupListItemEntity {
	String? chatsName;
	String? creatorId;
	String? description;
	String? avatarUrl;
	String? notice;
	String? inviteConfirm;

	GroupListItemEntity();

	factory GroupListItemEntity.fromJson(Map<String, dynamic> json) => $GroupListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $GroupListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}