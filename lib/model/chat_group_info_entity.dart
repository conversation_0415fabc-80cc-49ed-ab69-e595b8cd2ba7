import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_group_info_entity.g.dart';
import 'package:dada/model/chat_group_label_entity.dart';
import 'dart:convert';

import 'package:dada/model/friend_user_info_entity.dart';
export 'package:dada/generated/json/chat_group_info_entity.g.dart';

@JsonSerializable()
class ChatGroupInfoEntity {
	@JSONField(name: "chatsName")
	String? groupName;
	@JSONField(name: "id")
	String? groupId;
	@J<PERSON><PERSON>ield(name: "avatarUrl")
	String? faceUrl;
	@J<PERSON><PERSON>ield(name: "chatsUserCount")
	int? memberCount;
	List<FriendUserInfoEntity>? memberList;
	@JSONField(name: "tagList")
	List<ChatGroupLabelEntity>? labels;
	@JSONField(name: "isAddChats")
	int? isInGroup;
	String? notice;
	String? creatorId;
	String? inGroupNickname;
	List<String>? tagStrList;
	String? chatsId;
	String? chatsNo;


	ChatGroupInfoEntity();

	factory ChatGroupInfoEntity.fromJson(Map<String, dynamic> json) => $ChatGroupInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $ChatGroupInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}