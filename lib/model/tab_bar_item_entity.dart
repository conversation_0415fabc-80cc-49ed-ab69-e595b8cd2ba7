import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/tab_bar_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/tab_bar_item_entity.g.dart';

@JsonSerializable()
class TabBarItemEntity {
  String? title;
  String? icon;
  String? selectedIcon;
  double? width;
  String? badgeCount;
  double? selectedWidth;

  TabBarItemEntity();

  factory TabBarItemEntity.fromJson(Map<String, dynamic> json) =>
      $TabBarItemEntityFromJson(json);

  Map<String, dynamic> toJson() => $TabBarItemEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
