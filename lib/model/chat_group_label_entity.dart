import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_group_label_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/chat_group_label_entity.g.dart';

@JsonSerializable()
class ChatGroupLabelEntity {
	String? id;
	String? tagName;

	ChatGroupLabelEntity();

	factory ChatGroupLabelEntity.fromJson(Map<String, dynamic> json) => $ChatGroupLabelEntityFromJson(json);

	Map<String, dynamic> toJson() => $ChatGroupLabelEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}