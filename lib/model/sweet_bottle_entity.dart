import 'dart:convert';

import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/sweet_bottle_entity.g.dart';

// TODO 文档没有写，自己先编的
@JsonSerializable()
class SweetBottleEntity {
  String? sweetBottleId;
  String? url;
  String? content;
  String? userId;
  String? daUserId;

  SweetBottleEntity();

  factory SweetBottleEntity.fromJson(Map<String, dynamic> json) => $SweetBottleEntityFromJson(json);

  Map<String, dynamic> toJson() => $SweetBottleEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}