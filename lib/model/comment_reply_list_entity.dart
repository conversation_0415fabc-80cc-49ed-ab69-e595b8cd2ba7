import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/comment_reply_list_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/comment_reply_item_entity.dart';
export 'package:dada/generated/json/comment_reply_list_entity.g.dart';

@JsonSerializable()
class CommentReplyListEntity {
	int? total;
	List<CommentReplyItemEntity>? list;

	CommentReplyListEntity();

	factory CommentReplyListEntity.fromJson(Map<String, dynamic> json) => $CommentReplyListEntityFromJson(json);

	Map<String, dynamic> toJson() => $CommentReplyListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}