import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/dynamic_resonance_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/dynamic_resonance_entity.g.dart';

@JsonSerializable()
class DynamicResonanceEntity {
	String? content;
	String? imageUrl;
	String? resonateDate;
	String? postId;
	int? resonateCount;
	int? postType;
	int? notReadResonateCount;

	DynamicResonanceEntity();

	factory DynamicResonanceEntity.fromJson(Map<String, dynamic> json) => $DynamicResonanceEntityFromJson(json);

	Map<String, dynamic> toJson() => $DynamicResonanceEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}