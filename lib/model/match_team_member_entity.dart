import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/match_team_member_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/match_team_member_entity.g.dart';

@JsonSerializable()
class MatchTeamMemberEntity {
	String? teamMemberId;
	String? teamId;
	String? userId;
	int? role; //1. 成员； 2.房主
	String? nickname;
	String? avatar;

	MatchTeamMemberEntity();

	factory MatchTeamMemberEntity.fromJson(Map<String, dynamic> json) => $MatchTeamMemberEntityFromJson(json);

	Map<String, dynamic> toJson() => $MatchTeamMemberEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}