import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/small_room_task_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/small_room_task_entity.g.dart';

@JsonSerializable()
class SmallRoomTaskEntity {
	String? roomTaskId;
	String? taskUrl;
	String? taskName;
	int? taskTotalProgress;
	String? taskText;
	int? type;
	int? taskProgress;
	int? completed; ///0: 未完成；1：已完成；2：已领取

	SmallRoomTaskEntity();

	factory SmallRoomTaskEntity.fromJson(Map<String, dynamic> json) => $SmallRoomTaskEntityFromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomTaskEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}