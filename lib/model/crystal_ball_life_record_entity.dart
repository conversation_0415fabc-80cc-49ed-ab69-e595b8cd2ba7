import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/crystal_ball_life_record_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/crystal_ball_life_record_entity.g.dart';

@JsonSerializable()
class CrystalBallLifeRecordEntity {
	String? lifeId;
	String? userId;
	List<String>? imgUrls;
	String? content;
	int? year;
	String? month;
	String? createdDate;
	bool? isExist;

	CrystalBallLifeRecordEntity();

	factory CrystalBallLifeRecordEntity.fromJson(Map<String, dynamic> json) => $CrystalBallLifeRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $CrystalBallLifeRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}