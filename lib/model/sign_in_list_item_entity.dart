import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/sign_in_list_item_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/reward_item_entity.dart';
export 'package:dada/generated/json/sign_in_list_item_entity.g.dart';

@JsonSerializable()
class SignInListItemEntity {
	int? weekDay;
	int? isSign;

	List<RewardItemEntity>? prizesDTOList;

	SignInListItemEntity();

	factory SignInListItemEntity.fromJson(Map<String, dynamic> json) => $SignInListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $SignInListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}