import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/dada_match_result_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/user_info_entity.dart';
export 'package:dada/generated/json/dada_match_result_entity.g.dart';

@JsonSerializable()
class DadaMatchResultEntity {
	List<UserInfoEntity>? matchDada;
	int? type;

	DadaMatchResultEntity();

	factory DadaMatchResultEntity.fromJson(Map<String, dynamic> json) => $DadaMatchResultEntityFromJson(json);

	Map<String, dynamic> toJson() => $DadaMatchResultEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}