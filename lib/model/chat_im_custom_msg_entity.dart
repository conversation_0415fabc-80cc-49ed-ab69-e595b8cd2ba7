import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_im_custom_msg_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/chat_im_custom_msg_entity.g.dart';

@JsonSerializable()
class ChatImCustomMsgEntity {
	String? type; //ChatImCustomMsgType
	dynamic data;

	ChatImCustomMsgEntity();

	factory ChatImCustomMsgEntity.fromJson(Map<String, dynamic> json) => $ChatImCustomMsgEntityFromJson(json);

	Map<String, dynamic> toJson() => $ChatImCustomMsgEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}