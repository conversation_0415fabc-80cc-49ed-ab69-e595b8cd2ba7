import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/small_room_explore_info_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/prop_entity.dart';
export 'package:dada/generated/json/small_room_explore_info_entity.g.dart';

@JsonSerializable()
class SmallRoomExploreInfoEntity {
	String? eventName;
	String? eventDesc;
	String? exploreDate;//收获时间
	int? state;//开始探索0/收获1
	SmallRoomExploreInfoExploreProp1? exploreProp1;//道具1
	SmallRoomExploreInfoExploreProp2? exploreProp2;//道具2
	List<PropEntity>? propList;

	SmallRoomExploreInfoEntity();

	factory SmallRoomExploreInfoEntity.fromJson(Map<String, dynamic> json) => $SmallRoomExploreInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomExploreInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class SmallRoomExploreInfoExploreProp1 {
	String? userExplorePropId;
	String? userId;
	String? explorePropId;
	String? createdDate;
	String? explorePropName;
	int? explorePropType;
	String? img;
	String? probability;
	int? explorePropLevel;

	SmallRoomExploreInfoExploreProp1();

	factory SmallRoomExploreInfoExploreProp1.fromJson(Map<String, dynamic> json) => $SmallRoomExploreInfoExploreProp1FromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomExploreInfoExploreProp1ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class SmallRoomExploreInfoExploreProp2 {
	String? userExplorePropId;
	String? userId;
	String? explorePropId;
	String? createdDate;
	String? explorePropName;
	int? explorePropType;
	String? img;
	String? probability;
	int? explorePropLevel;

	SmallRoomExploreInfoExploreProp2();

	factory SmallRoomExploreInfoExploreProp2.fromJson(Map<String, dynamic> json) => $SmallRoomExploreInfoExploreProp2FromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomExploreInfoExploreProp2ToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}