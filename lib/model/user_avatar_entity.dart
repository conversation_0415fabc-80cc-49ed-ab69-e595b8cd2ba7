import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/user_avatar_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/user_avatar_entity.g.dart';

@JsonSerializable()
class UserAvatarEntity {
	String? avatarId;
	String? expireTime;
	int? expireDay;
	String? createdDate;
	int? isUse;
	String? url;
	String? avatarName;
	int? type;
	int? useTime;
	int? price;
	int? priceType;

	UserAvatarEntity();

	factory UserAvatarEntity.fromJson(Map<String, dynamic> json) => $UserAvatarEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserAvatarEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}