import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/contact_group_list_item_entity.g.dart';
import 'package:dada/model/chat_group_info_entity.dart';
import 'dart:convert';

import 'package:dada/model/friend_group_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';

@JsonSerializable()
class ContactGroupListItemEntity {
	String? groupName;
	String? id;
	@JSONField(name: "listType")
	int? groupType;
	@JSONField(name: "userGroupFriendListVo")
	FriendGroupEntity? friendGroupInfo;
	List<FriendUserInfoEntity>? daziList;
	@JSONField(name: "crowdChatsDTOS")
	List<ChatGroupInfoEntity>? groupsList;
	@JSONField(name: "userBlacklist")
	List<FriendUserInfoEntity>? blacklist;
	@JSONField(name: "newFriendList")
	List<FriendUserInfoEntity>? newFriendsList;
	bool? unfold;

	ContactGroupListItemEntity();

	factory ContactGroupListItemEntity.fromJson(Map<String, dynamic> json) => $ContactGroupListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContactGroupListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}