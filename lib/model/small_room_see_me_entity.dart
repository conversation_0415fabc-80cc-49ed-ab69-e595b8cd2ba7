import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/small_room_see_me_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/small_room_see_me_entity.g.dart';

@JsonSerializable()
class SmallRoomSeeMeEntity {
	String? visitId;
	String? userId;
	String? visitedId;
	String? visitTime;
	String? avatar;
	String? nickname;
	int? sex;
	int? age;
	int? isPioneer;
	int? isInitUser;
	int? isFireKeeper;

	SmallRoomSeeMeEntity();

	factory SmallRoomSeeMeEntity.fromJson(Map<String, dynamic> json) => $SmallRoomSeeMeEntityFromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomSeeMeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}