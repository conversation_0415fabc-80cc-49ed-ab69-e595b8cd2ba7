import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/simple_user_info_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/simple_user_info_entity.g.dart';

@JsonSerializable()
class SimpleUserInfoEntity {
	String? userId;
	String? avatar;
	String? nickname;

	SimpleUserInfoEntity();

	factory SimpleUserInfoEntity.fromJson(Map<String, dynamic> json) => $SimpleUserInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $SimpleUserInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}