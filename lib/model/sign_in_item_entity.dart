import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/sign_in_item_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/sign_in_list_item_entity.dart';
export 'package:dada/generated/json/sign_in_item_entity.g.dart';

@JsonSerializable()
class SignInItemEntity {
	List<SignInListItemEntity>? list;
	bool? today;
	bool? isWeekSignOver;

	SignInItemEntity();

	factory SignInItemEntity.fromJson(Map<String, dynamic> json) => $SignInItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $SignInItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}