import 'dart:convert';

import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/invite_play_entity.g.dart';

@JsonSerializable()
class InvitePlayEntity {
  String? id;
  String? userId;
  String? text;

  InvitePlayEntity();

  factory InvitePlayEntity.fromJson(Map<String, dynamic> json) => $InvitePlayEntityFromJson(json);

  Map<String, dynamic> toJson() => $InvitePlayEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
