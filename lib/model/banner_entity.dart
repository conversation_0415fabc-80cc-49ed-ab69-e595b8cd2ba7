import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/banner_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/banner_entity.g.dart';

@JsonSerializable()
class BannerEntity {
	int? id;
	String? url;
	int? status;
	String? page;
	int? isClick;
	String? bannerName;
	String? createDate;
	int? sort;

	BannerEntity();

	factory BannerEntity.fromJson(Map<String, dynamic> json) => $BannerEntityFromJson(json);

	Map<String, dynamic> toJson() => $BannerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}