import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_room_online_member_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/chat_room_online_member_entity.g.dart';

@JsonSerializable()
class ChatRoomOnlineMemberEntity {
	String? userId;
	String? nickname;
	String? avatar;
	int? sex;
	int? age;
	bool? onSeat;
	int? muteUtil;//禁言结束时间戳
	bool? isMute; //是否已被静音
	int? seatIndex;

	ChatRoomOnlineMemberEntity();

	factory ChatRoomOnlineMemberEntity.fromJson(Map<String, dynamic> json) => $ChatRoomOnlineMemberEntityFromJson(json);

	Map<String, dynamic> toJson() => $ChatRoomOnlineMemberEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}