import 'package:dada/model/api_response_entity.g.dart';
import 'dart:convert';
export 'package:dada/model/api_response_entity.g.dart';

class ApiResponseEntity<T> {
	int? code;
	String? msg;
	T? data;

	ApiResponseEntity();

	factory ApiResponseEntity.fromJson(Map<String, dynamic> json) => $ApiResponseEntityFromJson<T>(json);

	Map<String, dynamic> toJson() => $ApiResponseEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}