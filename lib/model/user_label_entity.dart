import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/user_label_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/user_label_entity.g.dart';

@JsonSerializable()
class UserLabelEntity {
	String? labelId;
	String? userId;
	String? labelName;
	List<String>? labelText;
	List<String>? labelImgs;

	UserLabelEntity();

	factory UserLabelEntity.fromJson(Map<String, dynamic> json) => $UserLabelEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserLabelEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}