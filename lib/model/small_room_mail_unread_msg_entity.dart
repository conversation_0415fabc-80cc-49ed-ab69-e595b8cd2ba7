import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/small_room_mail_unread_msg_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/small_room_mail_unread_msg_entity.g.dart';

@JsonSerializable()
class SmallRoomMailUnreadMsgEntity {
	int? daNo;
	int? system;
	int? barrage;

	SmallRoomMailUnreadMsgEntity();

	factory SmallRoomMailUnreadMsgEntity.fromJson(Map<String, dynamic> json) => $SmallRoomMailUnreadMsgEntityFromJson(json);

	Map<String, dynamic> toJson() => $SmallRoomMailUnreadMsgEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}