import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_room_history_list_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/chat_room_history_list_item_entity.g.dart';

@JsonSerializable()
class ChatRoomHistoryListItemEntity {
	String? msgId;
	String? userId;
	String? nickname;
	int? seatIndex;
	String? content;
	String? sendDate;

	ChatRoomHistoryListItemEntity();

	factory ChatRoomHistoryListItemEntity.fromJson(Map<String, dynamic> json) => $ChatRoomHistoryListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $ChatRoomHistoryListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}