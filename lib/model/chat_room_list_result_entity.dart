import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_room_list_result_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/chat_room_info_entity.dart';
export 'package:dada/generated/json/chat_room_list_result_entity.g.dart';

@JsonSerializable()
class ChatRoomListResultEntity {
  int? isSearchResult;
  List<ChatRoomInfoEntity>? roomVoLists;

  ChatRoomListResultEntity();

  factory ChatRoomListResultEntity.fromJson(Map<String, dynamic> json) =>
      $ChatRoomListResultEntityFromJson(json);

  Map<String, dynamic> toJson() => $ChatRoomListResultEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
