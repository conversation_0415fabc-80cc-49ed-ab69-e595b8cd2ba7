import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/global_notice_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/global_notice_entity.g.dart';

@JsonSerializable()
class GlobalNoticeEntity {
	String? msg;
	String? avatar;
	String? nickname;
	int? time;
	int? type;

	GlobalNoticeEntity();

	factory GlobalNoticeEntity.fromJson(Map<String, dynamic> json) => $GlobalNoticeEntityFromJson(json);

	Map<String, dynamic> toJson() => $GlobalNoticeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}