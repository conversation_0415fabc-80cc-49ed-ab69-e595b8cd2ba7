import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/search_room_history_result_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/search_room_history_result_entity.g.dart';

@JsonSerializable()
class SearchRoomHistoryResultEntity {
  List<String>? searchHistoryList;
  List<String>? hotTopicList;
  List<SearchRoomHistoryResultTopicBoosterList>? topicBoosterList;

  SearchRoomHistoryResultEntity();

  factory SearchRoomHistoryResultEntity.fromJson(Map<String, dynamic> json) =>
      $SearchRoomHistoryResultEntityFromJson(json);

  Map<String, dynamic> toJson() => $SearchRoomHistoryResultEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SearchRoomHistoryResultTopicBoosterList {
  String? topic;
  int? roomCount;
  int? topicCount;

  SearchRoomHistoryResultTopicBoosterList();

  factory SearchRoomHistoryResultTopicBoosterList.fromJson(
          Map<String, dynamic> json) =>
      $SearchRoomHistoryResultTopicBoosterListFromJson(json);

  Map<String, dynamic> toJson() =>
      $SearchRoomHistoryResultTopicBoosterListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
