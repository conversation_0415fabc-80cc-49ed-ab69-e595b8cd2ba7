import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/dynamic_message_item_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/dynamic_message_item_entity.g.dart';

@JsonSerializable()
class DynamicMessageItemEntity {
  String? msgId;
  String? senderId;
  String? recipientId;
  int? type; //1.动态点赞 2.评论点赞 3.回复点赞 4.动态评论 5.评论回复
  int? postId;
  int? isRead;
  String? avatar;
  String? nickname;
  String? content;
  String? createdDate;
  int? contentType;//1. 文本，2. 图片， 3.视频
  String? postMsg;

  DynamicMessageItemEntity();

  factory DynamicMessageItemEntity.fromJson(Map<String, dynamic> json) =>
      $DynamicMessageItemEntityFromJson(json);

  Map<String, dynamic> toJson() => $DynamicMessageItemEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
