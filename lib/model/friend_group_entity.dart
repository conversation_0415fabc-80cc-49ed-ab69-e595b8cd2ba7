import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/friend_group_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/friend_sub_group_entity.dart';
import 'package:dada/model/friend_user_info_entity.dart';
export 'package:dada/generated/json/friend_group_entity.g.dart';

@JsonSerializable()
class FriendGroupEntity {
	@JSONField(name: "userFriendList")
	List<FriendUserInfoEntity>? unGroupedFriendList;
	@JSONField(name: "userGroupVOS")
	List<FriendSubGroupEntity>? subGroupList;

	FriendGroupEntity();

	factory FriendGroupEntity.fromJson(Map<String, dynamic> json) => $FriendGroupEntityFromJson(json);

	Map<String, dynamic> toJson() => $FriendGroupEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}