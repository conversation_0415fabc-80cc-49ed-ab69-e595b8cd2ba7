import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/user_id_card_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/user_id_card_entity.g.dart';

@JsonSerializable()
class UserIdCardEntity {
	String? cardId;
	String? userId;
	String? cardName;
	String? cardNickname;
	String? gameId;
	String? serverName;
	List<String>? cardText;
	int? cardState;
	int? cardType; /// 1: 身份卡，2：晒欧卡，3：穿搭卡，4：高光卡
	String? createdDate;
	bool? liked;
	bool? known;
	bool? read;
	int? likeNo;
	int? readNo;
	int? knowNo;
	String? cardUrl;

	UserIdCardEntity();

	factory UserIdCardEntity.fromJson(Map<String, dynamic> json) => $UserIdCardEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserIdCardEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}