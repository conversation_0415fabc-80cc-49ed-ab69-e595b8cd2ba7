import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/user_info_entity.g.dart';
import 'dart:convert';
import 'package:dada/model/user_label_entity.dart';
export 'package:dada/generated/json/user_info_entity.g.dart';

@JsonSerializable()
class UserInfoEntity {
  String? id;
  String? avatar;
  String? nickname;
  int? sex; //0：男，1：女
  int? age;
  int? level;
  int? dadaLevel;
  String? place;
  bool? isAuthIdentity;
  String? voiceSignature;
  String? txtSignature;
  String? hometown;
  String? vocation;
  String? birthday;
  int? voiceLength;
  int? socialState;
  String? dadaNo;
  String? work;
  List<UserLabelEntity>? labels;
  int? ties; //-1：陌生人，0：普通好友，1：纯搭，2：浅搭，3：浅搭随缘，4：深搭，5：开始了解中
  String? avatarFrame; //头像框
  @JSONField(name: "dialogBox")
  String? chatBubble; //聊天气泡
  int? isAccept; //是否接收搭子申请
  bool? monthlyPassUser; //是否是月卡用户
  UserInfoEntity? inviteUserInfo;
  String? inviteCode;
  String? phone;
  String? recentState;
  int? isFriend;
  String? beginKnowdDate; //开始了解倒计时
  int? state;
  int? genderChangeCount; //初始性别转换剩余次数
  bool? isRealName; //是否实名认证
  String? currentDressNo; //当前穿搭衣服编号
  String? ipRegion; //ip属地
  String? friendLevel; //亲密度
  String? monthlyPassDay; //月卡剩余天数
  int? daStickNum; //剩余搭棒数量
  String? constellation; //星座
  int? isPioneer; //是否是先行者
  int? isInitUser; //是否是元老用户
  int? isFireKeeper; //是否是传火者
  int? isFirstRecharge; //是否是首次充值 0:否，1：是
  String? chatFontColor; //聊天字体颜色
  String? chatOffset; //聊天字体气泡偏移

  UserInfoEntity();

  factory UserInfoEntity.fromJson(Map<String, dynamic> json) =>
      $UserInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserInfoEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  bool isDaziRelation() {
    return ties == 1 || ties == 2 || ties == 3 || ties == 4;
  }
}
