import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/api_response_entity.dart';

ApiResponseEntity<T> $ApiResponseEntityFromJson<T>(Map<String, dynamic> json) {
  final ApiResponseEntity<T> apiResponseEntity = ApiResponseEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    apiResponseEntity.code = code;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    apiResponseEntity.msg = msg;
  }
  T? data;
  if (json['data'] != null) {
    if (json['data'] is List) {
      if (T.toString() == "List<String>") {
        List<String> list = [];
        if (json['data'] != null) {
          list = List.from(json['data']);
        }
        data = list as T?;
      } else {
        data = JsonConvert.fromJsonAsT(json['data']);
      }
    } else {
      data = jsonConvert.convert<T>(json['data']);
    }
  }
  if (data != null) {
    apiResponseEntity.data = data;
  }
  return apiResponseEntity;
}

Map<String, dynamic> $ApiResponseEntityToJson(ApiResponseEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['msg'] = entity.msg;
  data['data'] = entity.data?.toJson();
  return data;
}

extension ApiResponseEntityExtension on ApiResponseEntity {
  ApiResponseEntity<T> copyWith<T>({
    int? code,
    String? msg,
    T? data,
  }) {
    return ApiResponseEntity<T>()
      ..code = code ?? this.code
      ..msg = msg ?? this.msg
      ..data = data ?? this.data;
  }
}