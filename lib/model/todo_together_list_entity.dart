import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/todo_together_list_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/todo_together_list_entity.g.dart';

@JsonSerializable()
class TodoTogetherListEntity {
	String? userTaskId;
	String? content;
	int? sex;
	int? taskSex;
	String? taskAvatar;
	String? taskNickname;
	int? taskAge;
	int? state;
	String? creatorId;
	String? createdDate;
	int? type;
	String? userId;

	TodoTogetherListEntity();

	factory TodoTogetherListEntity.fromJson(Map<String, dynamic> json) => $TodoTogetherListEntityFromJson(json);

	Map<String, dynamic> toJson() => $TodoTogetherListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}