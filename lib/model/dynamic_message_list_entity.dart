import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/dynamic_message_list_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/dynamic_message_item_entity.dart';
export 'package:dada/generated/json/dynamic_message_list_entity.g.dart';

@JsonSerializable()
class DynamicMessageListEntity {
	int? total;
	List<DynamicMessageItemEntity>? list;

	DynamicMessageListEntity();

	factory DynamicMessageListEntity.fromJson(Map<String, dynamic> json) => $DynamicMessageListEntityFromJson(json);

	Map<String, dynamic> toJson() => $DynamicMessageListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}