import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/home_list_item_entity.g.dart';
import 'dart:convert';

export 'package:dada/generated/json/home_list_item_entity.g.dart';

@JsonSerializable()
class HomeListItemEntity {
	String? groupID;
	String? groupName;
	String? groupDesc;
	String? groupNotice;

	HomeListItemEntity();

	factory HomeListItemEntity.fromJson(Map<String, dynamic> json) => $HomeListItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $HomeListItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}