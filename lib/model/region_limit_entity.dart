import 'dart:convert';

import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/region_limit_entity.g.dart';

@JsonSerializable()
class RegionLimitEntity {
  String? id;
  String? userId;
  String? region;

  RegionLimitEntity();

  factory RegionLimitEntity.fromJson(Map<String, dynamic> json) => $RegionLimitEntityFromJson(json);

  Map<String, dynamic> toJson() => $RegionLimitEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}