import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/teen_mode_status_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/teen_mode_status_entity.g.dart';

@JsonSerializable()
class TeenModeStatusEntity {
	int? id;
	int? userId;
	String? password;
	String? toggle;

	TeenModeStatusEntity();

	factory TeenModeStatusEntity.fromJson(Map<String, dynamic> json) => $TeenModeStatusEntityFromJson(json);

	Map<String, dynamic> toJson() => $TeenModeStatusEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}