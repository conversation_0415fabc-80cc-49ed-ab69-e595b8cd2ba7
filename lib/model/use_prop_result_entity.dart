import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/use_prop_result_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/use_prop_result_entity.g.dart';

@JsonSerializable()
class UsePropResultEntity {
	String? dadaNo;
	String? oldDadaNo;
	String? msg;
	int? code;

	UsePropResultEntity();

	factory UsePropResultEntity.fromJson(Map<String, dynamic> json) => $UsePropResultEntityFromJson(json);

	Map<String, dynamic> toJson() => $UsePropResultEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}