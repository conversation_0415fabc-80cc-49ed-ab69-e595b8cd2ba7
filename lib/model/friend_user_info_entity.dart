import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/friend_user_info_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/small_room_bubble_word_entity.dart';
export 'package:dada/generated/json/friend_user_info_entity.g.dart';

@JsonSerializable()
class FriendUserInfoEntity {
  String? id;
  String? userFriendId; //好有关系用这个id
  String? userId;
  String? userBlackId;
  String? daUserId; //搭子关系用这个Id
  String? friendLevel; //亲密度
  String? friendRemark;
  String? nickname;
  String? avatar;
  String? friendGroupId;
  String? friendGroupName;
  String? friendSubGroupName;
  String? userDescribes;
  int? describesFlag; //0：语音，1：文字
  int? voiceLength;
  int?
      friendType; //(新好友关系：0.已同意；1：好友申请中；2：已过期; )(搭子关系中: 0.普通好友, 1.纯搭；2.浅搭；3.浅搭随缘；4.深搭；5.开始了解；6.好友申请中；7.已过期)
  String? senderId;
  String? applyMsg;
  String? daName;
  String? dadaNo;
  int? isFriend;
  int? daCircleLimit;
  int? isDisturb; //是否开启消息免打扰
  int? lookMeState; //是否允许访问我的搭圈 0:否， 1：是
  int? lookFriendState; //是否可以访问好友的搭圈
  int? isBlack;
  List<SmallRoomBubbleWordEntity>? bubbleDTOList;
  String? knowId;
  int? sex;
  String? expreDate;
  String? userImageNo; //形象编码（小屋使用）
  String? currentDressNo; //衣服编码
  int? isPioneer; //是否是先行者
  int? isInitUser; //是否是元老用户
  int? isOnline; //是否在线

  FriendUserInfoEntity();

  factory FriendUserInfoEntity.fromJson(Map<String, dynamic> json) =>
      $FriendUserInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => $FriendUserInfoEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  bool isDaziRelation() {
    return friendType == 1 ||
        friendType == 2 ||
        friendType == 3 ||
        friendType == 4;
  }
}
