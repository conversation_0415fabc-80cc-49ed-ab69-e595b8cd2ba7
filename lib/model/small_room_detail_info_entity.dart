import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/small_room_detail_info_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/friend_user_info_entity.dart';
import 'package:dada/model/small_room_barrage_item_entity.dart';
import 'package:dada/model/small_room_bubble_word_entity.dart';
export 'package:dada/generated/json/small_room_detail_info_entity.g.dart';

@JsonSerializable()
class SmallRoomDetailInfoEntity {
  List<FriendUserInfoEntity>? beginKnowList;
  List<FriendUserInfoEntity>? dadaList;
  List<SmallRoomBarrageItemEntity>? msgList;
  SmallRoomInfoRoom? room;
  String? imageVo;
  int? noReadNum;
  List<SmallRoomBubbleWordEntity>? bubbleDTOList;

  SmallRoomDetailInfoEntity();

  factory SmallRoomDetailInfoEntity.fromJson(Map<String, dynamic> json) =>
      $SmallRoomDetailInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => $SmallRoomDetailInfoEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SmallRoomInfoRoom {
  String? dadaRoomId;
  String? userId;
  String? computerContent;
  String? musicContent;
  String? bookContent;
  int? roomLifeState;

  SmallRoomInfoRoom();

  factory SmallRoomInfoRoom.fromJson(Map<String, dynamic> json) =>
      $SmallRoomInfoRoomFromJson(json);

  Map<String, dynamic> toJson() => $SmallRoomInfoRoomToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
