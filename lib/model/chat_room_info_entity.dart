import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/chat_room_info_entity.g.dart';
import 'dart:convert';

import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/model/topic_item_entity.dart';
export 'package:dada/generated/json/chat_room_info_entity.g.dart';

@JsonSerializable()
class ChatRoomInfoEntity {
  String? id;
  String? roomNo;
  String? roomName;
  String? createDate;
  String? createRoomUserId;
  String? currentRoomUserId;
  int? roomType; //1. 市集；2.茶壶
  List<TopicItemEntity>? roomTopicList;
  List<ChatRoomSeatInfoEntity>? seatList;
  int? onlineNumber; //在线人数
  String? avatar; //当前房主头像

  ChatRoomInfoEntity();

  factory ChatRoomInfoEntity.fromJson(Map<String, dynamic> json) =>
      $ChatRoomInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => $ChatRoomInfoEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
