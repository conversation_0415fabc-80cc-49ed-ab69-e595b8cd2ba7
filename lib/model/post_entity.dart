import 'package:dada/generated/json/base/json_field.dart';
import 'package:dada/generated/json/post_entity.g.dart';
import 'dart:convert';
export 'package:dada/generated/json/post_entity.g.dart';

@JsonSerializable()
class PostEntity {
  String? id;
  String? postId;
  String? userId;
  String? title;
  String? content;
  int? state;
  int? isTop;
  int? forwardNo;
  String? topic;
  List<String>? imgUrls;
  String? createdDate;
  int? postType;
  int? likeNo;
  int? commentNo;
  String? nickname;
  String? avatar;
  bool? likeState;
  bool? isFriend;
  bool? isChat; //是否点亮或挠一下
  String? resonateUserId;

  PostEntity();

  factory PostEntity.fromJson(Map<String, dynamic> json) =>
      $PostEntityFromJson(json);

  Map<String, dynamic> toJson() => $PostEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
