import 'dart:io';

class AppConfig {
  ///腾讯 IM id
  static const int imAppId = 1600057087;
  static const int imAppId_Dev = 1600074759;

  ///腾讯推送服务 AppKey
  static const int pushCertID_Dev = 44368;
  static const int pushCertID_Dis = 44369;

  static const String pushAppKey =
      "wMmtV2ihU1nbXh4CXwumgEFtK4GYkPoGpGslakzlsT6uyU6Ki8UExNjvKGlaxs9N";

  ///Universal Link
  static const String iOSUniversalLink = "https://daya.com";

  ///微信 AppId
  static const String wxAppId = "wx6c0829119d9f0014";
  static const String wxAppSecret = "25c4d97879b566d85db4d1ebeee248f0";
  static const String wxAppPartnerId = "1706021247";

  ///穿山甲广告插件
  static const String gromoreAdsAppId_iOS = "5660391";
  static const String gromoreRewardAdsID_iOS = "103364992";

  static const String gromoreAdsAppId_Android = "5673736";
  static const String gromoreRewardAdsID_Android = "103409574";

  /// 官方账号
  static const String officialUser1 = "1";

  ///隐私协议地址
  static const String privacyUrl =
      "http://www.dada6.cn/user/privacyAgreement.html";

  ///用户协议地址
  static const String userAgreementUrl =
      "http://www.dada6.cn/user/serviceAgreement.html";

  ///用户协议地址
  static const String vipAgreementUrl =
      "https://www.dada6.cn/user/memberAgreement.html";

  ///官方网址
  static const String officialUrl = "http://www.dada6.cn/";

  ///渠道(可针对不同应用市场)
  ///eg: xiaomi/huawei/other
  static const String channel = "huawei";

  /// 获取开屏广告位id
  static String get splashId {
    return Platform.isAndroid ? '103616758' : '103617230';
  }
}
