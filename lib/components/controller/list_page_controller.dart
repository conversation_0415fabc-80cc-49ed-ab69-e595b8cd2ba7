import 'package:dada/components/controller/base_page_controller.dart';
import 'package:easy_refresh/easy_refresh.dart';

abstract class ListPageController<T, M> extends BasePageController {

  /// 分页的页数
  int pageIndex = 1;

  ///是否还有更多数据
  bool hasMore = true;

  /// 用于列表刷新的id
  Object refreshId = "${M.toString()}_list";

  /// 列表数据
  List<T> data = <T>[];

  final EasyRefreshController refreshController = EasyRefreshController(
      controlFinishLoad: true, controlFinishRefresh: true);

  @override
  void onReady() {
    super.onReady();
    /// 进入页面刷新数据
    refreshData();
  }

  /// 刷新数据
  Future<void> refreshData() async {
    initPage();
    pageState = PageState.loading;
    await _loadData();
  }

  ///初始化分页数据
  void initPage() {
    pageIndex = 1;
    data.clear();
  }

  /// 数据加载
  Future<List<T>?> _loadData({bool? isLoadMore}) async {
    List<T>? list = await loadData(pageIndex);

    /// 数据不为空，则将数据添加到 data 中
    /// 并且分页页数 pageIndex + 1
    if (list != null && list.isNotEmpty) {
      data.addAll(list);
    }

    pageState = PageState.success;

    /// 判断是否有更多数据
    if ((list?.length ?? 0) > 0 && data.isNotEmpty) {
      hasMore = true;
      if (isLoadMore == true) {
        refreshController.finishLoad(IndicatorResult.success);
      } else {
        refreshController.finishRefresh(IndicatorResult.success);
      }
    } else {
      hasMore = false;
      if (isLoadMore == true) {
        refreshController.finishLoad(IndicatorResult.noMore, true);
      } else {
        refreshController.finishRefresh(IndicatorResult.success);
      }
    }

    /// 更新界面
    update([refreshId]);
    return list;
  }

  /// 加载更多
  Future<void> loadMoreData() async {
    pageIndex += 1;
    await _loadData(isLoadMore: true);
  }

  /// 最终加载数据的方法
  Future<List<T>?> loadData(int page);

  @override
  void update([List<Object>? ids, bool condition = true]) {
    ids ??= [refreshId];
    super.update(ids, condition);
  }
}
