import 'dart:io';
import 'package:cached_video_player_plus/cached_video_player_plus.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

class VideoFullScreenPlayPage extends StatefulWidget {
  final VideoPlayerController? videoPlayerController;
  final String? videoUrl;
  final String? heroTag;

  const VideoFullScreenPlayPage(
      {super.key, this.videoPlayerController, this.videoUrl, this.heroTag});

  @override
  State<VideoFullScreenPlayPage> createState() =>
      _VideoFullScreenPlayPageState();
}

class _VideoFullScreenPlayPageState extends State<VideoFullScreenPlayPage> {
  VideoPlayerController? _controller;
  CachedVideoPlayerPlusController? _cachedVideoPlayer;

  @override
  void initState() {
    super.initState();

    if (Platform.isIOS) {
      if (widget.videoPlayerController != null) {
        _controller = widget.videoPlayerController!;
        if (_controller!.value.isInitialized) {
          _controller!.setLooping(true);
          _controller!.play();
        } else {
          _controller!.initialize().then((value) {
            setState(() {
              _controller!.play();
            });
          });
          _controller!.setLooping(true);
        }
      } else {
        bool loop = true;
        _controller = VideoPlayerController.networkUrl(
          Uri.parse(widget.videoUrl!),
        )
          ..initialize().then((value) {
            setState(() {
              _controller!.play();
            });
          })
          ..setLooping(loop);
      }
    } else {
      if (widget.videoUrl != null) {
        _cachedVideoPlayer = CachedVideoPlayerPlusController.networkUrl(
          Uri.parse(widget.videoUrl!),
          videoPlayerOptions: VideoPlayerOptions(
            mixWithOthers: true,
          ),
          invalidateCacheIfOlderThan: const Duration(days: 69),
        )..initialize().then((value) {
            setState(() {
              _cachedVideoPlayer?.play();
            });
          });
        _cachedVideoPlayer?.addListener(() {
          if (_cachedVideoPlayer!.value.isCompleted) {
            _cachedVideoPlayer?.dispose();
            _cachedVideoPlayer = CachedVideoPlayerPlusController.networkUrl(
              Uri.parse(widget.videoUrl!),
              videoPlayerOptions: VideoPlayerOptions(
                mixWithOthers: true,
              ),
              invalidateCacheIfOlderThan: const Duration(days: 69),
            )..initialize().then((value) {
              setState(() {
                _cachedVideoPlayer?.play();
              });
            });
            _cachedVideoPlayer?.setLooping(true);
          } else if (_cachedVideoPlayer!.value.hasError) {
            _cachedVideoPlayer?.dispose();
            ToastUtils.showToast("视频加载失败，请稍后再试");
            Get.back();
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      body: Platform.isAndroid
          ? GestureDetector(
              onTap: () {
                if (_cachedVideoPlayer != null) {
                  _cachedVideoPlayer!.pause();
                  _cachedVideoPlayer!.seekTo(Duration.zero);
                  Get.back();
                }
              },
              child: Center(
                child: _cachedVideoPlayer?.value.isInitialized == true
                    ? Hero(
                        tag: widget.heroTag ??
                            HeroTagName.postImg.of("${widget.videoUrl}"),
                        child: AspectRatio(
                          aspectRatio: _cachedVideoPlayer!.value.aspectRatio,
                          child: CachedVideoPlayerPlus(_cachedVideoPlayer!),
                        ),
                      )
                    : Container(),
              ),
            )
          : GestureDetector(
              onTap: () {
                if (_controller != null) {
                  _controller!.pause();
                  _controller!.seekTo(Duration.zero);
                  Get.back();
                }
              },
              child: Center(
                child: _controller?.value.isInitialized == true
                    ? Hero(
                        tag: widget.heroTag ??
                            HeroTagName.postImg.of("${widget.videoUrl}"),
                        child: AspectRatio(
                          aspectRatio: _controller!.value.aspectRatio,
                          child: VideoPlayer(_controller!),
                        ),
                      )
                    : Container(),
              ),
            ),
    );
  }

  @override
  void dispose() {
    if (widget.videoPlayerController == null) {
      _controller?.dispose();
    }
    if (_cachedVideoPlayer != null) {
      _cachedVideoPlayer?.dispose();
    }
    super.dispose();
  }
}
