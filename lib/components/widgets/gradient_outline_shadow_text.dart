import 'package:flutter/material.dart';

class GradientOutlineShadowText extends StatelessWidget {
  final String text;
  final String fontFamily;
  final double fontSize;
  final Gradient gradient;
  final Color outlineColor;
  final double outlineWidth;
  final Color shadowColor;
  final double shadowBlurRadius;
  final Offset shadowOffset;

  const GradientOutlineShadowText({
    super.key,
    required this.text,
    required this.fontSize,
    required this.fontFamily,
    required this.gradient,
    required this.outlineColor,
    required this.outlineWidth,
    required this.shadowColor,
    required this.shadowBlurRadius,
    required this.shadowOffset,
  });

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Stack(
        children: [
          // 阴影
          Text(
            text,
            style: TextStyle(
              fontSize: fontSize,
              fontFamily: fontFamily,
              shadows: [
                Shadow(
                  color: shadowColor,
                  blurRadius: shadowBlurRadius,
                  offset: shadowOffset,
                ),
              ],
            ),
          ),

          // 描边
          Text(
            text,
            style: TextStyle(
              fontSize: fontSize,
              fontFamily: fontFamily,
              foreground: Paint()
                ..style = PaintingStyle.stroke
                ..strokeWidth = outlineWidth
                ..color = outlineColor,
            ),
          ),

          // 填充
          Text(
            text,
            style: TextStyle(
              fontSize: fontSize,
              fontFamily: fontFamily,
            ),
          ),
        ],
      ),
    );
  }
}
