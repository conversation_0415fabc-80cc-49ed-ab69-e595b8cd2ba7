import 'package:dada/common/values/colors.dart';
import 'package:dada/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommonGradientBtn extends StatelessWidget {
  final String title;
  final double? height;
  final double? width;
  final double? topMargin;
  final double? bottomMargin;
  final double? horizontalMargin;
  final TextStyle? textStyle;
  final bool? enabled;
  final String? normalImage;
  final String? disabledImage;
  final bool? shouldStretch;
  final BoxFit? fit;
  final Function() onTap;

  const CommonGradientBtn({
    super.key,
    required this.title,
    this.height,
    this.width,
    this.topMargin,
    this.bottomMargin,
    this.horizontalMargin,
    this.textStyle,
    this.enabled = true,
    this.normalImage,
    this.disabledImage,
    this.shouldStretch,
    this.fit,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(
          top: topMargin ?? 0,
          bottom: bottomMargin ?? 0,
          left: width != null ? 0 : horizontalMargin ?? 35.w,
          right: width != null ? 0 : horizontalMargin ?? 35.w,
        ),
        padding: EdgeInsets.only(bottom: 1.5.h),
        height: height ?? 50.h,
        width: width,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              enabled == true
                  ? normalImage ?? Assets.imagesLoginBtnBg
                  : disabledImage ?? Assets.imagesGradientBtnDisabledBg1,
            ),
            scale: shouldStretch == true ? 3 : 1,
            centerSlice: shouldStretch == true
                ? Rect.fromLTRB(40.w, 12.h, 50.w, 18.h)
                : null,
            fit: fit ?? BoxFit.fill,
          ),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: textStyle?.color ?? AppColors.colorFF3D3D3D,
            fontSize: textStyle?.fontSize ?? 16.sp,
            height: textStyle?.height ?? 1.5,
          ),
        ),
      ),
    );
  }
}
