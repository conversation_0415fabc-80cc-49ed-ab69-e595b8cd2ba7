import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BadgeWidget extends StatelessWidget {
  final Widget? child;
  final String? text;
  final Color? textColor;
  final Color? borderColor;
  final double? fontSize;
  final double? smallSize;
  final double? largeSize;
  final bool? showBorder;
  final EdgeInsets? padding;
  final Offset? offset;
  final bool? visible;

  const BadgeWidget({
    super.key,
    this.child,
    this.text,
    this.showBorder,
    this.smallSize,
    this.largeSize,
    this.textColor,
    this.borderColor,
    this.fontSize,
    this.padding,
    this.offset,
    this.visible,
  });

  @override
  Widget build(BuildContext context) {
    return Badge(
      padding: EdgeInsets.zero,
      offset: offset,
      label: text != null && int.parse(text!) > 0
          ? Container(
              height: largeSize ?? 20.w,
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 6.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular((largeSize ?? 20.w) / 2),
                border: Border.all(color: Colors.white),
                color: AppColors.colorFFF44444,
              ),
              child: Text(
                int.parse(text!) > 99 ? "99+" : text!,
                maxLines: 1,
                style: TextStyles.common(
                  fontSize ?? 14.sp,
                  textColor ?? Colors.white,
                  w: FontWeight.w500,
                ),
              ),
            )
          : null,
      largeSize: largeSize ?? 20.w,
      smallSize: visible == true ? smallSize : 0,
      backgroundColor: AppColors.colorFFF44444,
      child: child,
    );
  }
}
