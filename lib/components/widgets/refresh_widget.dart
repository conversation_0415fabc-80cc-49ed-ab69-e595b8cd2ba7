import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';

class RefreshWidget {
  static Widget build(
      {required Widget child,
      VoidCallback? onRefresh,
      VoidCallback? onLoadMore,
      EasyRefreshController? refreshController,
      bool enablePullUp = true,
      bool enablePullDown = true,
      Axis? triggerAxis}) {
    return EasyRefresh(
      canRefreshAfterNoMore: true,
      controller: refreshController,
      onRefresh: onRefresh,
      onLoad: onLoadMore,
      header: const ClassicHeader(
        safeArea: false,
        dragText: "下拉刷新",
        armedText: "松开刷新",
        processedText: "刷新完成",
        processingText: "加载中......",
        readyText: "加载中......",
        messageText: "上次刷新时间: %T",
      ),
      footer: ClassicFooter(
        safeArea: false,
        dragText: "上拉加载更多",
        armedText: "松开加载更多",
        processingText: "加载中......",
        messageText: "",
        readyText: "加载中......",
        noMoreText: "没有更多了~",
        noMoreIcon: Container(),
      ),
      triggerAxis: triggerAxis,
      child: child,
    );
  }
}
