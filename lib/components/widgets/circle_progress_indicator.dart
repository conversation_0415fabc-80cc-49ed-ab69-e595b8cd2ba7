import 'package:flutter/material.dart';
import 'dart:math' as math;

class CircleProgressIndicator extends CustomPainter {
  final double size;
  final double progress;
  final Color? color;
  final double? strokeWidth;

  final Paint _paint;
  late double _radius;

  CircleProgressIndicator(this.size, this.progress,
      {this.color, this.strokeWidth})
      : _paint = Paint() {
    _radius = size / 2 - (strokeWidth ?? 1) / 2;
  }

  @override
  void paint(Canvas canvas, Size size) {
    canvas.translate(strokeWidth ?? 1 / 2, strokeWidth ?? 1 / 2);
    _paint //背景
      ..style = PaintingStyle.stroke
      ..color = const Color(0xFFDCDCDC)
      ..strokeWidth = strokeWidth ?? 1;
    canvas.drawCircle(Offset(_radius, _radius), _radius, _paint);

    _paint //进度
      ..color = color ?? const Color(0xFFF63232)
      ..strokeWidth = strokeWidth ?? 1
      ..strokeCap = StrokeCap.round;

    final double sweepAngle = 2 * math.pi * progress;
    canvas.drawArc(
      Rect.fromLTWH(0, 0, _radius * 2, _radius * 2),
      -math.pi / 2,
      sweepAngle,
      false,
      _paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
