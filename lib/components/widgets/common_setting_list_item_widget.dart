import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommonSettingListItemWidget extends StatelessWidget {
  final String title;
  final double? height;
  final String? subTitle;
  final Function()? onTap;
  final Widget? rightWidget;
  final bool? hideSeparatorLine;
  final EdgeInsets? padding;
  final bool? hideMore;

  const CommonSettingListItemWidget(
      {super.key,
      required this.title,
      this.onTap,
      this.subTitle,
      this.rightWidget,
      this.height,
      this.hideSeparatorLine,
      this.padding,
      this.hideMore});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: padding ?? EdgeInsets.symmetric(horizontal: 15.w),
        height: height ?? 50.h,
        decoration: BoxDecoration(
          border: hideSeparatorLine == true
              ? null
              : Border(
                  bottom: BorderSide(
                    width: 1.h,
                    color: AppTheme.themeData.colorScheme.inverseSurface,
                  ),
                ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyles.normal(16.sp),
            ),
            rightWidget ??
                Row(
                  children: [
                    Text(
                      subTitle ?? S.current.unSetting,
                      style: TextStyles.normal(16.sp,
                          c: subTitle?.isNotEmpty == true
                              ? AppTheme.themeData.textTheme.bodyMedium?.color
                              : AppTheme.themeData.textTheme.labelSmall?.color),
                    ),
                    hideMore == true
                        ? Container()
                        : Row(
                            children: [
                              SizedBox(
                                width: 10.w,
                              ),
                              ImageUtils.getImage(
                                  Assets.imagesCommonListMore, 8.w, 14.h),
                            ],
                          )
                  ],
                ),
          ],
        ),
      ),
    );
  }
}
