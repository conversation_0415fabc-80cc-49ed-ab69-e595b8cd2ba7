import 'package:dada/common/values/themes.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CustomAppBar extends AppBar {
  CustomAppBar(
      {super.key,
      String? title,
      Color? backgroundColor,
      Widget? centerWidget,
      Widget? leftWidget,
      List<Widget>? rightWidgets,
      Function()? backAction,
      double? toolbarOpacity})
      : super(
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              systemNavigationBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.dark, // 设置状态栏图标亮度
              systemNavigationBarIconBrightness: Brightness.dark,
            ),
            backgroundColor:
                backgroundColor ?? AppTheme.themeData.scaffoldBackgroundColor,
            elevation: 0,
            centerTitle: true,
            leading: leftWidget ??
                IconButton(
                  onPressed: backAction ?? () => Get.back(),
                  icon: Container(
                    width: 28.w,
                    height: 28.w,
                    alignment: Alignment.center,
                    child:
                        ImageUtils.getImage(Assets.imagesNaviBack, 8.w, 14.w),
                  ),
                ),
            title: centerWidget ??
                Text(
                  title ?? "",
                  style: TextStyle(
                    color: AppTheme.themeData.textTheme.titleLarge?.color,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
            actions: rightWidgets);
}
