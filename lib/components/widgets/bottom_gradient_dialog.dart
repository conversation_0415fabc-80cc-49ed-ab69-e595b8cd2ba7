import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class BottomGradientDialog extends StatelessWidget {
  final double? height;
  final String title;
  final Widget child;
  final Widget? titleLeftBtn;
  final List<double>? stops;
  final bool? isShowCloseBtn;

  const BottomGradientDialog(
      {super.key,
      required this.title,
      required this.child,
      this.stops,
      this.height,
      this.titleLeftBtn,
      this.isShowCloseBtn});

  @override
  Widget build(BuildContext context) {
    return GradientWidget(
      width: ScreenUtil().screenWidth,
      height: height,
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
      ),
      stops: stops ?? const [0, 0.45],
      colors: const [AppColors.colorFFD2F6C0, Colors.white],
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.w),
            child: IntrinsicHeight(
              child: Stack(
                children: [
                  ///title 左边按钮
                  titleLeftBtn != null
                      ? Align(
                          alignment: Alignment.centerLeft,
                          child: titleLeftBtn!,
                        )
                      : Container(),

                  ///标题
                  Center(
                    child: Text(
                      title,
                      style: TextStyles.medium(18.sp),
                    ),
                  ),
                  ///title 右边关闭按钮
                  Visibility(
                    visible: isShowCloseBtn ?? true,
                    child: Positioned(
                      right: 0.w,
                      top: 0.h,
                      child: GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.close,
                          size: 22.w,
                          weight: 500,
                          color: AppColors.colorFF666666,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          child,
        ],
      ),
    );
  }
}
