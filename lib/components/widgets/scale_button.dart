import 'package:dada/utils/extensions/anim_extension.dart';
import 'package:dada/utils/extensions/lifecycle_extension.dart';
import 'package:dada/components/mixins/state_lifecycle_mixin.dart';
import 'package:flutter/material.dart';

///按下缩小，松手回弹的按钮
class ScaleButton extends StatefulWidget {
  final Widget child;
  final Function onTap;

  const ScaleButton({required this.onTap, required this.child, super.key});

  @override
  State<ScaleButton> createState() => _ScaleButtonState();
}

class _ScaleButtonState extends State<ScaleButton>
    with SingleTickerProviderStateMixin, StateLifecycleMixin {
  //没有点击的话，不初始化动画相关的东西
  bool pressed = false;

  late AnimationController animationController = AnimationController(
      vsync: this, duration: const Duration(milliseconds: 120))
    ..attachLifecycle(this);

  late Animation<double> scaleAnimation =
      Tween<double>(begin: 1.0, end: 0.94).animate(animationController);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      //按下前，不初始化动画相关
      child: pressed
          ? ScaleTransition(scale: scaleAnimation, child: widget.child)
          : widget.child,
    );
  }

  ///恢复大小，触发点击
  _reverseAndPerformClick() {
    animationController.reverse().whenComplete(() {
      if (mounted) {
        widget.onTap.call();
      }
    });
  }

  _onTapDown(TapDownDetails details) {
    if (!pressed) {
      setState(() => pressed = true);
    }
    if (animationController.isDismissed) {
      animationController.forward();
    }
  }

  _onTapUp(TapUpDetails details) {
    animationController.doOnAnimComplete(() {
      _reverseAndPerformClick();
    });
  }

  _onTapCancel() {
    if (animationController.status == AnimationStatus.forward ||
        animationController.isCompleted) {
      animationController.reverse();
    }
  }
}
