import 'package:dada/utils/string_util.dart';
import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';

typedef TextBuilder = Text Function(
    BuildContext context, String text, TextStyle textStyle);

class MarqueeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double containerWidth;
  final double containerHeight;
  final TextBuilder textBuilder;
  final double? scrollTime;
  final Axis? scrollDirection;

  const MarqueeText({
    super.key,
    required this.text,
    required this.textStyle,
    required this.containerWidth,
    required this.containerHeight,
    required this.textBuilder,
    this.scrollDirection,
    this.scrollTime,
  });

  @override
  Widget build(BuildContext context) {
    Axis scrollAxis = Axis.horizontal;
    final textWidth =
        StringUtils.calculateTextWidth(text, textStyle: textStyle);
    final textHeight = StringUtils.calculateTextHeight(text,
        textStyle: textStyle, maxWidth: containerWidth);
    if (textHeight > containerHeight) {
      scrollAxis = Axis.vertical;
    } else if (textWidth > containerWidth) {
      scrollAxis = Axis.horizontal;
    }
    if (scrollDirection != null) {
      scrollAxis = scrollDirection!;
    }

    return textWidth < containerWidth && textHeight < containerHeight
        ? textBuilder(context, text, textStyle)
        : Container(
            alignment: scrollAxis == Axis.horizontal
                ? Alignment.centerLeft
                : Alignment.center,
            width: containerWidth,
            height: containerHeight,
            child: Marquee(
              text: text,
              style: textStyle,
              scrollAxis: scrollAxis,
              crossAxisAlignment: scrollAxis == Axis.horizontal
                  ? CrossAxisAlignment.start
                  : CrossAxisAlignment.center,
              blankSpace: 20.0,
              velocity: scrollTime != null
                  ? ((textWidth - containerWidth) / scrollTime!)
                  : 30,
              pauseAfterRound: const Duration(milliseconds: 2000),
              showFadingOnlyWhenScrolling: true,
              fadingEdgeStartFraction: 0,
              fadingEdgeEndFraction: 0,
              startPadding: 0.0,
              accelerationDuration: const Duration(milliseconds: 100),
              accelerationCurve: Curves.linear,
              decelerationDuration: const Duration(milliseconds: 100),
              // decelerationCurve: Curves.easeOut,
              textDirection: TextDirection.ltr,
            ),
          );
  }
}
