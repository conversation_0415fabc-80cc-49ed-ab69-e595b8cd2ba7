import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class InputTextFieldDialog extends StatefulWidget {
  final String title;
  final String? subTitle;
  final String? text;
  final String? hintText;
  final bool? isMultiLine;
  final double? leftMargin;
  final int? maxLength;
  final double? width;
  final double? textFieldHeight;
  final double? textFieldCornerRadius;
  final double? horizontalMargin;
  final String? confirmBtnTitle;
  final bool? showCloseBtn;
  final bool? hideCancelBtn;
  final Function(String value) onSubmit;

  const InputTextFieldDialog(
      {super.key,
      required this.title,
      this.subTitle,
      this.text,
      required this.onSubmit,
      this.leftMargin,
      this.maxLength,
      this.isMultiLine,
      this.width,
      this.horizontalMargin,
      this.confirmBtnTitle,
      this.hintText,
      this.textFieldCornerRadius,
      this.showCloseBtn,
      this.hideCancelBtn,
      this.textFieldHeight});

  @override
  State<InputTextFieldDialog> createState() => _InputTextFieldDialogState();
}

class _InputTextFieldDialogState extends State<InputTextFieldDialog> {
  TextEditingController editingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    editingController.text = widget.text ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ??
          (widget.horizontalMargin != null
              ? (ScreenUtil().screenWidth - widget.horizontalMargin! * 2)
              : 270.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        gradient: const LinearGradient(
          colors: [
            AppColors.colorFFD2F6C0,
            Colors.white,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0, 0.32],
        ),
      ),
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 25.h),
                child: Text(
                  widget.title,
                  style: TextStyles.medium(16.sp, w: FontWeight.w500),
                ),
              ),
              Visibility(
                visible: widget.subTitle?.isNotEmpty == true,
                child: Padding(
                  padding: EdgeInsets.only(top: 10.h),
                  child: Text(
                    widget.subTitle ?? "",
                    style: TextStyles.common(14.sp, AppColors.colorFF666666),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    top: 15.h,
                    left: widget.leftMargin ?? 45.w,
                    right: widget.leftMargin ?? 45.w),
                height: widget.isMultiLine == true
                    ? 80.h
                    : (widget.textFieldHeight ?? 35.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                      widget.textFieldCornerRadius ?? 5.r),
                  border: Border.all(
                    color: AppColors.colorFFE1E1E1,
                  ),
                  color: AppColors.colorFFF5F5F5,
                ),
                child: CustomTextField.build(
                    controller: editingController,
                    hintText: widget.hintText,
                    contentPadding: EdgeInsets.only(
                        left: 10.w,
                        right: 10.w,
                        bottom: widget.isMultiLine == true ? 15.h : 0),
                    style: TextStyles.normal(16.sp),
                    showSuffixIcon: widget.isMultiLine != true,
                    maxLines: widget.isMultiLine == true ? 3 : 1,
                    maxLength: widget.maxLength,
                    showLeftLength:
                        widget.maxLength != null && widget.isMultiLine == true,
                    onChanged: (value) {}),
              ),
              Padding(
                padding: EdgeInsets.only(bottom: 28.h, top: 20.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Visibility(
                      visible: !(widget.hideCancelBtn == true),
                      child: GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          width: 80.w,
                          height: 35.h,
                          margin: EdgeInsets.only(right: 10.w),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(17.5.r),
                            border: Border.all(color: AppColors.colorFF999999),
                          ),
                          child: Text(
                            S.current.cancel,
                            style: TextStyles.normal(16.sp),
                          ),
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        widget.onSubmit.call(editingController.text);
                        Get.back();
                      },
                      child: Container(
                        width: 80.w,
                        height: 35.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              AppColors.colorFFA0F6A5,
                              AppColors.colorFF58C75D
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(17.5.r),
                        ),
                        child: Text(
                          widget.confirmBtnTitle ?? S.current.sure,
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Visibility(
            visible: widget.showCloseBtn == true,
            child: Positioned(
              right: 10.w,
              top: 10.h,
              child: GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Icon(
                  Icons.close,
                  size: 20.w,
                  color: AppColors.colorFF666666,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
