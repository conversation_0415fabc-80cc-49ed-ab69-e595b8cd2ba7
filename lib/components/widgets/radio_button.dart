import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RadioButton extends StatelessWidget {
  final String? text;
  final bool? selected;
  final double? fontSize;
  final double? radioSize;
  final Color? normalColor;
  final Color? selectedColor;
  final Color? normalTextColor;
  final Color? selectedTextColor;
  final double? spacing;
  final Function()? onTap;

  const RadioButton(
      {super.key,
      this.text,
      this.selected = false,
      this.fontSize,
      this.radioSize,
      this.normalColor,
      this.selectedColor,
      this.normalTextColor,
      this.selectedTextColor,
      this.spacing,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            selected == true
                ? Icons.radio_button_checked
                : Icons.radio_button_off,
            size: radioSize ?? 16.w,
            color: selected == true
                ? selectedColor ?? AppColors.colorFF62CE67
                : normalColor ?? AppColors.colorFF999999,
          ),
          SizedBox(
            width: text?.isNotEmpty == true ? spacing ?? 2.w : 0.w,
          ),
          Text(
            text ?? "",
            style: TextStyles.normal(
              fontSize ?? 16.sp,
              c: selected == true
                  ? selectedTextColor ??
                      selectedColor ??
                      AppColors.colorFF62CE67
                  : normalTextColor ?? normalColor ?? AppColors.colorFF3D3D3D,
            ),
          ),
        ],
      ),
    );
  }
}
