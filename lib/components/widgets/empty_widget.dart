import 'package:dada/common/values/themes.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/utils/extensions/string_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class EmptyWidget extends StatelessWidget {
  /// 加载时候文案
  final String contentText;

  /// 加载时候图片
  final String? imagePath;

  final EdgeInsets? padding;

  final double? imageWidth;
  final double? imageHeight;

  final TextStyle? contentNormalTextStyle;

  final Function()? onTap;

  final Widget? child;

  final Color? backgroundColor;

  final double? imageTextSpacing;

  EmptyWidget({
    super.key,
    String? content,
    EdgeInsets? padding,
    double? imageWidth,
    double? imageHeight,
    String? image,
    double? imageTextSpacing,
    TextStyle? contentNormalTextStyle,
    this.child,
    this.onTap,
    this.backgroundColor,
  })  : contentText = content ?? S.current.emptyData,
        imagePath = image ?? Assets.imagesEmptyDefault,
        padding = padding ?? EdgeInsets.zero,
        imageWidth = imageWidth ?? 221.w,
        imageHeight = imageHeight ?? 176.h,
        imageTextSpacing = imageTextSpacing ?? 20.h,
        contentNormalTextStyle = contentNormalTextStyle ??
            TextStyle(
                fontSize: 16.sp,
                color: AppTheme.themeData.textTheme.displayLarge?.color);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor,
      child: InkWell(
        onTap: onTap,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: padding?.top,
              ),
              imagePath?.isNotEmpty == true
                  ? Image(
                      width: imageWidth!,
                      height: imageHeight!,
                      image: AssetImage(imagePath!))
                  : Container(),
              SizedBox(
                height: imageTextSpacing,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                child: Text(
                  contentText.fixAutoLines(),
                  style: contentNormalTextStyle!,
                  textAlign: TextAlign.center,
                ),
              ),
              child ?? Container(),
              Container(
                height: padding?.bottom,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
