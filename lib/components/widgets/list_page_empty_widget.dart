import 'package:dada/components/widgets/empty_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ListPageEmptyWidget extends StatelessWidget {
  final double? naviBarHeight;
  final double? bottomBarHeight;
  final String? content;
  final Function()? onTap;
  final Widget? child;

  const ListPageEmptyWidget(
      {super.key,
      this.naviBarHeight,
      this.bottomBarHeight,
      this.content,
      this.onTap, this.child});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.zero,
      children: [
        SizedBox(
          height: ScreenUtil().screenHeight -
              ScreenUtil().statusBarHeight -
              ScreenUtil().bottomBarHeight -
              (naviBarHeight ?? 50.h) -
              (bottomBarHeight ?? 56.h),
          child: EmptyWidget(
            content: content,
            onTap: onTap,
          ),
        )
      ],
    );
  }
}
