import 'package:dada/generated/assets.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AvatarWidget extends StatelessWidget {
  final double size;
  final String? url;
  final UserInfoEntity? userInfo;
  final String? heroTag;
  final double? cornerRadius;
  final double? borderWidth;
  final String? dressUp;
  final Color? borderColor;
  final bool? showBorder;
  final bool? showPlaceholder;
  final bool? showDressUp;
  final Function()? onTap;

  const AvatarWidget(
      {super.key,
      required this.size,
      this.url,
      this.userInfo,
      this.heroTag,
      this.cornerRadius,
      this.dressUp,
      this.showPlaceholder = true,
      this.showDressUp = true,
      this.borderWidth,
      this.borderColor,
      this.showBorder,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return SizedOverflowBox(
      size: Size(size, size),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: size,
            height: size,
            decoration: borderColor != null
                ? BoxDecoration(
                    border: Border.all(
                      color: borderColor ?? Colors.white,
                      width: borderWidth ?? 1.w,
                    ),
                  )
                : null,
            child: GestureDetector(
              onTap: onTap,
              child: heroTag != null
                  ? Hero(
                      tag: heroTag!,
                      child: ClipOval(
                        child: ImageUtils.getImage(
                          userInfo?.avatar ?? url ?? "",
                          size,
                          size,
                          fit: BoxFit.cover,
                          showPlaceholder: showPlaceholder == true,
                          placeholder: showPlaceholder == true
                              ? Assets.imagesAvatarPlaceholder
                              : null,
                        ),
                      ),
                    )
                  : ClipOval(
                      child: ImageUtils.getImage(
                        userInfo?.avatar ?? url ?? "",
                        size,
                        size,
                        fit: BoxFit.cover,
                        showPlaceholder: showPlaceholder == true,
                        placeholder: showPlaceholder == true
                            ? Assets.imagesAvatarPlaceholder
                            : null,
                      ),
                    ),
            ),
          ),
          showDressUp == true &&
                  (userInfo?.avatarFrame != null || dressUp != null)
              ? ImageUtils.getImage(userInfo?.avatarFrame ?? dressUp ?? "",
                  size * 1.5, size * 1.5,
                  fit: BoxFit.cover)
              : Container(),
        ],
      ),
    );
  }
}
