import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SexAgeWidget extends StatelessWidget {
  final int sex;
  final int age;
  final double? height;

  const SexAgeWidget(
      {super.key, required this.sex, required this.age, this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 20.h,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 5.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: sex == 1 ? AppColors.colorFFFF97E0 : AppColors.colorFF6265FF,
      ),
      child: Row(
        children: [
          ImageUtils.getImage(
              sex == 1
                  ? Assets.imagesSexFemaleWhite
                  : Assets.imagesSexMaleWhite,
              10.w,
              10.w),
          SizedBox(
            width: 6.w,
          ),
          Text(
            age.toString(),
            style: TextStyles.common(12.sp, Colors.white),
          ),
        ],
      ),
    );
  }
}
