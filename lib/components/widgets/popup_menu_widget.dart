import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_popup_menu_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PopupMenuWidget extends StatelessWidget {
  final Widget child;
  final List<String>? titles;
  final List<PopupMenuItem<int>>? items;
  final Offset? offset;
  final Function(int index)? onSelected;

  ///titles 和 items 不能同时为空
  const PopupMenuWidget(
      {super.key,
      required this.child,
      this.titles,
      this.items,
      this.offset,
      this.onSelected})
      : assert(
          !(titles == null && items == null),
          'You must set one of [titles] or [items], not both null.',
        );

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton(
      offset: offset ?? Offset.zero,
      itemBuilder: (context) {
        if (items?.isNotEmpty == true) {
          return items!;
        } else {
          return titles?.isNotEmpty == true
              ? titles!.map((e) {
                  int index = titles!.indexOf(e);
                  return CustomPopupMenuItem.create(
                      child: Center(
                        child: Text(
                          e,
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                      value: index,
                      onTap: () {
                        onSelected?.call(index);
                      });
                }).toList()
              : [];
        }
      },
      child: child,
    );
  }
}
