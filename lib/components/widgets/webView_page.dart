import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? controller;
  InAppWebViewSettings? settings;
  late String _webUrl;
  RxString title = "".obs;

  @override
  void initState() {
    super.initState();

    _webUrl = Get.parameters["url"] ?? "";

    settings = InAppWebViewSettings(
      applicationNameForUserAgent: "搭吖",
      isInspectable: true,
      allowFileAccessFromFileURLs: true,
      allowUniversalAccessFromFileURLs: true,
      useOnLoadResource: true,
      preferredContentMode: UserPreferredContentMode.MOBILE,
      useShouldOverrideUrlLoading: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        centerWidget: Obx(
          () => Text(
            title.value,
            style: TextStyles.normal(16.sp),
          ),
        ),
      ),
      body: InAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(_webUrl)),
        initialSettings: settings,
        onWebViewCreated: (controller) {
          this.controller = controller;
        },
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          final uri = navigationAction.request.url;
          if (uri != null) {
            final scheme = uri.scheme;
            if (!scheme.startsWith('http')) {
              return NavigationActionPolicy.CANCEL;
            }
          }
          return NavigationActionPolicy.ALLOW;
        },
        onLoadStop: (controller, url) async {
          final getTitle = await controller.getTitle();
          if (getTitle != null) {
            // title.value = getTitle;
          }
        },
      ),
    );
  }
}
