import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/themes.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomTabBar extends TabBar {
  CustomTabBar({
    super.key,
    required super.tabs,
    super.tabAlignment,
    super.controller,
    super.isScrollable = true,
    super.padding,
    super.indicatorColor,
    super.automaticIndicatorColorAdjustment = true,
    double? indicatorWeight,
    EdgeInsetsGeometry? indicatorPadding,
    Decoration? indicator,
    TabBarIndicatorSize? indicatorSize,
    Color? dividerColor,
    super.labelColor = AppColors.colorFF333333,
    TextStyle? labelStyle,
    EdgeInsetsGeometry? labelPadding,
    super.unselectedLabelColor = AppColors.colorFF666666,
    TextStyle? unselectedLabelStyle,
    super.dragStartBehavior = DragStartBehavior.start,
    super.overlayColor,
    super.mouseCursor,
    super.enableFeedback,
    super.onTap,
    super.physics,
    super.splashFactory,
    super.splashBorderRadius,
  }) : super(
            dividerColor: dividerColor ?? Colors.transparent,
            labelPadding: labelPadding ?? EdgeInsets.zero,
            indicatorSize: indicatorSize ?? TabBarIndicatorSize.label,
            indicatorPadding: indicatorPadding ??
                EdgeInsets.only(bottom: 15.h, top: 24.h, left: 5.w, right: 5.w),
            indicator: indicator ??
                const BoxDecoration(
                  color: AppColors.colorFF23AF28,
                ),
            indicatorWeight: indicatorWeight ?? 4.h,
            labelStyle: labelStyle ??
                TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.colorFF333333),
            unselectedLabelStyle: unselectedLabelStyle ??
                TextStyle(fontSize: 16.sp, color: AppColors.colorFF666666));
}

class ScrollableTabBar extends TabBar {
  ScrollableTabBar({
    super.key,
    required super.tabs,
    super.controller,
    super.isScrollable = true,
    EdgeInsetsGeometry? padding,
    super.indicatorColor,
    super.automaticIndicatorColorAdjustment = true,
    super.indicatorWeight = 2.0,
    EdgeInsetsGeometry? indicatorPadding,
    Decoration? indicator,
    super.indicatorSize,
    super.dividerColor,
    super.labelColor,
    TextStyle? labelStyle,
    super.labelPadding,
    super.unselectedLabelColor,
    TextStyle? unselectedLabelStyle,
    super.dragStartBehavior = DragStartBehavior.start,
    super.overlayColor,
    super.mouseCursor,
    super.enableFeedback,
    super.onTap,
    super.physics,
    super.splashFactory,
    super.splashBorderRadius,
  }) : super(
            padding: padding ?? EdgeInsets.symmetric(horizontal: 12.w),
            indicatorPadding:
                indicatorPadding ?? EdgeInsets.symmetric(vertical: 9.w),
            indicator: indicator ??
                const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(100)),
                  gradient: LinearGradient(colors: [
                    Colors.orange,
                    Colors.cyan,
                  ]),
                ),
            labelStyle: labelStyle ??
                TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w700,
                    color: AppTheme
                        .themeData.textTheme.labelMedium?.decorationColor),
            unselectedLabelStyle: unselectedLabelStyle ??
                TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme
                        .themeData.textTheme.labelSmall?.decorationColor));
}
