import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';
import 'package:svgaplayer_flutter/svgaplayer_flutter.dart';

class SaveAudioPage extends StatefulWidget {
  final String audioUrl;
  final int duration;
  final String? author;

  const SaveAudioPage(
      {super.key, required this.audioUrl, required this.duration, this.author});

  @override
  State<SaveAudioPage> createState() => _SaveAudioPageState();
}

class _SaveAudioPageState extends State<SaveAudioPage>
    with SingleTickerProviderStateMixin {
  TextEditingController editingController = TextEditingController();
  late SVGAAnimationController svgaAnimationController;
  final AudioPlayerUtils audioPlayer = AudioPlayerUtils();

  RxInt inputNum = 0.obs;

  @override
  void initState() {
    super.initState();

    svgaAnimationController = SVGAAnimationController(vsync: this);
    setSvgaVideoItem();

    audioPlayer.onInit();
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.complete) {
        audioPlayer.stop();
        svgaAnimationController.stop();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: S.current.saveAudio,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 10.h, left: 20.w),
            child: Text(
              S.current.saveAudio,
              style: TextStyles.normal(16.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h, left: 20.w),
            child: Text(
              widget.author ?? "",
              style: TextStyles.common(14.sp, AppColors.colorFF666666),
            ),
          ),
          GestureDetector(
            onTap: () {
              playOrStopAudioPlayer();
            },
            child: Container(
              width: 127.w,
              height: 40.h,
              margin: EdgeInsets.only(top: 5.h, left: 20.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: AppColors.colorFFACE49E,
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 12.w,
                  ),
                  SizedBox(
                    width: 12.w,
                    height: 16.h,
                    child: Stack(
                      children: [
                        Visibility(
                          visible: !svgaAnimationController.isAnimating,
                          child: ImageUtils.getImage(
                              Assets.imagesAudioBoxVolume, 12.w, 16.h),
                        ),
                        Visibility(
                          visible: svgaAnimationController.isAnimating,
                          child: SVGAImage(
                            svgaAnimationController,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Text(
                    "${widget.duration}\"",
                    style: TextStyles.normal(16.sp),
                  ),
                ],
              ),
            ),
          ),
          Container(
            height: 1,
            margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
            color: AppColors.colorFFE5E5E5,
          ),
          Padding(
            padding: EdgeInsets.only(left: 20.w),
            child: Text(
              S.current.remark,
              style: TextStyles.normal(16.sp),
            ),
          ),
          Container(
            margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 10.h),
            child: Container(
              height: 90.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: AppColors.colorFFF5F5F5,
              ),
              child: Stack(
                children: [
                  CustomTextField.build(
                    contentPadding: EdgeInsets.only(left: 15.w, top: 10.h),
                    controller: editingController,
                    hintText: S.current.inputRemark,
                    maxLines: 5,
                    maxLength: 20,
                    showLeftLength: true,
                    onChanged: (value) {
                      inputNum.value = editingController.text.length;
                    },
                    suffixIconOnTap: () {
                      inputNum.value = editingController.text.length;
                    },
                  ),
                  Positioned(
                    bottom: 10.h,
                    right: 0.w,
                    child: Obx(
                      () => Text(
                        "${inputNum.value}/20",
                        style: TextStyles.common(12.sp,
                            AppTheme.themeData.textTheme.labelSmall?.color),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              ///保存
              saveAudioToAudioBox();
            },
            child: Container(
              alignment: Alignment.center,
              margin: EdgeInsets.only(top: 15.h),
              padding: EdgeInsets.symmetric(horizontal: 35.w),
              height: 50.h,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(Assets.imagesLoginBtnBg),
                ),
              ),
              child: Center(
                child: Text(
                  S.current.save,
                  style: TextStyle(
                    color: AppColors.colorFF3D3D3D,
                    fontSize: 16.sp,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void playOrStopAudioPlayer() {
    if (audioPlayer.state == AudioPlayerState.playing) {
      audioPlayer.stop();
      svgaAnimationController.stop();
      setState(() {});
    } else {
      audioPlayer.setUrl(widget.audioUrl);
      audioPlayer.play();
      svgaAnimationController.repeat();
      setState(() {});
    }
  }

  void setSvgaVideoItem() async {
    final videoItem =
        await SVGAParser.shared.decodeFromAssets(Assets.svgaAudioPlaying);
    svgaAnimationController.videoItem = videoItem;
    svgaAnimationController.reset();
  }

  void saveAudioToAudioBox() async {
    String remark = "";
    if(editingController.text=="" || editingController.text.isEmpty){
      remark="${widget.author!}的语音";
    }else{
      remark=editingController.text;
    }
    bool success = await ApiService().saveAudioToAudioBox(
        audioUrl: widget.audioUrl,
        duration: widget.duration,
        remark: remark);
    if (success) {
      Get.back();
    }
  }

  @override
  void dispose() {
    svgaAnimationController.stop();
    audioPlayer.stop();

    super.dispose();
  }
}
