import 'package:dada/common/values/colors.dart';
import 'package:flutter/material.dart';

class SwitchButton extends StatelessWidget {
  final bool value;
  final Function(bool) onChanged;

  const SwitchButton({super.key, required this.value, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return Switch(
      value: value,
      onChanged: onChanged,
      inactiveThumbColor: Colors.white,
      inactiveTrackColor: AppColors.colorFFD3D3D3,
      activeColor: Colors.white,
      activeTrackColor: AppColors.colorFF65D06A,
      trackOutlineColor: WidgetStateProperty.all<Color>(Colors.transparent),
      trackOutlineWidth: WidgetStateProperty.all<double>(0),
    );
  }
}
