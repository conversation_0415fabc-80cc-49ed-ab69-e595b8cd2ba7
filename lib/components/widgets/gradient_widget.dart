import 'package:dada/common/values/colors.dart';
import 'package:flutter/material.dart';

class GradientWidget extends StatelessWidget {
  final double? width;
  final double? height;
  final List<Color>? colors;
  final List<double>? stops;
  final double? cornerRadius;
  final BorderRadiusGeometry? borderRadius;
  final Widget? child;
  final AlignmentGeometry? begin;
  final AlignmentGeometry? end;
  final BoxBorder? border;

  const GradientWidget({
    super.key,
    this.width,
    this.height,
    this.colors,
    this.stops,
    this.cornerRadius,
    this.child,
    this.begin,
    this.end,
    this.borderRadius,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(cornerRadius ?? 0),
        border: border,
        gradient: LinearGradient(
          begin: begin ?? Alignment.topCenter,
          end: end ?? Alignment.bottomCenter,
          stops: stops ?? [0, 1],
          colors: colors ??
              [
                AppColors.colorFFADF0B0,
                AppColors.color00E4FFBA,
              ],
        ),
      ),
      child: child,
    );
  }
}
