import 'package:dada/generated/assets.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tolyui/tolyui.dart';

///用户标签视图
class UserMedalTagWidget extends StatelessWidget {
  final UserInfoEntity? userInfo;
  final Placement? placement;

  const UserMedalTagWidget({super.key, required this.userInfo, this.placement});

  @override
  Widget build(BuildContext context) {
    if (userInfo == null) {
      return Container();
    }
    return Padding(
      padding: EdgeInsets.only(top: 2.5.h, bottom: 2.5.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ///先行者勋章
          Visibility(
            visible: userInfo!.isPioneer == 1,
            child: TolyTooltip(
              gap: 10.h,
              triggerMode: TooltipTriggerMode.tap,
              placement: placement ?? Placement.top,
              message: "乘风破浪的先行者。可以在各种活动中获得额外福利！",
              child:
                  ImageUtils.getImage(Assets.imagesUserTagXianXing, 20.w, 20.w),
            ),
          ),

          ///先行者勋章
          Visibility(
            visible: userInfo!.isInitUser == 1,
            child: Padding(
              padding: EdgeInsets.only(left: 5.w),
              child: TolyTooltip(
                gap: 10.h,
                triggerMode: TooltipTriggerMode.tap,
                placement: placement ?? Placement.top,
                message: "我们珍视每一位元老，会增加好运哦！",
                child: ImageUtils.getImage(
                    Assets.imagesUserTagYuanLao, 20.w, 20.w),
              ),
            ),
          ),

          ///传火者勋章
          Visibility(
            visible: userInfo!.isFireKeeper == 1,
            child: Padding(
              padding: EdgeInsets.only(left: 5.w),
              child: TolyTooltip(
                gap: 10.h,
                triggerMode: TooltipTriggerMode.tap,
                placement: placement ?? Placement.top,
                message: "传火者，在后续活动中可以获得特效奖励！",
                child: ImageUtils.getImage(Assets.imagesUserTagHuo, 20.w, 20.w),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
