import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HighlightTextLabel extends StatelessWidget {
  final String content;
  final String? targetText;
  final TextStyle? normalTextStyle;
  final TextStyle? highlightTextStyle;

  const HighlightTextLabel(
      {super.key,
      required this.content,
      this.normalTextStyle,
      this.highlightTextStyle,
      this.targetText});

  @override
  Widget build(BuildContext context) {
    if (targetText == null || targetText?.isEmpty == true) {
      return Text(
        content,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: normalTextStyle,
      );
    }

    List<TextSpan> richList = [];
    int start = 0;
    int end;

    //遍历，进行多处高亮
    while ((end = content.indexOf(targetText!, start)) != -1) {
      //如果搜索内容在开头位置，直接高亮，此处不执行
      if (end != 0) {
        richList.add(
          TextSpan(
            text: content.substring(start, end),
            style: normalTextStyle ?? TextStyles.normal(16.sp),
          ),
        );
      }
      //高亮内容
      richList.add(
        TextSpan(
          text: targetText,
          style: highlightTextStyle ??
              TextStyles.common(16.sp, AppColors.colorFF23AF28),
        ),
      );
      //赋值索引
      start = end + targetText!.length;
    }
    //搜索内容只有在开头或者中间位置，才执行
    if (start != content.length) {
      richList.add(
        TextSpan(
            text: content.substring(start, content.length),
            style: normalTextStyle ?? TextStyles.normal(16.sp)),
      );
    }

    return RichText(
      maxLines: 1,
      text: TextSpan(children: richList),
      overflow: TextOverflow.ellipsis,
    );
  }
}
