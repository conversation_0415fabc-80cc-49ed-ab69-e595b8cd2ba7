import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CustomTextField {
  static Widget build({
    required TextEditingController controller,
    FocusNode? focusNode,
    int? maxLines = 1,
    int? minLines = 1,
    TextStyle? style,
    ValueChanged<String>? onChanged,
    Function()? onTap,
    ValueChanged<String>? onSubmitted,
    String? hintText,
    String? prefixText,
    TextStyle? hintStyle,
    int? maxLength,
    bool obscureText = false,
    Color? fillColor,
    bool isCollapsed = false,
    VoidCallback? onEditingComplete,
    TextInputAction? textInputAction,
    bool autofocus = false,
    TextInputType? keyboardType,
    bool? expands,
    bool? enabled,
    bool? readOnly,
    EdgeInsetsGeometry? contentPadding,
    TextAlign? textAlign,
    List<TextInputFormatter>? inputFormatters,
    InputDecoration? decoration,
    bool? showSuffixIcon,
    bool? showLeftLength,
    Widget? suffixIcon,
    BoxConstraints? suffixIconConstraints,
    Function()? suffixIconOnTap,
    Color? leftCountColor,
  }) {
    RxString inputString = "".obs;
    inputString.value = controller.text;
    controller.addListener(() {
      inputString.value = controller.text;
    });
    return Stack(
      alignment:
          maxLines == 1 ? Alignment.centerLeft : AlignmentDirectional.topStart,
      children: [
        TextField(
          expands: expands ?? false,
          focusNode: focusNode,
          inputFormatters: inputFormatters,
          controller: controller,
          keyboardType: keyboardType,
          textAlign: textAlign ?? TextAlign.left,
          cursorColor: Colors.blue,
          autofocus: autofocus,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          obscureText: obscureText,
          style: style ??
              TextStyle(
                  color: AppTheme.themeData.textTheme.labelMedium?.color,
                  fontSize: style?.fontSize),
          onChanged: (value) {
            inputString.value = controller.text;
            onChanged?.call(inputString.value);
          },
          onSubmitted: onSubmitted,
          onEditingComplete: onEditingComplete,
          onTap: onTap,
          textInputAction: textInputAction,
          enabled: enabled,
          readOnly: readOnly ?? false,

          ///样式
          decoration: decoration ??
              InputDecoration(
                counterText: "",
                isCollapsed: isCollapsed,
                fillColor: fillColor ?? Colors.transparent,
                filled: true,
                hintText: hintText ?? "",
                prefixText: prefixText ?? "",
                hintStyle: hintStyle ??
                    TextStyle(
                        color: AppTheme.themeData.hintColor,
                        fontSize: style?.fontSize),
                border: _buildBorder(),
                enabledBorder: _buildBorder(),
                disabledBorder: _buildBorder(),
                focusedBorder: _buildBorder(),
                contentPadding:
                    contentPadding ?? EdgeInsets.only(top: 5.h, bottom: 5.h),
                suffixIcon: suffixIcon ??
                    ((showSuffixIcon == true)
                        ? Obx(
                            () {
                              if (inputString.isNotEmpty) {
                                return GestureDetector(
                                  onTap: () {
                                    controller.clear();
                                    inputString.value = controller.text;
                                    onChanged?.call("");
                                    suffixIconOnTap?.call();
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        right: showLeftLength == true &&
                                                maxLines == 1
                                            ? 50.w
                                            : 15.w),
                                    child: ImageUtils.getImage(
                                        Assets.imagesCloseRounded, 16.w, 16.w),
                                  ),
                                );
                              }
                              return const SizedBox();
                            },
                          )
                        : null),
                suffixIconConstraints: suffixIconConstraints ??
                    ((showSuffixIcon == true)
                        ? BoxConstraints(
                            maxWidth: (showLeftLength == true && maxLines == 1
                                    ? 66.w
                                    : 16.w) +
                                15.w,
                            maxHeight: 16.w,
                            minWidth: 16.w,
                            minHeight: 16.w,
                          )
                        : null),
              ),
        ),
        Visibility(
          visible: maxLength != null && showLeftLength == true,
          child: Positioned(
            right: maxLines != 1 ? 5.w : 10.w,
            bottom: maxLines != 1 ? 5.h : null,
            child: Obx(
              () => Text(
                "${inputString.value.length}/$maxLength",
                style: TextStyles.common(
                    14.sp, leftCountColor ?? AppColors.colorFF666666),
              ),
            ),
          ),
        ),
      ],
    );
  }

  static _buildBorder() {
    return const OutlineInputBorder(
      borderSide: BorderSide(
        color: Colors.transparent,
      ),
    );
  }

  ///输入框外观通用配置
  static InputDecoration textFieldDecoration(
      {required String hintText,
      required Widget suffixIcon,
      required BoxConstraints suffixIconConstraints}) {
    return InputDecoration(
      counterText: "",
      hintText: hintText,
      border: _buildBorder(),
      enabledBorder: _buildBorder(),
      disabledBorder: _buildBorder(),
      focusedBorder: _buildBorder(),
      contentPadding: EdgeInsets.zero,
      suffixIcon: suffixIcon,
      suffixIconConstraints: suffixIconConstraints,
    );
  }
}

/// 自定义兼容中文拼音输入法正则校验输入框
class CustomizedTextInputFormatter extends TextInputFormatter {
  final Pattern filterPattern;

  CustomizedTextInputFormatter({required this.filterPattern});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.isComposingRangeValid) return newValue;
    return FilteringTextInputFormatter.allow(filterPattern)
        .formatEditUpdate(oldValue, newValue);
  }
}

/// 自定义兼容中文拼音输入法长度限制输入框
class CustomizedLengthTextInputFormatter extends TextInputFormatter {
  final int maxLength;

  CustomizedLengthTextInputFormatter(this.maxLength);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.isComposingRangeValid) return newValue;
    return LengthLimitingTextInputFormatter(maxLength)
        .formatEditUpdate(oldValue, newValue);
  }
}
