import 'package:dada/common/values/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PermissionUseDescDialog extends StatefulWidget {
  final String title;
  final String desc;

  const PermissionUseDescDialog(
      {super.key, required this.title, required this.desc});

  @override
  State<PermissionUseDescDialog> createState() =>
      _PermissionUseDescDialogState();
}

class _PermissionUseDescDialogState extends State<PermissionUseDescDialog> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: TextStyles.medium(16.sp),
        ),
        Padding(
          padding: EdgeInsets.only(top: 3.h),
          child: Text(
            widget.desc,
            maxLines: 5,
            style: TextStyles.normal(14.sp),
          ),
        ),
      ],
    );
  }
}
