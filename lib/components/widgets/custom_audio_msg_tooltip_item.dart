import 'package:dada/components/widgets/save_audio_page.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class CustomAudioMsgTooltipItem {
  static MessageToolTipItem build({
    required V2TimMessage message,
    required Function() closeTooltip,
  }) {
    return MessageToolTipItem(
      label: S.current.saveAudio,
      id: "save_audio",
      iconImageAsset: Assets.imagesChatMsgItemTooltipSaveAudio,
      onClick: () async {
        V2TimMessageOnlineUrl? onlineUrl = await ChatIMManager.sharedInstance
            .getMessageOnlineAudioUrl(msgID: message.msgID!);
        String? audioUrl = onlineUrl?.soundElem?.url;
        int? duration = onlineUrl?.soundElem?.duration;
        if (audioUrl != null && duration != null) {
          closeTooltip.call();
          Get.to(
            () => SaveAudioPage(
              audioUrl: audioUrl,
              duration: duration,
              author: message.nickName,
            ),
          );
        }
      },
    );
  }
}
