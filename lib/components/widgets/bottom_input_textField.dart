import 'package:dada/common/values/colors.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class BottomInputTextField extends StatefulWidget {
  final String? hintText;
  final String? text;
  final int? maxLength;

  const BottomInputTextField({super.key, this.text, this.hintText, this.maxLength});

  @override
  State<BottomInputTextField> createState() => _BottomInputTextFieldState();
}

class _BottomInputTextFieldState extends State<BottomInputTextField> {
  TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();

    textEditingController.text = widget.text ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      height: 56.h,
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 40.h,
              margin: EdgeInsets.only(left: 15.w, right: 10.w),
              padding: EdgeInsets.only(left: 13.w, right: 10.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: AppColors.colorFFF5F5F5,
              ),
              child: CustomTextField.build(
                controller: textEditingController,
                hintText: widget.hintText,
                autofocus: true,
                maxLength: widget.maxLength,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 13.w),
            child: CommonGradientBtn(
              title: "发送",
              width: 84.w,
              height: 44.h,
              normalImage: Assets.imagesCommonGradientBtnBg44h,
              onTap: () {
                if (textEditingController.text.isNotEmpty) {
                  Get.back(result: textEditingController.text);
                } else {
                  ToastUtils.showToast("请输入发送内容");
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    textEditingController.dispose();
    super.dispose();
  }
}
