import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoadingWidget extends StatelessWidget {
  final String? loadingText;

  const LoadingWidget({super.key, this.loadingText});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Center(
        child: Container(
            width: 120.w,
            height: 100.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: Colors.black.withOpacity(0.75),
            ),
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 40.w,
                  height: 40.w,
                  child: const CircularProgressIndicator(),
                ),
                loadingText != null
                    ? Text(
                        loadingText!,
                        style: TextStyle(color: Colors.white, fontSize: 14.sp),
                      )
                    : const SizedBox()
              ],
            )),
      ),
    );
  }
}
