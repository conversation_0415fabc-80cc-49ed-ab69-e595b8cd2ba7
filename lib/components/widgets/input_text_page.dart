import 'dart:math';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class InputTextPage extends StatefulWidget {
  final String? title;
  final String? text;
  final String? placeholder;
  final bool? isMultiLine;
  final int? maxLines;
  final int? maxLength;
  final Function(String text) callback;
  final bool? readOnly;
  final bool? showRecommend;
  final double? height;

  const InputTextPage({
    super.key,
    this.title,
    this.text,
    this.placeholder,
    this.isMultiLine,
    this.maxLines,
    this.maxLength,
    required this.callback,
    this.readOnly,
    this.showRecommend,
    this.height,
  });

  @override
  State<InputTextPage> createState() => _InputTextPageState();
}

class _InputTextPageState extends State<InputTextPage> {
  RxString selectedLabel = "".obs;
  final TextEditingController editingController = TextEditingController();
  RxInt inputNum = 0.obs;
  FocusNode focusNode = FocusNode();

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    editingController.text = widget.text ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: widget.title,
        rightWidgets: [
          GestureDetector(
            onTap: () {
              widget.callback.call(editingController.text);
              Get.back();
            },
            child: Visibility(
              visible: widget.readOnly != true,
              child: Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: Text(
                  S.current.sure,
                  style: TextStyles.normal(16.sp,
                      c: AppTheme.themeData.colorScheme.onTertiary),
                ),
              ),
            ),
          ),
        ],
      ),
      body: ListView(
        children: [
          GestureDetector(
            onTap: () {
              focusNode.requestFocus();
            },
            child: Container(
              margin: EdgeInsets.only(top: 6.h, left: 10.w, right: 10.w),
              height:
                  widget.height ?? (widget.isMultiLine == true ? 160.h : 50.h),
              width: ScreenUtil().screenWidth,
              padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 3.h),
              alignment: widget.isMultiLine == true
                  ? Alignment.topLeft
                  : Alignment.centerLeft,
              decoration: BoxDecoration(
                color: AppTheme.themeData.colorScheme.inverseSurface,
                borderRadius: BorderRadius.circular(5.r),
                border: Border.all(
                  color: AppColors.colorFFE1E1E1,
                  width: 1.w,
                ),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: CustomTextField.build(
                      focusNode: focusNode,
                      readOnly: widget.readOnly,
                      controller: editingController,
                      hintText: widget.placeholder,
                      textInputAction: widget.isMultiLine == true
                          ? TextInputAction.newline
                          : TextInputAction.done,
                      keyboardType: widget.isMultiLine == true
                          ? null
                          : TextInputType.text,
                      // maxLines: isMultiLine == true ? maxLines ?? 5 : 1,
                      maxLines: 100,
                      showSuffixIcon: widget.isMultiLine == true ? false : true,
                      showLeftLength:
                          widget.readOnly == false && widget.maxLength != null,
                      maxLength: widget.maxLength,
                      onChanged: (value) {
                        inputNum.value = editingController.text.length;
                      },
                      suffixIconOnTap: () {
                        inputNum.value = editingController.text.length;
                        selectedLabel.value = "";
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          Visibility(
            visible: widget.showRecommend == true,
            child: Padding(
              padding: EdgeInsets.only(top: 15.h, left: 15.w, right: 15.w),
              child: Wrap(
                spacing: 15.w,
                runSpacing: 15.w,
                children: _getRecommendLabel()
                    .map((e) => _buildLabelItem(e))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLabelItem(String label) {
    return Obx(() {
      bool selected = selectedLabel.value == label;
      Color textColor = AppColors.colorFF999999;
      Color bgColor = Colors.white;
      Color borderColor = AppColors.colorFFE1E4E0;
      int randomIndex = Random().nextInt(4);
      if (selected) {
        textColor = _getRandomTextColor(randomIndex);
        bgColor = textColor.withOpacity(0.15);
        borderColor = textColor;
      }
      return LabelItemWidget(
        text: label,
        fontSize: 16.sp,
        height: 35.h,
        textColor: textColor,
        editable: false,
        padding: EdgeInsets.only(left: 14.w, right: 14.w),
        minWidth: 70.w,
        bgColor: bgColor,
        borderColor: borderColor,
        alignment: MainAxisAlignment.center,
        borderRadius: 17.5.r,
        onTap: () {
          selectedLabel.value = label;
          editingController.text = label;
        },
      );
    });
  }

  Color _getRandomTextColor(int index) {
    List<Color> colors = [
      AppColors.colorFFFF75AB,
      AppColors.colorFFEDB10C,
      AppColors.colorFF26DED7,
      AppColors.colorFFA847E8,
    ];
    return colors[index];
  }

  List<String> _getRecommendLabel() {
    return [
      "三排",
      "四排",
      "5人组队",
      "双人",
      "私人房间",
      "王者荣耀",
      "永劫无间",
      "绝地求生",
      "CS:go",
      "无畏契约",
      "守望先锋",
      "第五人格",
      "梦幻西游",
      "蛋仔派对",
      "金铲铲之战",
      "英雄联盟",
      "三角洲行动",
      "双人成行",
      "我的世界",
      "迷你世界",
      "崩坏3",
      "原神",
      "决战！平安京",
      "红警",
      "帝国时代",
      "和平精英",
      "逆战",
      "流放之路",
      "天涯明月刀",
      "逆水寒",
      "剑网3",
      "明日之后",
      "方舟：生存进化",
      "三国杀",
      "穿越火线",
      "生死狙击2",
    ];
  }
}
