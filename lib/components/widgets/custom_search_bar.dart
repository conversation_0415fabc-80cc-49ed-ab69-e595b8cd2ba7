import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final Function(String value) onChanged;
  final FocusNode? focusNode;
  final double? height;
  final EdgeInsets? margin;
  final double? cornerRadius;
  final Color? bgColor;
  final String? placeholder;
  final TextStyle? textStyle;
  final Function(String value)? onSubmit;

  const CustomSearchBar({
    super.key,
    required this.controller,
    required this.onChanged,
    this.height,
    this.margin,
    this.cornerRadius,
    this.bgColor,
    this.placeholder,
    this.textStyle,
    this.onSubmit,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 35.h,
      margin: margin ??
          EdgeInsets.symmetric(
            horizontal: 15.w,
            vertical: 10.h,
          ),
      padding: EdgeInsets.only(left: 9.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(cornerRadius ?? 17.5.r),
        color: bgColor ?? AppTheme.themeData.colorScheme.surfaceTint,
      ),
      child: Row(
        children: [
          ImageUtils.getImage(Assets.imagesHomeSearchIcon, 14.w, 14.w),
          SizedBox(
            width: 7.w,
          ),
          Expanded(
            child: CustomTextField.build(
              focusNode: focusNode,
              controller: controller,
              hintText: placeholder ?? S.current.search,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.text,
              style: textStyle ?? TextStyles.normal(16.sp),
              showSuffixIcon: true,
              onChanged: onChanged,
              onSubmitted: onSubmit,
            ),
          ),
        ],
      ),
    );
  }
}
