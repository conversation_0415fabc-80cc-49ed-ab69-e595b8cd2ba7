import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class SayHiDialog extends StatelessWidget {
  final String userId;
  SayHiDialog({super.key, required this.userId});

  final TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        height: 220.h,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                  offset: Offset(0, 4.h),
                  blurRadius: 15.r,
                  spreadRadius: 8.r,
                  color: Color(0xFF000000).withOpacity(0.15))
            ],
            gradient: LinearGradient(
                colors: [Color(0xFF57C4FF).withOpacity(0.32), Colors.white],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter)),
        child: Stack(
          alignment: Alignment.topCenter,
          clipBehavior: Clip.none,
          children: [
            Positioned(
                top: -36.h,
                child: Column(
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesDialogSayHiTopImg, 118.w, 67.h),
                    SizedBox(height: 13.h),
                    ImageUtils.getImage(
                        Assets.imagesDialogSayHiTitle, 96.56.w, 22.h),
                    SizedBox(height: 21.h),
                    Container(
                      width: 249.w,
                      height: 50.h,
                      padding: EdgeInsets.symmetric(horizontal: 8.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          color: Color(0xFF8F8F8F).withOpacity(0.07)),
                      child: CustomTextField.build(
                          controller: _controller,
                          hintText: "跟TA说点什么?",
                          hintStyle:
                              TextStyles.common(16.sp, Color(0xFF989898))),
                    ),
                    SizedBox(height: 23.h),
                    Row(
                      children: [
                        _bottomButtonBuilder(
                            context,
                            "取消",
                            Color(0xFF0865AC).withOpacity(0.07),
                            Color(0xFF000000)),
                        SizedBox(width: 22.w),
                        _bottomButtonBuilder(
                            context, "发送", Color(0xFF6CC0FF), Colors.white),
                      ],
                    )
                  ],
                ))
          ],
        ),
      ),
    );
  }

  Widget _bottomButtonBuilder(
      BuildContext c, String title, Color bacColor, Color textColor) {
    return GestureDetector(
      onTap: () async {
        if (title == '取消') {
          Get.back();
        } else {
          if (_controller.text.isEmpty) {
            ToastUtils.showToast('请输入您想说的话');
            return;
          }
          V2TimMessage? message = await ChatIMManager.sharedInstance
              .sendTextMessage(text: _controller.text, toUserID: userId);
          if (message == null) {
            ToastUtils.showToast('发送失败');
            return;
          }
          Get.back();
        }
      },
      child: Container(
        alignment: Alignment.center,
        width: 114.w,
        height: 38.h,
        decoration: BoxDecoration(
            color: bacColor, borderRadius: BorderRadius.circular(8.r)),
        child: Text(
          title,
          style: TextStyles.normal(16.sp, c: textColor),
        ),
      ),
    );
  }
}
