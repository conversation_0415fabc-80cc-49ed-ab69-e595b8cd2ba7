import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class ImageBrowserArgs {
  final List<String> tags;
  final List<String> imageUrls;
  final int? index;
  final List<AssetEntity>? assets;

  ImageBrowserArgs(this.tags, this.imageUrls, {this.index, this.assets});
}

class ImageBrowser extends StatefulWidget {
  final ImageBrowserArgs args;

  const ImageBrowser({super.key, required this.args});

  @override
  State<ImageBrowser> createState() => _ImageBrowserState();
}

class _ImageBrowserState extends State<ImageBrowser> {
  late int _currentPage;
  late ImageBrowserArgs _args;

  @override
  void initState() {
    super.initState();

    _args = widget.args;
    _currentPage = _args.index ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.back();
      },
      child: Container(
        color: Colors.black,
        child: _args.imageUrls.length == 1
            ? _singlePhotoView()
            : _multiPhotoView(),
      ),
    );
  }

  Widget _singlePhotoView() {
    AssetEntity? asset = _args.assets?.first;
    String imageUrl = _args.imageUrls.isNotEmpty ? _args.imageUrls.first : "";
    PhotoViewHeroAttributes heroAttributes =
        PhotoViewHeroAttributes(tag: _args.tags.first);
    return asset != null
        ? PhotoView(
            imageProvider: AssetEntityImageProvider(asset, isOriginal: true),
            heroAttributes: heroAttributes,
          )
        : fileIsExist(imageUrl)
            ? PhotoView(
                imageProvider: FileImage(File(imageUrl)),
                heroAttributes: heroAttributes,
              )
            : isNetworkUrl(imageUrl)
                ? PhotoView(
                    imageProvider: CachedNetworkImageProvider(imageUrl),
                    heroAttributes: heroAttributes,
                  )
                : PhotoView(
                    imageProvider: AssetImage(imageUrl),
                    heroAttributes: heroAttributes,
                  );
  }

  Widget _multiPhotoView() {
    return PhotoViewGallery.builder(
      itemCount:
          _args.assets != null ? _args.assets?.length : _args.imageUrls.length,
      pageController: PageController(initialPage: _currentPage),
      onPageChanged: (index) {
        setState(() {
          _currentPage = index;
        });
      },
      builder: ((context, index) {
        AssetEntity? asset = _args.assets?[index];
        String imageUrl =
            _args.imageUrls.isNotEmpty ? _args.imageUrls[index] : "";
        String tag = _args.tags[index];
        PhotoViewHeroAttributes heroAttributes =
            PhotoViewHeroAttributes(tag: tag);
        return asset != null
            ? PhotoViewGalleryPageOptions(
                imageProvider:
                    AssetEntityImageProvider(asset, isOriginal: true),
                heroAttributes: heroAttributes,
              )
            : fileIsExist(imageUrl)
                ? PhotoViewGalleryPageOptions(
                    imageProvider: ResizeImage(FileImage(File(imageUrl))),
                    heroAttributes: heroAttributes,
                  )
                : isNetworkUrl(imageUrl)
                    ? PhotoViewGalleryPageOptions(
                        imageProvider: NetworkImage(imageUrl),
                        heroAttributes: heroAttributes)
                    : PhotoViewGalleryPageOptions(
                        imageProvider: AssetImage(imageUrl),
                        heroAttributes: heroAttributes);
      }),
    );
  }

  bool fileIsExist(String path) {
    return File(path).existsSync();
  }

  bool isNetworkUrl(String url) {
    return url.startsWith("http://") || url.startsWith("https://");
  }
}
