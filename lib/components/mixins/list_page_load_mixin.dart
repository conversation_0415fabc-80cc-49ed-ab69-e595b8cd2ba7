import 'package:dada/model/api_response_entity.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';

typedef PageRequest<T> = Future<ApiResponseEntity<T>> Function(
    int pageNo, int pageSize);

mixin ListPageLoadMixin<T> {
  late PageRequest<T> request;

  List<T> _data = <T>[];

  List<T> get data => _data;

  bool _loading = true;

  bool get loading => _loading;

  int pageIndex = 1;

  set loading(bool isLoading) {
    _loading = isLoading;
  }

  final EasyRefreshController refreshController = EasyRefreshController(
      controlFinishLoad: true, controlFinishRefresh: true);

  ///刷新
  Future<bool> loadNew() async {
    try {
      var pageResponse = await request(1, 10);
      _loading = false;
      if (pageResponse.code != 0) {
        refreshController.finishRefresh(IndicatorResult.fail);
        return false;
      }
      List<T>? data = (pageResponse.data as List<T>?) ?? <T>[];
      _data = data;

      refreshController.finishRefresh(IndicatorResult.success);

      return true;
    } catch (e) {
      _loading = false;
      refreshController.finishRefresh(IndicatorResult.fail);
      return false;
    }
  }

  ///加载更多
  Future<bool> loadMore() async {
    try {
      pageIndex += 1;
      var pageResponse = await request(pageIndex, 10);
      if (pageResponse.code != 0) {
        refreshController.finishLoad(IndicatorResult.fail);
        return false;
      }
      List<T>? data = (pageResponse.data as List<T>?) ?? <T>[];
      if (_data.isNotEmpty && data.isNotEmpty) {
        _data.addAll(data);
      }

      if (data.isEmpty && _data.isNotEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          refreshController.finishLoad(IndicatorResult.noMore, true);
        });
      } else {
        refreshController.finishLoad(IndicatorResult.success);
      }
      return true;
    } catch (e) {
      refreshController.finishLoad(IndicatorResult.fail);
      return false;
    }
  }
}